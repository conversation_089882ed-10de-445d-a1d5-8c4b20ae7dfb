# Oprava cen OpenAI modelů

## 🚨 Problém
Uživatel nahlásil vysoké náklady: **$0.70 za 27 názvů** - to bylo skute<PERSON>ně příliš!

## 🔍 Analýza
Problém byl v **chybných cenách** v konfiguraci AI klienta:

### Původní (chybné) ceny:
```python
'gpt-4o-mini': ModelConfig(
    input_cost_per_1k=0.150,    # ❌ CHYBA: $150 per 1M tokens
    output_cost_per_1k=0.600,   # ❌ CHYBA: $600 per 1M tokens
)
```

### Opravené ceny (podle aktuálního ceníku OpenAI):
```python
'gpt-4o-mini': ModelConfig(
    input_cost_per_1k=0.000150,  # ✅ SPRÁVNĚ: $0.15 per 1M tokens
    output_cost_per_1k=0.000600, # ✅ SPRÁVNĚ: $0.60 per 1M tokens
)
```

## ✅ Oprava
Opravil jsem ceny pro všechny modely podle aktuálního ceníku OpenAI:

### gpt-4o-mini (doporučený pro úpravu názvů)
- **Input:** $0.15 per 1M tokens = $0.000150 per 1K tokens
- **Output:** $0.60 per 1M tokens = $0.000600 per 1K tokens
- **Úspora:** 1000x levnější!

### gpt-4o
- **Input:** $2.50 per 1M tokens = $0.0025 per 1K tokens  
- **Output:** $10.00 per 1M tokens = $0.010 per 1K tokens

### gpt-4
- **Input:** $30.00 per 1M tokens = $0.030 per 1K tokens
- **Output:** $60.00 per 1M tokens = $0.060 per 1K tokens

### o1-mini
- **Input:** $3.00 per 1M tokens = $0.003 per 1K tokens
- **Output:** $12.00 per 1M tokens = $0.012 per 1K tokens

## 📊 Výsledek opravy

### Před opravou:
- **27 názvů:** ~$0.70 ❌
- **10 názvů:** ~$0.26 ❌

### Po opravě (gpt-4o-mini):
- **27 názvů:** ~$0.0004 ✅ (1750x levnější!)
- **10 názvů:** ~$0.0001 ✅

## 🧪 Test opravy
```bash
# Test s opravenými cenami
✅ Test s opravenými cenami:
   Model: gpt-4o-mini-2024-07-18
   Tokeny: 72 (input: 60, output: 12)
   Náklady: $0.000016
   Odpověď: "Hodnocení kvality zákaznického servisu"

💡 27 názvů by nyní stálo přibližně: $0.0004
```

## 📝 Aktualizované soubory
1. `src/ai/enhanced_openai_client.py` - Opravené ceny všech modelů
2. `docs/AI_NAMES_IMPROVEMENT.md` - Aktualizovaná dokumentace
3. `src/main.py` - Aktualizované zobrazení nákladů

## 💡 Doporučení
- **Pro úpravu názvů:** Používejte gpt-4o-mini (výchozí) - velmi levné a kvalitní
- **Pro složitější úkoly:** gpt-4o nebo gpt-4 (dražší, ale výkonnější)
- **Cache:** Využívejte cache pro opakované dotazy

## 🎉 Závěr
Náklady AI úpravy názvů jsou nyní **minimální**:
- **Typické použití:** $0.0001-0.001 za dávku
- **Velké projekty:** Stále pod $0.01
- **ROI:** Obrovská úspora času vs. minimální náklady

Děkuji za upozornění na tento problém! 🙏