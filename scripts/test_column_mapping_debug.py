#!/usr/bin/env python3
"""
Test pro debug mapován<PERSON> sloupců mezi CSV a mapping
"""

import sys
import os
sys.path.append('src')

import pandas as pd

def test_column_mapping():
    """Test mapování sloupců"""
    
    survey_id = "827822"
    csv_path = f"src/data/{survey_id}/responses.csv"
    mapping_path = f"src/data/{survey_id}/question_mapping.csv"
    
    print("🔍 Debug mapování sloupců")
    print("=" * 60)
    
    # Načtení dat
    df = pd.read_csv(csv_path, sep=';')
    mapping_df = pd.read_csv(mapping_path)
    
    print(f"📊 CSV má {len(df.columns)} sloupců")
    print(f"📋 Mapování má {len(mapping_df)} otázek")
    
    # Analýza sloupců CSV
    print(f"\n🔍 Prvních 10 sloupců CSV:")
    for i, col in enumerate(df.columns[:10]):
        print(f"   [{i}]: '{col}'")
    
    # Analýza question_codes z mapování
    print(f"\n📋 Prvních 10 question_codes z mapování:")
    for i, code in enumerate(mapping_df['question_code'].head(10)):
        print(f"   [{i}]: '{code}'")
    
    # Test současného mapování
    all_question_codes = mapping_df['question_code'].tolist()
    question_cols = [col for col in all_question_codes if col in df.columns]
    
    print(f"\n❌ Současné mapování:")
    print(f"   Nalezené sloupce: {len(question_cols)}")
    print(f"   Příklady: {question_cols[:5]}")
    
    # Test vylepšeného mapování
    print(f"\n🔧 Vylepšené mapování:")
    
    # Vytvoříme mapování mezi čistými názvy a názvy s mezerami
    csv_cols_clean = {}
    for col in df.columns:
        clean_col = col.strip().strip('"').strip()
        csv_cols_clean[clean_col] = col
    
    print(f"   Příklady čištění sloupců:")
    for i, (clean, original) in enumerate(list(csv_cols_clean.items())[:5]):
        print(f"     '{original}' -> '{clean}'")
    
    # Nové mapování
    question_cols_fixed = []
    for code in all_question_codes:
        if code in csv_cols_clean:
            question_cols_fixed.append(csv_cols_clean[code])
    
    print(f"\n✅ Opravené mapování:")
    print(f"   Nalezené sloupce: {len(question_cols_fixed)}")
    print(f"   Příklady: {question_cols_fixed[:5]}")
    
    # Test id sloupce
    id_cols_original = [col for col in df.columns if col not in question_cols]
    id_cols_fixed = [col for col in df.columns if col not in question_cols_fixed]
    
    print(f"\n🆔 ID sloupce:")
    print(f"   Původní počet: {len(id_cols_original)}")
    print(f"   Opravený počet: {len(id_cols_fixed)}")
    print(f"   Příklady opravených: {id_cols_fixed[:5]}")

if __name__ == "__main__":
    test_column_mapping()
