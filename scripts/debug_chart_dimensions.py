#!/usr/bin/env python3
"""
Hloubkové prozkoumání rozměrů grafu PhLVp
"""

import json
import time
from src.datawrapper_client import DatawrapperClient

def debug_chart_dimensions():
    """Prozkoumání rozměrů grafu PhLVp"""
    print("=== HLOUBKOVÉ PROZKOUMÁNÍ ROZMĚRŮ GRAFU ===")
    
    chart_id = "PhLVp"  # Graf z uživatelova logu
    
    try:
        client = DatawrapperClient()
        
        print(f"🔍 Analyzuji graf {chart_id}...")
        
        # Získání úplných informací o grafu
        chart_info = client.get_chart(chart_id)
        
        if not chart_info:
            print("❌ Nepodařilo se získat informace o grafu")
            return
            
        print("✅ Informace o grafu získány")
        
        # Výpis VŠECH dostupných rozměrů
        print("\n📊 VŠECHNY DOSTUPNÉ ROZMĚRY:")
        
        metadata = chart_info.get("metadata", {})
        
        # 1. Publish metadata
        publish_data = metadata.get("publish", {})
        print(f"  publish.embed-width: {publish_data.get('embed-width')}")
        print(f"  publish.embed-height: {publish_data.get('embed-height')}")
        
        # 2. Visualize metadata (možná skutečné rozměry grafu)
        visualize_data = metadata.get("visualize", {})
        print(f"  visualize: {json.dumps(visualize_data, indent=4) if visualize_data else 'None'}")
        
        # 3. Describe metadata
        describe_data = metadata.get("describe", {})
        print(f"  describe: {json.dumps(describe_data, indent=4) if describe_data else 'None'}")
        
        # 4. Chart metadata
        chart_data = metadata.get("chart", {})
        print(f"  chart: {json.dumps(chart_data, indent=4) if chart_data else 'None'}")
        
        # 5. Celkové metadata
        print(f"\n📋 CELÁ METADATA STRUKTURA:")
        print(json.dumps(metadata, indent=2))
        
        # Test různých způsobů získání rozměrů
        print(f"\n🧪 TEST RŮZNÝCH ZDROJŮ ROZMĚRŮ:")
        
        # Způsob 1: embed rozměry (současný)
        embed_width = publish_data.get("embed-width")
        embed_height = publish_data.get("embed-height")
        if embed_width and embed_height:
            embed_ratio = embed_width / embed_height
            print(f"  1. embed rozměry: {embed_width}×{embed_height} (poměr {embed_ratio:.3f})")
        
        # Způsob 2: možné jiné zdroje rozměrů
        # Zkusím najít jiné možné zdroje rozměrů v metadatech
        
        # Test exportu s různými přístupy
        print(f"\n🔬 TEST EXPORTU S RŮZNÝMI PŘÍSTUPY:")
        
        # Přístup 1: Bez rozměrů (nechat Datawrapper rozhodnout)
        print("  Test 1: Export bez width/height parametrů...")
        test_export_no_dimensions(client, chart_id)
        
        # Přístup 2: S embed rozměry
        if embed_width and embed_height:
            print(f"  Test 2: Export s embed rozměry {embed_width}×{embed_height}...")
            test_export_with_dimensions(client, chart_id, embed_width, embed_height)
        
        # Přístup 3: Počkání a opětovné získání rozměrů
        print("  Test 3: Počkání 5 sekund a opětovné získání rozměrů...")
        time.sleep(5)
        
        chart_info_delayed = client.get_chart(chart_id)
        if chart_info_delayed:
            delayed_metadata = chart_info_delayed.get("metadata", {})
            delayed_publish = delayed_metadata.get("publish", {})
            delayed_width = delayed_publish.get("embed-width")
            delayed_height = delayed_publish.get("embed-height")
            
            if delayed_width and delayed_height:
                delayed_ratio = delayed_width / delayed_height
                print(f"    Po čekání: {delayed_width}×{delayed_height} (poměr {delayed_ratio:.3f})")
                
                if delayed_width != embed_width or delayed_height != embed_height:
                    print("    ⚠️  ROZMĚRY SE ZMĚNILY PO ČEKÁNÍ!")
                else:
                    print("    ✅ Rozměry zůstaly stejné")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()

def test_export_no_dimensions(client, chart_id):
    """Test exportu bez rozměrů"""
    try:
        # Upravím dočasně export_chart pro test bez rozměrů
        import requests
        
        params = {
            "borderWidth": 10,
            "zoom": 2,
            "plain": "true",
            "mode": "rgb",
            "unit": "px"
            # ŽÁDNÉ width/height!
        }
        
        response = requests.get(
            f"{client.api_url}/charts/{chart_id}/export/png",
            headers=client.headers,
            params=params
        )
        
        if response.status_code == 200:
            # Uložení a analýza
            with open(f"test_charts/debug_no_dimensions_{chart_id}.png", 'wb') as f:
                f.write(response.content)
            
            try:
                from PIL import Image
                with Image.open(f"test_charts/debug_no_dimensions_{chart_id}.png") as img:
                    width, height = img.size
                    ratio = width / height
                    print(f"    Výsledek: {width}×{height}px (poměr {ratio:.3f})")
                    
                    # Zpětný výpočet bez zoom
                    base_width = width // 2
                    base_height = height // 2
                    base_ratio = base_width / base_height
                    print(f"    Základní rozměry: {base_width}×{base_height}px (poměr {base_ratio:.3f})")
                    
            except Exception as e:
                print(f"    Chyba při analýze: {e}")
        else:
            print(f"    Export selhal: {response.status_code}")
            
    except Exception as e:
        print(f"    Chyba: {e}")

def test_export_with_dimensions(client, chart_id, width, height):
    """Test exportu s konkrétními rozměry"""
    try:
        png_data = client.export_chart(
            chart_id,
            export_format='png',
            target_width=width,
            border_width=10,
            zoom=2,
            plain=True,
            mode='rgb'
        )
        
        if png_data:
            with open(f"test_charts/debug_with_dimensions_{chart_id}.png", 'wb') as f:
                f.write(png_data)
            print(f"    Export úspěšný: test_charts/debug_with_dimensions_{chart_id}.png")
        else:
            print(f"    Export selhal")
            
    except Exception as e:
        print(f"    Chyba: {e}")

def main():
    print("HLOUBKOVÉ PROZKOUMÁNÍ ROZMĚRŮ GRAFU PhLVp")
    print("=" * 60)
    
    debug_chart_dimensions()
    
    print("\n" + "=" * 60)
    print("✅ Analýza dokončena")
    print("\nKlíčové otázky k prozkoumání:")
    print("1. Jsou embed-width/height skutečné rozměry?")
    print("2. Mění se rozměry po čase?")
    print("3. Existují jiné zdroje rozměrů?")
    print("4. Jak vypadá export bez rozměrů?")

if __name__ == "__main__":
    main()