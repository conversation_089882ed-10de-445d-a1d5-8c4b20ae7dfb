#!/usr/bin/env python3
"""
Test Menu 8 s v<PERSON>b<PERSON><PERSON> jazyka
"""

import sys
import os
sys.path.append('src')

def test_menu8_language_selection():
    """Test výběru jazyka v Menu 8"""
    
    survey_id = "827822"
    
    print("🧪 Test Menu 8 - výběr jazyka pro generování grafů")
    print("=" * 60)
    
    # Import potřebných modulů
    from translation_manager import TranslationManager

    # Změníme do src adresáře
    os.chdir('src')
    
    # Vytvoření TranslationManager
    tm = TranslationManager(survey_id)
    
    # Test dostupných jazykových verzí
    base_chart_data_path = f"data/{survey_id}/chart_data.json"
    available_languages = tm.get_available_chart_languages(base_chart_data_path)
    
    print(f"📁 Základní soubor: {base_chart_data_path}")
    print(f"   Existuje: {os.path.exists(base_chart_data_path)}")
    
    print(f"\n🌍 Dostupné jazykové verze:")
    for lang_code, lang_name in available_languages.items():
        print(f"   {lang_code}: {lang_name}")
        
        # Kontrola existence souboru
        if lang_code == 'original':
            filepath = base_chart_data_path
        else:
            filepath = base_chart_data_path.replace('.json', f'_{lang_code}.json')
        
        exists = os.path.exists(filepath)
        print(f"     Soubor: {filepath}")
        print(f"     Existuje: {exists}")
        
        if exists:
            import json
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"     Počet grafů: {len(data)}")
            print(f"     První název: '{data[0].get('name', 'N/A')}'")
        print()
    
    print(f"📊 Celkem dostupných verzí: {len(available_languages)}")
    
    # Simulace výběru jazyka
    print(f"\n🎯 Simulace výběru jazyka:")
    lang_options = list(available_languages.items())
    
    for i, (lang_code, lang_name) in enumerate(lang_options):
        print(f"   {i+1}. {lang_name} ({lang_code})")
        
        # Určení cesty k souboru
        if lang_code == 'original':
            chart_data_path = base_chart_data_path
        else:
            chart_data_path = base_chart_data_path.replace('.json', f'_{lang_code}.json')
        
        print(f"      → Cesta: {chart_data_path}")
        print(f"      → Dostupný: {os.path.exists(chart_data_path)}")
    
    print(f"\n✅ Test dokončen!")
    print(f"🎉 Menu 8 je připraveno pro výběr jazyka!")
    
    # Návrat do původního adresáře
    os.chdir('..')

if __name__ == "__main__":
    test_menu8_language_selection()
