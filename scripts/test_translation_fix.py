#!/usr/bin/env python3
"""
Test opravy aplikace anglických překladů
"""

import sys
import os
sys.path.append('src')

from translation_manager import TranslationManager
import json

def test_translation_fix():
    """Test opravy aplikace překladů"""

    survey_id = "827822"
    chart_data_path = f"src/data/{survey_id}/chart_data.json"
    translations_path = f"src/data/{survey_id}/translations_en-US.json"
    output_path = f"src/data/{survey_id}/chart_data_test_en.json"
    
    print("🔧 Test opravy aplikace anglických překladů")
    print("=" * 60)
    
    # Kontrola metadat v translations_en-US.json
    print("📋 Kontrola metadat v translations_en-US.json:")
    with open(translations_path, 'r', encoding='utf-8') as f:
        translations = json.load(f)
    
    metadata = translations.get('metadata', {})
    language_settings = translations.get('language_settings', {})
    
    print(f"   metadata.language: {metadata.get('language')}")
    print(f"   language_settings.chart_language: {language_settings.get('chart_language')}")
    
    if metadata.get('language') == 'en-US' and language_settings.get('chart_language') == 'en-US':
        print("   ✅ Metadata jsou správně nastavena!")
    else:
        print("   ❌ Metadata jsou stále špatně!")
        return
    
    # Test aplikace překladů
    print(f"\n🧪 Test aplikace překladů:")
    
    # Načtení původních dat
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    print(f"   Původní chart_data má {len(original_data)} grafů")
    
    # Aplikace překladů - musíme být v src adresáři
    import os
    os.chdir('src')
    tm = TranslationManager(survey_id)
    result = tm.apply_translations_from_language(f"data/{survey_id}/chart_data.json", "en-US", f"data/{survey_id}/chart_data_test_en.json")
    os.chdir('..')
    
    if result:
        print("   ✅ Aplikace překladů proběhla úspěšně!")
        
        # Kontrola výsledku
        with open(f"src/data/{survey_id}/chart_data_test_en.json", 'r', encoding='utf-8') as f:
            translated_data = json.load(f)
        
        print(f"   Přeložená data mají {len(translated_data)} grafů")
        
        # Porovnání prvního grafu
        if len(original_data) > 0 and len(translated_data) > 0:
            orig_title = original_data[0].get('title', '')
            trans_title = translated_data[0].get('title', '')
            
            print(f"   Původní název: '{orig_title}'")
            print(f"   Přeložený název: '{trans_title}'")
            
            if orig_title != trans_title:
                print("   🎯 Překlady byly aplikovány!")
            else:
                print("   ⚠️  Názvy jsou stejné - možná nejsou překlady k dispozici")
    else:
        print("   ❌ Aplikace překladů selhala!")

if __name__ == "__main__":
    test_translation_fix()
