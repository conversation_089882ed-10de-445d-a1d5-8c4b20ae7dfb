import os
import sys
import json

# Přidání cesty k src, aby bylo možné importovat moduly
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

from limesurvey_client import LimeSurveyClient
from logger import get_logger

logger = get_logger(__name__)

def generate_structure_printout(survey_id: str, output_file: str):
    """
    Vygeneruje HTML report se kompletní strukturou průzkumu přímo z API.
    """
    client = None # Inicializace pro finally blok
    try:
        logger.info(f"Spouštím generování tisku struktury pro průzkum ID: {survey_id}")
        client = LimeSurveyClient()
        
        # 1. Získat session key
        session_key = client.get_session_key()
        if not session_key:
            logger.error("Nepodařilo se získat session key. Končím.")
            return

        # 2. Získat seznam skupin a seřadit je
        groups = client._make_request("list_groups", [session_key, survey_id])
        if not groups:
            logger.error(f"Nepodařilo se načíst skupiny pro průzkum {survey_id}.")
            return
        
        groups = sorted(groups, key=lambda g: int(g.get('group_order', 0)))
        
        html = """
        <!DOCTYPE html>
        <html lang="cs">
        <head>
            <meta charset="UTF-8">
            <title>Tisk struktury průzkumu {survey_id}</title>
            <style>
                body {{ font-family: sans-serif; line-height: 1.6; margin: 20px; }}
                .group {{ border: 2px solid #007bff; border-radius: 10px; margin-bottom: 30px; padding: 20px; }}
                .group-title {{ color: #007bff; font-size: 2em; margin-bottom: 20px; }}
                .question {{ border: 1px solid #ccc; border-radius: 8px; margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; }}
                .question-title {{ font-size: 1.2em; font-weight: bold; color: #333; }}
                .question-meta {{ font-size: 0.9em; color: #666; margin-bottom: 10px; }}
                .question-text {{ margin-bottom: 15px; }}
                .details {{ margin-left: 20px; }}
                .details h4 {{ margin-bottom: 5px; color: #555; }}
                .details ul {{ list-style-type: none; padding-left: 0; }}
                .details li {{ background-color: #fff; border: 1px solid #ddd; padding: 8px; margin-bottom: 5px; border-radius: 4px; }}
                .code {{ font-family: monospace; background-color: #e9e9e9; padding: 2px 4px; border-radius: 3px; }}
                .hidden {{ display: none; }}
            </style>
        </head>
        <body>
            <h1>Tisk struktury průzkumu ID: {survey_id}</h1>
        """

        # 3. Projít skupiny a jejich otázky
        for group in groups:
            gid = group['gid']
            group_name = group['group_name']
            html += f'<div class="group">'
            html += f'<h2 class="group-title">Skupina {gid}: {group_name}</h2>'

            questions = client._make_request("list_questions", [session_key, survey_id, gid])
            if not questions:
                html += "<p>V této skupině nebyly nalezeny žádné otázky.</p>"
                html += "</div>"
                continue

            for q_summary in questions:
                qid = q_summary['qid']
                
                # 4. Pro každou otázku získat kompletní vlastnosti
                q_props = client.get_question_properties(qid)
                if not q_props:
                    logger.warning(f"Nepodařilo se získat vlastnosti pro otázku {qid}.")
                    continue

                html += f'<div class="question">'
                html += f'<h3 class="question-title">{q_props.get("title", "N/A")}</h3>'
                html += f'<div class="question-meta">QID: {qid} | Typ: <span class="code">{q_props.get("type", "N/A")}</span> | Povinná: {q_props.get("mandatory", "N/A")}</div>'
                html += f'<div class="question-text">{q_props.get("question", "")}</div>'

                # 5. Vytisknout subotázky (řádky matice)
                subquestions_data = q_props.get("subquestions", {})
                if isinstance(subquestions_data, str) and subquestions_data.strip().startswith('{'):
                    try:
                        subquestions_data = json.loads(subquestions_data)
                    except json.JSONDecodeError:
                        logger.warning(f"Nepodařilo se parsovat JSON string pro subotázky u QID {qid}")
                        subquestions_data = {}
                
                if isinstance(subquestions_data, dict) and subquestions_data:
                    html += '<div class="details">'
                    html += f'<h4>Subotázky ({len(subquestions_data)}):</h4>'
                    html += "<ul>"
                    for subq_key, subq_val in subquestions_data.items():
                        html += f'<li><span class="code">{subq_val.get("title", "")}</span>: {subq_val.get("question", "")}</li>'
                    html += "</ul></div>"

                # 6. Vytisknout možnosti odpovědí (sloupce matice)
                answeroptions_data = q_props.get("answeroptions", {})
                if isinstance(answeroptions_data, str) and answeroptions_data.strip().startswith('{'):
                    try:
                        answeroptions_data = json.loads(answeroptions_data)
                    except json.JSONDecodeError:
                        logger.warning(f"Nepodařilo se parsovat JSON string pro možnosti odpovědí u QID {qid}")
                        answeroptions_data = {}

                if isinstance(answeroptions_data, dict) and answeroptions_data:
                    html += '<div class="details">'
                    html += f'<h4>Možnosti odpovědí ({len(answeroptions_data)}):</h4>'
                    html += "<ul>"
                    for ans_key, ans_val in answeroptions_data.items():
                        html += f'<li><span class="code">{ans_val.get("code", "")}</span>: {ans_val.get("answer", "")}</li>'
                    html += "</ul></div>"
                
                # 7. Vytisknout podmínky (relevance equation)
                if "relevance" in q_props and q_props["relevance"] and str(q_props["relevance"]).strip() != "1":
                    html += '<div class="details">'
                    html += f'<h4>Podmínka zobrazení:</h4>'
                    html += f'<p class="code">{q_props["relevance"]}</p>'
                    html += "</div>"

                html += "</div>"

            html += "</div>"

        html += """
        </body>
        </html>
        """

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html)
        
        logger.info(f"HTML report byl úspěšně vygenerován do souboru: {output_file}")

    except Exception as e:
        logger.error(f"Došlo k neočekávané chybě: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if client and client.session_key:
            client.release_session()
            logger.info("Session key byla uvolněna.")

if __name__ == "__main__":
    SURVEY_ID_TO_DEBUG = "827822"
    OUTPUT_HTML_FILE = os.path.abspath(os.path.join(os.path.dirname(__file__), '../debug_structure_printout.html'))
    generate_structure_printout(SURVEY_ID_TO_DEBUG, OUTPUT_HTML_FILE)