#!/usr/bin/env python3
"""
Oprava klíčů v translations_en-US.json aby odpovídaly názvům v chart_data.json
"""

import sys
import os
sys.path.append('src')

import json

def fix_translation_keys():
    """Oprava klíčů v překladech"""
    
    survey_id = "827822"
    chart_data_path = f"src/data/{survey_id}/chart_data.json"
    translations_path = f"src/data/{survey_id}/translations_en-US.json"
    
    print("🔧 Oprava klíčů v translations_en-US.json")
    print("=" * 60)
    
    # Načtení dat
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        chart_data = json.load(f)
    
    with open(translations_path, 'r', encoding='utf-8') as f:
        translations = json.load(f)
    
    # Získání všech názvů z chart_data
    chart_names = [item.get('name', '') for item in chart_data]
    print(f"📊 Nalezeno {len(chart_names)} názvů v chart_data")
    
    # Vytvoření nového mapování
    new_question_names = {}
    
    # Mapování názvů (ruční, protože jsou velmi odlišné)
    name_mapping = {
        "Typ prohlížeče (technický údaj)": "Browser Type (Technical Data)",
        "Zastupovaná instituce/organizace": "Represented Institution/Organization",
        "Právní forma zúčastněných organizací": "Legal Form of Participating Organizations",
        "Specifikace odpovědi 'Jiným způsobem'": "Specification of 'In another way' response",
        "Specifikace odpovědi 'Jinak'": "Specification of 'Other' response",
        "Zájem o zaslání výsledků průzkumu": "Interest in Receiving Survey Results",
        "Zájem o zapojení do konzultace výstupů projektu": "Interest in Consulting on Project's Final Outputs",
        "Kontaktní e-mail": "Contact E-mail",
        "Hlavní tematické oblasti působení organizací": "Main Thematic Areas of Organizations",
        "Fáze zapojení do legislativního procesu (EU a ČR)": "Phases of Involvement in the Legislative Process (EU & CZ)",
        "Informační kanály o konzultacích k legislativě EU": "Information Channels for EU Legislation Consultations",
        "Relevance informací z komunikačních kanálů": "Relevance of Information from Communication Channels",
        "Informační kanály o implementaci legislativy EU v ČR": "Information Channels for EU Legislation Implementation in the Czech Republic",
        "Míra vysoké spokojenosti se zapojením do legislativy": "Rate of High Satisfaction with Legislative Involvement",
        "Míra vysoké nespokojenosti se zapojením do legislativy": "Rate of High Dissatisfaction with Legislative Involvement",
        "Míra ztotožnění s uvedenými názory": "Level of Agreement with Stated Opinions",
        "Postrádané informační kanály o legislativním procesu": "Missing Information Channels on the Legislative Process"
    }
    
    # Aplikace mapování
    for czech_name in chart_names:
        if czech_name in name_mapping:
            new_question_names[czech_name] = name_mapping[czech_name]
            print(f"   ✅ '{czech_name}' -> '{name_mapping[czech_name]}'")
        else:
            print(f"   ❌ Nenalezeno mapování pro: '{czech_name}'")
    
    # Aktualizace překladů
    translations['question_names'] = new_question_names
    
    # Uložení opravených překladů
    with open(translations_path, 'w', encoding='utf-8') as f:
        json.dump(translations, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ Opraveno {len(new_question_names)} klíčů v {translations_path}")
    print(f"📋 Celkem mapování: {len(name_mapping)}")

if __name__ == "__main__":
    fix_translation_keys()
