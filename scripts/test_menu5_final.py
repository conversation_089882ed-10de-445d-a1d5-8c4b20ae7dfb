#!/usr/bin/env python3
"""
Test finální opravy Menu 5 - transformace do long formátu
"""

import sys
import os
sys.path.append('src')

from data_transformer import transform_to_long_format
import pandas as pd

def test_menu5_final():
    """Test finální opravy transformace do long formátu"""
    
    survey_id = "827822"
    csv_path = f"src/data/{survey_id}/responses.csv"
    mapping_path = f"src/data/{survey_id}/question_mapping.csv"
    output_path = f"src/data/{survey_id}/responses_long_final.csv"
    
    print("🎯 Test finální opravy Menu 5")
    print("=" * 60)
    
    # Kontrola mapování
    mapping_df = pd.read_csv(mapping_path)
    print(f"📋 Mapování má {len(mapping_df)} otázek")
    print(f"   Příklady: {mapping_df['question_code'].head(5).tolist()}")
    
    # Test transformace s filtrováním dokončených
    print(f"\n🧪 Test 1: Transformace POUZE dokončených záznamů")
    success1 = transform_to_long_format(csv_path, mapping_path, output_path, filter_completed_only=True)
    
    if success1:
        df_long1 = pd.read_csv(output_path)
        print(f"   ✓ Úspěch! Long formát má {len(df_long1)} řádků")
        
        if len(df_long1) > 0:
            unique_ids = df_long1['id'].nunique()
            print(f"   📊 Unikátní ID respondentů: {unique_ids}")
            print(f"   📋 Sloupce: {list(df_long1.columns)}")
            print(f"   🔍 Ukázka prvního řádku:")
            print(f"     ID: {df_long1.iloc[0]['id']}")
            print(f"     Otázka: {df_long1.iloc[0]['question_code']}")
            print(f"     Odpověď: {df_long1.iloc[0]['response']}")
            
            # Očekáváme cca 58 dokončených záznamů
            if unique_ids >= 50:
                print(f"   🎯 Počet dokončených záznamů vypadá správně! ({unique_ids})")
            else:
                print(f"   ⚠️  Očekávali jsme ~58 dokončených záznamů, máme {unique_ids}")
        else:
            print("   ❌ Long formát je stále prázdný!")
    else:
        print("   ❌ Transformace selhala!")
    
    # Test transformace všech záznamů pro porovnání
    print(f"\n🧪 Test 2: Transformace VŠECH záznamů")
    output_path2 = f"src/data/{survey_id}/responses_long_all_final.csv"
    success2 = transform_to_long_format(csv_path, mapping_path, output_path2, filter_completed_only=False)
    
    if success2:
        df_long2 = pd.read_csv(output_path2)
        unique_ids2 = df_long2['id'].nunique()
        print(f"   ✓ Všechny záznamy: {len(df_long2)} řádků, {unique_ids2} respondentů")
    
    print(f"\n🎉 Závěr:")
    if success1 and len(pd.read_csv(output_path)) > 0:
        print("   ✅ Menu 5 je OPRAVENO! Transformace do long formátu funguje správně.")
    else:
        print("   ❌ Menu 5 stále nefunguje správně.")

if __name__ == "__main__":
    test_menu5_final()
