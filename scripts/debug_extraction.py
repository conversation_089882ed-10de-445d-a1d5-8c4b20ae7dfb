#!/usr/bin/env python3
"""
Debug extrakce škálových odpovědí na skutečných datech
"""

import sys
import os
import json

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def debug_extraction():
    """Debug extrakce na skutečných datech"""
    print("🔍 Debug extrakce škálových odpovědí...")
    
    try:
        from translation_manager import TranslationManager
        
        chart_data_path = "src/data/827822/chart_data.json"
        if not os.path.exists(chart_data_path):
            print("❌ Soubor chart_data.json neexistuje")
            return False
        
        tm = TranslationManager("827822")
        
        # Načtení dat
        with open(chart_data_path, 'r', encoding='utf-8') as f:
            chart_data = json.load(f)
        
        print(f"✅ Načteno {len(chart_data)} polo<PERSON>ek z chart_data.json")
        
        # Ruční extrakce pro debug
        all_response_labels = set()
        array_items = 0
        
        for i, item in enumerate(chart_data):
            if item.get('type') == 'array' and 'data' in item:
                array_items += 1
                print(f"\n📊 Array otázka {i+1}: {item.get('name', 'Bez názvu')[:50]}...")
                
                for j, data_item in enumerate(item['data'][:2]):  # Jen první 2 pro debug
                    if 'responses' in data_item:
                        responses = list(data_item['responses'].keys())
                        print(f"   Podotázka {j+1}: {len(responses)} odpovědí")
                        print(f"   Odpovědi: {responses}")
                        
                        for response_label in responses:
                            all_response_labels.add(response_label)
        
        print(f"\n✅ Nalezeno {array_items} array otázek")
        print(f"✅ Celkem {len(all_response_labels)} unikátních odpovědí")
        
        # Kontrola škálových odpovědí
        scale_responses = {"rozhodně ano", "spíše ano", "spíše ne", "rozhodně ne", "neumím to posoudit"}
        found_scale = all_response_labels & scale_responses
        
        print(f"\n🎯 Škálové odpovědi:")
        print(f"   Očekávané: {scale_responses}")
        print(f"   Nalezené: {found_scale}")
        print(f"   Chybí: {scale_responses - found_scale}")
        
        # Test TranslationManager extrakce
        print(f"\n🧪 Test TranslationManager extrakce...")
        strings = tm.extract_translatable_strings(chart_data_path)
        
        print(f"✅ TranslationManager extrahoval:")
        print(f"   - {len(strings['question_names'])} názvů otázek")
        print(f"   - {len(strings['question_texts'])} textů otázek")
        print(f"   - {len(strings['subquestions'])} podotázek")
        print(f"   - {len(strings['response_labels'])} odpovědí")
        
        tm_scale_found = set(strings['response_labels']) & scale_responses
        print(f"   - Škálové odpovědi: {tm_scale_found}")
        
        if len(tm_scale_found) >= 4:
            print("✅ TranslationManager správně extrahuje škálové odpovědi")
            return True
        else:
            print("❌ TranslationManager neextrahuje škálové odpovědi správně")
            print(f"   Všechny nalezené odpovědi: {list(strings['response_labels'])[:10]}...")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při debug: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_template_generation():
    """Test generování šablony"""
    print("\n🧪 Test generování šablony...")
    
    try:
        from translation_manager import TranslationManager
        
        chart_data_path = "src/data/827822/chart_data.json"
        if not os.path.exists(chart_data_path):
            print("❌ Soubor chart_data.json neexistuje")
            return False
        
        tm = TranslationManager("827822", "cs-CZ")
        
        # Vygenerování šablony
        if tm.create_translation_template_for_language(chart_data_path, "cs-CZ", overwrite=True):
            translation_file = f"src/data/827822/translations_cs-CZ.json"
            
            if os.path.exists(translation_file):
                with open(translation_file, 'r', encoding='utf-8') as f:
                    translations = json.load(f)
                
                response_labels = translations.get('response_labels', {})
                scale_responses = {"rozhodně ano", "spíše ano", "spíše ne", "rozhodně ne", "neumím to posoudit"}
                found_in_template = set(response_labels.keys()) & scale_responses
                
                print(f"✅ Šablona vygenerována: {translation_file}")
                print(f"✅ Response_labels v šabloně: {len(response_labels)}")
                print(f"✅ Škálové odpovědi v šabloně: {found_in_template}")
                
                if len(found_in_template) >= 4:
                    print("✅ Škálové odpovědi jsou v šabloně!")
                    
                    # Ukázka obsahu
                    print(f"\n📋 Ukázka response_labels:")
                    for i, (key, value) in enumerate(list(response_labels.items())[:10]):
                        print(f"   \"{key}\": \"{value}\"")
                    
                    return True
                else:
                    print("❌ Škálové odpovědi chybí v šabloně")
                    print(f"   Nalezené klíče: {list(response_labels.keys())[:10]}...")
                    return False
            else:
                print("❌ Soubor šablony nebyl vytvořen")
                return False
        else:
            print("❌ Nepodařilo se vygenerovat šablonu")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu šablony: {str(e)}")
        return False

def main():
    """Hlavní debug funkce"""
    print("🚀 Debug extrakce škálových odpovědí na skutečných datech")
    print("=" * 70)
    
    if debug_extraction():
        if test_template_generation():
            print("\n✅ Extrakce i generování šablony funguje!")
            print("\n🎯 Pokud stále nevidíš škálové odpovědi v Menu 10:")
            print("   1. Zkus znovu vygenerovat šablonu (Menu 10 → Volba 1)")
            print("   2. Zkontroluj soubor translations_cs-CZ.json")
            print("   3. Měl by obsahovat 'rozhodně ano', 'spíše ano', atd.")
        else:
            print("\n❌ Problém s generováním šablony")
    else:
        print("\n❌ Problém s extrakcí")

if __name__ == "__main__":
    main()
