#!/usr/bin/env python3
"""
Test opravy Menu 5 - transformace do long formátu
"""

import sys
import os
sys.path.append('src')

from data_transformer import transform_to_long_format, _filter_completed_responses
import pandas as pd

def test_menu5_fix():
    """Test opravy transformace do long formátu"""
    
    survey_id = "827822"
    csv_path = f"src/data/{survey_id}/responses.csv"
    mapping_path = f"src/data/{survey_id}/question_mapping.csv"
    output_path = f"src/data/{survey_id}/responses_long_fixed.csv"
    
    print("🔧 Test opravy Menu 5 - transformace do long formátu")
    print("=" * 60)
    
    # Test filtrování dokončených záznamů
    print("🧪 Test 1: Filtrování dokončených záznamů")
    df = pd.read_csv(csv_path, sep=';')
    print(f"   Ce<PERSON><PERSON>: {len(df)}")
    
    # Test opravené funkce
    completed_df = _filter_completed_responses(df)
    print(f"   Dokončené z<PERSON>y: {len(completed_df)}")
    
    if len(completed_df) > 0:
        print("   ✓ Filtrování funguje!")
        
        # Najdeme submitdate sloupec
        submitdate_col = None
        for col in df.columns:
            if col.strip().strip('"').strip() == 'submitdate':
                submitdate_col = col
                break
        
        if submitdate_col:
            print(f"   📅 Příklady dokončených submitdate:")
            for i, val in enumerate(completed_df[submitdate_col].tail(3)):
                print(f"     {val}")
    else:
        print("   ❌ Filtrování stále nefunguje!")
    
    print("\n🧪 Test 2: Kompletní transformace do long formátu")
    success = transform_to_long_format(csv_path, mapping_path, output_path, filter_completed_only=True)
    
    if success:
        df_long = pd.read_csv(output_path)
        print(f"   ✓ Úspěch! Long formát má {len(df_long)} řádků")
        unique_ids = df_long['id'].nunique()
        print(f"   📊 Unikátní ID respondentů: {unique_ids}")
        
        if len(df_long) > 0:
            print(f"   📋 Ukázka dat:")
            print(f"     Sloupce: {list(df_long.columns)}")
            print(f"     První řádek: {df_long.iloc[0]['question_code']} -> {df_long.iloc[0]['response']}")
        
        # Očekáváme cca 58 dokončených záznamů
        if unique_ids >= 50:
            print("   🎯 Počet dokončených záznamů vypadá správně!")
        else:
            print(f"   ⚠️  Očekávali jsme ~58 dokončených záznamů, máme {unique_ids}")
            
    else:
        print("   ❌ Transformace selhala!")

if __name__ == "__main__":
    test_menu5_fix()
