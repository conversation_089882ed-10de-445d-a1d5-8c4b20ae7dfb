#!/usr/bin/env python3
"""
Test opravy Menu 10 - má vytvořit chart_data_{jazyk}.json
"""

import sys
import os
sys.path.append('src')

from translation_manager import TranslationManager
import json
import shutil

def test_menu10_fix():
    """Test opravy Menu 10"""
    
    survey_id = "827822"
    
    print("🧪 Test opravy Menu 10")
    print("=" * 60)
    
    # Změníme do src adresáře
    os.chdir('src')
    
    chart_data_path = f"data/{survey_id}/chart_data.json"
    
    # Záloha původního souboru
    backup_path = f"data/{survey_id}/chart_data_backup.json"
    if os.path.exists(chart_data_path):
        shutil.copy2(chart_data_path, backup_path)
        print(f"📁 Vytvořena záloha: {backup_path}")
    
    # Načtení původních dat
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    original_first_name = original_data[0].get('name', '') if original_data else ''
    print(f"📊 Původní první název: '{original_first_name}'")
    
    # Simulace Menu 10 logiky
    print(f"\n🔧 Simulace nové Menu 10 logiky:")
    
    tm = TranslationManager(survey_id)
    
    # Test pro anglický jazyk
    selected_lang = "en-US"
    print(f"   Vybraný jazyk: {selected_lang}")
    print(f"   Vytvoří se soubor: chart_data_{selected_lang}.json")
    
    # Aplikace překladů podle nové logiky
    if selected_lang == 'legacy':
        output_path = f"data/{survey_id}/chart_data_translated.json"
        tm.translations = tm._load_translations()
        result = tm.apply_translations(chart_data_path, output_path)
        success_message = f"✓ Přeložená data uložena: {output_path}"
    else:
        result = tm.apply_translations_from_language(chart_data_path, selected_lang)
        output_path = f"data/{survey_id}/chart_data_{selected_lang}.json"
        success_message = f"✓ Přeložená data uložena: {output_path}"
    
    print(f"   Výsledek: {result}")
    
    if result:
        print(f"   {success_message}")
        print("   Pro generování grafů s překlady použijte přeložený soubor")
    else:
        print("   ✗ Chyba při aplikaci překladů")
    
    # Kontrola původního souboru
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        after_data = json.load(f)
    
    after_first_name = after_data[0].get('name', '') if after_data else ''
    print(f"\n📊 Kontrola původního souboru:")
    print(f"   Před: '{original_first_name}'")
    print(f"   Po:   '{after_first_name}'")
    print(f"   Nezměněn: {original_first_name == after_first_name}")
    
    # Kontrola vytvořeného souboru
    print(f"\n📁 Kontrola vytvořeného souboru:")
    print(f"   Soubor: {output_path}")
    print(f"   Existuje: {os.path.exists(output_path)}")
    
    if os.path.exists(output_path):
        with open(output_path, 'r', encoding='utf-8') as f:
            translated_data = json.load(f)
        
        translated_first_name = translated_data[0].get('name', '') if translated_data else ''
        print(f"   První název: '{translated_first_name}'")
        print(f"   Počet položek: {len(translated_data)}")
    
    # Test pro český jazyk
    print(f"\n🇨🇿 Test pro český jazyk:")
    selected_lang_cz = "cs-CZ"
    print(f"   Vybraný jazyk: {selected_lang_cz}")
    
    result_cz = tm.apply_translations_from_language(chart_data_path, selected_lang_cz)
    output_path_cz = f"data/{survey_id}/chart_data_{selected_lang_cz}.json"
    
    print(f"   Výsledek: {result_cz}")
    print(f"   Soubor: {output_path_cz}")
    print(f"   Existuje: {os.path.exists(output_path_cz)}")
    
    # Finální kontrola původního souboru
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        final_data = json.load(f)
    
    final_first_name = final_data[0].get('name', '') if final_data else ''
    print(f"\n📊 Finální kontrola:")
    print(f"   Původní: '{original_first_name}'")
    print(f"   Finální: '{final_first_name}'")
    
    if original_first_name == final_first_name:
        print(f"   ✅ ÚSPĚCH: Původní soubor zůstal nezměněn!")
    else:
        print(f"   ❌ PROBLÉM: Původní soubor byl změněn!")
        
        # Obnovení ze zálohy
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, chart_data_path)
            print(f"   🔄 Obnoveno ze zálohy")
    
    # Smazání zálohy
    if os.path.exists(backup_path):
        os.remove(backup_path)
        print(f"   🗑️  Záloha smazána")
    
    print(f"\n🎯 Test dokončen!")
    
    # Návrat do původního adresáře
    os.chdir('..')

if __name__ == "__main__":
    test_menu10_fix()
