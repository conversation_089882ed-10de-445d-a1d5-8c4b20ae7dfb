#!/usr/bin/env python3
"""
Doplnění kompletních českých překladů ze všech dat
"""

import sys
import os
sys.path.append('src')

import json

def complete_czech_translations():
    """Doplnění kompletních českých překladů"""
    
    survey_id = "827822"
    chart_data_path = f"src/data/{survey_id}/chart_data.json"
    czech_path = f"src/data/{survey_id}/translations_cs-CZ.json"
    
    print("🔧 Doplnění kompletních českých překladů")
    print("=" * 60)
    
    # Načtení aktuálního chart_data.json
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        chart_data = json.load(f)
    
    # Načtení aktuální české verze
    with open(czech_path, 'r', encoding='utf-8') as f:
        czech_data = json.load(f)
    
    print(f"📊 Extrakce všech textů z chart_data.json...")
    
    # Extrakce všech subquestions
    subquestions = set()
    responses = set()
    
    for item in chart_data:
        # Subquestions z array typů
        if item.get('type') == 'array' and 'data' in item:
            for data_item in item['data']:
                if 'subquestion' in data_item:
                    subquestions.add(data_item['subquestion'])
                
                # Responses z array typů
                if 'responses' in data_item:
                    for response_key in data_item['responses'].keys():
                        responses.add(response_key)
        
        # Responses z scale typů
        elif item.get('type') == 'scale' and 'data' in item:
            for data_item in item['data']:
                if 'label' in data_item:
                    responses.add(data_item['label'])
    
    print(f"   Nalezeno {len(subquestions)} subquestions")
    print(f"   Nalezeno {len(responses)} responses")
    
    # Mapování anglických textů na české
    subquestion_mapping = {
        "Czech government institutions satisfactorily inform the interested public about EU legislation.": "České vládní instituce uspokojivě informují zainteresovanou veřejnost o legislativě EU.",
        "The Czech Republic should be more active in the preparation of EU legislation.": "Česká republika by měla být aktivnější při přípravě legislativy EU.",
        "Czech organizations have sufficient opportunities to participate in EU legislative processes.": "České organizace mají dostatečné příležitosti k účasti na legislativních procesech EU.",
        "EU legislation is sufficiently adapted to Czech conditions during implementation.": "Legislativa EU je při implementaci dostatečně přizpůsobena českým podmínkám.",
        "There is good coordination between government departments in the preparation of EU legislation.": "Při přípravě legislativy EU existuje dobrá koordinace mezi vládními rezorty.",
        "It is necessary to work on timely communication and coordination to prevent late conflicts between government departments.": "Je třeba zapracovat na včasné komunikaci a koordinaci, aby se předešlo pozdním názorovým střetům mezi vládními rezorty.",
        "Czech organizations should be more systematically involved in the preparation of EU legislation.": "České organizace by měly být systematičtěji zapojovány do přípravy legislativy EU.",
        "The preparation of EU legislation should be more transparent and open to public participation.": "Příprava legislativy EU by měla být transparentnější a otevřenější veřejné účasti.",
        "During the creation of the content framework of the proposal": "Při tvoření obsahového rámce návrhu",
        "During the preparation of the legislative proposal by the European Commission": "Při přípravě legislativního návrhu Evropskou komisí",
        "During the creation of the national position (the Czech Republic's official stance on the legislative proposal)": "Při tvorbě národní pozice (oficiálního stanoviska ČR k legislativnímu návrhu)",
        "During the implementation phase of approved European legislation into Czech law": "Ve fázi implementace schválené evropské legislativy do českého právního řádu",
        "In none of the above phases": "Ani v jedné z výše uvedených fází"
    }
    
    response_mapping = {
        "Strongly agree": "rozhodně ano",
        "Agree": "spíše ano", 
        "Disagree": "spíše ne",
        "Strongly disagree": "rozhodně ne",
        "Cannot assess": "neumím to posoudit",
        "Very relevant": "velmi relevantní",
        "Mostly relevant": "převážně relevantní",
        "Little relevant": "málo relevantní",
        "Not relevant": "nerelevantní",
        "Always": "vždy",
        "Almost always": "skoro vždy",
        "In most cases": "ve většině případů",
        "In minority of cases": "v menšině případů",
        "Only exceptionally": "jen výjimečně",
        "Never": "nikdy",
        "Yes": "Ano",
        "No": "Ne"
    }
    
    # Doplnění subquestions
    for eng_text in subquestions:
        if eng_text in subquestion_mapping:
            czech_data['subquestions'][eng_text] = subquestion_mapping[eng_text]
            print(f"   ✅ Subquestion: '{eng_text[:50]}...' -> '{subquestion_mapping[eng_text][:50]}...'")
        else:
            # Pokud není mapování, ponech anglicky s poznámkou
            czech_data['subquestions'][eng_text] = f"[PŘELOŽIT] {eng_text}"
            print(f"   ⚠️  Bez mapování: '{eng_text[:50]}...'")
    
    # Doplnění responses
    for eng_text in responses:
        if eng_text in response_mapping:
            czech_data['scale_responses'][eng_text] = response_mapping[eng_text]
            print(f"   ✅ Response: '{eng_text}' -> '{response_mapping[eng_text]}'")
        else:
            # Pokud není mapování, ponech původní
            czech_data['scale_responses'][eng_text] = eng_text
            print(f"   ⚠️  Bez mapování: '{eng_text}'")
    
    # Uložení doplněné české verze
    with open(czech_path, 'w', encoding='utf-8') as f:
        json.dump(czech_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ Česká verze doplněna!")
    print(f"📁 Uloženo do: {czech_path}")
    print(f"📊 Celkem:")
    print(f"   Question names: {len(czech_data['question_names'])}")
    print(f"   Subquestions: {len(czech_data['subquestions'])}")
    print(f"   Scale responses: {len(czech_data['scale_responses'])}")

if __name__ == "__main__":
    complete_czech_translations()
