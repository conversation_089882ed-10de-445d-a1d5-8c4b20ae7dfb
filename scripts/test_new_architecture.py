#!/usr/bin/env python3
"""
Test nové architektury s chart_data_{jazyk}.json
"""

import sys
import os
sys.path.append('src')

from translation_manager import TranslationManager
import json

def test_new_architecture():
    """Test nové architektury"""
    
    survey_id = "827822"
    
    print("🏗️ Test nové architektury chart_data_{jazyk}.json")
    print("=" * 60)
    
    # Změníme do src adresáře
    os.chdir('src')
    
    chart_data_path = f"data/{survey_id}/chart_data.json"
    
    # Vytvoření TranslationManager
    tm = TranslationManager(survey_id)
    
    print(f"📁 Původní soubor: {chart_data_path}")
    print(f"   Existuje: {os.path.exists(chart_data_path)}")
    
    # Test 1: Aplikace českých překladů
    print(f"\n🇨🇿 Test 1: Aplikace českých překladů")
    success_cz = tm.apply_translations_from_language(chart_data_path, "cs-CZ")
    print(f"   Výsledek: {success_cz}")
    
    # Kontrola vytvořeného souboru
    czech_file = f"data/{survey_id}/chart_data_cs-CZ.json"
    print(f"   Vytvořený soubor: {czech_file}")
    print(f"   Existuje: {os.path.exists(czech_file)}")
    
    if os.path.exists(czech_file):
        with open(czech_file, 'r', encoding='utf-8') as f:
            czech_data = json.load(f)
        print(f"   Počet grafů: {len(czech_data)}")
        print(f"   První název: '{czech_data[0].get('name', 'N/A')}'")
    
    # Test 2: Aplikace anglických překladů
    print(f"\n🇺🇸 Test 2: Aplikace anglických překladů")
    success_en = tm.apply_translations_from_language(chart_data_path, "en-US")
    print(f"   Výsledek: {success_en}")
    
    # Kontrola vytvořeného souboru
    english_file = f"data/{survey_id}/chart_data_en-US.json"
    print(f"   Vytvořený soubor: {english_file}")
    print(f"   Existuje: {os.path.exists(english_file)}")
    
    if os.path.exists(english_file):
        with open(english_file, 'r', encoding='utf-8') as f:
            english_data = json.load(f)
        print(f"   Počet grafů: {len(english_data)}")
        print(f"   První název: '{english_data[0].get('name', 'N/A')}'")
    
    # Test 3: Kontrola původního souboru
    print(f"\n📊 Test 3: Kontrola původního souboru")
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    print(f"   Původní soubor nezměněn: {os.path.exists(chart_data_path)}")
    print(f"   Počet grafů: {len(original_data)}")
    print(f"   První název: '{original_data[0].get('name', 'N/A')}'")
    
    # Test 4: Dostupné jazykové verze
    print(f"\n🌍 Test 4: Dostupné jazykové verze")
    available_languages = tm.get_available_chart_languages(chart_data_path)
    print(f"   Dostupné verze:")
    for lang_code, lang_name in available_languages.items():
        print(f"     {lang_code}: {lang_name}")
    
    print(f"\n🎉 Test dokončen!")
    print(f"✅ Nová architektura funguje - původní soubor zůstává nezměněn")
    print(f"✅ Jazykové verze se vytváří jako samostatné soubory")
    
    # Návrat do původního adresáře
    os.chdir('..')

if __name__ == "__main__":
    test_new_architecture()
