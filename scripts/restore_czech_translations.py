#!/usr/bin/env python3
"""
Obnovení správné české verze translations_cs-CZ.json
"""

import sys
import os
sys.path.append('src')

import json

def restore_czech_translations():
    """Obnovení správné české verze"""
    
    survey_id = "827822"
    chart_data_path = f"src/data/{survey_id}/chart_data.json"
    czech_path = f"src/data/{survey_id}/translations_cs-CZ.json"
    
    print("🔧 Obnovení správné české verze translations_cs-CZ.json")
    print("=" * 60)
    
    # Načtení aktuálního chart_data.json
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        chart_data = json.load(f)
    
    # Získání aktuálních názvů
    current_names = [item.get('name', '') for item in chart_data]
    print(f"📊 Aktuální názvy v chart_data.json:")
    for i, name in enumerate(current_names[:5]):
        print(f"   [{i+1}]: '{name}'")
    
    # Vytvoření správné české struktury
    czech_data = {
        "metadata": {
            "survey_id": survey_id,
            "language": "cs-CZ",
            "created": "restored-functional-version",
            "version": "3.0",
            "structure": "categorized_for_llm_safety"
        },
        "language_settings": {
            "chart_language": "cs-CZ",
            "available_languages": {
                "cs-CZ": "Čeština",
                "en-US": "English (US)",
                "en-GB": "English (UK)",
                "de-DE": "Deutsch",
                "fr-FR": "Français",
                "es-ES": "Español"
            }
        },
        "question_names": {},
        "subquestions": {},
        "scale_responses": {},
        "choice_responses": {},
        "free_text_responses": {"by_question": {}},
        "chart_titles": {}
    }
    
    # Mapování aktuálních názvů na zkrácené české názvy
    name_mapping = {
        "Typ prohlížeče": "Typ prohlížeče (technický údaj)",
        "Uveďte prosím název instituce/organizace/společnosti, kterou v tomto dotazníkovém průzkumu zastupujete.": "Zastupovaná instituce/organizace",
        "Uveďte, prosím, právní formu této organizace.": "Právní forma zúčastněných organizací",
        "Jiným způsobem. Můžete uvést jakým?": "Specifikace odpovědi 'Jiným způsobem'",
        "Jinak? Můžete uvést jak?": "Specifikace odpovědi 'Jinak'",
        "Přejete si být informováni o výsledcích průzkumu?": "Zájem o zaslání výsledků průzkumu",
        "Přejete si zapojit se do konzultování/připomínkování finálního výstupu tohoto projektu, ve kterém budou formulovány stěžejní závěry a doporučení?": "Zájem o zapojení do konzultace výstupů projektu",
        "Vyplňte, prosím, kontaktní mail.": "Kontaktní e-mail",
        "Uveďte prosím 1-3 hlavní tematické oblasti, kterými se Vaše instituce/organizace/ společnost zabývá.": "Hlavní tematické oblasti působení organizací",
        "V jakých fázích se Vaše organizace přímo zapojuje do přípravy evropských právních předpisů a jejich následné implementace do českého právního řádu.": "Fáze zapojení do legislativního procesu (EU a ČR)",
        "Prostřednictvím kterých komunikačních kanálů se dozvídáte o možnosti zapojit se do konzultování právních předpisů připravovaných na půdě EU?": "Informační kanály o konzultacích k legislativě EU",
        "Jak relevantní jsou informace a podklady, které získává Vaše organizace prostřednictvím těchto komunikačních kanálů?": "Relevance informací z komunikačních kanálů",
        "Prostřednictvím kterých komunikačních kanálů se dozvídáte o možnosti zapojit se do procesu implementace evropských právních předpisů do českého právního řádu?": "Informační kanály o implementaci legislativy EU v ČR",
        "Odhadněte prosím podíl případů, kdy jste byli OPRAVDU SPOKOJENI s níže uvedeným aspekty zapojení do přípravy a implementace evropské legislativy.": "Míra vysoké spokojenosti se zapojením do legislativy",
        "Odhadněte prosím podíl případů, kdy jste byli VÝRAZNĚ NESPOKOJENI s níže uvedeným aspekty zapojení do přípravy a implementace evropské legislativy.": "Míra vysoké nespokojenosti se zapojením do legislativy",
        "Ztotožňujete s níže uvedenými názory?": "Míra ztotožnění s uvedenými názory",
        "Chybí vám jedna nebo více možností informování o legislativním procesu:": "Postrádané informační kanály o legislativním procesu"
    }
    
    # Aplikace mapování na aktuální názvy
    mapped_count = 0
    for czech_name in current_names:
        if czech_name in name_mapping:
            czech_data['question_names'][czech_name] = name_mapping[czech_name]
            print(f"   ✅ '{czech_name}' -> '{name_mapping[czech_name]}'")
            mapped_count += 1
        else:
            # Pokud není mapování, použij původní název
            czech_data['question_names'][czech_name] = czech_name
            print(f"   ⚠️  Bez mapování: '{czech_name}'")
    
    # Základní subquestions
    czech_data['subquestions'] = {
        "Při tvoření obsahového rámce návrhu": "Při tvoření obsahového rámce návrhu",
        "Při přípravě legislativního návrhu Evropskou komisí": "Při přípravě legislativního návrhu Evropskou komisí",
        "Při tvorbě národní pozice (oficiálního stanoviska ČR k legislativnímu návrhu)": "Při tvorbě národní pozice (oficiálního stanoviska ČR k legislativnímu návrhu)",
        "Ve fázi implementace schválené evropské legislativy do českého právního řádu": "Ve fázi implementace schválené evropské legislativy do českého právního řádu",
        "Ani v jedné z výše uvedených fází": "Ani v jedné z výše uvedených fází"
    }
    
    # Základní scale_responses
    czech_data['scale_responses'] = {
        "rozhodně ano": "rozhodně ano",
        "spíše ano": "spíše ano",
        "spíše ne": "spíše ne",
        "rozhodně ne": "rozhodně ne",
        "neumím to posoudit": "neumím to posoudit",
        "velmi relevantní": "velmi relevantní",
        "převážně relevantní": "převážně relevantní",
        "málo relevantní": "málo relevantní",
        "nerelevantní": "nerelevantní"
    }
    
    # Základní choice_responses
    czech_data['choice_responses'] = {
        "Ano": "Ano",
        "Ne": "Ne"
    }
    
    # Uložení opravené české verze
    with open(czech_path, 'w', encoding='utf-8') as f:
        json.dump(czech_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ Česká verze obnovena!")
    print(f"📁 Uloženo do: {czech_path}")
    print(f"📊 Mapováno: {mapped_count}/{len(current_names)} názvů")

if __name__ == "__main__":
    restore_czech_translations()
