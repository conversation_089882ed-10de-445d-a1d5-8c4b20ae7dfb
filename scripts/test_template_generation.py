#!/usr/bin/env python3
"""
Test generování šablony pro nový jazyk
"""

import sys
import os
sys.path.append('src')

from translation_manager import TranslationManager
import json

def test_template_generation():
    """Test generování šablony pro nový jazyk"""
    
    survey_id = "827822"
    chart_data_path = f"data/{survey_id}/chart_data.json"
    test_language = "de-DE"
    
    print("🧪 Test generování šablony pro nový jazyk")
    print("=" * 60)
    
    # Změníme do src adresáře
    os.chdir('src')
    
    # Vytvoříme TranslationManager
    tm = TranslationManager(survey_id)
    
    print(f"📋 Aktuální jazyk: {tm.current_language}")
    print(f"📁 Survey dir: {tm.survey_dir}")
    
    # Test vytvoření šablony pro němčinu
    print(f"\n🇩🇪 Vytváření šablony pro {test_language}...")
    
    # Nejdříve smažeme soubor, pokud existuje
    test_file = f"data/{survey_id}/translations_{test_language}.json"
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"   🗑️  Smazán existující soubor: {test_file}")
    
    # Vytvoříme šablonu
    success = tm.create_translation_template_for_language(chart_data_path, test_language, overwrite=True)
    
    if success:
        print(f"   ✅ Šablona úspěšně vytvořena!")
        
        # Kontrola metadat
        with open(test_file, 'r', encoding='utf-8') as f:
            template_data = json.load(f)
        
        metadata = template_data.get('metadata', {})
        language_settings = template_data.get('language_settings', {})
        
        print(f"\n🔍 Kontrola metadat:")
        print(f"   metadata.language: {metadata.get('language')}")
        print(f"   metadata.survey_id: {metadata.get('survey_id')}")
        print(f"   language_settings.chart_language: {language_settings.get('chart_language')}")
        
        if metadata.get('language') == test_language and language_settings.get('chart_language') == test_language:
            print(f"   ✅ Metadata jsou správně nastavena pro {test_language}!")
        else:
            print(f"   ❌ Metadata jsou špatně nastavena!")
        
        # Kontrola obsahu
        question_names = template_data.get('question_names', {})
        print(f"\n📊 Obsah šablony:")
        print(f"   Počet question_names: {len(question_names)}")
        print(f"   Příklady klíčů: {list(question_names.keys())[:3]}")
        
        # Smazání testovacího souboru
        os.remove(test_file)
        print(f"\n🗑️  Testovací soubor smazán")
        
    else:
        print(f"   ❌ Vytvoření šablony selhalo!")
    
    # Návrat do původního adresáře
    os.chdir('..')

if __name__ == "__main__":
    test_template_generation()
