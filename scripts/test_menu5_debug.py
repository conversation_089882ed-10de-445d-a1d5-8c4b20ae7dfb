#!/usr/bin/env python3
"""
Test pro debug Menu 5 - transformace do long formátu
"""

import sys
import os
sys.path.append('src')

from data_transformer import transform_to_long_format
import pandas as pd

def test_menu5_debug():
    """Test transformace do long formátu s debugem"""
    
    survey_id = "827822"
    csv_path = f"src/data/{survey_id}/responses.csv"
    mapping_path = f"src/data/{survey_id}/question_mapping.csv"
    output_path = f"src/data/{survey_id}/responses_long_test.csv"
    
    print("🔍 Debug Menu 5 - transformace do long formátu")
    print("=" * 60)
    
    # Načtení CSV pro analýzu
    print("📊 Analýza CSV dat:")
    df = pd.read_csv(csv_path, sep=';')
    print(f"   Celkem záznamů: {len(df)}")
    
    # Analýza submitdate
    if 'submitdate' in df.columns:
        completed_by_submitdate = df[df['submitdate'].notna() & (df['submitdate'] != '')]
        print(f"   Dokončené podle submitdate: {len(completed_by_submitdate)}")
        print(f"   Nedokončené podle submitdate: {len(df) - len(completed_by_submitdate)}")
    
    # Analýza lastpage
    if 'lastpage' in df.columns:
        max_page = df['lastpage'].max()
        completed_by_lastpage = df[df['lastpage'] == max_page] if pd.notna(max_page) else pd.DataFrame()
        print(f"   Max lastpage: {max_page}")
        print(f"   Dokončené podle lastpage: {len(completed_by_lastpage)}")
    
    print("\n🧪 Test 1: Transformace POUZE dokončených záznamů")
    success1 = transform_to_long_format(csv_path, mapping_path, output_path, filter_completed_only=True)
    if success1:
        df_long1 = pd.read_csv(output_path)
        print(f"   ✓ Úspěch! Long formát má {len(df_long1)} řádků")
        unique_ids = df_long1['id'].nunique()
        print(f"   📊 Unikátní ID respondentů: {unique_ids}")
    else:
        print("   ✗ Chyba při transformaci!")
    
    print("\n🧪 Test 2: Transformace VŠECH záznamů")
    output_path2 = f"src/data/{survey_id}/responses_long_all_test.csv"
    success2 = transform_to_long_format(csv_path, mapping_path, output_path2, filter_completed_only=False)
    if success2:
        df_long2 = pd.read_csv(output_path2)
        print(f"   ✓ Úspěch! Long formát má {len(df_long2)} řádků")
        unique_ids2 = df_long2['id'].nunique()
        print(f"   📊 Unikátní ID respondentů: {unique_ids2}")
    else:
        print("   ✗ Chyba při transformaci!")
    
    print("\n💡 Doporučení:")
    if success1 and success2:
        if len(pd.read_csv(output_path)) == 0:
            print("   ⚠️  Filtrování dokončených záznamů vrací prázdný výsledek!")
            print("   🔧 Řešení: Spusťte Menu 5 s volbou 'Všechny záznamy'")
        else:
            print("   ✓ Filtrování dokončených záznamů funguje správně")

if __name__ == "__main__":
    test_menu5_debug()
