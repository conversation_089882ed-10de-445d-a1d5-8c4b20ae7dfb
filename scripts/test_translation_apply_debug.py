#!/usr/bin/env python3
"""
Debug test pro aplikaci překladů - kontrola cest a obsahu
"""

import sys
import os
sys.path.append('src')

from translation_manager import TranslationManager
import json
import hashlib

def get_file_hash(filepath):
    """Získá hash souboru pro porovnání"""
    try:
        with open(filepath, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    except:
        return None

def test_translation_apply_debug():
    """Debug test pro aplikaci překladů"""
    
    survey_id = "827822"
    
    print("🔍 Debug test aplikace překladů")
    print("=" * 60)
    
    # Změníme do src adresáře
    os.chdir('src')
    
    # Cesty k souborům
    chart_data_path = f"data/{survey_id}/chart_data.json"
    translations_path = f"data/{survey_id}/translations_en-US.json"
    
    print(f"📁 Pracovní adres<PERSON>ř: {os.getcwd()}")
    print(f"📊 Chart data: {chart_data_path}")
    print(f"📋 Překlady: {translations_path}")
    
    # Kontrola existence souborů
    print(f"\n🔍 Kontrola existence souborů:")
    print(f"   Chart data existuje: {os.path.exists(chart_data_path)}")
    print(f"   Překlady existují: {os.path.exists(translations_path)}")
    
    if not os.path.exists(chart_data_path):
        print(f"   ❌ Chart data neexistuje!")
        return
    
    # Hash původního souboru
    original_hash = get_file_hash(chart_data_path)
    print(f"   Původní hash: {original_hash}")
    
    # Načtení původních dat
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    print(f"   Původní počet grafů: {len(original_data)}")
    print(f"   První název: '{original_data[0].get('name', 'N/A')}'")
    
    # Vytvoření TranslationManager
    tm = TranslationManager(survey_id)
    
    print(f"\n🔧 TranslationManager:")
    print(f"   Aktuální jazyk: {tm.current_language}")
    print(f"   Survey dir: {tm.survey_dir}")
    
    # Test aplikace překladů s explicitní cestou
    print(f"\n🧪 Test aplikace překladů:")
    print(f"   Aplikuji překlady z en-US na chart_data.json...")
    
    # Aplikace překladů
    success = tm.apply_translations_from_language(chart_data_path, "en-US", chart_data_path)
    
    print(f"   Výsledek: {success}")
    
    # Kontrola změn
    new_hash = get_file_hash(chart_data_path)
    print(f"   Nový hash: {new_hash}")
    print(f"   Soubor se změnil: {original_hash != new_hash}")
    
    # Načtení nových dat
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        new_data = json.load(f)
    
    print(f"   Nový počet grafů: {len(new_data)}")
    print(f"   První název: '{new_data[0].get('name', 'N/A')}'")
    
    # Porovnání prvních názvů
    if len(original_data) > 0 and len(new_data) > 0:
        orig_name = original_data[0].get('name', '')
        new_name = new_data[0].get('name', '')
        
        print(f"\n📊 Porovnání prvního grafu:")
        print(f"   Původní: '{orig_name}'")
        print(f"   Nový: '{new_name}'")
        print(f"   Změnil se: {orig_name != new_name}")
    
    # Návrat do původního adresáře
    os.chdir('..')

if __name__ == "__main__":
    test_translation_apply_debug()
