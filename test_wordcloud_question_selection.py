#!/usr/bin/env python3
"""
Test nového výběru textových otázek pro WordCloud
"""

import os
import sys
import json

# Přidání src do path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_question_selection_display():
    """Test zobrazení číslovaného seznamu otázek"""
    print("=== Test číslovaného výběru textových otázek ===")
    
    try:
        from menu.ai_menu_functions import _get_text_questions_from_survey
        
        # Test s existujícím průzkumem
        test_survey_id = "827822"
        
        print(f"🔍 Testování s průzkumem {test_survey_id}...")
        
        # Načtení textových otázek
        text_questions = _get_text_questions_from_survey(test_survey_id)
        
        if not text_questions:
            print("⚠️ Žádné textové otázky nenalezeny")
            print("💡 Zkontrolujte, zda průzkum existuje a obsahuje textové otázky")
            return
        
        # Simulace zobrazení číslovaného seznamu
        question_list = list(text_questions.items())
        
        print(f"\n📋 Nalezeno {len(text_questions)} textových otázek:")
        print("=" * 80)
        
        for i, (q_id, q_info) in enumerate(question_list, 1):
            q_text = q_info['name']
            response_count = q_info.get('response_count', 0)
            
            # Zobrazení s číslováním
            print(f"{i:2d}. [{q_id}] {q_text}")
            print(f"     📊 Počet odpovědí: {response_count}")
            print()
        
        print("=" * 80)
        
        # Test parsování výběru
        test_selections = [
            "1",           # Jedna otázka
            "1,2",         # Dvě otázky
            "1,3,5",       # Více otázek (pokud existují)
            "",            # Všechny otázky
            "1,99",        # Neplatné číslo
            "abc",         # Neplatný formát
        ]
        
        print(f"\n🧪 Test parsování výběrů:")
        
        for test_input in test_selections:
            print(f"\nTest vstupu: '{test_input}'")
            
            if not test_input:
                # Všechny otázky
                selected_questions = [q_id for q_id, _ in question_list]
                print(f"✅ Všechny otázky ({len(selected_questions)} otázek)")
                continue
            
            try:
                selected_numbers = [int(num.strip()) for num in test_input.split(',') if num.strip()]
                
                # Validace čísel
                valid_numbers = [num for num in selected_numbers if 1 <= num <= len(question_list)]
                invalid_numbers = [num for num in selected_numbers if num not in valid_numbers]
                
                if invalid_numbers:
                    print(f"⚠️ Neplatná čísla: {', '.join(map(str, invalid_numbers))}")
                
                if valid_numbers:
                    selected_questions = [question_list[num-1][0] for num in valid_numbers]
                    print(f"✅ Vybrané otázky ({len(selected_questions)}):")
                    for num in valid_numbers:
                        q_id, q_info = question_list[num-1]
                        q_text = q_info['name'][:50] + "..." if len(q_info['name']) > 50 else q_info['name']
                        print(f"   {num}. [{q_id}] {q_text}")
                else:
                    print("❌ Žádná platná čísla")
                
            except ValueError:
                print("❌ Neplatný formát čísel")
        
        print(f"\n✅ Test číslovaného výběru úspěšný!")
        
    except Exception as e:
        print(f"❌ Chyba při testu: {e}")
        import traceback
        traceback.print_exc()

def test_integration_with_menu():
    """Test integrace s menu funkcí"""
    print("\n=== Test integrace s menu ===")
    
    try:
        # Test importu aktualizované funkce
        from menu.ai_menu_functions import menu_ai_wordcloud
        print("✅ Menu funkce úspěšně importována")
        
        # Test helper funkcí
        from menu.ai_menu_functions import _get_text_questions_from_survey, _get_text_responses_from_survey
        print("✅ Helper funkce dostupné")
        
        print("💡 Nový číslovaný výběr je připraven k použití v Menu 20")
        
    except Exception as e:
        print(f"❌ Chyba při testu integrace: {e}")

def create_demo_output():
    """Vytvoří ukázku výstupu nového výběru"""
    print("\n=== Ukázka nového výběru ===")
    
    # Simulace textových otázek
    demo_questions = {
        "G3Q00002": {
            "name": "Jiným způsobem. Můžete uvést jakým?",
            "type": "text",
            "response_count": 3
        },
        "G4Q00002": {
            "name": "Jinak? Můžete uvést jak?", 
            "type": "text",
            "response_count": 5
        },
        "Q15": {
            "name": "Máte nějaké další komentáře nebo návrhy na zlepšení našich služeb?",
            "type": "text", 
            "response_count": 12
        },
        "Q20": {
            "name": "Co se vám na naší organizaci líbí nejvíce?",
            "type": "text",
            "response_count": 8
        }
    }
    
    question_list = list(demo_questions.items())
    
    print(f"\n📋 Nalezeno {len(demo_questions)} textových otázek:")
    print("=" * 80)
    
    for i, (q_id, q_info) in enumerate(question_list, 1):
        q_text = q_info['name']
        response_count = q_info.get('response_count', 0)
        
        print(f"{i:2d}. [{q_id}] {q_text}")
        print(f"     📊 Počet odpovědí: {response_count}")
        print()
    
    print("=" * 80)
    print(f"\n🎯 Výběr textových otázek:")
    print(f"Zadejte čísla otázek oddělená čárkou (např. 1,3,4)")
    print(f"Pro výběr všech otázek stiskněte Enter")
    print(f"\nPříklady výběru:")
    print(f"  '1' → Vybere pouze první otázku")
    print(f"  '1,3' → Vybere otázky 1 a 3")
    print(f"  '2,3,4' → Vybere otázky 2, 3 a 4")
    print(f"  '' (Enter) → Vybere všechny otázky")

if __name__ == "__main__":
    test_question_selection_display()
    test_integration_with_menu()
    create_demo_output()