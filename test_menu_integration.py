#!/usr/bin/env python3
"""
Test integrace AI úpravy názvů do menu 10
"""

import os
import sys
import json

# Přidání src do path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_chart_data():
    """Vytvoří testovací chart_data.json"""
    test_data = [
        {
            "code": "Q1",
            "name": "Jak hodnotíte kvalitu našich služeb v oblasti zákaznického servisu a podpory klientů?",
            "type": "single",
            "data": [
                {"label": "<PERSON>elmi spokojen", "value": 25},
                {"label": "Spokojen", "value": 35},
                {"label": "Neutrální", "value": 15}
            ]
        },
        {
            "code": "Q2", 
            "name": "Doporučili byste naše služby svým přátelům, známým a kolegům v pr<PERSON><PERSON>?",
            "type": "single",
            "data": [
                {"label": "Rozhodně ano", "value": 40},
                {"label": "Sp<PERSON>še ano", "value": 30},
                {"label": "Nevím", "value": 20}
            ]
        }
    ]
    
    os.makedirs("test_data", exist_ok=True)
    with open("test_data/chart_data.json", "w", encoding="utf-8") as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    return "test_data/chart_data.json"

def test_translation_manager_integration():
    """Test integrace s TranslationManager"""
    print("=== Test integrace s TranslationManager ===")
    
    # Vytvoření testovacích dat
    chart_data_path = create_test_chart_data()
    print(f"✅ Testovací data vytvořena: {chart_data_path}")
    
    try:
        from translation_manager import TranslationManager
        
        # Vytvoření TranslationManager pro testovací survey
        tm = TranslationManager("test_survey")
        print("✅ TranslationManager inicializován")
        
        # Test extrakce názvů
        strings = tm.extract_translatable_strings(chart_data_path)
        question_names = strings.get('question_names', [])
        
        print(f"📊 Extrahováno {len(question_names)} názvů otázek:")
        for i, name in enumerate(question_names, 1):
            print(f"   {i}. {name}")
        
        # Test dostupných jazyků
        available_languages = tm.get_available_languages()
        print(f"\n🌍 Dostupné jazyky: {list(available_languages.keys())}")
        
        # Simulace AI úprav
        print(f"\n🤖 Simulace AI úprav...")
        
        # Přidání AI úprav do překladů
        if 'question_names' not in tm.translations:
            tm.translations['question_names'] = {}
        
        # Simulované AI úpravy
        ai_improvements = {
            "Jak hodnotíte kvalitu našich služeb v oblasti zákaznického servisu a podpory klientů?": "Kvalita zákaznického servisu",
            "Doporučili byste naše služby svým přátelům, známým a kolegům v práci?": "Doporučení služeb"
        }
        
        for original, improved in ai_improvements.items():
            tm.translations['question_names'][original] = improved
        
        # Uložení překladů
        if tm.save_translations():
            print(f"✅ AI úpravy uloženy do {tm.translation_file}")
        
        # Test aplikace překladů
        output_path = chart_data_path.replace('.json', '_ai_improved.json')
        if tm.apply_translations(chart_data_path, output_path):
            print(f"✅ AI úpravy aplikovány: {output_path}")
            
            # Ověření výsledku
            with open(output_path, 'r', encoding='utf-8') as f:
                improved_data = json.load(f)
            
            print("\n📋 Výsledek AI úprav:")
            for item in improved_data:
                print(f"   {item['code']}: {item['name']}")
        
        print("\n✅ Test integrace úspěšný!")
        
    except Exception as e:
        print(f"❌ Chyba při testu integrace: {e}")
        import traceback
        traceback.print_exc()

def test_menu_display():
    """Test zobrazení menu s novou položkou"""
    print("\n=== Test zobrazení menu ===")
    
    try:
        from main import translation_management_menu
        
        # Simulace menu (bez interakce)
        print("✅ Menu funkce importována úspěšně")
        print("💡 Nová položka '7. 🤖 AI úprava názvů pro grafy' by měla být viditelná v menu 10")
        
    except Exception as e:
        print(f"❌ Chyba při importu menu: {e}")

if __name__ == "__main__":
    test_translation_manager_integration()
    test_menu_display()
    
    # Cleanup
    import shutil
    if os.path.exists("test_data"):
        shutil.rmtree("test_data")
        print("\n🧹 Testovací data vyčištěna")