#!/usr/bin/env python3
"""
Datawrapper Export Module - Příklady použití

<PERSON> různých způsobů použití Datawrapper Export modulu.
"""

import os
import sys
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from datawrapper_export import (
    ExportManager,
    DatawrapperExportClient,
    ChartCollector,
    ChartConfigurator,
    HTMLGenerator,
    validate_environment,
    display_validation_results,
    ExportLogger,
    ProgressTracker
)


def example_1_basic_export():
    """Příklad 1: Základní export"""
    print("=" * 60)
    print("PŘÍKLAD 1: Základní export")
    print("=" * 60)
    
    # Validace prostředí
    validation = validate_environment()
    if not validation['valid']:
        print("❌ Prostředí není připraveno")
        display_validation_results(validation)
        return
    
    # Základní export
    manager = ExportManager()
    
    result = manager.export_survey_charts(
        survey_id="123456",  # Nahraďte skutečným Survey ID
        output_dir="exports",
        survey_name="Základní export test"
    )
    
    if result.success:
        print(f"✅ Export úspěšný: {result.output_file}")
    else:
        print(f"❌ Export selhal: {result.errors}")


def example_2_filtered_export():
    """Příklad 2: Export s filtrováním"""
    print("=" * 60)
    print("PŘÍKLAD 2: Export s filtrováním")
    print("=" * 60)
    
    manager = ExportManager()
    
    # Export pouze českých grafů
    result = manager.export_survey_charts(
        survey_id="123456",
        output_dir="exports",
        survey_name="České grafy",
        language_filter="cs",
        force_reconfigure=False  # Rychlejší
    )
    
    if result.success:
        print(f"✅ České grafy exportovány: {result.output_file}")
        print(f"📊 Počet grafů: {result.charts_exported}")
    
    # Export pouze anglických grafů
    result = manager.export_survey_charts(
        survey_id="123456",
        output_dir="exports",
        survey_name="English charts",
        language_filter="en",
        force_reconfigure=False
    )
    
    if result.success:
        print(f"✅ Anglické grafy exportovány: {result.output_file}")


def example_3_custom_progress():
    """Příklad 3: Custom progress callback"""
    print("=" * 60)
    print("PŘÍKLAD 3: Custom progress tracking")
    print("=" * 60)
    
    # Custom progress callback
    def detailed_progress(progress):
        print(f"🔄 Krok {progress.step_number}/{progress.total_steps}: {progress.current_step}")
        if progress.total_charts > 0:
            print(f"   📊 Grafy: {progress.charts_processed}/{progress.total_charts}")
        if progress.errors:
            print(f"   ❌ Chyby: {len(progress.errors)}")
    
    manager = ExportManager(progress_callback=detailed_progress)
    
    result = manager.export_survey_charts(
        survey_id="123456",
        output_dir="exports",
        survey_name="Progress tracking test"
    )
    
    print(f"Výsledek: {'✅ Úspěch' if result.success else '❌ Chyba'}")


def example_4_manual_workflow():
    """Příklad 4: Manuální workflow - krok za krokem"""
    print("=" * 60)
    print("PŘÍKLAD 4: Manuální workflow")
    print("=" * 60)
    
    survey_id = "123456"
    
    # Krok 1: Inicializace klienta
    client = DatawrapperExportClient()
    print("✅ Klient inicializován")
    
    # Krok 2: Sběr grafů
    collector = ChartCollector(client)
    charts = collector.collect_charts_for_survey(survey_id)
    
    if not charts:
        print("❌ Nebyly nalezeny žádné grafy")
        return
    
    print(f"✅ Nalezeno {len(charts)} grafů")
    
    # Krok 3: Zobrazení statistik
    stats = collector.get_collection_stats()
    print(f"📊 České: {stats.czech_charts}, Anglické: {stats.english_charts}")
    
    # Krok 4: Konfigurace (pouze první 3 grafy pro demo)
    configurator = ChartConfigurator(client)
    demo_charts = charts[:3]
    
    print(f"⚙️ Konfiguruji {len(demo_charts)} grafů...")
    config_results = configurator.configure_charts_for_export(demo_charts)
    
    successful_configs = sum(1 for r in config_results if r.success)
    print(f"✅ Konfigurace: {successful_configs}/{len(demo_charts)} úspěšných")
    
    # Krok 5: Generování HTML
    generator = HTMLGenerator()
    output_file = f"manual_export_{survey_id}.html"
    
    success = generator.generate_export_html(
        charts=charts,
        survey_id=survey_id,
        output_path=output_file,
        survey_name="Manuální export"
    )
    
    if success:
        print(f"✅ HTML vygenerován: {output_file}")
        
        # Validace HTML
        validation = generator.validate_charts_for_export(charts)
        print(f"📋 Validace: {validation['valid_charts']} platných grafů")
    else:
        print("❌ Generování HTML selhalo")


def example_5_error_handling():
    """Příklad 5: Error handling a logging"""
    print("=" * 60)
    print("PŘÍKLAD 5: Error handling a logging")
    print("=" * 60)
    
    # Nastavení loggingu
    logger = ExportLogger("demo_export", "logs/demo_export.log")
    
    try:
        # Test s neexistujícím survey ID
        manager = ExportManager()
        
        logger.info("Začínám demo export s neplatným Survey ID")
        
        result = manager.export_survey_charts(
            survey_id="neexistuje",
            output_dir="exports",
            survey_name="Error handling test"
        )
        
        if result.success:
            logger.info(f"Export úspěšný: {result.output_file}")
        else:
            logger.error(f"Export selhal: {result.errors}")
            print("❌ Očekávaná chyba - neplatný Survey ID")
            
    except Exception as e:
        logger.error(f"Neočekávaná chyba: {str(e)}", exc_info=True)
        print(f"❌ Neočekávaná chyba: {e}")


def example_6_batch_export():
    """Příklad 6: Batch export více surveys"""
    print("=" * 60)
    print("PŘÍKLAD 6: Batch export více surveys")
    print("=" * 60)
    
    survey_ids = ["123456", "789012", "345678"]  # Nahraďte skutečnými ID
    
    manager = ExportManager()
    results = []
    
    for i, survey_id in enumerate(survey_ids, 1):
        print(f"\n📊 Exportuji survey {i}/{len(survey_ids)}: {survey_id}")
        
        result = manager.export_survey_charts(
            survey_id=survey_id,
            output_dir=f"batch_exports/survey_{survey_id}",
            survey_name=f"Batch Export {survey_id}",
            force_reconfigure=False  # Rychlejší pro batch
        )
        
        results.append((survey_id, result))
        
        if result.success:
            print(f"✅ Survey {survey_id}: {result.charts_exported} grafů")
        else:
            print(f"❌ Survey {survey_id}: {result.errors}")
    
    # Souhrn
    print("\n📋 SOUHRN BATCH EXPORTU:")
    successful = sum(1 for _, result in results if result.success)
    total_charts = sum(result.charts_exported for _, result in results if result.success)
    
    print(f"✅ Úspěšných: {successful}/{len(survey_ids)}")
    print(f"📊 Celkem grafů: {total_charts}")


def example_7_custom_template():
    """Příklad 7: Použití custom template"""
    print("=" * 60)
    print("PŘÍKLAD 7: Custom template")
    print("=" * 60)
    
    # Vytvoření custom template
    custom_template = """
<!DOCTYPE html>
<html>
<head>
    <title>Custom Export - {{ survey_id }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chart { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .chart h3 { color: #333; }
    </style>
</head>
<body>
    <h1>Custom Export: {{ survey_name or survey_id }}</h1>
    <p>Vygenerováno: {{ generated_at }}</p>
    <p>Počet grafů: {{ total_count }}</p>
    
    {% for chart in charts %}
    <div class="chart">
        <h3>{{ chart.title }}</h3>
        <p>ID: {{ chart.id }} | Typ: {{ chart.type }}</p>
        <iframe src="{{ chart.share_url }}" width="100%" height="400"></iframe>
        <p><a href="{{ chart.share_url }}" target="_blank">Otevřít graf</a></p>
    </div>
    {% endfor %}
</body>
</html>
    """
    
    # Použití custom template
    generator = HTMLGenerator()
    
    # Simulace dat (v reálném použití by se získala z API)
    from datawrapper_export import ChartInfo
    demo_charts = [
        ChartInfo(
            id="demo1",
            title="Demo Graf 1",
            type="d3-bars",
            status="published",
            url="",
            share_url="https://datawrapper.dwcdn.net/demo1/1/"
        )
    ]
    
    success = generator.generate_custom_html(
        template_content=custom_template,
        data={
            'survey_id': 'DEMO',
            'survey_name': 'Custom Template Demo',
            'charts': [
                {
                    'id': chart.id,
                    'title': chart.title,
                    'type': chart.type,
                    'share_url': chart.share_url
                } for chart in demo_charts
            ],
            'total_count': len(demo_charts),
            'generated_at': '2024-01-01 12:00'
        },
        output_path="custom_export_demo.html"
    )
    
    if success:
        print("✅ Custom template export úspěšný: custom_export_demo.html")
    else:
        print("❌ Custom template export selhal")


def main():
    """Hlavní funkce - spuštění všech příkladů"""
    print("🚀 DATAWRAPPER EXPORT MODULE - PŘÍKLADY POUŽITÍ")
    print("=" * 60)
    
    # Kontrola prostředí
    validation = validate_environment()
    if not validation['valid']:
        print("❌ Prostředí není připraveno pro spuštění příkladů")
        display_validation_results(validation)
        return
    
    examples = [
        ("Základní export", example_1_basic_export),
        ("Export s filtrováním", example_2_filtered_export),
        ("Custom progress tracking", example_3_custom_progress),
        ("Manuální workflow", example_4_manual_workflow),
        ("Error handling", example_5_error_handling),
        ("Batch export", example_6_batch_export),
        ("Custom template", example_7_custom_template)
    ]
    
    print("Dostupné příklady:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"{i}. {name}")
    
    print("0. Spustit všechny příklady")
    print("q. Ukončit")
    
    while True:
        try:
            choice = input("\nVyberte příklad (0-7, q): ").strip()
            
            if choice.lower() == 'q':
                break
            elif choice == '0':
                for name, func in examples:
                    print(f"\n🔄 Spouštím: {name}")
                    try:
                        func()
                    except Exception as e:
                        print(f"❌ Chyba v příkladu '{name}': {e}")
                break
            elif choice.isdigit() and 1 <= int(choice) <= len(examples):
                name, func = examples[int(choice) - 1]
                print(f"\n🔄 Spouštím: {name}")
                try:
                    func()
                except Exception as e:
                    print(f"❌ Chyba v příkladu '{name}': {e}")
            else:
                print("❌ Neplatná volba")
                
        except (EOFError, KeyboardInterrupt):
            break
    
    print("\n👋 Ukončuji příklady")


if __name__ == "__main__":
    main()
