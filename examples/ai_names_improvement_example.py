#!/usr/bin/env python3
"""
Příklad použití AI úpravy názvů pro grafy
"""

import os
import sys
import json

# Přidání src do path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def example_ai_names_improvement():
    """Příklad použití AI úpravy názvů"""
    print("=== Příklad AI úpravy názvů pro grafy ===")
    
    # Ukázka typických dlouhých názvů z průzkumů
    original_names = [
        "Jak celkově hodnotíte kvalitu poskytovaných služeb naší organizace v oblasti zákaznického servisu a technické podpory?",
        "Doporučili byste naše služby svým přátelům, známým, kolegům v práci nebo členům rodiny?",
        "Jaká je vaše celková spokojenost s komunikací ze strany našich zamě<PERSON>nanců během posledních 6 měsíců?",
        "Jak často využíváte naše online služby dostupné prostřednictvím webového portálu nebo mobilní aplikace?",
        "Které z následujících oblastí považujete za nejdůležitější pro zlepšení kvality našich služeb v budoucnosti?"
    ]
    
    print("📋 Původní názvy otázek:")
    for i, name in enumerate(original_names, 1):
        print(f"   {i}. {name}")
        print(f"      Délka: {len(name)} znaků")
    
    # Simulace AI úprav
    print(f"\n🤖 AI úpravy (simulace):")
    
    ai_improved_names = [
        "Kvalita zákaznického servisu",
        "Doporučení služeb",
        "Spokojenost s komunikací",
        "Využívání online služeb", 
        "Priority pro zlepšení"
    ]
    
    print("📊 Upravené názvy pro grafy:")
    for i, (original, improved) in enumerate(zip(original_names, ai_improved_names), 1):
        print(f"   {i}. {improved}")
        print(f"      Délka: {len(improved)} znaků (úspora: {len(original) - len(improved)} znaků)")
        print(f"      Úspora: {((len(original) - len(improved)) / len(original) * 100):.1f}%")
    
    # Ukázka JSON formátu pro AI
    print(f"\n📄 Formát pro AI zpracování:")
    ai_format = {
        "improvements": {
            original: improved 
            for original, improved in zip(original_names, ai_improved_names)
        }
    }
    
    print(json.dumps(ai_format, ensure_ascii=False, indent=2)[:500] + "...")
    
    print(f"\n✅ Příklad dokončen!")
    print(f"💡 Pro skutečné použití spusťte: python src/main.py → Menu 10 → 7")

if __name__ == "__main__":
    example_ai_names_improvement()