#!/bin/bash

# Report Canvas Launcher
# Spouští Report Canvas s virtuálním displejem

echo "🚀 Spouštím Report Canvas..."
echo "💡 Pro ukončení použijte Ctrl+C"
echo "============================================================"

# Kontrola závislostí
if ! command -v xvfb-run &> /dev/null; then
    echo "❌ Chyba: xvfb není nainstalován"
    echo "Instalujte: sudo apt-get install xvfb"
    exit 1
fi

# Přechod do správného adresáře
cd "$(dirname "$0")"

# Spuštění s virtuálním displejem
xvfb-run -a python src/main.py

echo "✅ Report Canvas ukončen"