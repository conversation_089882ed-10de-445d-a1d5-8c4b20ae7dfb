import pytest
from datetime import datetime
import argparse
from src.cli import parse_date, create_parser, validate_args

def test_parse_date_valid():
    date = parse_date("2024-03-15")
    assert isinstance(date, datetime)
    assert date.year == 2024
    assert date.month == 3
    assert date.day == 15

def test_parse_date_invalid():
    with pytest.raises(argparse.ArgumentTypeError):
        parse_date("15-03-2024")
    
    with pytest.raises(argparse.ArgumentTypeError):
        parse_date("2024/03/15")
    
    with pytest.raises(argparse.ArgumentTypeError):
        parse_date("invalid")

def test_create_parser():
    parser = create_parser()
    assert isinstance(parser, argparse.ArgumentParser)
    
    # Test povinných argumentů
    args = parser.parse_args(['123456'])
    assert args.survey_id == '123456'
    assert not args.completed_only
    assert not args.empty_graphs
    assert not args.publish
    assert not args.export_png
    assert args.start_date is None
    assert args.end_date is None

def test_validate_args_valid():
    args = argparse.Namespace(
        survey_id='123456',
        start_date=datetime(2024, 3, 1),
        end_date=datetime(2024, 3, 15)
    )
    validate_args(args)  # Nemělo by vyhodit výjimku

def test_validate_args_invalid_survey_id():
    args = argparse.Namespace(survey_id='12345')  # Příliš krátké
    with pytest.raises(ValueError):
        validate_args(args)
    
    args = argparse.Namespace(survey_id='1234567')  # Příliš dlouhé
    with pytest.raises(ValueError):
        validate_args(args)
    
    args = argparse.Namespace(survey_id='abcdef')  # Není číslo
    with pytest.raises(ValueError):
        validate_args(args)

def test_validate_args_invalid_dates():
    args = argparse.Namespace(
        survey_id='123456',
        start_date=datetime(2024, 3, 15),
        end_date=datetime(2024, 3, 1)
    )
    with pytest.raises(ValueError):
        validate_args(args)

def test_parser_with_all_options():
    parser = create_parser()
    args = parser.parse_args([
        '123456',
        '--completed-only',
        '--start-date', '2024-03-01',
        '--end-date', '2024-03-15',
        '--empty-graphs',
        '--publish',
        '--export-png'
    ])
    
    assert args.survey_id == '123456'
    assert args.completed_only
    assert args.empty_graphs
    assert args.publish
    assert args.export_png
    assert args.start_date == datetime(2024, 3, 1)
    assert args.end_date == datetime(2024, 3, 15)
