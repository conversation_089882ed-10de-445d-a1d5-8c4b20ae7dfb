#!/usr/bin/env python3
"""
Test script pro přímé testování enhanced charts menu s survey 827822
"""

import sys
import os
sys.path.append('.')

def test_enhanced_charts_menu():
    """Test enhanced charts menu pro survey 827822"""
    
    # Nastavíme survey ID
    survey_id = "827822"
    
    print(f"🧪 Testování enhanced charts menu pro survey {survey_id}")
    print("=" * 60)
    
    # Kontrola existence všech potřebných souborů
    required_files = [
        f"data/{survey_id}/structure.lss",
        f"data/{survey_id}/responses.csv", 
        f"data/{survey_id}/question_mapping.csv",
        f"data/{survey_id}/responses_long.csv"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            return
    
    print()
    
    try:
        # Import funkcí z main.py
        sys.path.append('src')
        
        # Simulace enhanced charts menu
        print("📊 Spouštím enhanced charts generování...")
        
        # Nastavíme globální proměnnou current_survey_id
        import src.main as main_module
        main_module.current_survey_id = survey_id
        
        # Zavoláme enhanced charts menu
        main_module.generate_enhanced_charts_menu()
        
    except Exception as e:
        print(f"❌ Chyba při testování: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_charts_menu()