#!/usr/bin/env python3
"""
Finální test úplně automatických rozměrů
"""

from src.chart_generator import ChartGenerator

def test_final_fix():
    """Test finální opravy - žádné width/height parametry"""
    print("=== FINÁLNÍ TEST: Žádné width/height parametry ===")
    
    # Vytvoření generátoru s automatickými hodnotami
    generator = ChartGenerator(
        survey_title="Test Survey",
        png_width=None,  # Automatické!
        png_border=10,
        png_scale=2,
        auto_height=True,
        full_header_footer=False,
        transparent_bg=False
    )
    
    print("✓ ChartGenerator parametry:")
    print(f"  png_width: {generator.png_width} ← Mělo by být None")
    print(f"  png_border: {generator.png_border}")
    print(f"  png_scale: {generator.png_scale}")
    print(f"  auto_height: {generator.auto_height}")
    
    # Kontroly
    if generator.png_width is None:
        print("✅ SPRÁVNĚ: png_width je None")
    else:
        print(f"❌ CHYBA: png_width je {generator.png_width}")
    
    # Simulace volání export_chart (jak by to vypadalo)
    print(f"\n✓ Simulace export_chart volání:")
    
    # Parametry jak by je vytvořil export_chart
    export_params = {
        'chart_id': 'test123',
        'export_format': 'png',
        # ŽÁDNÝ width parametr!
        'border_width': generator.png_border,
        'zoom': generator.png_scale,
        'plain': not generator.full_header_footer,
        'mode': 'rgba' if generator.transparent_bg else 'rgb'
    }
    
    print("  Parametry předané do export_chart:")
    for key, value in export_params.items():
        print(f"    {key}: {value}")
    
    # URL parametry jak by je vytvořil datawrapper_client
    url_params = {
        "borderWidth": export_params['border_width'],
        "zoom": export_params['zoom'],
        "plain": "true" if export_params['plain'] else "false",
        "mode": export_params['mode'],
        "unit": "px"
        # ŽÁDNÝ width ani height!
    }
    
    print("\n  URL parametry:")
    for key, value in url_params.items():
        print(f"    {key}={value}")
    
    # Finální URL
    url_string = "&".join([f"{k}={v}" for k, v in url_params.items()])
    final_url = f"https://api.datawrapper.de/v3/charts/test123/export/png?{url_string}"
    
    print(f"\n✓ Finální export URL:")
    print(f"  {final_url}")
    
    # Kontroly URL
    checks = [
        ("width=", "❌ CHYBA: URL obsahuje width!"),
        ("height=", "❌ CHYBA: URL obsahuje height!"),
        ("borderWidth=10", "✅ Border správně nastaven"),
        ("zoom=2", "✅ Zoom správně nastaven"),
        ("plain=true", "✅ Plain správně nastaven"),
    ]
    
    print(f"\n✓ Kontroly URL:")
    for check, message in checks:
        if check.startswith("width=") or check.startswith("height="):
            if check in final_url:
                print(f"  {message}")
            else:
                print(f"  ✅ SPRÁVNĚ: URL neobsahuje {check.split('=')[0]} parametr")
        else:
            if check in final_url:
                print(f"  {message}")
            else:
                print(f"  ❌ CHYBA: URL neobsahuje {check}")

def test_expected_behavior():
    """Test očekávaného chování"""
    print("\n=== Očekávané chování ===")
    
    print("Před opravou:")
    print("  URL: /export/png?width=600&height=400&borderWidth=10&zoom=2")
    print("  Výsledek: 1200×800px (fixní, ořízlé)")
    
    print("\nPo opravě:")
    print("  URL: /export/png?borderWidth=10&zoom=2&plain=true&mode=rgb&unit=px")
    print("  Výsledek: AUTO×AUTO px (podle skutečného grafu)")
    
    print("\nPro vysoký graf (např. 400×800px):")
    print("  Před: 1200×800px (ořízlý)")
    print("  Po: 800×1600px (celý graf)")
    
    print("\nPro široký graf (např. 800×400px):")
    print("  Před: 1200×800px (roztažený)")
    print("  Po: 1600×800px (správný poměr)")

def main():
    print("FINÁLNÍ TEST: Úplně automatické rozměry")
    print("=" * 50)
    
    test_final_fix()
    test_expected_behavior()
    
    print("\n" + "=" * 50)
    print("✅ FINÁLNÍ TEST DOKONČEN")
    print("\nKlíčové změny:")
    print("- ŽÁDNÝ width parametr v URL")
    print("- ŽÁDNÝ height parametr v URL") 
    print("- Datawrapper použije automatické rozměry publikovaného grafu")
    print("- Zoom se aplikuje na automatické rozměry")
    print("- Každý graf bude mít své správné rozměry!")

if __name__ == "__main__":
    main()