#!/usr/bin/env python3
"""
Test pro ověření oprav názvů array otázek a legend
"""

import sys
import os
import json

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_array_question_title_lookup():
    """Test hledání názvu array otázky z LSS struktury"""
    print("🧪 Test hledání názvu array otázky...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        generator = EnhancedChartGenerator()
        generator.survey_id = "827822"  # Nastavíme survey_id
        
        # Test hledání názvu
        question_code = "G2Q00003"
        title = generator._get_array_question_title(question_code, None)
        
        print(f"✅ Název pro {question_code}: {title}")
        
        # Ověříme, že není generický
        if "Otázka pole:" not in title or len(title) > 50:
            print("✅ Název vypadá jako skutečný název otázky")
            return True
        else:
            print("⚠️ Stále se používá generický název")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu názvu: {str(e)}")
        return False

def test_legend_metadata():
    """Test metadat pro legendu"""
    print("\n🧪 Test metadat pro legendu...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        generator = EnhancedChartGenerator()
        
        # Testovací data pro array graf
        test_dw_data = [
            {'Subotázka': 'Test 1', '1': 5, '2': 12, '3': 25},
            {'Subotázka': 'Test 2', '1': 3, '2': 8, '3': 20}
        ]
        
        # Test přípravy metadat
        metadata = generator._prepare_chart_metadata_for_array(
            'array', 'd3-bars-stacked', test_dw_data
        )
        
        print("✅ Metadata připravena:")
        print(json.dumps(metadata, indent=2, ensure_ascii=False))
        
        # Ověření legendy
        if 'visualize' in metadata and 'legend' in metadata['visualize']:
            legend_config = metadata['visualize']['legend']
            if legend_config.get('show') is True:
                print("✅ Legenda je zapnutá")
                return True
            else:
                print("❌ Legenda není zapnutá")
                return False
        else:
            print("❌ Metadata pro legendu chybí")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu legendy: {str(e)}")
        return False

def test_basic_chart_metadata():
    """Test základních metadat pro všechny grafy"""
    print("\n🧪 Test základních metadat...")
    
    try:
        from datawrapper_client import DatawrapperClient
        
        # Simulace vytvoření grafu (bez skutečného API volání)
        payload = {
            "title": "Test graf",
            "type": "d3-bars",
            "metadata": {
                "describe": {
                    "intro": "Test popis",
                    "source-name": "Test zdroj",
                    "source-url": "",
                    "byline": "Test autor"
                },
                "visualize": {
                    "chart": {
                        "margin": {
                            "top": 10,
                            "right": 10,
                            "bottom": 10,
                            "left": 10
                        }
                    },
                    "legend": {
                        "show": True,
                        "position": "right"
                    }
                }
            }
        }
        
        print("✅ Základní metadata s legendou:")
        print(json.dumps(payload["metadata"]["visualize"]["legend"], indent=2))
        
        # Ověření legendy
        legend_config = payload["metadata"]["visualize"]["legend"]
        if legend_config.get('show') is True:
            print("✅ Legenda je zapnutá v základních metadatech")
            return True
        else:
            print("❌ Legenda není zapnutá v základních metadatech")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu základních metadat: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test oprav názvů array otázek a legend")
    print("=" * 50)
    
    tests = [
        test_array_question_title_lookup,
        test_legend_metadata,
        test_basic_chart_metadata
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Všechny testy prošly! Opravy jsou funkční.")
        return True
    else:
        print("❌ Některé testy selhaly. Zkontrolujte implementaci.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
