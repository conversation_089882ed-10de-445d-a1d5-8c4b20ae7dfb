#!/usr/bin/env python3
"""
Test automatické výšky PNG exportu
"""

import json
from src.datawrapper_client import DatawrapperClient

def test_automatic_dimensions():
    """Test automatických rozměrů podle publikovaného grafu"""
    print("=== Test automatických rozměrů PNG exportu ===")
    
    try:
        # Vytvoření klienta
        client = DatawrapperClient()
        
        # Test simulace získání informací o grafu
        print("✓ DatawrapperClient vytvořen")
        
        # Simulace dat grafu (jak by vypadala odpověď z API)
        mock_chart_data = {
            "id": "test123",
            "metadata": {
                "publish": {
                    "embed-width": 720,   # Skutečná šířka grafu
                    "embed-height": 450   # Skutečná výška grafu
                }
            }
        }
        
        print(f"✓ Simulace grafu s rozměry: {mock_chart_data['metadata']['publish']['embed-width']}x{mock_chart_data['metadata']['publish']['embed-height']}px")
        
        # Test parametrů pro export s automatickou šířkou
        width = None  # Automatická detekce
        border_width = 10
        zoom = 2
        
        # Simulace logiky z export_chart
        if width is None:
            # Simulace získání skutečné šířky
            actual_width = mock_chart_data["metadata"]["publish"].get("embed-width")
            if actual_width:
                width = actual_width
                print(f"✓ Automaticky detekována šířka: {width}px")
            else:
                width = 600
                print("⚠ Použita fallback šířka: 600px")
        
        # Parametry pro export
        params = {
            "borderWidth": border_width,
            "zoom": zoom,
            "plain": "true",
            "mode": "rgb",
            "unit": "px"
        }
        
        # Přidání width pouze pokud je nastaven
        if width:
            params["width"] = width
            
        print("\n✓ Parametry pro PNG export:")
        for key, value in params.items():
            print(f"  {key}={value}")
            
        # Kontroly
        if "height" not in params:
            print("✅ Výška se NENASTAVUJE - bude automatická podle obsahu!")
        else:
            print("❌ CHYBA: Výška se stále nastavuje!")
            
        if params.get("width") == 720:  # Skutečná šířka z grafu
            print("✅ Používá se skutečná šířka publikovaného grafu!")
        else:
            print(f"⚠ Používá se jiná šířka: {params.get('width')}px")
            
        # Finální rozměry
        final_width = params.get("width", "auto") 
        final_zoom = params.get("zoom", 1)
        
        if isinstance(final_width, int):
            exported_width = final_width * final_zoom
            print(f"\n✓ Finální exportované rozměry:")
            print(f"  Šířka: {exported_width}px ({final_width}px × {final_zoom}x zoom)")
            print(f"  Výška: AUTOMATICKÁ podle obsahu grafu")
        else:
            print(f"\n✓ Finální exportované rozměry:")
            print(f"  Šířka: AUTOMATICKÁ podle grafu")
            print(f"  Výška: AUTOMATICKÁ podle obsahu grafu")
            
        # Test URL
        chart_id = "test123"
        base_url = "https://api.datawrapper.de/v3"
        export_url = f"{base_url}/charts/{chart_id}/export/png"
        
        url_params = "&".join([f"{k}={v}" for k, v in params.items()])
        full_url = f"{export_url}?{url_params}"
        
        print(f"\n✓ Export URL:")
        print(f"  {full_url}")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()

def test_fallback_behavior():
    """Test fallback chování když se nepodaří získat rozměry"""
    print("\n=== Test fallback chování ===")
    
    # Simulace situace kdy se nepodaří získat rozměry
    chart_info = None  # Nepodařilo se získat info
    width = None
    
    if width is None:
        if chart_info and "metadata" in chart_info and "publish" in chart_info["metadata"]:
            actual_width = chart_info["metadata"]["publish"].get("embed-width")
            if actual_width:
                width = actual_width
            else:
                width = 600  # fallback
        else:
            width = 600  # fallback
            print("⚠ Nepodařilo se získat informace o grafu, použita fallback šířka 600px")
    
    print(f"✓ Fallback šířka: {width}px")

def main():
    print("Test automatické výšky PNG exportu")
    print("=" * 50)
    
    test_automatic_dimensions()
    test_fallback_behavior()
    
    print("\n" + "=" * 50)
    print("✅ Test dokončen")
    print("\nKlíčové změny:")
    print("- Šířka se získává z publikovaného grafu (embed-width)")
    print("- Výška se VŮBEC nenastavuje - je automatická")
    print("- Zoom se aplikuje na skutečnou šířku grafu")

if __name__ == "__main__":
    main()