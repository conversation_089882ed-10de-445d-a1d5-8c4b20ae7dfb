#!/usr/bin/env python3
"""
Test přímého přístupu ke složce 329553
"""

import sys
import os
import requests
import json

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_direct_folder_access():
    """Test přímého přístupu ke složce"""
    print("🧪 Test přímého přístupu ke složce 329553...")
    
    try:
        from config_loader import load_config
        
        config = load_config()
        api_token = os.getenv('DATAWRAPPER_API_TOKEN')
        
        if not api_token:
            print("❌ Chybí DATAWRAPPER_API_TOKEN")
            return False
        
        headers = {
            'Authorization': f'Bearer {api_token}',
            'Content-Type': 'application/json'
        }
        
        # Zkusíme různé endpointy pro složku 329553
        endpoints = [
            "https://api.datawrapper.de/v3/folders/329553",
            "https://api.datawrapper.de/v3/folders/329553/charts",
            "https://api.datawrapper.de/folders/329553",
        ]
        
        for endpoint in endpoints:
            print(f"\n🔍 Zkouším: {endpoint}")
            
            try:
                response = requests.get(endpoint, headers=headers)
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ Úspěch! Typ dat: {type(data)}")
                    
                    if isinstance(data, dict):
                        print(f"   Klíče: {list(data.keys())}")
                        
                        # Hledáme grafy
                        charts = []
                        if 'charts' in data:
                            charts = data['charts']
                        elif 'children' in data:
                            charts = [item for item in data['children'] if item.get('type') == 'chart']
                        elif 'list' in data:
                            charts = data['list']
                        
                        if charts:
                            print(f"   📊 Nalezeno {len(charts)} grafů:")
                            for i, chart in enumerate(charts[:5]):  # Zobrazíme prvních 5
                                chart_id = chart.get('id', 'N/A')
                                chart_title = chart.get('title', 'Bez názvu')
                                print(f"      {i+1}. {chart_id}: {chart_title[:60]}...")
                            
                            if len(charts) > 5:
                                print(f"      ... a dalších {len(charts) - 5} grafů")
                            
                            return True
                        else:
                            print(f"   📊 Žádné grafy nenalezeny")
                    
                    elif isinstance(data, list):
                        print(f"   📊 Seznam s {len(data)} položkami")
                        if data:
                            print(f"   První položka: {data[0]}")
                
                elif response.status_code == 404:
                    print(f"   ❌ Složka neexistuje (404)")
                elif response.status_code == 403:
                    print(f"   ❌ Nemáme oprávnění (403)")
                else:
                    print(f"   ❌ Chyba {response.status_code}: {response.text[:200]}")
                    
            except Exception as e:
                print(f"   ❌ Výjimka: {str(e)}")
        
        return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        return False

def test_folders_api():
    """Test základního folders API"""
    print("\n🧪 Test základního /folders API...")
    
    try:
        api_token = os.getenv('DATAWRAPPER_API_TOKEN')
        
        if not api_token:
            print("❌ Chybí DATAWRAPPER_API_TOKEN")
            return False
        
        headers = {
            'Authorization': f'Bearer {api_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            "https://api.datawrapper.de/v3/folders",
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Úspěch! Typ: {type(data)}")
            
            if isinstance(data, dict) and 'list' in data:
                for item in data['list']:
                    if item.get('type') == 'team' and item.get('id') == '57Zj-Xbm':
                        print(f"✅ Nalezen team: {item.get('name')}")
                        
                        folders = item.get('folders', [])
                        print(f"📁 Folders v team: {len(folders)}")
                        
                        for folder in folders:
                            folder_id = folder.get('id')
                            folder_name = folder.get('title', folder.get('name', 'Bez názvu'))
                            print(f"   - {folder_id}: {folder_name}")
                            
                            if folder_name == "827822" or folder_id == "329553":
                                print(f"   🎯 NALEZENA CÍLOVÁ SLOŽKA!")
                                return True
                        
                        return len(folders) > 0
            
        else:
            print(f"❌ Chyba: {response.status_code}")
            print(response.text[:500])
        
        return False
        
    except Exception as e:
        print(f"❌ Chyba: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test přímého přístupu ke složce 329553")
    print("=" * 60)
    
    success1 = test_direct_folder_access()
    success2 = test_folders_api()
    
    print("\n" + "=" * 60)
    if success1 or success2:
        print("✅ Alespoň jeden test prošel!")
    else:
        print("❌ Všechny testy selhaly")
        print("💡 Zkontroluj API token a oprávnění")

if __name__ == "__main__":
    main()
