#!/usr/bin/env python3
"""
Test Enhanced Chart Generation
Testuje novou funkcionalitu pro různé typy LimeSurvey otázek
"""

import os
import sys
import json

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from lss_question_analyzer import LSSQuestionAnalyzer
from enhanced_data_transformer import EnhancedDataTransformer
from enhanced_chart_generator import EnhancedChartGenerator

def test_lss_analyzer():
    """Test LSS Question Analyzer"""
    print("🔍 TEST 1: LSS Question Analyzer")
    print("=" * 50)
    
    analyzer = LSSQuestionAnalyzer()
    
    # Test analýzy jednotlivých typů otázek
    test_questions = [
        {'type': 'L', 'title': 'Q1', 'question': 'Výběr ze seznamu'},
        {'type': 'Y', 'title': 'Q2', 'question': 'Ano/Ne <PERSON>t<PERSON><PERSON>'},
        {'type': 'M', 'title': 'Q3', 'question': '<PERSON><PERSON><PERSON> mo<PERSON>'},
        {'type': 'A', 'title': 'Q4', 'question': 'Pole s 5-bodovou škálou'},
        {'type': 'N', 'title': 'Q5', 'question': 'Číselný vstup'},
        {'type': 'S', 'title': 'Q6', 'question': 'Krátký text'},
        {'type': 'R', 'title': 'Q7', 'question': 'Ranking'},
        {'type': 'X', 'title': 'Q8', 'question': 'Informační text'}
    ]
    
    for question in test_questions:
        analysis = analyzer.analyze_question(question)
        print(f"📊 Typ '{question['type']}': {analysis['description']}")
        print(f"   Graf: {analysis['chart_type']}")
        print(f"   Datawrapper: {analysis['datawrapper_type']}")
        print(f"   Podporuje graf: {'✅' if analysis['supports_chart'] else '❌'}")
        print()
    
    return True

def test_lss_structure_analysis():
    """Test analýzy celé LSS struktury"""
    print("🔍 TEST 2: Analýza LSS struktury")
    print("=" * 50)
    
    analyzer = LSSQuestionAnalyzer()
    lss_path = "data/214947/structure.lss"
    
    if not os.path.exists(lss_path):
        print(f"❌ LSS soubor nenalezen: {lss_path}")
        return False
    
    try:
        analysis = analyzer.analyze_lss_structure(lss_path)
        
        if 'error' in analysis:
            print(f"❌ Chyba při analýze: {analysis['error']}")
            return False
        
        print(f"📋 Survey ID: {analysis['survey_id']}")
        print(f"📊 Celkem otázek: {analysis['total_questions']}")
        print(f"📈 Vhodných pro grafy: {analysis['chartable_questions']}")
        print(f"📊 Úspěšnost: {analysis['chartable_questions']/analysis['total_questions']*100:.1f}%")
        
        print("\n🔢 Typy otázek:")
        for q_type, count in analysis['question_types'].items():
            mapping = analyzer.QUESTION_TYPE_MAPPING.get(q_type, {})
            description = mapping.get('description', 'Neznámý typ')
            chart_type = mapping.get('chart_type', 'Bez grafu')
            print(f"   {q_type}: {count}x - {description} → {chart_type}")
        
        # Test doporučených grafů
        recommended = analyzer.get_recommended_charts(analysis)
        print(f"\n📈 Doporučené grafy: {len(recommended)}")
        
        for i, chart in enumerate(recommended[:5]):  # Zobrazíme prvních 5
            print(f"   {i+1}. {chart['question_title']}: {chart['chart_type']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {str(e)}")
        return False

def test_data_transformer():
    """Test Enhanced Data Transformer"""
    print("🔍 TEST 3: Enhanced Data Transformer")
    print("=" * 50)
    
    try:
        import pandas as pd
        
        transformer = EnhancedDataTransformer()
        
        # Test kategorických dat
        print("📊 Test kategorických dat:")
        categorical_df = pd.DataFrame({
            'response': ['Ano', 'Ne', 'Ano', 'Nevím', 'Ano', 'Ne']
        })
        
        categorical_analysis = {
            'data_type': 'categorical',
            'chart_type': 'column-chart'
        }
        
        result = transformer.prepare_chart_data_by_analysis(categorical_df, categorical_analysis)
        print(f"   Typ: {result['data_type']}")
        print(f"   Data: {len(result['data'])} kategorií")
        for item in result['data']:
            print(f"      {item['label']}: {item['value']}")
        
        # Test binárních dat
        print("\n📊 Test binárních dat:")
        binary_analysis = {
            'data_type': 'binary',
            'chart_type': 'pie-chart'
        }
        
        result = transformer.prepare_chart_data_by_analysis(categorical_df, binary_analysis)
        print(f"   Typ: {result['data_type']}")
        print(f"   Data: {len(result['data'])} možnosti")
        
        # Test číselných dat
        print("\n📊 Test číselných dat:")
        numerical_df = pd.DataFrame({
            'response': [1, 2, 3, 4, 5, 3, 4, 2, 5, 1, 3, 4]
        })
        
        numerical_analysis = {
            'data_type': 'numerical',
            'chart_type': 'histogram'
        }
        
        result = transformer.prepare_chart_data_by_analysis(numerical_df, numerical_analysis)
        print(f"   Typ: {result['data_type']}")
        print(f"   Intervaly: {len(result['data'])}")
        print(f"   Statistiky: průměr={result['statistics']['mean']:.2f}, "
              f"medián={result['statistics']['median']:.2f}")
        
        return True
        
    except ImportError:
        print("❌ Pandas není dostupný")
        return False
    except Exception as e:
        print(f"❌ Chyba: {str(e)}")
        return False

def test_enhanced_chart_generator():
    """Test Enhanced Chart Generator"""
    print("🔍 TEST 4: Enhanced Chart Generator")
    print("=" * 50)
    
    try:
        generator = EnhancedChartGenerator()
        
        # Test podporovaných typů
        supported_types = generator.get_supported_question_types()
        print(f"📊 Podporované typy otázek: {len(supported_types)}")
        
        for limesurvey_type, description in list(supported_types.items())[:10]:
            print(f"   {limesurvey_type}: {description}")
        
        # Test analýzy LSS souboru
        lss_path = "data/214947/structure.lss"
        if os.path.exists(lss_path):
            print(f"\n📋 Analýza LSS souboru:")
            analysis = generator.analyze_lss_file(lss_path)
            
            if 'error' not in analysis:
                print(f"   Survey ID: {analysis['survey_id']}")
                print(f"   Otázky celkem: {analysis['total_questions']}")
                print(f"   Vhodné pro grafy: {analysis['chartable_questions']}")
            else:
                print(f"   ❌ Chyba: {analysis['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {str(e)}")
        return False

def test_integration():
    """Test integrace všech komponent"""
    print("🔍 TEST 5: Integrace komponent")
    print("=" * 50)
    
    try:
        # Kontrola dostupnosti souborů
        lss_path = "data/214947/structure.lss"
        responses_path = "data/214947/responses.csv"
        
        if not os.path.exists(lss_path):
            print(f"❌ LSS soubor nenalezen: {lss_path}")
            return False
            
        if not os.path.exists(responses_path):
            print(f"❌ Responses soubor nenalezen: {responses_path}")
            return False
        
        print("✅ Všechny potřebné soubory nalezeny")
        
        # Test celého workflow
        analyzer = LSSQuestionAnalyzer()
        
        # 1. Analýza LSS
        print("📋 1. Analyzuji LSS strukturu...")
        lss_analysis = analyzer.analyze_lss_structure(lss_path)
        
        if 'error' in lss_analysis:
            print(f"❌ Chyba při analýze LSS: {lss_analysis['error']}")
            return False
        
        print(f"✅ Nalezeno {lss_analysis['chartable_questions']} otázek vhodných pro grafy")
        
        # 2. Získání doporučených grafů
        print("📈 2. Získávám doporučené grafy...")
        recommended = analyzer.get_recommended_charts(lss_analysis)
        
        if not recommended:
            print("❌ Žádné doporučené grafy")
            return False
        
        print(f"✅ Doporučeno {len(recommended)} grafů")
        
        # 3. Zobrazení prvních 5 doporučení
        print("📊 3. Top 5 doporučených grafů:")
        for i, chart in enumerate(recommended[:5]):
            print(f"   {i+1}. {chart['question_title']}")
            print(f"      Typ: {chart['chart_type']}")
            print(f"      Datawrapper: {chart['datawrapper_type']}")
            print(f"      Priorita: {chart['priority']}")
            print()
        
        print("✅ Integrace komponent úspěšná!")
        return True
        
    except Exception as e:
        print(f"❌ Chyba při integraci: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 TESTOVÁNÍ ENHANCED CHART GENERATION")
    print("=" * 60)
    
    tests = [
        ("LSS Question Analyzer", test_lss_analyzer),
        ("LSS Structure Analysis", test_lss_structure_analysis),
        ("Enhanced Data Transformer", test_data_transformer),
        ("Enhanced Chart Generator", test_enhanced_chart_generator),
        ("Integration Test", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 60)
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                print(f"✅ {test_name} - ÚSPĚCH")
            else:
                print(f"❌ {test_name} - SELHÁNÍ")
                
        except Exception as e:
            print(f"💥 {test_name} - CHYBA: {str(e)}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    print("\n" + "=" * 60)
    print("📊 SHRNUTÍ TESTŮ")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        print(f"{test_name}: {status}")
    
    print(f"\n🎯 CELKOVÝ VÝSLEDEK: {passed}/{total} testů úspěšných ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 VŠECHNY TESTY ÚSPĚŠNÉ! Implementace je připravena k použití.")
    else:
        print("⚠️  Některé testy selhaly. Zkontrolujte implementaci.")

if __name__ == "__main__":
    main()