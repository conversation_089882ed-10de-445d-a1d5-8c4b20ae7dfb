#!/usr/bin/env python3
"""
Test opravy vytváření složek v Datawrapper
"""

import sys
import os

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_folder_creation():
    """Test vytváření slo<PERSON>, která už existuje"""
    print("🧪 Test vytváření existující slo<PERSON>ky...")

    try:
        from datawrapper_client import DatawrapperClient

        client = DatawrapperClient()

        # Test vytvoření slo<PERSON>, která už existuje
        folder_name = "827822"  # Testa složka

        print(f"✅ Pokouším se vytvořit složku: {folder_name}")
        print(f"✅ Parent folder ID: {client.limesurvey_folder_id}")

        result = client.create_folder(folder_name)

        print(f"✅ Výsledek: {result}")

        # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že výsledek obsahuje 'id'
        if isinstance(result, dict) and result is not None:
            if 'id' in result:
                print(f"✅ Složka má ID: {result['id']}")
                print(f"✅ Název: {result.get('name', 'N/A')}")
                print(f"✅ Parent ID: {result.get('parentId', 'N/A')}")
                return True
            else:
                print(f"❌ Složka nemá ID: {result}")
                return False
        elif result is None:
            print("❌ Výsledek je None")
            return False
        else:
            print(f"❌ Výsledek není dict: {type(result)}")
            return False

    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_folder_finding():
    """Test hledání existující složky"""
    print("\n🧪 Test hledání existující složky...")
    
    try:
        from datawrapper_client import DatawrapperClient
        
        client = DatawrapperClient()
        
        # Test hledání složky podle názvu
        folder_name = "827822"
        
        print(f"✅ Hledám složku: {folder_name}")
        
        result = client.get_folder_by_name(folder_name)
        
        print(f"✅ Výsledek hledání: {result}")
        
        if result and 'id' in result:
            print(f"✅ Složka nalezena s ID: {result['id']}")
            return True
        else:
            print("❌ Složka nenalezena nebo nemá ID")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při hledání: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test opravy vytváření složek v Datawrapper")
    print("=" * 60)
    
    tests = [
        test_folder_finding,
        test_folder_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Oprava složek funguje!")
        print("\n📋 Co bylo opraveno:")
        print("   • Když složka existuje (409), hledá se její ID")
        print("   • Funkce vrací správnou strukturu s 'id'")
        print("   • Grafy se mohou vytvářet ve správné složce")
        return True
    else:
        print("❌ Oprava složek nefunguje úplně.")
        print("\n🔧 Možná řešení:")
        print("   • Zkontrolovat API klíč Datawrapper")
        print("   • Ověřit, že složka skutečně existuje")
        print("   • Zkontrolovat oprávnění k přístupu ke složkám")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
