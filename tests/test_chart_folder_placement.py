#!/usr/bin/env python3
"""
Test script pro ověření, kam se ukládají grafy v Datawrapper
"""

import sys
import os
sys.path.append('.')

# Explicitně načteme .env soubor
try:
    from dotenv import load_dotenv
    load_dotenv()
    print(f"🔧 .env soubor načten")
except ImportError:
    print("⚠️  dotenv není dostupný")

def test_chart_folder_placement():
    """Test vytvoření grafu a ověření jeho umístění"""
    
    survey_id = "827822"
    
    print(f"🧪 Testování umístění grafů pro survey {survey_id}")
    print("=" * 60)
    
    try:
        # Import Datawrapper klienta
        from src.datawrapper_client import DatawrapperClient
        
        # Vytvoření klienta
        print("🔗 Připojuji se k Datawrapper API...")
        dw = DatawrapperClient()
        
        print(f"✅ Datawrapper API token nalezen")
        print(f"🏢 Team ID: {dw.team_id}")
        print(f"📁 LimeSurvey Folder ID: {dw.limesurvey_folder_id}")
        
        # Nejprve vytvoříme/najdeme složku
        print(f"\n📁 Vytvářím/hledám složku '{survey_id}'...")
        folder = dw.create_folder(survey_id)
        
        if folder:
            print(f"✅ Složka nalezena/vytvořena:")
            print(f"   ID: {folder.get('id')}")
            print(f"   Název: {folder.get('name')}")
            print(f"   Parent ID: {folder.get('parentId')}")
            print(f"   Team ID: {folder.get('teamId')}")
            
            folder_id = folder.get('id')
            
            # Nyní vytvoříme testovací graf VE SLOŽCE
            print(f"\n🎨 Vytvářím testovací graf ve složce {folder_id}...")
            
            test_chart = dw.create_chart(
                title=f"TEST GRAF - {survey_id}",
                chart_type="d3-bars",
                folder_id=folder_id  # EXPLICITNĚ nastavujeme folder_id
            )
            
            if test_chart:
                print(f"✅ Graf vytvořen:")
                print(f"   Graf ID: {test_chart.get('id')}")
                print(f"   Název: {test_chart.get('title')}")
                print(f"   Folder ID v grafu: {test_chart.get('folderId')}")
                print(f"   Public URL: {test_chart.get('publicUrl', 'N/A')}")
                
                # Ověříme, že graf je skutečně ve správné složce
                if str(test_chart.get('folderId')) == str(folder_id):
                    print(f"✅ Graf je ve správné složce!")
                else:
                    print(f"❌ Graf NENÍ ve správné složce!")
                    print(f"   Očekáváno: {folder_id}")
                    print(f"   Skutečnost: {test_chart.get('folderId')}")
                
                # Přidáme nějaká testovací data
                print(f"\n📊 Přidávám testovací data...")
                test_data = "kategorie,hodnota\nA,10\nB,20\nC,15"
                data_success = dw.update_chart_data(test_chart.get('id'), test_data)
                
                if data_success:
                    print(f"✅ Data přidána")
                else:
                    print(f"❌ Nepodařilo se přidat data")
                
                # Publikujeme graf
                print(f"\n📤 Publikuji graf...")
                publish_result = dw.publish_chart(test_chart.get('id'))
                
                if publish_result:
                    print(f"✅ Graf publikován")
                    print(f"   Public URL: {publish_result.get('data', {}).get('publicUrl', 'N/A')}")
                else:
                    print(f"❌ Nepodařilo se publikovat graf")
                
                # Získáme aktuální informace o grafu
                print(f"\n🔍 Kontroluji finální umístění grafu...")
                final_chart = dw.get_chart(test_chart.get('id'))
                
                if final_chart:
                    print(f"📋 Finální informace o grafu:")
                    print(f"   ID: {final_chart.get('id')}")
                    print(f"   Název: {final_chart.get('title')}")
                    print(f"   Folder ID: {final_chart.get('folderId')}")
                    print(f"   Organization ID: {final_chart.get('organizationId', 'N/A')}")
                    print(f"   Author ID: {final_chart.get('authorId', 'N/A')}")
                    print(f"   Public URL: {final_chart.get('publicUrl', 'N/A')}")
                
                print(f"\n🎯 VÝSLEDEK:")
                print(f"   Graf '{test_chart.get('title')}' byl vytvořen")
                print(f"   📍 Folder ID: {final_chart.get('folderId')}")
                print(f"   🔗 Zkontrolujte v Datawrapper: Shared/Můj tým/LimeSurvey/{survey_id}/")
                print(f"   📄 Graf ID pro kontrolu: {test_chart.get('id')}")
                
                # NEBUDEME mazat graf, abychom ho mohli zkontrolovat
                print(f"\n⚠️  Graf NEBYL smazán pro účely kontroly!")
                print(f"   Můžete ho najít v Datawrapper s ID: {test_chart.get('id')}")
                
            else:
                print("❌ Nepodařilo se vytvořit testovací graf")
                
        else:
            print("❌ Nepodařilo se vytvořit/najít složku")
            
    except Exception as e:
        print(f"❌ Chyba při testování: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_chart_folder_placement()