#!/usr/bin/env python3
"""
Test opravy aplikace překladů
"""

import sys
import os
import json
import tempfile
import shutil

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_translation_application_fix():
    """Test opravené aplikace překladů"""
    print("🧪 Test opravené aplikace překladů...")
    
    try:
        from translation_manager import TranslationManager
        
        # Vytvoření testovacích dat
        test_chart_data = [
            {
                "code": "G1Q00001",
                "name": "Uveďte prosím název instituce/organizace/společnosti, kterou v tomto dotazníkovém průzkumu zastupujete.",
                "type": "single_choice",
                "data": [
                    {"label": "Ano", "value": 10},
                    {"label": "Ne", "value": 5}
                ]
            },
            {
                "code": "G2Q00001", 
                "name": "Otázka pole: G2Q00001",
                "type": "array",
                "data": [
                    {
                        "subquestion": "první podotázka",
                        "responses": {
                            "rozhodně ano": 15,
                            "spíše ne": 8
                        }
                    }
                ]
            }
        ]
        
        # Vytvoření dočasného adresáře
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_dir = os.path.join(temp_dir, "827822")
            os.makedirs(survey_dir, exist_ok=True)
            
            # Uložení testovacích dat
            chart_data_path = os.path.join(survey_dir, "chart_data.json")
            with open(chart_data_path, 'w', encoding='utf-8') as f:
                json.dump(test_chart_data, f, ensure_ascii=False, indent=2)
            
            # Vytvoření TranslationManager
            tm = TranslationManager("827822")
            tm.survey_dir = survey_dir
            tm.translation_file = os.path.join(survey_dir, "translations.json")
            
            # Vytvoření testovacích překladů
            tm.translations = {
                "metadata": {"survey_id": "827822"},
                "question_names": {
                    "Uveďte prosím název instituce/organizace/společnosti, kterou v tomto dotazníkovém průzkumu zastupujete.": "Please provide the name of the institution/organization/company you represent in this survey.",
                    "Otázka pole: G2Q00001": "Array Question: G2Q00001"
                },
                "subquestions": {
                    "první podotázka": "first subquestion"
                },
                "response_labels": {
                    "Ano": "Yes",
                    "Ne": "No", 
                    "rozhodně ano": "definitely yes",
                    "spíše ne": "rather no"
                }
            }
            
            # Uložení překladů
            tm.save_translations()
            
            print(f"✅ Testovací data připravena v {survey_dir}")
            
            # Test aplikace překladů
            backup_path = chart_data_path + ".backup"
            shutil.copy2(chart_data_path, backup_path)
            
            print("✅ Aplikuji překlady...")
            result = tm.apply_translations(chart_data_path, chart_data_path)
            
            if not result:
                print("❌ Aplikace překladů selhala")
                return False
            
            # Ověření výsledku
            with open(chart_data_path, 'r', encoding='utf-8') as f:
                translated_data = json.load(f)
            
            print("✅ Kontroluji výsledky...")
            
            # Kontrola přeložených názvů otázek
            found_translated_name = False
            for item in translated_data:
                if item.get('name') == "Please provide the name of the institution/organization/company you represent in this survey.":
                    found_translated_name = True
                    print(f"✅ Nalezen přeložený název otázky")
                    break
            
            # Kontrola přeložených odpovědí
            found_translated_responses = False
            for item in translated_data:
                if item.get('type') == 'single_choice':
                    for data_item in item.get('data', []):
                        if data_item.get('label') in ['Yes', 'No']:
                            found_translated_responses = True
                            print(f"✅ Nalezena přeložená odpověď: {data_item.get('label')}")
                            break
                if found_translated_responses:
                    break
            
            # Kontrola přeložených podotázek
            found_translated_subquestion = False
            for item in translated_data:
                if item.get('type') == 'array':
                    for data_item in item.get('data', []):
                        if data_item.get('subquestion') == 'first subquestion':
                            found_translated_subquestion = True
                            print(f"✅ Nalezena přeložená podotázka")
                            break
                if found_translated_subquestion:
                    break
            
            # Kontrola přeložených response labels v array
            found_translated_array_responses = False
            for item in translated_data:
                if item.get('type') == 'array':
                    for data_item in item.get('data', []):
                        responses = data_item.get('responses', {})
                        if 'definitely yes' in responses or 'rather no' in responses:
                            found_translated_array_responses = True
                            print(f"✅ Nalezeny přeložené odpovědi v array")
                            break
                if found_translated_array_responses:
                    break
            
            # Výsledek
            success_count = sum([
                found_translated_name,
                found_translated_responses, 
                found_translated_subquestion,
                found_translated_array_responses
            ])
            
            print(f"\n✅ Výsledky: {success_count}/4 typů překladů úspěšně aplikováno")
            
            if success_count >= 3:
                print("✅ Aplikace překladů funguje správně!")
                return True
            else:
                print("❌ Aplikace překladů nefunguje správně")
                print("\nOriginální data:")
                with open(backup_path, 'r', encoding='utf-8') as f:
                    original = json.load(f)
                print(json.dumps(original[0], ensure_ascii=False, indent=2))
                
                print("\nPřeložená data:")
                print(json.dumps(translated_data[0], ensure_ascii=False, indent=2))
                
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test opravy aplikace překladů")
    print("=" * 60)
    
    if test_translation_application_fix():
        print("\n✅ Oprava aplikace překladů funguje!")
        print("\n📋 Co bylo opraveno:")
        print("   • Znovu načítání překladů ze souboru před aplikací")
        print("   • Možnost přepsat původní chart_data.json")
        print("   • Lepší debug informace o aplikovaných překladech")
        print("   • Kontrola podobných klíčů (trim whitespace)")
        print("\n🎯 Jak použít:")
        print("   1. Menu 10 → Volba 1 (vygenerovat šablonu)")
        print("   2. Editujte translations.json")
        print("   3. Menu 10 → Volba 3 → Volba 1 (přepsat původní)")
        print("   4. Zkontrolujte chart_data.json - změny jsou aplikovány")
        return True
    else:
        print("\n❌ Oprava nefunguje správně")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
