#!/usr/bin/env python3
"""
Test pro ověření funkce stažení PNG grafů
"""

import sys
import os
import json
from unittest.mock import Mock, patch

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_get_user_charts():
    """Test získání seznamu grafů uživatele"""
    print("🧪 Test získání seznamu grafů...")
    
    try:
        from datawrapper_client import DatawrapperClient
        
        # Simulace odpovědi API
        mock_response = {
            "list": [
                {
                    "id": "abc123",
                    "title": "Graf pro průzkum 827822",
                    "metadata": {
                        "describe": {
                            "intro": "Graf z průzkumu 827822"
                        }
                    }
                },
                {
                    "id": "def456",
                    "title": "Jiný graf",
                    "metadata": {
                        "describe": {
                            "intro": "Něco jiného"
                        }
                    }
                }
            ]
        }
        
        # Mock DatawrapperClient
        with patch('requests.get') as mock_get:
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = mock_response
            
            dw = DatawrapperClient()
            charts = dw.get_user_charts()
            
            print(f"✅ Získáno {len(charts)} grafů")
            for chart in charts:
                print(f"   - {chart['id']}: {chart['title']}")
            
            # Ověření filtrování podle ID průzkumu
            survey_id = "827822"
            matching_charts = []
            for chart in charts:
                chart_title = chart.get('title', '')
                chart_description = chart.get('metadata', {}).get('describe', {}).get('intro', '')
                
                if survey_id in chart_title or survey_id in chart_description:
                    matching_charts.append(chart)
            
            print(f"✅ Nalezeno {len(matching_charts)} grafů pro průzkum {survey_id}")
            
            if len(matching_charts) == 1:
                print("✅ Filtrování podle ID průzkumu funguje správně")
                return True
            else:
                print("❌ Filtrování podle ID průzkumu nefunguje")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu získání grafů: {str(e)}")
        return False

def test_safe_filename_generation():
    """Test generování bezpečných názvů souborů"""
    print("\n🧪 Test generování bezpečných názvů...")
    
    try:
        import re
        
        # Testovací názvy s problematickými znaky
        test_cases = [
            ("Graf s / lomítkem", "Graf s _ lomítkem"),
            ("Graf s ? otazníkem", "Graf s _ otazníkem"),
            ("Graf s : dvojtečkou", "Graf s _ dvojtečkou"),
            ('Graf s " uvozovkami', "Graf s _ uvozovkami"),
            ("Graf s * hvězdičkou", "Graf s _ hvězdičkou"),
            ("Graf s < > závorkami", "Graf s _ _ závorkami"),
            ("Graf s | svislítkem", "Graf s _ svislítkem")
        ]
        
        for original, expected in test_cases:
            # Simulace logiky z main.py
            safe_title = re.sub(r'[<>:"/\\|?*]', '_', original)
            chart_id = "TEST123"
            png_filename = f"{chart_id}_{safe_title}.png"
            
            print(f"   '{original}' → '{png_filename}'")
            
            # Ověření, že neobsahuje problematické znaky
            problematic_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
            has_problematic = any(char in png_filename for char in problematic_chars)
            
            if has_problematic:
                print(f"❌ Název stále obsahuje problematické znaky: {png_filename}")
                return False
        
        print("✅ Všechny problematické znaky byly správně nahrazeny")
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testu názvů souborů: {str(e)}")
        return False

def test_png_export_parameters():
    """Test parametrů pro PNG export"""
    print("\n🧪 Test parametrů PNG exportu...")
    
    try:
        # Simulace parametrů
        test_params = {
            'png_width': 600,
            'png_border': 10,
            'png_scale': 2,
            'full_header_footer': False,
            'transparent_bg': False
        }
        
        print("✅ Testovací parametry PNG exportu:")
        for key, value in test_params.items():
            print(f"   {key}: {value}")
        
        # Ověření typů
        type_checks = [
            ('png_width', int),
            ('png_border', int),
            ('png_scale', int),
            ('full_header_footer', bool),
            ('transparent_bg', bool)
        ]
        
        all_correct = True
        for param, expected_type in type_checks:
            actual_type = type(test_params[param])
            if actual_type == expected_type:
                print(f"✅ {param}: správný typ {expected_type.__name__}")
            else:
                print(f"❌ {param}: nesprávný typ {actual_type.__name__}, očekáváno {expected_type.__name__}")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Chyba při testu parametrů: {str(e)}")
        return False

def test_directory_creation():
    """Test vytváření adresářů"""
    print("\n🧪 Test vytváření adresářů...")
    
    try:
        import tempfile
        import shutil
        
        # Vytvoření dočasného adresáře pro test
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_id = "827822"
            charts_root = temp_dir
            charts_dir = os.path.join(charts_root, survey_id)
            
            print(f"✅ Testovací adresář: {charts_dir}")
            
            # Simulace logiky z main.py
            if not os.path.exists(charts_dir):
                os.makedirs(charts_dir, exist_ok=True)
                print(f"✅ Adresář vytvořen: {charts_dir}")
            
            # Ověření existence
            if os.path.exists(charts_dir) and os.path.isdir(charts_dir):
                print("✅ Adresář byl úspěšně vytvořen")
                return True
            else:
                print("❌ Adresář nebyl vytvořen")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu adresářů: {str(e)}")
        return False

def test_config_loading():
    """Test načítání konfigurace"""
    print("\n🧪 Test načítání konfigurace...")
    
    try:
        from config_loader import load_config
        
        config = load_config()
        
        print("✅ Načtená konfigurace:")
        print(f"   charts_root: {config.get('charts_root', 'CHYBÍ')}")
        
        # Ověření, že charts_root je nastaven
        if 'charts_root' in config:
            print("✅ Konfigurace charts_root je přítomna")
            return True
        else:
            print("❌ Konfigurace charts_root chybí")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu konfigurace: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test funkce stažení PNG grafů")
    print("=" * 50)
    
    tests = [
        test_get_user_charts,
        test_safe_filename_generation,
        test_png_export_parameters,
        test_directory_creation,
        test_config_loading
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Všechny testy prošly! Funkce stažení PNG je připravena.")
        print("\n📋 Funkce menu 9:")
        print("   • Zadání ID průzkumu")
        print("   • Nastavení PNG parametrů (šířka, okraj, zoom, atd.)")
        print("   • Získání seznamu grafů z Datawrapper")
        print("   • Filtrování podle ID průzkumu")
        print("   • Stažení a uložení PNG souborů")
        print("   • Uložení do charts/ID_průzkumu/")
        return True
    else:
        print("❌ Některé testy selhaly. Zkontrolujte implementaci.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
