#!/usr/bin/env python3
"""
Kompletní test vytvoření grafu s daty ve správné slo<PERSON>
"""

import sys
import os
sys.path.append('.')

# Explicitně načteme .env soubor
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

def test_complete_chart_creation():
    """Test kompletního vytvoření grafu s daty"""
    
    survey_id = "827822"
    
    print(f"🧪 Kompletní test vytvoření grafu pro survey {survey_id}")
    print("=" * 60)
    
    try:
        from src.datawrapper_client import DatawrapperClient
        
        print("🔗 Připojuji se k Datawrapper API...")
        dw = DatawrapperClient()
        
        # Vytvoříme/najdeme složku
        print(f"\n📁 Vytvářím/hledám složku '{survey_id}'...")
        folder = dw.create_folder(survey_id)
        
        if folder:
            folder_id = folder.get('id')
            print(f"✅ Složka ID: {folder_id}")
            
            # Vytvoříme graf s daty
            print(f"\n🎨 Vytvářím kompletní graf ve složce...")
            
            test_chart = dw.create_chart(
                title=f"KOMPLETNÍ TEST - {survey_id}",
                chart_type="d3-bars",
                folder_id=folder_id,
                description="Testovací graf pro ověření funkčnosti",
                data_source="LimeSurvey Test",
                byline="Pavel Roušar"
            )
            
            if test_chart:
                chart_id = test_chart.get('id')
                print(f"✅ Graf vytvořen: {chart_id}")
                
                # Přidáme testovací data
                print(f"\n📊 Přidávám testovací data...")
                test_data = "Kategorie,Hodnota\nOdpověď A,25\nOdpověď B,35\nOdpověď C,20\nOdpověď D,15"
                
                data_success = dw.update_chart_data(chart_id, test_data)
                
                if data_success:
                    print(f"✅ Data úspěšně přidána")
                    
                    # Publikujeme graf
                    print(f"\n📤 Publikuji graf...")
                    publish_result = dw.publish_chart(chart_id)
                    
                    if publish_result:
                        public_url = publish_result.get('data', {}).get('publicUrl', 'N/A')
                        print(f"✅ Graf publikován")
                        print(f"   🌐 Public URL: {public_url}")
                        
                        # Exportujeme graf jako PNG
                        print(f"\n💾 Exportuji graf jako PNG...")
                        try:
                            png_result = dw.export_chart(chart_id, format='png', width=600, border_width=10, zoom=2)
                            if png_result:
                                print(f"✅ PNG export úspěšný")
                            else:
                                print(f"❌ PNG export neúspěšný")
                        except Exception as e:
                            print(f"⚠️  PNG export není dostupný: {str(e)}")
                        
                        print(f"\n🎯 FINÁLNÍ VÝSLEDEK:")
                        print(f"   📊 Graf: KOMPLETNÍ TEST - {survey_id}")
                        print(f"   🆔 ID: {chart_id}")
                        print(f"   📁 Folder ID: {folder_id}")
                        print(f"   🌐 URL: {public_url}")
                        print(f"   📍 Umístění: Shared/Můj tým/LimeSurvey/{survey_id}/")
                        
                        print(f"\n⚠️  Graf NEBYL smazán pro kontrolu!")
                        
                    else:
                        print(f"❌ Nepodařilo se publikovat graf")
                else:
                    print(f"❌ Nepodařilo se přidat data")
            else:
                print(f"❌ Nepodařilo se vytvořit graf")
        else:
            print(f"❌ Nepodařilo se vytvořit složku")
            
    except Exception as e:
        print(f"❌ Chyba: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_complete_chart_creation()