#!/usr/bin/env python3
"""
Test script pro ověření oprav v Datawrapper modulu
"""

import json
import os
from src.datawrapper_client import DatawrapperClient
from src.chart_generator import ChartGenerator

def test_chart_settings():
    """Test nastavení grafu pro PNG export"""
    print("=== Test nastavení PNG exportu ===")
    
    # Vytvoření instance
    generator = ChartGenerator(
        survey_title="Test Survey",
        png_width=600,  # Pouze šířka, výška automatická
        png_border=10,
        png_scale=2,
        auto_height=True  # Automatická výška
    )
    
    print(f"✓ PNG šířka: {generator.png_width}px")
    print(f"✓ PNG border: {generator.png_border}px") 
    print(f"✓ PNG scale: {generator.png_scale}x")
    print(f"✓ Automatická výška: {generator.auto_height}")
    print(f"✓ Finální šířka: {generator.png_width * generator.png_scale}px")
    
    # Test DatawrapperClient nastavení
    if generator.dw:
        print("\n=== Test DatawrapperClient nastavení ===")
        
        # Simulace nastavení grafu (bez skutečného API volání)
        test_settings = {
            "width": generator.png_width,
            "padding": generator.png_border,  # Opraveno: používá 'padding' místo 'border'
            "scale": generator.png_scale,
            "logo": "auto",
            "mode": "rgb"
        }
        
        payload = {
            "metadata": {
                "publish": {
                    "export-png": test_settings  # Opraveno: používá 'export-png' místo 'export-pdf'
                }
            }
        }
        
        print("✓ Payload pro PNG export:")
        print(json.dumps(payload, indent=2))
        
        # Kontrola, že se nenastavuje výška
        if "height" not in test_settings:
            print("✓ Výška se nenastavuje - bude automatická")
        else:
            print("✗ CHYBA: Výška se stále nastavuje!")
            
        # Kontrola správného názvu parametru pro border
        if "padding" in test_settings:
            print("✓ Používá se 'padding' pro okraj")
        elif "borderWidth" in test_settings:
            print("? Používá se 'borderWidth' pro okraj")
        elif "border" in test_settings:
            print("? Používá se 'border' pro okraj")
        else:
            print("✗ CHYBA: Žádný parametr pro okraj!")
            
        # Kontrola správného klíče pro PNG export
        if "export-png" in payload["metadata"]["publish"]:
            print("✓ Používá se správný klíč 'export-png'")
        else:
            print("✗ CHYBA: Nesprávný klíč pro PNG export!")
    
    else:
        print("⚠ DatawrapperClient není dostupný (chybí API klíč)")

def main():
    print("Test oprav v Datawrapper modulu")
    print("=" * 50)
    
    try:
        test_chart_settings()
        print("\n" + "=" * 50)
        print("✓ Test dokončen")
        
    except Exception as e:
        print(f"\n✗ Chyba během testu: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()