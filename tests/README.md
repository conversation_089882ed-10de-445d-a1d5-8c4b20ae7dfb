# 🧪 Testovací soubory LimWrapp

Tento adresář obsahuje všechny testovací soubory vytvořené během vývoje projektu.

## 📋 Kategorie testů

### 🎯 **Aktuální důležité testy:**
- `test_ano_ne_order.py` - Test logického pořadí Ano/Ne odpovědí
- `test_row_sorting_complete.py` - Test řazení řádků v grafech
- `test_selective_chart_generation.py` - Test selektivního generování grafů
- `test_gui.py` - Test GUI aplikace
- `test_language_system.py` - Test jazykového systému
- `test_translation_fix.py` - Test překladového systému

### 🔧 **Opravy Array grafů:**
- `test_array_chart_fixes.py` - Opravy array grafů
- `test_array_names_and_legend.py` - N<PERSON>zvy a legendy array grafů
- `test_legend_and_scale_order.py` - Pořadí legend a škál
- `test_lss_order.py` - LSS pořadí odpovědí
- `test_lss_order_preservation.py` - <PERSON><PERSON>ní LSS pořadí
- `test_menu6_lss_order.py` - Test Menu 6 s LSS pořadím

### 🌐 **Datawrapper API testy:**
- `test_datawrapper_fixes.py` - Opravy Datawrapper API
- `test_datawrapper_real_fix.py` - Skutečné opravy API
- `test_folder_creation.py` - Vytváření složek
- `test_folder_fix.py` - Opravy složek
- `test_direct_329553.py` - Přímý přístup ke složce
- `test_team_folder_creation.py` - Vytváření team složek

### 📊 **Generování grafů:**
- `test_enhanced_chart_generation.py` - Enhanced generování
- `test_enhanced_charts_menu.py` - Enhanced menu
- `test_chart_generation_fixes.py` - Opravy generování
- `test_real_chart_generation.py` - Skutečné generování
- `test_complete_chart_creation.py` - Kompletní vytváření grafů

### 🖼️ **PNG export a rozměry:**
- `test_png_download.py` - Stahování PNG
- `test_png_naming.py` - Pojmenování PNG souborů
- `test_aspect_ratio_calculation.py` - Výpočet poměru stran
- `test_automatic_height.py` - Automatická výška
- `test_stable_dimensions.py` - Stabilní rozměry
- `test_no_width_parameter.py` - Test bez width parametru

### 🌍 **Vícejazyčnost:**
- `test_czech_language_in_chart.py` - Čeština v grafech
- `test_czech_based_filenames.py` - České názvy souborů
- `test_png_filename_language.py` - Jazyky v názvech PNG
- `test_new_translation_structure.py` - Nová struktura překladů
- `test_robust_translation_system.py` - Robustní překlady

### 🔄 **Data processing:**
- `test_data_ordering.py` - Řazení dat
- `test_csv_validation.py` - Validace CSV
- `test_completed_responses_filter.py` - Filtr dokončených odpovědí
- `test_missing_scale_responses.py` - Chybějící škálové odpovědi
- `test_question_texts_fix.py` - Oprava textů otázek

### 🏗️ **Integrace a workflow:**
- `test_main_integration.py` - Integrace main.py
- `test_enhanced_charts_with_labels.py` - Enhanced grafy s popisky
- `test_official_approach.py` - Oficiální přístup
- `test_final_fix.py` - Finální oprava
- `test_final_official_fix.py` - Finální oficiální oprava

### 🐛 **Debug a experimentální:**
- `test_*_debug.py` - Debug verze testů
- `test_correct_api_call.py` - Správné API volání
- `test_bar_sorting.py` - Řazení sloupců
- `test_chart_folder_placement.py` - Umístění složek grafů

## 🚀 **Spuštění testů**

### Jednotlivé testy:
```bash
cd tests
python test_ano_ne_order.py
python test_row_sorting_complete.py
python test_selective_chart_generation.py
```

### Všechny testy (pokud mají main):
```bash
cd tests
for test in test_*.py; do
    echo "Running $test..."
    python "$test"
done
```

## 📝 **Poznámky**

- **Historické testy** - Dokumentují celý proces vývoje
- **Debugging reference** - Užitečné pro řešení budoucích problémů
- **Různé přístupy** - Ukazují různé způsoby řešení problémů
- **Experimentální kód** - Obsahují i neúspěšné pokusy

## 🎯 **Pro nové PC**

Tyto testy jsou automaticky dostupné po klonování projektu a poskytují:
- **Kompletní historii vývoje**
- **Testovací případy pro debugging**
- **Referenční implementace**
- **Dokumentaci problémů a řešení**

**Augment AI má přehled o všech testech a může je využít pro budoucí vývoj!** 🤖✨
