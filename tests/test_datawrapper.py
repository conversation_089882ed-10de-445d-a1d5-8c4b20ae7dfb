import os
import pytest
import pandas as pd
from unittest.mock import patch, MagicMock
from src.datawrapper_client import Datawrapper<PERSON>lient

@pytest.fixture
def client():
    with patch.dict('os.environ', {'DATAWRAPPER_API_KEY': 'test-key'}):
        return DatawrapperClient()

@pytest.fixture
def mock_response():
    response = MagicMock()
    response.status_code = 201
    response.json.return_value = {
        'id': 'test-id',
        'title': 'Test Chart',
        'type': 'd3-bars'
    }
    return response

def test_create_folder(client, mock_response):
    with patch('requests.post', return_value=mock_response) as mock_post:
        folder = client.create_folder("Test Folder")
        assert folder['id'] == 'test-id'
        mock_post.assert_called_once()

def test_get_folder(client, mock_response):
    mock_response.status_code = 200
    with patch('requests.get', return_value=mock_response) as mock_get:
        folder = client.get_folder("test-folder-id")
        assert folder['id'] == 'test-id'
        mock_get.assert_called_once()

def test_move_chart_to_folder(client):
    mock_response = MagicMock()
    mock_response.status_code = 200
    with patch('requests.patch', return_value=mock_response) as mock_patch:
        result = client.move_chart_to_folder("test-chart-id", "test-folder-id")
        assert result is True
        mock_patch.assert_called_once()

def test_create_chart(client, mock_response):
    with patch('requests.post', return_value=mock_response) as mock_post:
        chart = client.create_chart(
            title="Test Chart",
            chart_type="d3-bars",
            folder_id="test-folder"
        )
        assert chart['id'] == 'test-id'
        assert chart['title'] == 'Test Chart'
        mock_post.assert_called_once()

def test_update_chart_data(client):
    mock_response = MagicMock()
    mock_response.status_code = 200
    test_data = pd.DataFrame({
        'Kategorie': ['A', 'B', 'C'],
        'Hodnoty': [10, 20, 30]
    })
    
    with patch('requests.put', return_value=mock_response) as mock_put:
        result = client.update_chart_data("test-chart-id", test_data)
        assert result is True
        mock_put.assert_called_once()

def test_publish_chart(client, mock_response):
    mock_response.status_code = 200
    with patch('requests.post', return_value=mock_response) as mock_post:
        result = client.publish_chart("test-chart-id")
        assert result['id'] == 'test-id'
        mock_post.assert_called_once()

def test_export_chart(client):
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.content = b"test-image-data"
    
    with patch('requests.get', return_value=mock_response) as mock_get:
        result = client.export_chart("test-chart-id")
        assert result == b"test-image-data"
        mock_get.assert_called_once()

def test_get_chart_types(client):
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = [
        {"id": "d3-bars", "name": "Bar Chart"},
        {"id": "d3-lines", "name": "Line Chart"}
    ]
    
    with patch('requests.get', return_value=mock_response) as mock_get:
        result = client.get_chart_types()
        assert len(result) == 2
        assert result[0]["id"] == "d3-bars"
        mock_get.assert_called_once()

def test_error_handling(client):
    mock_response = MagicMock()
    mock_response.status_code = 404
    mock_response.text = "Not found"
    
    with patch('requests.post', return_value=mock_response):
        folder = client.create_folder("Test Folder")
        assert folder is None
        
        chart = client.create_chart("Test Chart")
        assert chart is None
