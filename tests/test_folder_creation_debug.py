#!/usr/bin/env python3
"""
Detailní test script pro ověření vytvoření složky v Datawrapper
"""

import sys
import os
import json
sys.path.append('.')

def test_folder_creation_debug():
    """Detailní test vytvoření složky v Datawrapper pro survey 827822"""
    
    survey_id = "827822"
    
    print(f"🧪 Detailní testování vytvoření složky v Datawrapper pro survey {survey_id}")
    print("=" * 70)
    
    try:
        # Import Datawrapper klienta
        from src.datawrapper_client import DatawrapperClient
        
        # Vytvoření klienta
        print("🔗 Připojuji se k Datawrapper API...")
        dw = DatawrapperClient()
        
        if not dw.api_key:
            print("❌ Chybí Datawrapper API token")
            return
        
        print("✅ Datawrapper API token nalezen")
        
        # Nejprve získáme seznam všech složek
        print(f"\n📋 Získávám seznam všech složek...")
        try:
            folders = dw.get_folders()
            if folders:
                print(f"✅ Nalezeno {len(folders)} složek")
                print("📁 Existující složky:")
                for i, folder in enumerate(folders[:10]):  # Zobrazíme prvních 10
                    print(f"   {i+1}. {folder.get('name', 'Bez názvu')} (ID: {folder.get('id')})")
                if len(folders) > 10:
                    print(f"   ... a dalších {len(folders) - 10} složek")
                    
                # Zkontrolujeme, zda už složka existuje
                existing = None
                for folder in folders:
                    if folder.get('name') == survey_id:
                        existing = folder
                        break
                
                if existing:
                    print(f"\n⚠️  Složka '{survey_id}' už existuje!")
                    print(f"   ID: {existing.get('id')}")
                    print(f"   Název: {existing.get('name')}")
                    print(f"   Typ: {existing.get('type', 'N/A')}")
                else:
                    print(f"\n✅ Složka '{survey_id}' neexistuje, můžeme ji vytvořit")
            else:
                print("❌ Nepodařilo se získat seznam složek")
        except Exception as e:
            print(f"❌ Chyba při získávání složek: {str(e)}")
        
        # Test hledání složky podle názvu
        print(f"\n🔍 Testuji hledání složky podle názvu '{survey_id}'...")
        try:
            found_folder = dw.get_folder_by_name(survey_id)
            if found_folder:
                print(f"✅ Složka nalezena pomocí get_folder_by_name:")
                print(f"   ID: {found_folder.get('id')}")
                print(f"   Název: {found_folder.get('name')}")
            else:
                print(f"✅ Složka nenalezena pomocí get_folder_by_name (což je správně)")
        except Exception as e:
            print(f"❌ Chyba při hledání složky: {str(e)}")
        
        # Test vytvoření složky
        print(f"\n📁 Pokouším se vytvořit složku '{survey_id}'...")
        
        folder = dw.create_folder(survey_id)
        
        if folder:
            print(f"✅ Složka úspěšně vytvořena nebo nalezena!")
            print(f"📋 Detaily složky:")
            print(f"   ID: {folder.get('id', 'N/A')}")
            print(f"   Název: {folder.get('name', 'N/A')}")
            print(f"   Typ: {folder.get('type', 'N/A')}")
            
            # Ověříme, že složka je nyní v seznamu
            print(f"\n🔍 Ověřuji, že složka je nyní v seznamu...")
            try:
                updated_folders = dw.get_folders()
                if updated_folders:
                    survey_folder = None
                    for f in updated_folders:
                        if f.get('name') == survey_id:
                            survey_folder = f
                            break
                    
                    if survey_folder:
                        print(f"✅ Složka '{survey_id}' je nyní v seznamu!")
                        print(f"   ID: {survey_folder.get('id')}")
                        print(f"   Název: {survey_folder.get('name')}")
                        
                        # Test, že můžeme složku použít pro vytvoření grafu
                        print(f"\n🧪 Testuji použití složky pro vytvoření testovacího grafu...")
                        test_chart = dw.create_chart(
                            title="Test Graf",
                            chart_type="d3-bars",
                            folder_id=survey_folder.get('id')
                        )
                        
                        if test_chart:
                            print(f"✅ Testovací graf úspěšně vytvořen ve složce!")
                            print(f"   Graf ID: {test_chart.get('id')}")
                            print(f"   Název: {test_chart.get('title')}")
                            
                            # Smažeme testovací graf
                            try:
                                dw.delete_chart(test_chart.get('id'))
                                print(f"🗑️  Testovací graf smazán")
                            except:
                                print(f"⚠️  Nepodařilo se smazat testovací graf")
                        else:
                            print(f"❌ Nepodařilo se vytvořit testovací graf ve složce")
                    else:
                        print(f"❌ Složka '{survey_id}' nebyla nalezena v aktualizovaném seznamu")
                else:
                    print("❌ Nepodařilo se získat aktualizovaný seznam složek")
            except Exception as e:
                print(f"❌ Chyba při ověřování: {str(e)}")
        else:
            print("❌ Nepodařilo se vytvořit složku")
            
    except Exception as e:
        print(f"❌ Chyba při testování: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_folder_creation_debug()