import dearpygui.dearpygui as dpg

def resize_columns(sender, app_data):
    # Získání aktuální š<PERSON>řky okna
    window_width = dpg.get_item_width(main_window)
    
    # Pokud se mění levý sloupec
    if sender == left_col:
        left_width = dpg.get_item_width(left_col)
        dpg.set_item_width(right_col, window_width - left_width)
        # Nastavení <PERSON> vnitřn<PERSON>ch bloků
        dpg.configure_item(left_top, width=left_width-16)
        dpg.configure_item(left_bottom, width=left_width-16)
    # Pokud se mění celé okno
    else:
        # Zachování poměru sloupců
        left_ratio = dpg.get_item_width(left_col) / window_width
        new_left_width = window_width * left_ratio
        dpg.set_item_width(left_col, new_left_width)
        dpg.set_item_width(right_col, window_width * (1 - left_ratio))
        # Nasta<PERSON><PERSON> vnitř<PERSON><PERSON><PERSON> bloků
        dpg.configure_item(left_top, width=new_left_width-16)
        dpg.configure_item(left_bottom, width=new_left_width-16)

def resize_left_blocks(sender, app_data):
    # Získání aktuální výšky levého sloupce
    left_height = dpg.get_item_height(left_col)
    
    # Pokud se mění horní blok
    if sender == left_top:
        # Získání původní výšky horního bloku
        old_top_height = dpg.get_item_height(left_top)
        
        # Získání nové výšky horního bloku
        new_top_height = dpg.get_item_height(left_top)
        
        # Výpočet rozdílu výšky
        height_diff = old_top_height - new_top_height
        
        # Získání aktuální výšky spodního bloku
        current_bottom_height = dpg.get_item_height(left_bottom)
        
        # Nastavení nové výšky spodního bloku
        new_bottom_height = current_bottom_height + height_diff
        
        # Nastavení výšky spodního bloku
        dpg.set_item_height(left_bottom, new_bottom_height)
        
        # Posunutí spodního bloku pod horní blok
        dpg.set_item_pos(left_bottom, [0, new_top_height])
    # Pokud se mění celý sloupec
    else:
        # Zachování poměru bloků
        top_ratio = dpg.get_item_height(left_top) / left_height
        new_top_height = left_height * top_ratio
        dpg.set_item_height(left_top, new_top_height)
        dpg.set_item_height(left_bottom, left_height - new_top_height)
        dpg.set_item_pos(left_bottom, [0, new_top_height])

def on_window_resize(sender, app_data):
    # Zpracování změny velikosti hlavního okna
    resize_columns(sender, app_data)
    resize_left_blocks(sender, app_data)

dpg.create_context()

with dpg.window(label="Test Window", width=800, height=600, tag="main_window"):
    with dpg.group(horizontal=True):
        # Levý sloupec
        left_col = dpg.add_child_window(border=True, width=400, height=600, no_scrollbar=True, no_scroll_with_mouse=True, resizable_x=True, resizable_y=True)
        with dpg.item_handler_registry(tag="left_col_handler"):
            dpg.add_item_resize_handler(callback=resize_columns)
        dpg.bind_item_handler_registry(left_col, "left_col_handler")
        
        # Kontejner pro bloky
        with dpg.child_window(border=False, width=-1, height=-1, parent=left_col, no_scrollbar=True):
            # Horní část (50%)
            left_top = dpg.add_child_window(border=True, width=-1, height=300, no_scrollbar=True)
            with dpg.item_handler_registry(tag="left_top_handler"):
                dpg.add_item_resize_handler(callback=resize_left_blocks)
            dpg.bind_item_handler_registry(left_top, "left_top_handler")
            dpg.add_text("Horní část (50%)", parent=left_top)
            
            # Dolní část (50%)
            left_bottom = dpg.add_child_window(border=True, width=-1, height=-1, no_scrollbar=True)
            dpg.add_text("Dolní část (50%)", parent=left_bottom)
        
        # Pravý sloupec
        right_col = dpg.add_child_window(border=True, width=400, height=600)
        # Horní část (70%)
        with dpg.child_window(border=True, width=-1, height=420, parent=right_col):
            dpg.add_text("Horní část (70%)")
        
        # Střední část (15%)
        with dpg.child_window(border=True, width=-1, height=90, parent=right_col):
            dpg.add_text("Střední část (15%)")
        
        # Dolní část (5%)
        with dpg.child_window(border=True, width=-1, height=30, parent=right_col):
            dpg.add_text("Dolní část (5%)")

dpg.create_viewport(title='Test GUI', width=800, height=600)
dpg.setup_dearpygui()
dpg.show_viewport()
dpg.start_dearpygui()
dpg.destroy_context()
