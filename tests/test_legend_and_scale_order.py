#!/usr/bin/env python3
"""
Test pro ověření oprav legendy barev a zachování po<PERSON>
"""

import sys
import os
import json

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_color_legend_metadata():
    """Test správného atributu pro legendu barev"""
    print("🧪 Test atributu pro legendu barev...")
    
    try:
        from datawrapper_client import DatawrapperClient
        
        # Simulace vytvoření grafu
        payload = {
            "title": "Test graf",
            "type": "d3-bars",
            "metadata": {
                "describe": {
                    "intro": "Test popis",
                    "source-name": "Test zdroj",
                    "source-url": "",
                    "byline": "Test autor"
                },
                "visualize": {
                    "chart": {
                        "margin": {
                            "top": 10,
                            "right": 10,
                            "bottom": 10,
                            "left": 10
                        }
                    },
                    "show-color-key": True
                }
            }
        }
        
        print("✅ Metadata s legendou barev:")
        print(json.dumps(payload["metadata"]["visualize"], indent=2))
        
        # Ově<PERSON>ení správného atributu
        if payload["metadata"]["visualize"].get("show-color-key") is True:
            print("✅ Správný atribut 'show-color-key' je nastaven")
            return True
        else:
            print("❌ Atribut 'show-color-key' není nastaven")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu legendy: {str(e)}")
        return False

def test_scale_order_preservation():
    """Test zachování pořadí škálových hodnot"""
    print("\n🧪 Test zachování pořadí škál...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        generator = EnhancedChartGenerator()
        
        # Testovací data s škálou v určitém pořadí
        test_chart_data = [
            {
                'subquestion': 'Kvalita služeb',
                'responses': {
                    'Výborný': 25,
                    'Dobrý': 18,
                    'Neutrální': 12,
                    'Špatný': 8,
                    'Velmi špatný': 5
                }
            },
            {
                'subquestion': 'Rychlost obsluhy',
                'responses': {
                    'Výborný': 20,
                    'Dobrý': 22,
                    'Neutrální': 15,
                    'Špatný': 8,
                    'Velmi špatný': 3
                }
            }
        ]
        
        # Simulace zpracování dat
        all_responses = []
        response_order = {}
        
        for item in test_chart_data:
            if 'responses' in item:
                for response in item['responses'].keys():
                    if response not in response_order:
                        response_order[response] = len(all_responses)
                        all_responses.append(response)
        
        response_columns = all_responses
        
        print("✅ Pořadí škálových hodnot:")
        for i, response in enumerate(response_columns):
            print(f"   {i+1}. {response}")
        
        # Ověření, že pořadí odpovídá logické škále
        expected_order = ['Výborný', 'Dobrý', 'Neutrální', 'Špatný', 'Velmi špatný']
        if response_columns == expected_order:
            print("✅ Pořadí škály je zachováno správně")
            return True
        else:
            print(f"❌ Pořadí škály není správné. Očekáváno: {expected_order}")
            print(f"   Získáno: {response_columns}")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu pořadí škál: {str(e)}")
        return False

def test_array_metadata_with_color_legend():
    """Test metadat pro array grafy s legendou barev"""
    print("\n🧪 Test metadat array grafů s legendou...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        generator = EnhancedChartGenerator()
        
        # Testovací data
        test_dw_data = [
            {'Subotázka': 'Test 1', 'Výborný': 5, 'Dobrý': 12, 'Neutrální': 25},
            {'Subotázka': 'Test 2', 'Výborný': 3, 'Dobrý': 8, 'Neutrální': 20}
        ]
        
        # Test přípravy metadat
        metadata = generator._prepare_chart_metadata_for_array(
            'array', 'd3-bars-stacked', test_dw_data
        )
        
        print("✅ Metadata array grafu:")
        print(json.dumps(metadata, indent=2, ensure_ascii=False))
        
        # Ověření legendy barev
        if metadata.get('visualize', {}).get('show-color-key') is True:
            print("✅ Legenda barev je zapnutá v array metadatech")
            return True
        else:
            print("❌ Legenda barev není zapnutá v array metadatech")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu array metadat: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test oprav legendy barev a pořadí škál")
    print("=" * 50)
    
    tests = [
        test_color_legend_metadata,
        test_scale_order_preservation,
        test_array_metadata_with_color_legend
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Všechny testy prošly! Opravy jsou funkční.")
        print("\n📋 Shrnutí oprav:")
        print("   • Legenda barev: 'show-color-key': true")
        print("   • Pořadí škál: zachováno původní pořadí z dat")
        return True
    else:
        print("❌ Některé testy selhaly. Zkontrolujte implementaci.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
