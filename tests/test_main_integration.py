#!/usr/bin/env python3
"""
Test integrace hlav<PERSON><PERSON> s<PERSON> s automatickou detekcí r<PERSON>
"""

from src.chart_generator import ChartGenerator

def test_main_integration():
    """Test že hlavní smyčka správně předává automatické parametry"""
    print("=== Test integrace s main.py ===")
    
    # Simulace parametrů z main.py po opravě
    main_params = {
        'survey_title': "Test Survey",
        'data_source': "Test Source", 
        'data_source_link': "https://test.com",
        'byline': "Test Author",
        'png_width': None,  # AUTOMATICKÁ DETEKCE!
        'png_border': 10,
        'png_scale': 2,
        'auto_height': True,
        'full_header_footer': False,
        'transparent_bg': False
    }
    
    print("✓ Parametry z main.py:")
    for key, value in main_params.items():
        if key == 'png_width' and value is None:
            print(f"  {key}: {value} ← AUTOMATICKÁ DETEKCE!")
        else:
            print(f"  {key}: {value}")
    
    # Vytvoření ChartGenerator s parametry z main.py
    generator = ChartGenerator(**main_params)
    
    print(f"\n✓ ChartGenerator vytvořen:")
    print(f"  png_width: {generator.png_width} ← Mělo by být None")
    print(f"  png_border: {generator.png_border}")
    print(f"  png_scale: {generator.png_scale}")
    print(f"  auto_height: {generator.auto_height}")
    
    # Kontroly
    if generator.png_width is None:
        print("✅ SPRÁVNĚ: png_width je None - bude automatická detekce!")
    else:
        print(f"❌ CHYBA: png_width je {generator.png_width} místo None!")
        
    if generator.auto_height is True:
        print("✅ SPRÁVNĚ: auto_height je True")
    else:
        print(f"❌ CHYBA: auto_height je {generator.auto_height}!")
        
    # Simulace volání export_chart
    print(f"\n✓ Simulace volání export_chart:")
    export_params = {
        'chart_id': 'test123',
        'export_format': 'png',
        'width': generator.png_width,  # None = automatická detekce
        'border_width': generator.png_border,
        'zoom': generator.png_scale,
        'plain': not generator.full_header_footer,
        'mode': 'rgba' if generator.transparent_bg else 'rgb'
    }
    
    for key, value in export_params.items():
        if key == 'width' and value is None:
            print(f"  {key}: {value} ← AUTOMATICKÁ DETEKCE Z GRAFU!")
        else:
            print(f"  {key}: {value}")

def test_cli_output():
    """Test výstupu CLI"""
    print("\n=== Test CLI výstupu ===")
    
    print("Před opravou CLI se ptalo:")
    print("  Šířka PNG (px) [600]: _")
    print("  Automatická výška? (y/n) [y]: _")
    
    print("\nPo opravě CLI říká:")
    print("  ℹ️  Šířka se automaticky detekuje z publikovaného grafu")
    print("  ℹ️  Výška se automaticky přizpůsobí obsahu grafu")
    print("  Okraj/Border (px) [10]: _")
    print("  Zoom factor (násobič) [2]: _")
    
    print("\n✅ CLI už se neptá na šířku - je automatická!")

def main():
    print("Test integrace main.py s automatickou detekcí")
    print("=" * 55)
    
    test_main_integration()
    test_cli_output()
    
    print("\n" + "=" * 55)
    print("✅ Test dokončen")
    print("\nKlíčové změny v main.py:")
    print("- Odstraněn input pro šířku PNG")
    print("- Odstraněn input pro automatickou výška") 
    print("- png_width = None (automatická detekce)")
    print("- auto_height = True (vždy automatická)")

if __name__ == "__main__":
    main()