#!/usr/bin/env python3
"""
Test výpočtu poměru stran a správné výšky
"""

from src.datawrapper_client import DatawrapperClient

def test_aspect_ratio_calculation():
    """Test výpočtu poměru stran pro existující graf"""
    print("=== Test výpočtu poměru stran ===")
    
    try:
        # Použijeme existující testovací graf
        chart_id = "UdncQ"  # Graf z předchozího testu
        target_width = 600  # Cílová šířka
        
        client = DatawrapperClient()
        
        # Získání informací o grafu
        print(f"✓ Získávám informace o grafu {chart_id}...")
        chart_info = client.get_chart(chart_id)
        
        if not chart_info:
            print("❌ Nepodařilo se získat informace o grafu")
            return
            
        # Získání rozměrů
        metadata = chart_info.get("metadata", {})
        publish_data = metadata.get("publish", {})
        
        embed_width = publish_data.get("embed-width")
        embed_height = publish_data.get("embed-height")
        
        if not embed_width or not embed_height:
            print("❌ Nepodařilo se získat rozměry publikovaného grafu")
            print(f"embed-width: {embed_width}")
            print(f"embed-height: {embed_height}")
            return
            
        print(f"✓ Publikovaný graf: {embed_width}×{embed_height}px")
        
        # Výpočet poměru stran
        aspect_ratio = embed_width / embed_height
        calculated_height = round(target_width / aspect_ratio)
        
        print(f"✓ Poměr stran: {aspect_ratio:.3f}")
        print(f"✓ Pro šířku {target_width}px vypočítaná výška: {calculated_height}px")
        
        # Porovnání s ručním exportem
        manual_height = 212  # Jak říkáš, že dostáváš při ručním exportu
        manual_ratio = target_width / manual_height
        
        print(f"\n📊 Porovnání:")
        print(f"  Vypočítaný poměr: {aspect_ratio:.3f} → výška {calculated_height}px")
        print(f"  Ruční export: {manual_ratio:.3f} → výška {manual_height}px")
        
        if abs(calculated_height - manual_height) < 5:
            print("✅ SPRÁVNĚ: Vypočítaná výška odpovídá ručnímu exportu!")
        else:
            print("⚠️  ROZDÍL: Vypočítaná výška se liší od ručního exportu")
            print(f"    Rozdíl: {abs(calculated_height - manual_height)}px")
            
        # Test exportu s vypočítanými rozměry
        print(f"\n✓ Test exportu s rozměry {target_width}×{calculated_height}px...")
        
        png_data = client.export_chart(
            chart_id,
            export_format='png',
            target_width=target_width,
            border_width=10,
            zoom=2,
            plain=True,
            mode='rgb'
        )
        
        if png_data:
            # Uložení PNG
            import os
            output_dir = "test_charts"
            os.makedirs(output_dir, exist_ok=True)
            
            png_filename = f"aspect_ratio_test_{target_width}x{calculated_height}.png"
            png_path = os.path.join(output_dir, png_filename)
            
            with open(png_path, 'wb') as f:
                f.write(png_data)
                
            print(f"✅ Graf exportován: {png_path}")
            
            # Zjištění skutečných rozměrů
            try:
                from PIL import Image
                with Image.open(png_path) as img:
                    actual_width, actual_height = img.size
                    print(f"  Skutečné rozměry PNG: {actual_width}×{actual_height}px")
                    
                    # Kontrola zoom faktoru
                    expected_width = target_width * 2  # zoom = 2
                    expected_height = calculated_height * 2
                    
                    print(f"  Očekávané rozměry (s zoom 2x): {expected_width}×{expected_height}px")
                    
                    if actual_width == expected_width and actual_height == expected_height:
                        print("✅ PERFEKTNÍ: Rozměry přesně odpovídají!")
                    else:
                        print(f"⚠️  ROZDÍL: Skutečné vs očekávané rozměry")
                        
            except ImportError:
                print("  (PIL není dostupná pro zjištění rozměrů)")
            except Exception as e:
                print(f"  (Chyba při zjišťování rozměrů: {e})")
        else:
            print("❌ Export selhal")
            
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("Test výpočtu poměru stran a správné výšky")
    print("=" * 50)
    
    test_aspect_ratio_calculation()
    
    print("\n" + "=" * 50)
    print("✅ Test dokončen")

if __name__ == "__main__":
    main()