#!/usr/bin/env python3
"""
Test script pro ověření, že enhanced chart generator používá správné labely z long formátu
"""

import sys
import os
sys.path.append('.')

from src.enhanced_chart_generator import EnhancedChartGenerator
from src.logger import get_logger

def test_enhanced_charts_with_labels():
    """Test enhanced chart generator s long formátem dat"""
    logger = get_logger(__name__)
    
    # Použijeme survey 827822, který má všechny potřebné soubory
    survey_id = "827822"
    
    print(f"🧪 Testování enhanced chart generator pro survey {survey_id}")
    print("=" * 60)
    
    # Kontrola existence souborů
    required_files = [
        f"data/{survey_id}/structure.lss",
        f"data/{survey_id}/responses.csv", 
        f"data/{survey_id}/question_mapping.csv",
        f"data/{survey_id}/responses_long.csv"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            return
    
    print()
    
    try:
        # Vytvoření enhanced chart generator
        generator = EnhancedChartGenerator()
        generator.survey_id = survey_id  # Nastavíme survey_id pro použití v _filter_question_data
        
        # Test načtení dat pomocí analyzeru
        print("📊 Testování načtení dat...")
        from src.lss_question_analyzer import LSSQuestionAnalyzer
        analyzer = LSSQuestionAnalyzer()
        
        lss_path = f"data/{survey_id}/structure.lss"
        lss_analysis = analyzer.analyze_lss_structure(lss_path)
        
        if 'error' not in lss_analysis:
            recommended_charts = analyzer.get_recommended_charts(lss_analysis)
            print(f"✅ Načteno {len(recommended_charts)} doporučených grafů")
            
            # Zobrazíme první několik otázek
            print("\n📋 První 3 otázky:")
            for i, chart in enumerate(recommended_charts[:3]):
                print(f"  {i+1}. ID: {chart['question_id']}, Typ: {chart['chart_type']}, Název: {chart['question_title'][:50]}...")
            
            if recommended_charts:
                # Test filtrování dat pro první otázku
                print(f"\n🔍 Testování filtrování dat pro první otázku...")
                first_chart = recommended_charts[0]
                first_question_id = first_chart['question_id']
                first_question_title = first_chart['question_title']
                
                # Načtení responses
                import pandas as pd
                responses_path = f"data/{survey_id}/responses.csv"
                responses_df = pd.read_csv(responses_path, sep=';')
                
                # Test filtrování
                filtered_data = generator._filter_question_data(
                    responses_df,
                    first_question_id,
                    first_question_title
                )
                
                if not filtered_data.empty:
                    print(f"✅ Filtrování úspěšné - {len(filtered_data)} odpovědí")
                    print(f"📊 Ukázka dat:")
                    print(filtered_data.head(10))
                    
                    # Kontrola, zda data obsahují správné labely (ne jen ID)
                    sample_responses = filtered_data['response'].dropna().head(5).tolist()
                    print(f"\n🏷️  Ukázka response hodnot:")
                    for i, response in enumerate(sample_responses, 1):
                        print(f"  {i}. '{response}'")
                    
                    # Kontrola, zda obsahuje lidsky čitelné labely
                    has_readable_labels = any(
                        len(str(resp)) > 3 and not str(resp).isdigit()
                        for resp in sample_responses if resp
                    )
                    
                    if has_readable_labels:
                        print("✅ Data obsahují lidsky čitelné labely!")
                    else:
                        print("⚠️  Data stále obsahují pouze ID - možná potřebujeme další mapování")
                        
                else:
                    print("❌ Filtrování neúspěšné - žádná data")
                    
        else:
            print(f"❌ Chyba při analýze LSS: {lss_analysis['error']}")
            
    except Exception as e:
        print(f"❌ Chyba při testování: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_charts_with_labels()