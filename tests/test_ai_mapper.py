import pytest
from unittest.mock import patch, MagicMock
from src.ai_name_mapper import AINameMapper

def test_init_missing_key():
    with patch.dict('os.environ', clear=True):
        with pytest.raises(ValueError):
            AINameMapper()

@patch('openai.ChatCompletion.create')
def test_map_names(mock_create):
    mock_response = MagicMock()
    mock_response.choices[0].message.content = "Q1: Spokojenost\nQ2: Oblíbené barvy"
    mock_create.return_value = mock_response
    
    mapper = AINameMapper()
    with patch.dict('os.environ', {'OPENAI_API_KEY': 'test-key'}):
        result = mapper.map_names({
            'Q1': '<PERSON><PERSON> jste spokojeni s kvalitou služeb?',
            'Q2': '<PERSON><PERSON><PERSON> jsou vaše oblíbené barvy z následující nabídky?'
        })
    
    assert result == {
        'Q1': 'Spokojenost',
        'Q2': 'Obl<PERSON>bené barvy'
    }

def test_create_prompt():
    mapper = AINameMapper()
    with patch.dict('os.environ', {'OPENAI_API_KEY': 'test-key'}):
        prompt = mapper._create_prompt({
            'Q1': 'Test question 1',
            'Q2': 'Test question 2'
        })
    
    assert 'Q1: Test question 1' in prompt
    assert 'Q2: Test question 2' in prompt

def test_parse_response():
    mapper = AINameMapper()
    with patch.dict('os.environ', {'OPENAI_API_KEY': 'test-key'}):
        result = mapper._parse_response("Q1: Test 1\nQ2: Test 2\n")
    
    assert result == {
        'Q1': 'Test 1',
        'Q2': 'Test 2'
    }

@patch('openai.ChatCompletion.create')
def test_map_names_error(mock_create):
    mock_create.side_effect = Exception("API Error")
    
    mapper = AINameMapper()
    with patch.dict('os.environ', {'OPENAI_API_KEY': 'test-key'}):
        with pytest.raises(Exception):
            mapper.map_names({'Q1': 'Test'})
