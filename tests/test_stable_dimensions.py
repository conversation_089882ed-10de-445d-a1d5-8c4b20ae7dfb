#!/usr/bin/env python3
"""
Test stabilizace rozměrů grafu PhLVp
"""

from src.datawrapper_client import DatawrapperClient

def test_stable_dimensions():
    """Test stabilizace rozměrů"""
    print("=== TEST STABILIZACE ROZMĚRŮ ===")
    
    chart_id = "PhLVp"  # Graf z uživatelova logu
    
    try:
        client = DatawrapperClient()
        
        print(f"🔍 Test stabilizace rozměrů grafu {chart_id}...")
        
        # Test nové metody _get_stable_dimensions
        stable_dims = client._get_stable_dimensions(chart_id, max_attempts=3, wait_seconds=1)
        
        if stable_dims:
            width, height = stable_dims
            ratio = width / height
            print(f"✅ Stabilní rozměry: {width}×{height}px (poměr {ratio:.3f})")
            
            # Test exportu s těmito rozměry
            print(f"\n🔬 Test exportu se stabilními rozměry...")
            
            png_data = client.export_chart(
                chart_id,
                export_format='png',
                target_width=600,
                border_width=10,
                zoom=2,
                plain=True,
                mode='rgb'
            )
            
            if png_data:
                # Uložení PNG
                import os
                output_dir = "test_charts"
                os.makedirs(output_dir, exist_ok=True)
                
                png_filename = f"stable_dimensions_{chart_id}.png"
                png_path = os.path.join(output_dir, png_filename)
                
                with open(png_path, 'wb') as f:
                    f.write(png_data)
                    
                print(f"✅ Graf exportován: {png_path}")
                
                # Analýza výsledku
                try:
                    from PIL import Image
                    with Image.open(png_path) as img:
                        actual_width, actual_height = img.size
                        actual_ratio = actual_width / actual_height
                        print(f"  Skutečné rozměry PNG: {actual_width}×{actual_height}px")
                        print(f"  Skutečný poměr: {actual_ratio:.3f}")
                        
                        # Zpětný výpočet základních rozměrů (bez zoom)
                        base_width = actual_width // 2
                        base_height = actual_height // 2
                        base_ratio = base_width / base_height
                        
                        print(f"  Základní rozměry: {base_width}×{base_height}px")
                        print(f"  Základní poměr: {base_ratio:.3f}")
                        
                        # Porovnání s očekávanými rozměry
                        expected_height = round(600 / ratio)
                        expected_width_zoom = 600 * 2
                        expected_height_zoom = expected_height * 2
                        
                        print(f"\n📊 Porovnání:")
                        print(f"  Očekávané základní: 600×{expected_height}px (poměr {ratio:.3f})")
                        print(f"  Skutečné základní: {base_width}×{base_height}px (poměr {base_ratio:.3f})")
                        print(f"  Očekávané s zoom: {expected_width_zoom}×{expected_height_zoom}px")
                        print(f"  Skutečné s zoom: {actual_width}×{actual_height}px")
                        
                        # Kontrola přesnosti
                        width_diff = abs(actual_width - expected_width_zoom)
                        height_diff = abs(actual_height - expected_height_zoom)
                        
                        if width_diff <= 20 and height_diff <= 20:  # Tolerance 20px kvůli borderu
                            print("✅ PERFEKTNÍ: Rozměry odpovídají očekávaným!")
                        else:
                            print(f"⚠️  ROZDÍL: Šířka {width_diff}px, výška {height_diff}px")
                            
                except ImportError:
                    print("  (PIL není dostupná pro zjištění rozměrů)")
                except Exception as e:
                    print(f"  (Chyba při analýze: {e})")
            else:
                print("❌ Export selhal")
        else:
            print("❌ Nepodařilo se získat stabilní rozměry")
            
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()

def test_timing_comparison():
    """Porovnání rychlého vs pomalého získání rozměrů"""
    print("\n=== POROVNÁNÍ TIMING ===")
    
    chart_id = "PhLVp"
    
    try:
        client = DatawrapperClient()
        
        # Rychlé získání (okamžité)
        print("🏃 Rychlé získání rozměrů (okamžité):")
        chart_info = client.get_chart(chart_id)
        if chart_info:
            metadata = chart_info.get("metadata", {})
            publish_data = metadata.get("publish", {})
            quick_width = publish_data.get("embed-width")
            quick_height = publish_data.get("embed-height")
            if quick_width and quick_height:
                quick_ratio = quick_width / quick_height
                print(f"  Rozměry: {quick_width}×{quick_height}px (poměr {quick_ratio:.3f})")
        
        # Pomalé získání (se stabilizací)
        print("\n🐌 Pomalé získání rozměrů (se stabilizací):")
        stable_dims = client._get_stable_dimensions(chart_id, max_attempts=3, wait_seconds=1)
        if stable_dims:
            stable_width, stable_height = stable_dims
            stable_ratio = stable_width / stable_height
            print(f"  Rozměry: {stable_width}×{stable_height}px (poměr {stable_ratio:.3f})")
            
            # Porovnání
            if quick_width == stable_width and quick_height == stable_height:
                print("✅ STEJNÉ: Rychlé i pomalé získání dalo stejné rozměry")
            else:
                print("⚠️  ROZDÍL: Rychlé a pomalé získání dalo různé rozměry!")
                print(f"    Rychlé: {quick_width}×{quick_height}")
                print(f"    Pomalé: {stable_width}×{stable_height}")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")

def main():
    print("Test stabilizace rozměrů grafu PhLVp")
    print("=" * 50)
    
    test_stable_dimensions()
    test_timing_comparison()
    
    print("\n" + "=" * 50)
    print("✅ Test dokončen")
    print("\nKlíčové pozorování:")
    print("- Stabilizace rozměrů čeká na vykreslení grafu")
    print("- Měla by dát správný poměr stran ~1.007")
    print("- Export by měl respektovat tento poměr")

if __name__ == "__main__":
    main()