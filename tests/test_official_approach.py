#!/usr/bin/env python3
"""
Test oficiálního přístupu - pouze width, bez height!
"""

import os
import sys
import time
from PIL import Image

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from datawrapper_client import DatawrapperClient

def test_official_approach():
    """Test s oficiálním přístupem - pouze width parametr"""
    print("Test oficiálního přístupu (pouze width)")
    print("=" * 50)
    
    client = DatawrapperClient()
    chart_id = "PhLVp"  # Testovací graf s poměrem ~1:1
    
    print(f"🔍 Test grafu {chart_id} s oficiálním přístupem...")
    
    # OFICIÁLNÍ PŘÍSTUP - pouze width, žádný height!
    try:
        # Publikování grafu
        print("📤 Publikuji graf...")
        publish_result = client.publish_chart(chart_id)
        print(f"✅ Graf publikován")
        
        # Kr<PERSON>tké čekání na vykreslení
        print("⏳ Čekám 3 sekundy na vykreslení...")
        time.sleep(3)
        
        # Export POUZE s width parametrem (jako oficiální knihovna)
        print("🖼️ Exportuji s oficiálními parametry...")
        
        # Parametry podle oficiální knihovny
        export_params = {
            'unit': 'px',
            'mode': 'rgb', 
            'width': 600,  # POUZE width!
            'plain': False,
            'zoom': 2,
            'borderWidth': 10,  # Správný název parametru
            'transparent': False
        }
        
        # Export bez jakéhokoliv height parametru
        png_data = client._make_export_request(chart_id, 'png', export_params)
        
        # Uložení
        output_path = "test_charts/official_approach_PhLVp.png"
        os.makedirs("test_charts", exist_ok=True)
        
        with open(output_path, 'wb') as f:
            f.write(png_data)
        
        # Analýza výsledku
        with Image.open(output_path) as img:
            actual_width, actual_height = img.size
            actual_ratio = actual_width / actual_height
            
        print(f"✅ Graf exportován: {output_path}")
        print(f"  Skutečné rozměry PNG: {actual_width}×{actual_height}px")
        print(f"  Skutečný poměr: {actual_ratio:.3f}")
        
        # Základní rozměry (bez zoom)
        base_width = actual_width // 2  # zoom = 2
        base_height = actual_height // 2
        base_ratio = base_width / base_height
        
        print(f"  Základní rozměry: {base_width}×{base_height}px")
        print(f"  Základní poměr: {base_ratio:.3f}")
        
        # Očekáváme poměr ~1.007 (600×596)
        expected_ratio = 1.007
        ratio_diff = abs(actual_ratio - expected_ratio)
        
        if ratio_diff < 0.01:
            print(f"✅ PERFEKTNÍ: Poměr {actual_ratio:.3f} odpovídá očekávanému {expected_ratio:.3f}")
        else:
            print(f"❌ PROBLÉM: Poměr {actual_ratio:.3f} neodpovídá očekávanému {expected_ratio:.3f}")
            
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

if __name__ == "__main__":
    test_official_approach()