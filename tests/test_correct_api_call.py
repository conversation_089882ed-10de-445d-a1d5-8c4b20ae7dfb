#!/usr/bin/env python3
"""
Test se správným API voláním podle uživatele
"""

import os
import sys
import requests
from PIL import Image

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from datawrapper_client import DatawrapperClient

def test_correct_api():
    """Test se správným API voláním"""
    print("Test se správným API voláním")
    print("=" * 50)
    
    client = DatawrapperClient()
    chart_id = "PhLVp"
    
    # Publikování grafu
    print("📤 Publikuji graf...")
    publish_result = client.publish_chart(chart_id)
    print("✅ Graf publikován")
    
    # SPRÁVNÉ API volání podle uživatele
    url = f"https://api.datawrapper.de/v3/charts/{chart_id}/export/png"
    
    params = {
        "unit": "px",
        "mode": "rgb", 
        "width": "600",
        "height": "auto",  # ← TOTO JE KLÍČ!
        "plain": "false",
        "scale": "1",
        "zoom": "2", 
        "borderWidth": "20",
        "download": "false",
        "fullVector": "false",
        "ligatures": "true",
        "transparent": "false",
        "logo": "auto",
        "dark": "false"
    }
    
    headers = {
        "accept": "image/png",
        "Authorization": f"Bearer {client.api_key}"
    }
    
    print("🔍 API volání:")
    print(f"URL: {url}")
    print(f"Params: {params}")
    
    response = requests.get(url, headers=headers, params=params)
    
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        # Uložení
        output_path = "test_charts/correct_api_PhLVp.png"
        os.makedirs("test_charts", exist_ok=True)
        
        with open(output_path, 'wb') as f:
            f.write(response.content)
        
        # Analýza
        with Image.open(output_path) as img:
            width, height = img.size
            ratio = width / height
            
        print(f"✅ Export úspěšný: {output_path}")
        print(f"  Rozměry: {width}×{height}px")
        print(f"  Poměr: {ratio:.3f}")
        
        # Kontrola poměru
        expected_ratio = 1.007
        if abs(ratio - expected_ratio) < 0.01:
            print(f"✅ PERFEKTNÍ: Poměr odpovídá očekávanému!")
        else:
            print(f"❌ PROBLÉM: Poměr {ratio:.3f} neodpovídá očekávanému {expected_ratio:.3f}")
            
    else:
        print(f"❌ Chyba: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    test_correct_api()