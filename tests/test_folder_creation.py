#!/usr/bin/env python3
"""
Test script pro ověření vytvoření složky v Datawrapper
"""

import sys
import os
sys.path.append('.')

def test_folder_creation():
    """Test vytvoření složky v Datawrapper pro survey 827822"""
    
    survey_id = "827822"
    
    print(f"🧪 Testování vytvoření složky v Datawrapper pro survey {survey_id}")
    print("=" * 60)
    
    try:
        # Import Datawrapper klienta
        from src.datawrapper_client import DatawrapperClient
        
        # Vytvoření klienta
        print("🔗 Připojuji se k Datawrapper API...")
        dw = DatawrapperClient()
        
        if not dw.api_key:
            print("❌ Chybí Datawrapper API token")
            print("Nastavte DATAWRAPPER_API_TOKEN v environment variables")
            return
        
        print("✅ Datawrapper API token nalezen")
        
        # Test vytvoření složky
        print(f"\n📁 Pokouším se vytvořit složku '{survey_id}'...")
        
        folder = dw.create_folder(survey_id)
        
        if folder:
            print(f"✅ Složka úspěšně vytvořena nebo nalezena!")
            print(f"📋 Detaily složky:")
            print(f"   ID: {folder.get('id', 'N/A')}")
            print(f"   Název: {folder.get('name', 'N/A')}")
            print(f"   Typ: {folder.get('type', 'N/A')}")
            
            # Pokusíme se získat seznam složek pro ověření
            print(f"\n🔍 Ověřuji existenci složky v seznamu...")
            try:
                folders = dw.get_folders()
                if folders:
                    survey_folder = None
                    for f in folders:
                        if f.get('name') == survey_id:
                            survey_folder = f
                            break
                    
                    if survey_folder:
                        print(f"✅ Složka '{survey_id}' nalezena v seznamu!")
                        print(f"   ID: {survey_folder.get('id')}")
                    else:
                        print(f"⚠️  Složka '{survey_id}' nebyla nalezena v seznamu")
                        print("📋 Dostupné složky:")
                        for f in folders[:5]:  # Zobrazíme prvních 5
                            print(f"   - {f.get('name', 'Bez názvu')} (ID: {f.get('id')})")
                else:
                    print("❌ Nepodařilo se získat seznam složek")
            except Exception as e:
                print(f"⚠️  Chyba při získávání seznamu složek: {str(e)}")
        else:
            print("❌ Nepodařilo se vytvořit složku")
            
    except Exception as e:
        print(f"❌ Chyba při testování: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_folder_creation()