#!/usr/bin/env python3
"""
Test script pro ověření vytvoření složky v týmu LimeSurvey
"""

import sys
import os
sys.path.append('.')

# Explicitně načteme .env soubor
try:
    from dotenv import load_dotenv
    load_dotenv()
    print(f"🔧 .env soubor načten")
except ImportError:
    print("⚠️  dotenv není dostupný")

def test_team_folder_creation():
    """Test vytvoření složky v týmu LimeSurvey pro survey 827822"""
    
    survey_id = "827822"
    
    print(f"🧪 Testování vytvoření složky v týmu LimeSurvey pro survey {survey_id}")
    print("=" * 70)
    
    try:
        # Import Datawrapper klienta
        from src.datawrapper_client import DatawrapperClient
        
        # Vytvoření klienta
        print("🔗 Připojuji se k Datawrapper API...")
        dw = DatawrapperClient()
        
        print(f"✅ Datawrapper API token nalezen")
        print(f"🏢 Team ID: {dw.team_id}")
        print(f"📁 LimeSurvey Folder ID: {dw.limesurvey_folder_id}")
        
        if not dw.team_id:
            print("❌ DATAWRAPPER_TEAM_ID není nastaven v .env")
            return
            
        if not dw.limesurvey_folder_id:
            print("❌ DATAWRAPPER_LIMESURVEY_FOLDER_ID není nastaven v .env")
            return
        
        # Test vytvoření složky v týmu
        print(f"\n📁 Pokouším se vytvořit složku '{survey_id}' v týmu LimeSurvey...")
        
        folder = dw.create_folder(survey_id)
        
        if folder:
            print(f"✅ Složka úspěšně vytvořena nebo nalezena!")
            print(f"📋 Detaily složky:")
            print(f"   ID: {folder.get('id', 'N/A')}")
            print(f"   Název: {folder.get('name', 'N/A')}")
            print(f"   Parent ID: {folder.get('parentId', 'N/A')}")
            print(f"   Team ID: {folder.get('teamId', 'N/A')}")
            print(f"   Typ: {folder.get('type', 'N/A')}")
            
            # Ověření, že složka je ve správném týmu a parent složce
            if str(folder.get('teamId')) == str(dw.team_id):
                print(f"✅ Složka je ve správném týmu ({dw.team_id})")
            else:
                print(f"⚠️  Složka není ve správném týmu. Očekáváno: {dw.team_id}, Skutečnost: {folder.get('teamId')}")
                
            if str(folder.get('parentId')) == str(dw.limesurvey_folder_id):
                print(f"✅ Složka je ve správné parent složce ({dw.limesurvey_folder_id})")
            else:
                print(f"⚠️  Složka není ve správné parent složce. Očekáváno: {dw.limesurvey_folder_id}, Skutečnost: {folder.get('parentId')}")
            
            # Test, že můžeme složku použít pro vytvoření grafu
            print(f"\n🧪 Testuji použití složky pro vytvoření testovacího grafu...")
            test_chart = dw.create_chart(
                title="Test Graf v týmu",
                chart_type="d3-bars",
                folder_id=folder.get('id')
            )
            
            if test_chart:
                print(f"✅ Testovací graf úspěšně vytvořen ve složce týmu!")
                print(f"   Graf ID: {test_chart.get('id')}")
                print(f"   Název: {test_chart.get('title')}")
                print(f"   Folder ID: {test_chart.get('folderId')}")
                
                # Smažeme testovací graf
                try:
                    dw.delete_chart(test_chart.get('id'))
                    print(f"🗑️  Testovací graf smazán")
                except:
                    print(f"⚠️  Nepodařilo se smazat testovací graf")
            else:
                print(f"❌ Nepodařilo se vytvořit testovací graf ve složce týmu")
                
            print(f"\n🎯 VÝSLEDEK:")
            print(f"   Složka '{survey_id}' byla úspěšně vytvořena v:")
            print(f"   📍 Tým: {folder.get('teamId')} (LimeSurvey)")
            print(f"   📁 Parent složka: {folder.get('parentId')}")
            print(f"   🔗 URL: archive/team/{folder.get('teamId')}/{folder.get('id')}")
            
        else:
            print("❌ Nepodařilo se vytvořit složku")
            
    except Exception as e:
        print(f"❌ Chyba při testování: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_team_folder_creation()