#!/usr/bin/env python3
"""
Test že se NEPOSÍLÁ žádný width parametr pro úplně automatické rozměry
"""

from src.datawrapper_client import DatawrapperClient

def test_no_width_parameter():
    """Test že se neposílá width parametr"""
    print("=== Test ŽÁDNÝ width parametr ===")
    
    try:
        # Vytvoření klienta
        client = DatawrapperClient()
        
        # Simulace parametrů pro export (bez width!)
        border_width = 10
        zoom = 2
        plain = True
        mode = 'rgb'
        
        # Parametry jak by je vytvořil export_chart
        params = {
            "borderWidth": border_width,
            "zoom": zoom,
            "plain": "true" if plain else "false",
            "mode": mode,
            "unit": "px"
            # ŽÁDNÝ width parametr!
        }
        
        print("✓ Parametry pro PNG export:")
        for key, value in params.items():
            print(f"  {key}={value}")
        
        # Kontroly
        if "width" not in params:
            print("✅ SPRÁVNĚ: Žádný width parametr - úplně automatické rozměry!")
        else:
            print(f"❌ CHYBA: width parametr se stále posílá: {params['width']}")
            
        if "height" not in params:
            print("✅ SPRÁVNĚ: Žádný height parametr - automatická výška!")
        else:
            print(f"❌ CHYBA: height parametr se posílá: {params['height']}")
            
        # Test URL
        chart_id = "test123"
        base_url = "https://api.datawrapper.de/v3"
        export_url = f"{base_url}/charts/{chart_id}/export/png"
        
        url_params = "&".join([f"{k}={v}" for k, v in params.items()])
        full_url = f"{export_url}?{url_params}"
        
        print(f"\n✓ Export URL (BEZ width parametru):")
        print(f"  {full_url}")
        
        # Kontrola URL
        if "width=" not in full_url:
            print("✅ SPRÁVNĚ: URL neobsahuje width parametr!")
        else:
            print("❌ CHYBA: URL stále obsahuje width parametr!")
            
        if "height=" not in full_url:
            print("✅ SPRÁVNĚ: URL neobsahuje height parametr!")
        else:
            print("❌ CHYBA: URL obsahuje height parametr!")
            
    except Exception as e:
        print(f"❌ Chyba: {e}")

def test_comparison():
    """Porovnání s předchozím přístupem"""
    print("\n=== Porovnání přístupů ===")
    
    print("ŠPATNÝ přístup (předchozí):")
    print("  URL: /export/png?width=600&borderWidth=10&zoom=2")
    print("  Výsledek: 1200×800px (fixní rozměry)")
    
    print("\nSPRÁVNÝ přístup (nový):")
    print("  URL: /export/png?borderWidth=10&zoom=2&plain=true&mode=rgb&unit=px")
    print("  Výsledek: AUTO×AUTO px (podle grafu)")
    
    print("\n✅ Klíčová změna: ŽÁDNÝ width ani height parametr!")

def main():
    print("Test ŽÁDNÝ width parametr pro automatické rozměry")
    print("=" * 55)
    
    test_no_width_parameter()
    test_comparison()
    
    print("\n" + "=" * 55)
    print("✅ Test dokončen")
    print("\nKlíčové pozorování:")
    print("- Oficiální knihovna říká: 'If not specified, it takes the chart width'")
    print("- To znamená: NENASTAVOVAT width vůbec!")
    print("- Datawrapper automaticky použije rozměry publikovaného grafu")
    print("- Zoom se aplikuje na automatické rozměry")

if __name__ == "__main__":
    main()