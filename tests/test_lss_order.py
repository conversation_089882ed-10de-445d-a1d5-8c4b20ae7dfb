#!/usr/bin/env python3
"""
Test řazení šk<PERSON>lov<PERSON>ch hodnot podle LSS order atributu
"""

import sys
import os

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_lss_order_sorting():
    """Test řazení podle LSS order"""
    print("🧪 Test řazení škálových hodnot podle LSS order...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        # Simulace LSS struktury s answeroptions
        mock_lss_structure = {
            "G1": {
                "questions": {
                    "G1Q00001": {
                        "answeroptions": {
                            "AO01": {
                                "answer": "rozhodně ano",
                                "assessment_value": 0,
                                "scale_id": 0,
                                "order": 0
                            },
                            "AO02": {
                                "answer": "spíše ano", 
                                "assessment_value": 0,
                                "scale_id": 0,
                                "order": 1
                            },
                            "AO03": {
                                "answer": "spí<PERSON>e ne",
                                "assessment_value": 0,
                                "scale_id": 0,
                                "order": 2
                            },
                            "AO04": {
                                "answer": "rozhodně ne",
                                "assessment_value": 0,
                                "scale_id": 0,
                                "order": 3
                            },
                            "AO05": {
                                "answer": "neumím to posoudit",
                                "assessment_value": 0,
                                "scale_id": 0,
                                "order": 4
                            }
                        }
                    }
                }
            }
        }
        
        generator = EnhancedChartGenerator()
        generator.lss_structure = mock_lss_structure
        
        # Test dat - sloupce v abecedním pořadí (špatně)
        original_columns = [
            "neumím to posoudit",
            "rozhodně ano", 
            "rozhodně ne",
            "spíše ano",
            "spíše ne"
        ]
        
        # Očekávané pořadí podle LSS order (0, 1, 2, 3, 4)
        expected_order = [
            "rozhodně ano",      # order: 0
            "spíše ano",         # order: 1
            "spíše ne",          # order: 2
            "rozhodně ne",       # order: 3
            "neumím to posoudit" # order: 4
        ]
        
        print(f"✅ Původní pořadí (abecední): {original_columns}")
        print(f"✅ Očekávané pořadí (LSS order): {expected_order}")
        
        # Test řazení
        sorted_columns = generator._sort_columns_by_lss_order(original_columns, "G1Q00001")
        
        print(f"✅ Výsledné pořadí: {sorted_columns}")
        
        # Ověření
        if sorted_columns == expected_order:
            print("✅ Řazení podle LSS order funguje správně!")
            return True
        else:
            print("❌ Řazení podle LSS order nefunguje")
            print(f"   Očekáváno: {expected_order}")
            print(f"   Získáno: {sorted_columns}")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        return False

def test_fallback_behavior():
    """Test chování při chybějící LSS struktuře"""
    print("\n🧪 Test fallback chování...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        generator = EnhancedChartGenerator()
        # Bez LSS struktury
        
        original_columns = ["c", "a", "b"]
        
        # Mělo by vrátit původní pořadí
        result = generator._sort_columns_by_lss_order(original_columns, "neexistuje")
        
        if result == original_columns:
            print("✅ Fallback chování funguje - vrací původní pořadí")
            return True
        else:
            print("❌ Fallback chování nefunguje")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu fallback: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test řazení škálových hodnot podle LSS order")
    print("=" * 60)
    
    tests = [
        test_lss_order_sorting,
        test_fallback_behavior
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Všechny testy prošly! Řazení podle LSS order je funkční.")
        print("\n📋 Výhody:")
        print("   • Škálové hodnoty se řadí podle LSS order (0, 1, 2, 3, 4)")
        print("   • Zachovává se logické pořadí odpovědí")
        print("   • Fallback na původní pořadí při chybách")
        return True
    else:
        print("❌ Některé testy selhaly. Zkontrolujte implementaci.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
