import pytest
import os
from src.limesurvey_client import LimeSurveyClient
from src.data_transformer import DataTransformer
from src.datawrapper_client import DatawrapperClient

@pytest.fixture
def setup():
    """Příprava testovacího prostředí"""
    # Vytvoření klientů
    ls_client = LimeSurveyClient(data_dir="test_data")
    dw_client = DatawrapperClient()
    
    # Stažení dat
    survey_id = "214947"
    lss_path = ls_client.get_survey_structure(survey_id)
    csv_path = ls_client.get_responses(survey_id)
    
    # Vytvoření transformeru
    transformer = DataTransformer(lss_path, csv_path)
    
    yield ls_client, dw_client, transformer, survey_id
    
    # Cleanup
    if os.path.exists("test_data"):
        for file in os.listdir("test_data"):
            os.remove(os.path.join("test_data", file))
        os.rmdir("test_data")

def test_full_workflow(setup):
    """Test celého procesu zpracování dat"""
    ls_client, dw_client, transformer, survey_id = setup
    
    # Vytvoření složky pro průzkum
    folder = dw_client.create_folder(f"Survey_{survey_id}")
    assert folder is not None
    folder_id = folder['id']
    
    # Zpracování každé otázky
    for qid, question in transformer.structure.items():
        # Transformace dat
        try:
            data = transformer.transform_data(qid)
            
            # Vytvoření grafu
            chart = dw_client.create_chart(
                title=question['text'],
                chart_type='d3-bars',  # TODO: Vybrat typ podle dat
                folder_id=folder_id
            )
            assert chart is not None
            
            # Nahrání dat
            success = dw_client.update_chart_data(chart['id'], data)
            assert success
            
        except Exception as e:
            print(f"Chyba při zpracování otázky {qid}: {str(e)}")
            continue

def test_single_question_workflow(setup):
    """Test zpracování jedné otázky"""
    ls_client, dw_client, transformer, survey_id = setup
    
    # Najít single choice otázku
    question_id = next(
        qid for qid, q in transformer.structure.items() 
        if q['type'] == 'single_choice'
    )
    question = transformer.structure[question_id]
    
    # Transformace dat
    data = transformer.transform_data(question_id)
    assert len(data) > 0
    
    # Vytvoření grafu
    chart = dw_client.create_chart(
        title=question['text'],
        chart_type='d3-bars'
    )
    assert chart is not None
    
    # Nahrání dat
    success = dw_client.update_chart_data(chart['id'], data)
    assert success
    
    # Export grafu
    image = dw_client.export_chart(chart['id'])
    assert image is not None
