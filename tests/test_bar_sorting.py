#!/usr/bin/env python3
"""
Test pro ověření řazení sloupců v array grafech
"""

import sys
import os
import json

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_bar_sorting_metadata():
    """Test metadat pro řazení sloupců"""
    print("🧪 Test metadat pro řazení sloupců...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        generator = EnhancedChartGenerator()
        
        # Testovací data pro array graf
        test_dw_data = [
            {'Subotázka': '<PERSON><PERSON><PERSON> služ<PERSON>', '1': 5, '2': 12, '3': 25, '4': 18, '5': 8},
            {'Subotázka': 'Rychlost obsluhy', '1': 3, '2': 8, '3': 20, '4': 22, '5': 15},
            {'Subotázka': '<PERSON><PERSON> slu<PERSON>', '1': 8, '2': 15, '3': 18, '4': 12, '5': 5}
        ]
        
        # Test přípravy metadat
        metadata = generator._prepare_chart_metadata_for_array(
            'array', 'd3-bars-stacked', test_dw_data
        )
        
        print("✅ Metadata pro řazení sloupců:")
        print(json.dumps(metadata.get('visualize', {}), indent=2, ensure_ascii=False))
        
        # Ověření klíčových atributů pro řazení
        visualize = metadata.get('visualize', {})
        
        checks = [
            ('resort-bars', True, "Řazení sloupců zapnuto"),
            ('sort-asc', False, "Sestupné řazení nastaveno"),
            ('show-color-key', True, "Legenda barev zapnuta"),
            ('custom-sort-order', list, "Vlastní pořadí kategorií nastaveno")
        ]
        
        all_passed = True
        for key, expected_value, description in checks:
            actual_value = visualize.get(key)
            
            if key == 'custom-sort-order':
                # Pro custom-sort-order kontrolujeme, že je to seznam
                if isinstance(actual_value, list) and len(actual_value) > 0:
                    print(f"✅ {description}: {actual_value}")
                else:
                    print(f"❌ {description}: chybí nebo prázdný")
                    all_passed = False
            else:
                if actual_value == expected_value:
                    print(f"✅ {description}: {actual_value}")
                else:
                    print(f"❌ {description}: očekáváno {expected_value}, získáno {actual_value}")
                    all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Chyba při testu řazení sloupců: {str(e)}")
        return False

def test_category_sorting_logic():
    """Test logiky řazení kategorií podle celkových hodnot"""
    print("\n🧪 Test logiky řazení kategorií...")
    
    try:
        # Simulace dat s různými celkovými hodnotami
        test_data = [
            {'Subotázka': 'Kvalita služeb', '1': 5, '2': 12, '3': 25, '4': 18, '5': 8},    # Celkem: 68
            {'Subotázka': 'Rychlost obsluhy', '1': 3, '2': 8, '3': 20, '4': 22, '5': 15}, # Celkem: 68
            {'Subotázka': 'Cena služeb', '1': 8, '2': 15, '3': 18, '4': 12, '5': 5}       # Celkem: 58
        ]
        
        # Simulace logiky řazení z enhanced_chart_generator.py
        value_columns = [col for col in test_data[0].keys() if col != 'Subotázka']
        row_totals = []
        
        for row in test_data:
            total = sum(row.get(col, 0) for col in value_columns if isinstance(row.get(col), (int, float)))
            row_totals.append((row.get('Subotázka', ''), total))
        
        # Seřazení podle celkových hodnot (sestupně)
        sorted_rows = sorted(row_totals, key=lambda x: x[1], reverse=True)
        sorted_categories = [row[0] for row in sorted_rows]
        
        print("✅ Řazení kategorií podle celkových hodnot:")
        for i, (category, total) in enumerate(sorted_rows):
            print(f"   {i+1}. {category}: {total} odpovědí")
        
        # Ověření správného řazení (sestupně)
        totals = [total for _, total in sorted_rows]
        is_descending = all(totals[i] >= totals[i+1] for i in range(len(totals)-1))
        
        if is_descending:
            print("✅ Kategorie jsou seřazeny sestupně podle celkových hodnot")
            return True
        else:
            print("❌ Kategorie nejsou správně seřazeny")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu řazení kategorií: {str(e)}")
        return False

def test_complete_metadata_structure():
    """Test kompletní struktury metadat"""
    print("\n🧪 Test kompletní struktury metadat...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        generator = EnhancedChartGenerator()
        
        # Testovací data
        test_dw_data = [
            {'Subotázka': 'Test A', '1': 10, '2': 20, '3': 30},
            {'Subotázka': 'Test B', '1': 5, '2': 15, '3': 25}
        ]
        
        metadata = generator._prepare_chart_metadata_for_array(
            'array', 'd3-bars-stacked', test_dw_data
        )
        
        print("✅ Kompletní struktura metadat:")
        print(json.dumps(metadata, indent=2, ensure_ascii=False))
        
        # Kontrola hlavních sekcí
        required_sections = ['visualize', 'axes']
        required_visualize_keys = ['resort-bars', 'sort-asc', 'show-color-key', 'custom-sort-order']
        
        all_present = True
        
        for section in required_sections:
            if section in metadata:
                print(f"✅ Sekce '{section}' přítomna")
            else:
                print(f"❌ Sekce '{section}' chybí")
                all_present = False
        
        visualize = metadata.get('visualize', {})
        for key in required_visualize_keys:
            if key in visualize:
                print(f"✅ Klíč '{key}' přítomen")
            else:
                print(f"❌ Klíč '{key}' chybí")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print(f"❌ Chyba při testu struktury metadat: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test řazení sloupců v array grafech")
    print("=" * 50)
    
    tests = [
        test_bar_sorting_metadata,
        test_category_sorting_logic,
        test_complete_metadata_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Všechny testy prošly! Řazení sloupců je správně nastaveno.")
        print("\n📋 Shrnutí nastavení:")
        print("   • resort-bars: true (řazení sloupců zapnuto)")
        print("   • sort-asc: false (sestupné řazení)")
        print("   • show-color-key: true (legenda barev)")
        print("   • custom-sort-order: podle celkových hodnot")
        return True
    else:
        print("❌ Některé testy selhaly. Zkontrolujte implementaci.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
