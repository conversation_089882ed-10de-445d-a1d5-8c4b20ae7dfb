#!/usr/bin/env python3
"""
Test přímého přístupu ke složce 329553
"""

import sys
import os

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_folder_329553():
    """Test přímého přístupu ke složce 329553"""
    print("🧪 Test přímého přístupu ke složce 329553...")
    
    try:
        from datawrapper_client import DatawrapperClient
        
        dw = DatawrapperClient()
        
        print("🔍 Načítám obsah složky 329553...")
        contents = dw.get_folder_contents("329553")
        
        if contents:
            print(f"✅ Nalezeno {len(contents)} položek ve složce 329553")

            # API vrací jen ID grafů, ne kompletní objekty
            # Takže všechny položky jsou grafy (pokud mají jen 'id')
            charts = contents

            print(f"📊 Grafů: {len(charts)}")

            # Zobrazíme prvních 5 grafů
            for i, chart in enumerate(charts[:5]):
                chart_id = chart.get('id', 'N/A')
                print(f"   {i+1}. Graf ID: {chart_id}")

            if len(charts) > 5:
                print(f"   ... a dalších {len(charts) - 5} grafů")

            return len(charts) > 0
        else:
            print("❌ Složka 329553 je prázdná nebo neexistuje")
            return False
        
    except Exception as e:
        print(f"❌ Chyba: {str(e)}")
        return False

def main():
    """Hlavní funkce"""
    print("🚀 Test složky 329553 pro průzkum 827822")
    print("=" * 50)
    
    success = test_folder_329553()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Složka 329553 obsahuje grafy!")
        print("🎯 Menu 9 by mělo fungovat s ID 329553")
    else:
        print("❌ Složka 329553 neobsahuje grafy")

if __name__ == "__main__":
    main()
