import pytest
from unittest.mock import patch
from src.config_loader import load_config

def test_load_config_defaults():
    with patch.dict('os.environ', clear=True):
        config = load_config()
        assert config['DEFAULT_CHART_TYPE'] == 'd3-bars-vertical'
        assert config['GRAPH_FOOTER'] == 'Data z LimeSurvey'
        assert config['EXPORT_PNG'] is True

def test_load_config_custom():
    env_vars = {
        'DEFAULT_CHART_TYPE': 'd3-lines',
        'GRAPH_FOOTER': 'Custom Footer',
        'EXPORT_PNG': 'false'
    }
    with patch.dict('os.environ', env_vars):
        config = load_config()
        assert config['DEFAULT_CHART_TYPE'] == 'd3-lines'
        assert config['GRAPH_FOOTER'] == 'Custom Footer'
        assert config['EXPORT_PNG'] is False

def test_load_config_partial():
    env_vars = {
        'DEFAULT_CHART_TYPE': 'd3-lines'
    }
    with patch.dict('os.environ', env_vars, clear=True):
        config = load_config()
        assert config['DEFAULT_CHART_TYPE'] == 'd3-lines'
        assert config['GRAPH_FOOTER'] == 'Data z LimeSurvey'
        assert config['EXPORT_PNG'] is True

def test_load_config_invalid_export_png():
    env_vars = {
        'EXPORT_PNG': 'invalid'
    }
    with patch.dict('os.environ', env_vars, clear=True):
        config = load_config()
        assert config['EXPORT_PNG'] is False
