#!/usr/bin/env python3
"""
Test nové struktury překladů pro bezpečný překlad přes LLM
"""

import sys
import os
import json
import tempfile

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_new_structure_extraction():
    """Test extrakce s novou kategorizovanou strukturou"""
    print("🧪 Test nové struktury extrakce...")
    
    try:
        from translation_manager import TranslationManager
        
        # Vytvoření testovacích dat s různými typy odpovědí
        test_chart_data = [
            {
                "code": "G1Q00001",
                "name": "Testovací otázka škálová",
                "type": "array",
                "data": [
                    {
                        "subquestion": "Hodnocení spokojenosti",
                        "responses": {
                            "rozhodně ano": 15,
                            "spíše ano": 25,
                            "spíše ne": 8,
                            "rozhodně ne": 3,
                            "neumím to posoudit": 5
                        }
                    }
                ]
            },
            {
                "code": "G2Q00001",
                "name": "Testovací otázka s volbami",
                "type": "single_choice",
                "data": [
                    {"label": "Ano", "value": 30},
                    {"label": "Ne", "value": 20},
                    {"label": "Nevím", "value": 5}
                ]
            },
            {
                "code": "G3Q00001",
                "name": "Testovací otázka s volným textem",
                "type": "single_choice",
                "data": [
                    {"label": "Ministerstvo zdravotnictví", "value": 5},
                    {"label": "<EMAIL>", "value": 1},
                    {"label": "Velmi dlouhý specifický popis organizace která se zabývá...", "value": 2},
                    {"label": "Asociace krajů", "value": 3}
                ]
            }
        ]
        
        # Vytvoření dočasného adresáře
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_dir = os.path.join(temp_dir, "827822")
            os.makedirs(survey_dir, exist_ok=True)
            
            # Uložení testovacích dat
            chart_data_path = os.path.join(survey_dir, "chart_data.json")
            with open(chart_data_path, 'w', encoding='utf-8') as f:
                json.dump(test_chart_data, f, ensure_ascii=False, indent=2)
            
            # Test extrakce
            tm = TranslationManager("827822")
            tm.survey_dir = survey_dir
            
            strings = tm.extract_translatable_strings(chart_data_path)
            
            print(f"✅ Extrahováno:")
            print(f"   - {len(strings['question_names'])} názvů otázek")
            print(f"   - {len(strings['subquestions'])} podotázek")
            print(f"   - {len(strings['scale_responses'])} škálových odpovědí")
            print(f"   - {len(strings['choice_responses'])} jednoduchých odpovědí")
            print(f"   - {len(strings['free_text_responses'])} otázek s volným textem")
            
            # Kontrola škálových odpovědí
            expected_scale = {"rozhodně ano", "spíše ano", "spíše ne", "rozhodně ne", "neumím to posoudit"}
            found_scale = set(strings['scale_responses'])
            
            if expected_scale.issubset(found_scale):
                print(f"✅ Škálové odpovědi správně kategorizovány: {found_scale}")
            else:
                print(f"❌ Chybí škálové odpovědi: {expected_scale - found_scale}")
                return False
            
            # Kontrola jednoduchých odpovědí
            expected_choice = {"Ano", "Ne", "Nevím"}
            found_choice = set(strings['choice_responses'])
            
            if expected_choice.issubset(found_choice):
                print(f"✅ Jednoduché odpovědi správně kategorizovány: {found_choice}")
            else:
                print(f"❌ Chybí jednoduché odpovědi: {expected_choice - found_choice}")
                return False
            
            # Kontrola free text responses
            if 'G3Q00001' in strings['free_text_responses']:
                g3_data = strings['free_text_responses']['G3Q00001']
                safe_texts = g3_data.get('safe_to_translate', [])
                personal_data = g3_data.get('personal_data', [])
                
                print(f"✅ Free text pro G3Q00001:")
                print(f"   - Bezpečné: {safe_texts}")
                print(f"   - Osobní údaje: {personal_data}")
                
                # Kontrola, že email je v osobních údajích
                if any('@' in text for text in personal_data):
                    print("✅ Email správně identifikován jako osobní údaj")
                else:
                    print("❌ Email nebyl identifikován jako osobní údaj")
                    return False
                
                # Kontrola, že krátké texty jsou bezpečné
                if any('Ministerstvo' in text or 'Asociace' in text for text in safe_texts):
                    print("✅ Krátké organizační názvy správně identifikovány jako bezpečné")
                else:
                    print("❌ Krátké texty nebyly správně kategorizovány")
                    return False
                
                return True
            else:
                print("❌ Free text responses nebyly extrahovány")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_new_template_generation():
    """Test generování šablony s novou strukturou"""
    print("\n🧪 Test generování šablony s novou strukturou...")
    
    try:
        from translation_manager import TranslationManager
        
        chart_data_path = "src/data/827822/chart_data.json"
        if not os.path.exists(chart_data_path):
            print("⚠️  Skutečná data neexistují - přeskakuji test")
            return True
        
        # Vytvoření dočasného adresáře
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_dir = os.path.join(temp_dir, "827822")
            os.makedirs(survey_dir, exist_ok=True)
            
            tm = TranslationManager("827822", "en-US")
            tm.survey_dir = survey_dir
            
            # Generování šablony
            if tm.create_translation_template_for_language(chart_data_path, "en-US"):
                translation_file = os.path.join(survey_dir, "translations_en-US.json")
                
                with open(translation_file, 'r', encoding='utf-8') as f:
                    translations = json.load(f)
                
                print(f"✅ Šablona vygenerována")
                
                # Kontrola nové struktury
                expected_categories = ['question_names', 'subquestions', 'scale_responses', 'choice_responses', 'free_text_responses']
                for category in expected_categories:
                    if category in translations:
                        if category == 'free_text_responses':
                            by_question = translations[category].get('by_question', {})
                            print(f"   - {category}: {len(by_question)} otázek s volným textem")
                        else:
                            count = len(translations[category])
                            print(f"   - {category}: {count} položek")
                    else:
                        print(f"❌ Chybí kategorie: {category}")
                        return False
                
                # Kontrola škálových odpovědí
                scale_responses = translations.get('scale_responses', {})
                expected_scale = {"rozhodně ano", "spíše ano", "spíše ne", "rozhodně ne", "neumím to posoudit"}
                found_scale = set(scale_responses.keys()) & expected_scale
                
                if len(found_scale) >= 4:
                    print(f"✅ Škálové odpovědi v šabloně: {found_scale}")
                else:
                    print(f"❌ Škálové odpovědi chybí v šabloně: {found_scale}")
                    return False
                
                # Kontrola, že osobní údaje jsou označeny
                free_text_data = translations.get('free_text_responses', {}).get('by_question', {})
                found_personal_protection = False
                
                for question_code, question_data in free_text_data.items():
                    personal_data = question_data.get('personal_data', {})
                    for key, value in personal_data.items():
                        if "[NEPŘEKLÁDAT" in value:
                            found_personal_protection = True
                            print(f"✅ Osobní údaje chráněny: '{key}' → '{value}'")
                            break
                    if found_personal_protection:
                        break
                
                if found_personal_protection:
                    print("✅ Ochrana osobních údajů funguje")
                    return True
                else:
                    print("⚠️  Žádné osobní údaje nenalezeny (možná v pořádku)")
                    return True
            else:
                print("❌ Nepodařilo se vygenerovat šablonu")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu šablony: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test nové struktury překladů pro bezpečný překlad přes LLM")
    print("=" * 70)
    
    tests = [
        test_new_structure_extraction,
        test_new_template_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 70)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Nová struktura překladů funguje!")
        print("\n📋 Nová struktura:")
        print("   • question_names: Názvy otázek - bezpečné pro LLM")
        print("   • subquestions: Podotázky - bezpečné pro LLM")
        print("   • scale_responses: Škálové odpovědi - bezpečné pro LLM")
        print("   • choice_responses: Jednoduché volby - bezpečné pro LLM")
        print("   • free_text_responses:")
        print("     - by_question:")
        print("       - safe_to_translate: Krátké obecné texty")
        print("       - personal_data: Emaily, dlouhé texty - NEPŘEKLÁDAT")
        print("\n🎯 Výhody:")
        print("   • Škálové odpovědi oddělené → přeložit jednou, použít všude")
        print("   • Osobní údaje označené → nepošlou se do LLM")
        print("   • Kategorizace podle bezpečnosti → selektivní překlad")
        print("   • Organizace po otázkách → můžeš vybrat jen některé")
        return True
    else:
        print("❌ Nová struktura nefunguje správně")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
