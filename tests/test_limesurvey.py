import pytest
import os
import logging
from datetime import datetime
from src.limesurvey_client import LimeSurveyClient

@pytest.fixture(autouse=True)
def setup_logging():
    """Nastavení úrovně logování pro testy"""
    logging.getLogger().setLevel(logging.ERROR)

@pytest.fixture
def client():
    """Příprava testovacího klienta"""
    test_data_dir = "test_data"
    client = LimeSurveyClient(data_dir=test_data_dir)
    if not client.api_url or not client.username or not client.password:
        pytest.skip("Chybí konfigurace LimeSurvey v .env")
    yield client
    
    # Cleanup - smazán<PERSON> testovacích souborů
    if os.path.exists(test_data_dir):
        import shutil
        shutil.rmtree(test_data_dir)

def test_get_survey_structure(client):
    """Test získání struktury průzkumu"""
    survey_id = "214947"
    lss_path = client.get_survey_structure(survey_id)
    
    # Ov<PERSON><PERSON><PERSON><PERSON>, že byl vr<PERSON>cen platný path
    assert isinstance(lss_path, str)
    assert os.path.exists(lss_path)
    assert lss_path.endswith("structure.lss")
    
    # Načtení a validace struktury ze souboru
    import json
    with open(lss_path, 'r', encoding='utf-8') as f:
        structure = json.load(f)
    
    assert isinstance(structure, dict)
    assert 'groups' in structure
    assert 'questions' in structure
    
    # Základní validace struktury
    for group in structure['groups']:
        assert 'gid' in group
        assert 'group_name' in group
        assert 'questions' in group
        
        for question in group['questions']:
            assert 'qid' in question
            assert 'title' in question
            assert 'question' in question
            assert 'type' in question
            assert 'answers' in question

def test_get_question_properties(client):
    """Test získání vlastností otázky"""
    # Získání ID první otázky
    survey_id = "214947"
    lss_path = client.get_survey_structure(survey_id)
    
    # Načtení struktury ze souboru
    import json
    with open(lss_path, 'r', encoding='utf-8') as f:
        structure = json.load(f)
    question_id = structure['groups'][0]['questions'][0]['qid']
    
    # Získání základních vlastností
    properties = client.get_question_properties(question_id)
    assert isinstance(properties, dict)
    assert 'qid' in properties
    assert 'question' in properties
    assert 'type' in properties
    
    # Získání specifických vlastností
    specific_properties = client.get_question_properties(
        question_id,
        ['question', 'type', 'mandatory']
    )
    assert isinstance(specific_properties, dict)
    assert len(specific_properties) == 3
    assert 'question' in specific_properties
    assert 'type' in specific_properties
    assert 'mandatory' in specific_properties

def test_download_survey_data(client):
    """Test stažení dat z reálného průzkumu"""
    survey_id = "214947"
    
    # Stažení struktury
    lss_path = client.get_survey_structure(survey_id)
    assert os.path.exists(lss_path)
    assert lss_path.endswith("structure.lss")
    assert os.path.getsize(lss_path) > 0
    
    # Stažení odpovědí
    csv_path = client.get_responses(survey_id)
    assert os.path.exists(csv_path)
    assert csv_path.endswith("responses.csv")
    assert os.path.getsize(csv_path) > 0

def test_download_with_filters(client):
    """Test stažení dat s filtry"""
    survey_id = "214947"
    
    # Stažení pouze dokončených odpovědí
    csv_path = client.get_responses(
        survey_id,
        completed_only=True,
        start_date=datetime(2024, 1, 1),
        end_date=datetime(2024, 12, 31)
    )
    assert os.path.exists(csv_path)
    assert os.path.getsize(csv_path) > 0

def test_session_management(client):
    """Test správy session"""
    # Získání session key
    key = client.get_session_key()
    assert key is not None
    
    # Opětovné použití stejného klíče
    assert client.get_session_key() == key
    
    # Uvolnění session
    client.release_session()
    assert client.session_key is None
    
    # Nová session by měla mít jiný klíč
    new_key = client.get_session_key()
    assert new_key is not None
    assert new_key != key
