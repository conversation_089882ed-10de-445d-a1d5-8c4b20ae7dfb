#!/usr/bin/env python3
"""
Test řazení <PERSON> (kategorií) podle hodnot v Datawrapper grafech
"""

import sys
import os
import json
import tempfile

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_datawrapper_sort_metadata():
    """Test metadat pro řazení v Datawrapper"""
    print("🧪 Test Datawrapper sort metadat...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        # Simulace dat s různými hodnotami
        test_chart_data = [
            {
                'code': 'TEST001',
                'name': 'Test Array Question',
                'type': 'array',
                'data': [
                    {
                        'subquestion': 'Category A',
                        'responses': {'Ano': 10, 'Ne': 5}  # Celkem 15
                    },
                    {
                        'subquestion': 'Category B', 
                        'responses': {'Ano': 25, 'Ne': 2}  # Celkem 27 - nejv<PERSON><PERSON>
                    },
                    {
                        'subquestion': 'Category C',
                        'responses': {'Ano': 8, 'Ne': 12}  # Celkem 20
                    }
                ]
            }
        ]
        
        generator = EnhancedChartGenerator()
        
        # Simulace vytvoření dat pro Datawrapper
        df_data = []
        categories = []
        
        for item in test_chart_data[0]['data']:
            category = item['subquestion']
            categories.append(category)
            
            row = {'Kategorie': category}
            for response, count in item['responses'].items():
                row[response] = count
            df_data.append(row)
        
        print(f"✅ Testovací data:")
        for row in df_data:
            total = sum(v for k, v in row.items() if k != 'Kategorie')
            print(f"   {row['Kategorie']}: {total} (Ano: {row.get('Ano', 0)}, Ne: {row.get('Ne', 0)})")
        
        # Očekávané pořadí podle celkových hodnot (sestupně)
        expected_order = ['Category B', 'Category C', 'Category A']  # 27, 20, 15
        
        print(f"✅ Očekávané pořadí: {expected_order}")
        
        # Test, že metadata obsahují správné parametry
        value_columns = ['Ano', 'Ne']
        
        metadata = {
            'visualize': {
                'resort-bars': True,
                'sort-asc': False,
                'sort-by': value_columns[0] if value_columns else None,  # Řazení podle prvního sloupce
            }
        }
        
        print(f"✅ Metadata pro řazení:")
        print(f"   resort-bars: {metadata['visualize']['resort-bars']}")
        print(f"   sort-asc: {metadata['visualize']['sort-asc']}")
        print(f"   sort-by: {metadata['visualize']['sort-by']}")
        
        if metadata['visualize']['sort-by'] == 'Ano':
            print("✅ Řazení podle prvního sloupce (Ano) je nastaveno")
            return True
        else:
            print("❌ Řazení podle prvního sloupce není správně nastaveno")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        return False

def test_real_data_sorting():
    """Test řazení na skutečných datech"""
    print("\n🧪 Test řazení na skutečných datech...")
    
    chart_data_path = "src/data/827822/chart_data.json"
    
    if not os.path.exists(chart_data_path):
        print("⚠️  chart_data.json neexistuje")
        return True
    
    try:
        with open(chart_data_path, 'r', encoding='utf-8') as f:
            chart_data = json.load(f)
        
        # Najdeme array otázku pro test
        test_question = None
        for item in chart_data:
            if item.get('type') == 'array' and item.get('code') == 'G6Q00001':
                test_question = item
                break
        
        if not test_question:
            print("⚠️  Testovací array otázka nenalezena")
            return True
        
        print(f"✅ Testovací otázka: {test_question['name'][:50]}...")
        
        # Spočítáme celkové hodnoty pro každou subotázku
        subquestion_totals = []
        for subq_data in test_question['data']:
            subquestion = subq_data['subquestion']
            responses = subq_data['responses']
            total = sum(responses.values())
            
            subquestion_totals.append({
                'subquestion': subquestion[:30] + '...',
                'total': total,
                'responses': responses
            })
        
        # Seřadíme podle celkových hodnot (sestupně)
        sorted_subquestions = sorted(subquestion_totals, key=lambda x: x['total'], reverse=True)
        
        print(f"✅ Pořadí podle celkových hodnot (sestupně):")
        for i, subq in enumerate(sorted_subquestions[:5]):  # Top 5
            print(f"   {i+1}. {subq['subquestion']} (celkem: {subq['total']})")
        
        # Test, že první má nejvíc
        if len(sorted_subquestions) >= 2:
            first_total = sorted_subquestions[0]['total']
            second_total = sorted_subquestions[1]['total']
            
            if first_total >= second_total:
                print("✅ Řazení podle celkových hodnot funguje")
                return True
            else:
                print("❌ Řazení podle celkových hodnot nefunguje")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testu skutečných dat: {str(e)}")
        return False

def test_datawrapper_api_parameters():
    """Test parametrů Datawrapper API pro řazení"""
    print("\n🧪 Test Datawrapper API parametrů...")
    
    print("✅ Potřebné parametry pro řazení řádků:")
    print("   • 'resort-bars': true - zapne řazení řádků")
    print("   • 'sort-asc': false - sestupné řazení (největší první)")
    print("   • 'sort-by': 'column_name' - sloupec pro řazení")
    
    print("\n✅ Problém v původním kódu:")
    print("   • 'resort-bars': true ✅ (bylo)")
    print("   • 'sort-asc': false ✅ (bylo)")
    print("   • 'sort-by': undefined ❌ (chybělo!)")
    
    print("\n✅ Oprava:")
    print("   • 'sort-by': value_columns[0] ✅ (přidáno)")
    print("   • Řazení podle prvního sloupce (např. 'Ano', 'rozhodně ano')")
    
    print("\n🎯 Očekávaný výsledek:")
    print("   • Řádky seřazené podle hodnot prvního sloupce")
    print("   • Nejvyšší hodnoty nahoře")
    print("   • Nejnižší hodnoty dole")
    
    return True

def test_workflow():
    """Test workflow pro ověření opravy"""
    print("\n🧪 Test workflow...")
    
    print("✅ Kroky pro ověření opravy:")
    print("   1. Spusť Menu 8 (generování grafů s opraveným řazením)")
    print("   2. Zkontroluj graf G6Q00001 nebo podobný array graf")
    print("   3. Ověř, že řádky jsou seřazené podle hodnot")
    print("   4. Nejvyšší celkové hodnoty by měly být nahoře")
    
    print("\n✅ Testovací příklad:")
    print("   Pokud máš kategorie:")
    print("   • Category A: Ano=10, Ne=5 (celkem 15)")
    print("   • Category B: Ano=25, Ne=2 (celkem 27)")
    print("   • Category C: Ano=8, Ne=12 (celkem 20)")
    print("   ")
    print("   Správné pořadí v grafu:")
    print("   1. Category B (27) ← nejvíc")
    print("   2. Category C (20)")
    print("   3. Category A (15) ← nejméně")
    
    print("\n🔧 Pokud řazení nefunguje:")
    print("   • Zkontroluj metadata v Datawrapper")
    print("   • Možná je potřeba jiný parametr než 'sort-by'")
    print("   • Zkus 'sort-column': 0 (index sloupce)")
    
    return True

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test řazení řádků podle hodnot v Datawrapper grafech")
    print("=" * 65)
    
    tests = [
        test_datawrapper_sort_metadata,
        test_real_data_sorting,
        test_datawrapper_api_parameters,
        test_workflow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 65)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Test řazení řádků je připraven!")
        print("\n📋 Implementováno:")
        print("   • Přidán parametr 'sort-by' do Datawrapper metadat")
        print("   • Řazení podle prvního sloupce")
        print("   • Sestupné řazení (největší hodnoty nahoře)")
        print("\n🎯 Nyní:")
        print("   1. Spusť Menu 8 (grafy s opraveným řazením)")
        print("   2. Zkontroluj, že řádky jsou správně seřazené")
        print("   3. Pokud nefunguje, možná je potřeba jiný API parametr")
        return True
    else:
        print("❌ Test řazení řádků má problémy")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
