#!/usr/bin/env python3
"""
Test českého jazyka v Datawrapper grafech
"""

import sys
import os
import json

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_chart():
    """Vytvoří testovací graf pro ověření českého jazyka"""
    print("🧪 Vytváření testovacího grafu pro ověření českého jazyka...")
    
    try:
        from datawrapper_client import DatawrapperClient
        
        # Inicializace klienta
        client = DatawrapperClient()
        
        # Testovací data
        test_data = [
            {"Kategorie": "Ano", "Počet": 25},
            {"Kategorie": "Ne", "Počet": 15},
            {"Kategorie": "Nevím", "Počet": 10}
        ]
        
        print("✅ Vytvářím testovací graf s českým jazykem...")
        
        # Vytvoření grafu s explicitním českým jazykem
        chart = client.create_chart(
            title="Testovací graf - český jazyk",
            chart_type="d3-bars",
            description="Test zobrazení českého jazyka v patičce a formátování",
            data_source="LimeSurvey Test",
            data_source_link="https://example.com",
            byline="Test autor",
            language="cs-CZ"  # Explicitně český jazyk
        )
        
        if not chart:
            print("❌ Nepodařilo se vytvořit graf")
            return None
        
        chart_id = chart['id']
        print(f"✅ Graf vytvořen s ID: {chart_id}")
        
        # Nahrání dat
        if client.update_chart_data(chart_id, test_data):
            print("✅ Data nahrána")
        else:
            print("❌ Nepodařilo se nahrát data")
            return None
        
        # Publikování grafu
        if client.publish_chart(chart_id):
            print("✅ Graf publikován")
            
            # Získání URL grafu
            chart_info = client.get_chart(chart_id)
            if chart_info and 'publicUrl' in chart_info:
                chart_url = chart_info['publicUrl']
                print(f"✅ URL grafu: {chart_url}")
                
                print("\n" + "="*60)
                print("🎯 TESTOVACÍ GRAF VYTVOŘEN!")
                print("="*60)
                print(f"ID grafu: {chart_id}")
                print(f"URL: {chart_url}")
                print("\n📋 Co kontrolovat:")
                print("1. Otevřete URL v prohlížeči")
                print("2. Zkontrolujte patičku grafu:")
                print("   - Měla by být v češtině")
                print("   - 'Zdroj: LimeSurvey Test' (ne 'Source:')")
                print("   - České formátování čísel")
                print("3. Zkontrolujte popisky os a legendu")
                print("\n🔍 Pokud je patička anglicky, jazyk se nepromítá správně!")
                print("="*60)
                
                return {
                    'chart_id': chart_id,
                    'url': chart_url,
                    'language': 'cs-CZ'
                }
            else:
                print("❌ Nepodařilo se získat URL grafu")
                return None
        else:
            print("❌ Nepodařilo se publikovat graf")
            return None
        
    except Exception as e:
        print(f"❌ Chyba při vytváření testovacího grafu: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def create_comparison_chart():
    """Vytvoří srovnávací graf s anglickým jazykem"""
    print("\n🧪 Vytváření srovnávacího grafu s anglickým jazykem...")
    
    try:
        from datawrapper_client import DatawrapperClient
        
        client = DatawrapperClient()
        
        # Stejná data, ale anglický jazyk
        test_data = [
            {"Category": "Yes", "Count": 25},
            {"Category": "No", "Count": 15},
            {"Category": "Don't know", "Count": 10}
        ]
        
        # Vytvoření grafu s anglickým jazykem
        chart = client.create_chart(
            title="Test chart - English language",
            chart_type="d3-bars",
            description="Test of English language in footer and formatting",
            data_source="LimeSurvey Test",
            data_source_link="https://example.com",
            byline="Test author",
            language="en-US"  # Explicitně anglický jazyk
        )
        
        if chart:
            chart_id = chart['id']
            print(f"✅ Anglický graf vytvořen s ID: {chart_id}")
            
            if client.update_chart_data(chart_id, test_data):
                if client.publish_chart(chart_id):
                    chart_info = client.get_chart(chart_id)
                    if chart_info and 'publicUrl' in chart_info:
                        chart_url = chart_info['publicUrl']
                        print(f"✅ Anglický graf URL: {chart_url}")
                        
                        print("\n📊 SROVNÁNÍ:")
                        print("Český graf - patička by měla být: 'Zdroj: LimeSurvey Test'")
                        print("Anglický graf - patička by měla být: 'Source: LimeSurvey Test'")
                        
                        return {
                            'chart_id': chart_id,
                            'url': chart_url,
                            'language': 'en-US'
                        }
        
        return None
        
    except Exception as e:
        print(f"❌ Chyba při vytváření anglického grafu: {str(e)}")
        return None

def test_enhanced_chart_generator_language():
    """Test jazyka v EnhancedChartGenerator"""
    print("\n🧪 Test jazyka v EnhancedChartGenerator...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        # Test s českým jazykem
        generator_cs = EnhancedChartGenerator(
            survey_title="Test průzkum",
            data_source="LimeSurvey",
            language="cs-CZ"
        )
        
        print(f"✅ EnhancedChartGenerator vytvořen s jazykem: {generator_cs.language}")
        
        # Test s anglickým jazykem
        generator_en = EnhancedChartGenerator(
            survey_title="Test survey",
            data_source="LimeSurvey", 
            language="en-US"
        )
        
        print(f"✅ EnhancedChartGenerator vytvořen s jazykem: {generator_en.language}")
        
        if generator_cs.language == "cs-CZ" and generator_en.language == "en-US":
            print("✅ Jazyk se správně nastavuje v EnhancedChartGenerator")
            return True
        else:
            print("❌ Jazyk se nenastavuje správně")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu EnhancedChartGenerator: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test českého jazyka v Datawrapper grafech")
    print("=" * 60)
    
    # Test EnhancedChartGenerator
    if not test_enhanced_chart_generator_language():
        print("❌ EnhancedChartGenerator nefunguje správně")
        return False
    
    # Vytvoření testovacích grafů
    czech_chart = create_test_chart()
    english_chart = create_comparison_chart()
    
    if czech_chart and english_chart:
        print("\n" + "="*60)
        print("🎯 TESTOVACÍ GRAFY VYTVOŘENY!")
        print("="*60)
        print(f"🇨🇿 Český graf: {czech_chart['url']}")
        print(f"🇺🇸 Anglický graf: {english_chart['url']}")
        print("\n📋 ÚKOL PRO UŽIVATELE:")
        print("1. Otevřete oba grafy v prohlížeči")
        print("2. Porovnejte patičky:")
        print("   - Český: 'Zdroj: LimeSurvey Test'")
        print("   - Anglický: 'Source: LimeSurvey Test'")
        print("3. Zkontrolujte formátování čísel")
        print("4. Ohlaste, zda je rozdíl viditelný!")
        print("="*60)
        return True
    else:
        print("❌ Nepodařilo se vytvořit testovací grafy")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Testovací grafy vytvořeny - čekám na zpětnou vazbu o jazyce!")
    else:
        print("\n❌ Test selhal")
    sys.exit(0 if success else 1)
