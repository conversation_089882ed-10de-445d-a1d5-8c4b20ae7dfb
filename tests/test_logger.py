import os
import json
import shutil
from datetime import datetime
from src.logger import ProcessLogger

def setup_module():
    """Příprava testovacího pro<PERSON>ředí"""
    if os.path.exists("logs"):
        shutil.rmtree("logs")

def test_logger_initialization():
    """Test inicializace loggeru"""
    logger = ProcessLogger("123456")
    assert logger.survey_id == "123456"
    assert os.path.exists("logs")
    assert os.path.exists("logs/survey_123456.json")

def test_step_logging():
    """Test logování kroků"""
    logger = ProcessLogger("123456")
    
    # Test start kroku
    logger.start_step("test_step")
    with open(logger.log_file, 'r') as f:
        state = json.load(f)
        assert state["current_step"]["name"] == "test_step"
    
    # Test dokončení kroku
    logger.complete_step("test_step")
    with open(logger.log_file, 'r') as f:
        state = json.load(f)
        assert len(state["completed_steps"]) == 1
        assert state["completed_steps"][0]["name"] == "test_step"
        assert state["current_step"] is None

def test_error_logging():
    """Test logování chyb"""
    logger = ProcessLogger("123456")
    
    logger.log_error("Test error")
    with open(logger.log_file, 'r') as f:
        state = json.load(f)
        assert len(state["errors"]) == 1
        assert state["errors"][0]["error"] == "Test error"

def test_chart_logging():
    """Test logování grafů"""
    logger = ProcessLogger("123456")
    
    # Test vytvoření grafu
    logger.log_chart_created("Q1", "chart_123")
    with open(logger.log_file, 'r') as f:
        state = json.load(f)
        assert "Q1" in state["charts"]
        assert state["charts"]["Q1"]["chart_id"] == "chart_123"
    
    # Test aktualizace grafu
    logger.log_chart_updated("Q1", "published")
    with open(logger.log_file, 'r') as f:
        state = json.load(f)
        assert state["charts"]["Q1"]["status"] == "published"

def test_process_completion():
    """Test dokončení procesu"""
    logger = ProcessLogger("123456")
    
    logger.complete_process()
    with open(logger.log_file, 'r') as f:
        state = json.load(f)
        assert state["status"] == "completed"
        assert "end_time" in state

def test_resume_capability():
    """Test možnosti pokračování"""
    logger = ProcessLogger("123456")
    
    # Simulace dokončeného kroku
    logger.start_step("step1")
    logger.complete_step("step1")
    
    # Test možnosti pokračování
    assert logger.can_resume_from_step("step1") == True
    assert logger.can_resume_from_step("nonexistent_step") == False

def teardown_module():
    """Úklid po testech"""
    if os.path.exists("logs"):
        shutil.rmtree("logs")
