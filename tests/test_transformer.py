import pytest
import pandas as pd
import os
from unittest.mock import Mock, patch
from src.data_transformer import DataTransformer
from src.limesurvey_client import LimeSurveyClient

@pytest.fixture
def mock_survey_data(tmp_path):
    """Mockovaná testovací data"""
    # Vytvoření mock LSS souboru
    lss_content = """<?xml version="1.0" encoding="UTF-8"?>
<document>
    <LimeSurveyDocType>Survey</LimeSurveyDocType>
    <groups>
        <group id="G1">
            <group_name>Demografie</group_name>
            <questions>
                <question id="Q1" type="single_choice">
                    <title>Q1</title>
                    <question_text>Pohlaví</question_text>
                    <answers>
                        <answer code="1">Mu<PERSON></answer>
                        <answer code="2">Žena</answer>
                    </answers>
                </question>
                <question id="Q2" type="multiple_choice">
                    <title>Q2</title>
                    <question_text>Zájmy</question_text>
                    <answers>
                        <answer code="1">Sport</answer>
                        <answer code="2">Hudba</answer>
                        <answer code="3">Technologie</answer>
                    </answers>
                </question>
                <question id="Q3" type="text">
                    <title>Q3</title>
                    <question_text>Popište svůj den</question_text>
                </question>
                <question id="Q4" type="numeric">
                    <title>Q4</title>
                    <question_text>Věk</question_text>
                </question>
            </questions>
        </group>
    </groups>
</document>"""
    
    lss_path = tmp_path / "survey.lss"
    lss_path.write_text(lss_content)
    
    # Vytvoření mock CSV souboru
    csv_content = """Q1,Q2,Q3,Q4
1,"1;2","Dobrý den",25
2,"2;3","Ráno běh",30
1,"1;3","Práce na počítači",40
"""
    
    csv_path = tmp_path / "responses.csv"
    csv_path.write_text(csv_content)
    
    return str(lss_path), str(csv_path)

def test_load_survey_structure(mock_survey_data):
    """Test načtení struktury průzkumu z LSS"""
    lss_path, _ = mock_survey_data
    transformer = DataTransformer(lss_path=lss_path)
    
    assert len(transformer.structure) == 4
    
    # Kontrola struktury otázky
    for qid, question in transformer.structure.items():
        assert 'type' in question
        assert 'title' in question
        assert 'text' in question
        assert question['type'] in [
            'single_choice',
            'multiple_choice',
            'text',
            'numeric'
        ]

def test_transform_single_choice(mock_survey_data):
    """Test transformace single choice otázky"""
    lss_path, csv_path = mock_survey_data
    transformer = DataTransformer(lss_path, csv_path)
    
    result, chart_type = transformer.transform_data("Q1")
    assert isinstance(result, pd.DataFrame)
    assert 'option' in result.columns
    assert 'count' in result.columns
    assert len(result) == 2
    assert chart_type == "d3-bars"

def test_transform_multiple_choice(mock_survey_data):
    """Test transformace multiple choice otázky"""
    lss_path, csv_path = mock_survey_data
    transformer = DataTransformer(lss_path, csv_path)
    
    result, chart_type = transformer.transform_data("Q2")
    assert isinstance(result, pd.DataFrame)
    assert 'option' in result.columns
    assert 'count' in result.columns
    assert len(result) == 3
    assert chart_type == "d3-bars-stacked"

def test_transform_numeric(mock_survey_data):
    """Test transformace numerické otázky"""
    lss_path, csv_path = mock_survey_data
    transformer = DataTransformer(lss_path, csv_path)
    
    result, chart_type = transformer.transform_data("Q4")
    assert isinstance(result, pd.DataFrame)
    assert 'metric' in result.columns
    assert 'value' in result.columns
    assert len(result) == 4  # průměr, medián, min, max
    assert chart_type == "d3-lines"

def test_invalid_question_id(mock_survey_data):
    """Test chybového stavu pro neexistující otázku"""
    lss_path, csv_path = mock_survey_data
    transformer = DataTransformer(lss_path, csv_path)
    
    with pytest.raises(ValueError):
        transformer.transform_data("neexistující_id")

def test_chart_type_mapping(mock_survey_data):
    """Test mapování typů otázek na typy grafů"""
    lss_path, csv_path = mock_survey_data
    transformer = DataTransformer(lss_path, csv_path)
    
    # Test single_choice
    _, chart_type = transformer.transform_data("Q1")
    assert chart_type == "d3-bars"
    
    # Test multiple_choice
    _, chart_type = transformer.transform_data("Q2")
    assert chart_type == "d3-bars-stacked"
    
    # Test numeric
    _, chart_type = transformer.transform_data("Q4")
    assert chart_type == "d3-lines"
