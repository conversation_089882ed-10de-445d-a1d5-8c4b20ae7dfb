#!/usr/bin/env python3
"""
Test skutečného generování grafu s novými parametry
"""

import os
import json
import pandas as pd
from src.datawrapper_client import DatawrapperClient
from datetime import datetime

def create_test_chart():
    """Vytvoř<PERSON> testovací graf a stáhne ho"""
    print("=== Test skutečného generování grafu ===")
    
    try:
        # Vytvoření klienta
        client = DatawrapperClient()
        print("✓ DatawrapperClient vytvořen")
        
        # Vytvoření testovacích dat
        test_data = pd.DataFrame({
            'Kategorie': ['Velmi spokojen', 'Spokojen', 'Neu<PERSON><PERSON><PERSON><PERSON>', 'Nespokojen', '<PERSON>el<PERSON> nespokojen'],
            'Počet': [45, 32, 15, 6, 2]
        })
        
        print("✓ Testovací data vytvořena:")
        print(test_data)
        
        # Vytvoření grafu
        timestamp = datetime.now().strftime("%H%M%S")
        chart_title = f"Test automatických rozměrů {timestamp}"
        
        chart = client.create_chart(
            title=chart_title,
            chart_type="d3-bars",
            description="Test grafu s automatickými rozměry - bez width/height parametrů"
        )
        
        if not chart:
            print("❌ Nepodařilo se vytvořit graf")
            return None
            
        chart_id = chart['id']
        print(f"✓ Graf vytvořen: {chart_title} (ID: {chart_id})")
        
        # Nahrání dat
        if not client.update_chart_data(chart_id, test_data):
            print("❌ Nepodařilo se nahrát data")
            return None
            
        print("✓ Data nahrána")
        
        # Publikování grafu
        publish_result = client.publish_chart(chart_id)
        if not publish_result:
            print("❌ Nepodařilo se publikovat graf")
            return None
            
        print("✓ Graf publikován")
        print(f"  URL: {publish_result.get('url', 'N/A')}")
        
        # Export s novými parametry (BEZ width!)
        print("\n✓ Export s automatickými rozměry...")
        png_data = client.export_chart(
            chart_id,
            export_format='png',
            # ŽÁDNÝ width parametr!
            border_width=10,
            zoom=2,
            plain=True,
            mode='rgb'
        )
        
        if not png_data:
            print("❌ Nepodařilo se exportovat graf")
            return None
            
        # Uložení PNG
        output_dir = "test_charts"
        os.makedirs(output_dir, exist_ok=True)
        
        png_filename = f"test_chart_{timestamp}.png"
        png_path = os.path.join(output_dir, png_filename)
        
        with open(png_path, 'wb') as f:
            f.write(png_data)
            
        print(f"✅ Graf uložen: {png_path}")
        
        # Informace o souboru
        file_size = len(png_data)
        print(f"  Velikost souboru: {file_size:,} bytů")
        
        # Pokus o zjištění rozměrů (pokud je PIL dostupná)
        try:
            from PIL import Image
            with Image.open(png_path) as img:
                width, height = img.size
                print(f"  Rozměry PNG: {width}×{height}px")
                
                # Analýza rozměrů
                if width == 1200 and height == 800:
                    print("  ⚠️  STÁLE FIXNÍ ROZMĚRY! (1200×800)")
                elif width == 1240 and height == 840:
                    print("  ⚠️  STÁLE FIXNÍ ROZMĚRY! (1240×840)")
                else:
                    print(f"  ✅ AUTOMATICKÉ ROZMĚRY! ({width}×{height})")
                    
        except ImportError:
            print("  (PIL není dostupná pro zjištění rozměrů)")
        except Exception as e:
            print(f"  (Chyba při zjišťování rozměrů: {e})")
        
        return {
            'chart_id': chart_id,
            'png_path': png_path,
            'url': publish_result.get('url')
        }
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    print("Test skutečného generování grafu s automatickými rozměry")
    print("=" * 60)
    
    result = create_test_chart()
    
    if result:
        print("\n" + "=" * 60)
        print("✅ TEST DOKONČEN")
        print(f"\nGraf uložen v: {result['png_path']}")
        print(f"Chart ID: {result['chart_id']}")
        print(f"URL: {result.get('url', 'N/A')}")
        print("\nPodívej se na vygenerovaný PNG a zkontroluj rozměry!")
    else:
        print("\n❌ Test selhal")

if __name__ == "__main__":
    main()