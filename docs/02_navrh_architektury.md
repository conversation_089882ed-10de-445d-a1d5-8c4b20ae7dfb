# Návrh architektury

## Vysokoúrovňový návrh

```mermaid
graph TD
    A[Uživatelské rozhraní] --> B[API Gateway]
    B --> C[LimeSurvey API]
    B --> D[Datawrapper API]
    B --> E[Datový pipeline]
    E --> F[Čištění dat]
    E --> G[Transformace dat]
    E --> H[<PERSON>rování grafů]
    H --> I[Export výsledků]
```

## Hlavní moduly

1. **API Gateway**
   - Zprostředkovává komunikaci s externími API
   - Zajišťuje autentizaci a autorizaci
   - Implementuje cachování

2. **Datový pipeline**
   - Řídí tok dat mezi jednotlivými komponentami
   - Zajišťuje zpracování dat v definovan<PERSON>ch krocích
   - Implementuje chybové stavy a opakování

3. **GUI modul**
   - Zobrazuje uživatelské rozhraní
   - Zajišťuje interakci s uživatelem
   - Zobrazuje výsledky a statistiky

## Da<PERSON><PERSON> model

```mermaid
erDiagram
    SURVEY ||--o{ QUESTION : contains
    SURVEY {
        int id
        string title
        datetime created_at
        int response_count
    }
    QUESTION ||--o{ RESPONSE : has
    QUESTION {
        int id
        string text
        string type
    }
    RESPONSE {
        int id
        int question_id
        string value
        datetime created_at
    }
```

## Technologický zásobník

- **Programovací jazyk:** Python 3.10+
- **GUI framework:** PyQt5
- **Datové zpracování:** Pandas, NumPy
- **API komunikace:** Requests, aiohttp
- **Logování:** Loguru
- **Testování:** pytest
- **CI/CD:** GitHub Actions
