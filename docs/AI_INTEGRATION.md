# AI Integration v LimWrapp

Tento dokument popisuje integraci AI komponent př<PERSON><PERSON><PERSON><PERSON><PERSON> z existujících projektů do LimWrapp systému.

## 🎯 Přehled komponent

### Převzaté komponenty

1. **Enhanced OpenAI Client** (`src/ai/enhanced_openai_client.py`)
   - Rate limiting s token bucket algoritmem
   - Redis/in-memory caching
   - Fallback mechanismus mezi modely
   - Detailní cost tracking a statistiky
   - Retry logika s exponential backoff

2. **WordCloud Generator** (`src/ai/wordcloud_generator.py`)
   - AI-enhanced analýza textu
   - Tři typy vizualizací (WCS, WCT, WCH)
   - Různé vý<PERSON> formáty (PNG, SVG, JPG, WebP)
   - Barevná schémata a customizace

3. **Parameter Manager** (`src/ai/parameter_manager.py`)
   - Správa parametrů pro analýzy a vizualizace
   - Validace a type checking
   - Historie změn s undo funkcionalitou
   - Uložení/načtení konfigurací

4. **Prompt Manager** (`src/ai/prompt_manager.py`)
   - Správa AI promptů a šablon
   - YAML konfigurace promptů
   - Dynamické formátování s parametry
   - Kategorizace a validace

5. **Scenario Generator** (`src/ai/scenario_generator.py`)
   - Generování analytických scénářů
   - AI-powered návrhy analýz
   - Template-based scénáře
   - Validace a optimalizace

6. **AI Manager** (`src/ai/ai_manager.py`)
   - Centrální koordinátor všech AI komponent
   - Jednotné API pro AI funkce
   - Usage tracking a statistiky

## 🚀 Instalace a konfigurace

### 1. Instalace závislostí

```bash
pip install -r requirements_ai.txt
```

### 2. Konfigurace prostředí

Vytvořte `.env` soubor s následujícími proměnnými:

```bash
# OpenAI API
OPENAI_API_KEY=sk-proj-your-api-key-here
OPENAI_ORG_ID=your-org-id-here

# Redis (volitelné)
REDIS_URL=redis://localhost:6379/0

# Cost Control
DAILY_COST_LIMIT=50.0
MONTHLY_COST_LIMIT=500.0
```

### 3. Inicializace v aplikaci

```python
from src.ai import AIManager
from src.ai.integration import get_ai_integration

# Inicializace AI manageru
ai_manager = AIManager(
    api_key="your-openai-key",
    enable_cache=True
)

# Nebo použití integrace s existujícím systémem
ai_integration = get_ai_integration(config_manager)
```

## 📊 Použití komponent

### WordCloud generování

```python
# Základní použití
result = ai_manager.generate_wordcloud(
    text="Text k analýze",
    use_ai=True,
    max_words=200,
    language='cs'
)

# Uložení výsledku
if result['success']:
    with open('wordcloud.png', 'wb') as f:
        f.write(result['image_data'])
```

### Generování analytických scénářů

```python
# Charakteristiky průzkumu
survey_data = {
    'total_responses': 150,
    'total_questions': 25,
    'question_types': {'likert': 10, 'text': 5, 'choice': 10},
    'has_likert_scales': True,
    'has_text_responses': True,
    'survey_topic': 'spokojenost zákazníků'
}

# Generování scénářů
scenarios = ai_manager.generate_analysis_scenarios(
    survey_data=survey_data,
    user_objectives=['analýza spokojenosti', 'segmentace zákazníků']
)
```

### Správa parametrů

```python
# Nastavení parametrů
ai_manager.parameter_manager.set_parameter('viz_font_size', 14)
ai_manager.parameter_manager.set_parameters({
    'viz_color_scheme': 'blue',
    'wordcloud_max_words': 300
})

# Uložení konfigurace
ai_manager.parameter_manager.save_configuration(
    'my_config', 
    'Moje vlastní konfigurace'
)
```

### AI analýza textu

```python
# Analýza textu
result = ai_manager.analyze_text_with_ai(
    text="Text k analýze",
    analysis_type='sentiment',
    language='cs'
)

print(f"Analýza: {result['analysis']}")
print(f"Náklady: ${result['cost']:.4f}")
```

## 🔧 Integrace s existujícím systémem

### Menu rozšíření

AI funkce lze integrovat do existujících menu:

```python
# V menu_functions.py
from src.ai.integration import get_ai_integration, is_ai_available

def menu_ai_wordcloud():
    """Menu 10: AI WordCloud generování"""
    if not is_ai_available():
        print("❌ AI funkce nejsou dostupné")
        return
    
    ai = get_ai_integration()
    
    # Získání survey ID
    survey_id = input("Zadejte Survey ID: ")
    
    # Generování wordcloud
    result = ai.generate_wordcloud_for_survey(survey_id, use_ai=True)
    
    if result['success']:
        print("✅ WordCloud úspěšně vygenerován")
        print(f"📊 Počet slov: {result['metadata']['unique_words']}")
    else:
        print(f"❌ Chyba: {result['error']}")
```

### Datawrapper integrace

```python
def enhance_chart_with_ai(chart_data, chart_type):
    """Vylepšení grafu pomocí AI návrhů"""
    if not is_ai_available():
        return chart_data
    
    ai = get_ai_integration()
    
    # Získání návrhů parametrů
    suggestions = ai.get_parameter_suggestions(
        analysis_type=chart_type,
        data_characteristics={
            'record_count': len(chart_data),
            'data_type': 'survey_responses'
        }
    )
    
    # Aplikace návrhů
    if suggestions['success']:
        # Parsování AI návrhů a aplikace na chart_data
        pass
    
    return chart_data
```

## 📈 Monitoring a statistiky

### Usage tracking

```python
# Získání statistik
stats = ai_manager.get_usage_statistics()

print(f"Celkové požadavky: {stats['openai']['total_requests']}")
print(f"Celkové náklady: ${stats['openai']['total_cost']}")
print(f"Cache hit rate: {stats['openai']['cache_hit_rate']}%")
```

### Cost control

```python
# Kontrola nákladů před voláním
current_cost = ai_manager.get_usage_statistics()['openai']['total_cost']
if current_cost > DAILY_COST_LIMIT:
    print("⚠️ Denní limit nákladů překročen")
    return
```

## 🛠️ Rozšíření a customizace

### Vlastní prompty

Vytvořte YAML soubor v `src/ai/prompts/`:

```yaml
custom_analysis:
  system_prompt: |
    Jsi expert na vlastní typ analýzy...
  user_prompt: |
    Analyzuj data: {data}
    Parametry: {parameters}
  description: "Vlastní analýza"
  category: "custom"
  parameters:
    data: "Data k analýze"
    parameters: "Parametry analýzy"
```

### Vlastní scenario templates

```python
# Přidání vlastního template
ai_manager.scenario_generator.add_custom_template(
    'custom_analysis',
    {
        'title': 'Vlastní analýza',
        'description': 'Popis vlastní analýzy',
        'complexity': 'intermediate',
        'min_responses': 50
    }
)
```

## 🔒 Bezpečnost a best practices

1. **API klíče**: Nikdy neukládejte API klíče v kódu
2. **Cost limits**: Nastavte denní a měsíční limity
3. **Rate limiting**: Respektujte API limity
4. **Caching**: Používejte cache pro opakované dotazy
5. **Error handling**: Vždy ošetřete chyby AI volání
6. **Logging**: Logujte všechna AI volání pro audit

## 🐛 Troubleshooting

### Časté problémy

1. **"OpenAI API key is required"**
   - Zkontrolujte nastavení OPENAI_API_KEY v .env

2. **"Redis cache disabled"**
   - Redis není dostupný, používá se in-memory cache

3. **"Rate limit hit"**
   - Počkejte nebo snižte frekvenci volání

4. **"All models failed"**
   - Zkontrolujte API klíč a dostupnost modelů

### Debug mode

```python
import logging
logging.getLogger('limwrapp.ai').setLevel(logging.DEBUG)
```

## 📚 Reference

- [OpenAI API dokumentace](https://platform.openai.com/docs)
- [WordCloud dokumentace](https://amueller.github.io/word_cloud/)
- [Redis dokumentace](https://redis.io/documentation)

## 🔄 Aktualizace

Pro aktualizaci AI komponent:

1. Zkontrolujte nové verze v requirements_ai.txt
2. Otestujte kompatibilitu s existujícím kódem
3. Aktualizujte konfiguraci podle potřeby
4. Spusťte testy před nasazením
