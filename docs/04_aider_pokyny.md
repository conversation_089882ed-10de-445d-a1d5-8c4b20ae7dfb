# Pr<PERSON><PERSON> s aider

## <PERSON><PERSON><PERSON><PERSON><PERSON> pracovní postup

1. Na<PERSON><PERSON><PERSON> relevant<PERSON><PERSON><PERSON> so<PERSON>
   ```bash
   aider --file src/main.py --file src/limesurvey_client.py
   ```

2. Implementace nové funkcionality
   ```bash
   /add Implementuj metodu pro získání seznamu průzkumů
   ```

3. Úprava existují<PERSON><PERSON><PERSON> k<PERSON>
   ```bash
   /edit src/limesurvey_client.py
   ```

4. Spuštění testů
   ```bash
   /run pytest tests/test_limesurvey.py
   ```

## Doporučené příkazy

- Přidání nového souboru:
  ```bash
  /add src/new_module.py
  ```

- Úprava více souborů:
  ```bash
  /edit src/module1.py src/module2.py
  ```

- Spuštění testů s výstupem:
  ```bash
  /run pytest -v tests/
  ```

- Generování dokumentace:
  ```bash
  /add docs/new_feature.md
  ```

## Integrace s dokumentací

1. Při implementaci nové funkcionality odkazujte na příslušný úkol v 03_plan_vyvoje.md
2. Po dokončení úkolu aktualizujte stav v 05_stav_vyvoje.md
3. Při změnách v architektuře upravte 02_navrh_architektury.md
4. Nové požadavky zaznamenejte v 01_poadavky.md
