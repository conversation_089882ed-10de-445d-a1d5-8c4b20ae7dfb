# Opravy pro Array <PERSON> (Otázky typu pole se škálou)

## 🎯 Identifikované problémy

### 1. Nesprávný formát dat pro array otázky
- **Problém**: Data se posílala pod sebou místo jako kont<PERSON> ta<PERSON>
- **Důsledek**: Stack sloupcové grafy se nevytvářely správně, položky s různou hodnotou škály se opakovaly pod sebou

### 2. Chybějící řazení hodnot v grafech
- **Problém**: Grafy se nerovnaly podle počtů a byly "rozházené"
- **Důsledek**: Špatná vizualizace dat, obtížná interpretace výsledků

## 🔧 Implementované opravy

### 1. Přidána funkce pro aktualizaci metadat grafů
**Soubor**: `src/datawrapper_client.py`

```python
def update_chart_metadata(self, chart_id: str, metadata: dict) -> bool:
    """Aktualizace metadat grafu"""
```

- Umožňuje nastavit řazení hodnot a další metadata po vytvoření grafu
- Používá PATCH request na `/charts/{chart_id}` endpoint

### 2. Opraveno formátování dat pro array otázky
**Soubor**: `src/enhanced_chart_generator.py`

**Před opravou**:
```python
# Data se posílala pod sebou
for item in chart_data:
    dw_data.append({
        'label': item['label'],
        'value': item['value']
    })
```

**Po opravě**:
```python
# Data se posílají jako kontingenční tabulka
for item in chart_data:
    subquestion = item['subquestion']
    responses = item['responses']
    
    row = {'Subotázka': subquestion}
    for response_col in response_columns:
        row[str(response_col)] = responses.get(response_col, 0)
    
    dw_data.append(row)
```

### 3. Přidáno řazení hodnot podle počtu odpovědí
**Soubor**: `src/enhanced_chart_generator.py`

```python
def _prepare_chart_metadata_for_array(self, question_type: str, datawrapper_type: str, dw_data: list):
    """Připraví metadata specificky pro array grafy s řazením hodnot"""
```

- Vypočítává celkové hodnoty pro každou subotázku
- Seřazuje subotázky podle celkového počtu odpovědí (sestupně)
- Nastavuje metadata pro custom řazení v Datawrapper

### 4. Integrace do procesu generování grafů

Opravený workflow:
1. Načtení array dat z `chart_data.json`
2. **NOVÉ**: Formátování jako kontingenční tabulka s řádky pro subotázky
3. Vytvoření grafu v Datawrapper
4. Nahrání dat ve správném formátu
5. **NOVÉ**: Nastavení metadat pro řazení hodnot
6. Publikování grafu

## 📊 Výsledek oprav

### Před opravou:
- Array data: `[{label: "Subotázka 1 - Odpověď 1", value: 5}, {label: "Subotázka 1 - Odpověď 2", value: 3}, ...]`
- Výsledek: Položky se opakovaly pod sebou, stack grafy nefungovaly

### Po opravě:
- Array data: 
```
Subotázka          | 1 | 2 | 3 | 4 | 5
Kvalita služeb     | 5 |12 |25 |18 | 8
Rychlost obsluhy   | 3 | 8 |20 |22 |15
Cena služeb        | 8 |15 |18 |12 | 5
```
- Výsledek: Správné kontingenční tabulky, funkční stack sloupcové grafy, řazení podle celkových hodnot

## 🧪 Testování

Vytvořen test `test_array_chart_fixes.py` který ověřuje:
- ✅ Správné formátování array dat jako kontingenční tabulky
- ✅ Funkční přípravu metadat pro řazení
- ✅ Integrační test celého procesu

**Výsledek testů**: 3/3 prošlo ✅

## 🚀 Použití

Opravy jsou automaticky aktivní při použití menu 8 (Rozšířené generování grafů).

Pro array otázky se nyní:
1. Automaticky vytváří kontingenční tabulka
2. Nastavuje řazení podle celkových hodnot
3. Generuje správné stack sloupcové grafy

## 📝 Poznámky

- Opravy jsou zpětně kompatibilní
- Fungují pouze pro otázky typu `array` v `chart_data.json`
- Řazení se aplikuje pouze na stack sloupcové grafy (`d3-bars-stacked`)
- Metadata se nastavují automaticky, není potřeba ruční konfigurace
