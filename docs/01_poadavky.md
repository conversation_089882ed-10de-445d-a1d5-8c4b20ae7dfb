# Funkční a nefunkční požadavky

## <PERSON>čn<PERSON> požadavky

### FR-001: <PERSON><PERSON><PERSON><PERSON><PERSON> průzkumu [částe<PERSON>n<PERSON> implementováno]
- [x] Uživatel může vybrat průzkum z dostupných v LimeSurvey
- [ ] Filtrování průzkumů podle data, počtu odpovědí a dalších parametrů
- [x] Zobrazení základních metadat o vybraném průzkumu

### FR-002: Import a zpracování dat [částečně implementováno]
- [x] <PERSON><PERSON><PERSON> stažení CSV dat z vybraného průzkumu
- [x] Či<PERSON><PERSON><PERSON><PERSON><PERSON> dat (odstranění duplicit, oprava formátování)
- [ ] Anonymizace citlivých dat (volitelné)
- [x] Validace datové integrity
- [ ] Logování chyb a problémů v datech
- [x] Načít<PERSON><PERSON> dat do tabulky
- [x] Filtrov<PERSON><PERSON> dat
- [x] Stránkování
- [x] Řazení
- [x] Ukládání dat

### FR-003: Mapování struktury průzkumu [částečně implementováno]
- [x] Automatické načtení struktury průzkumu pomocí API
- [ ] Spárování dat s odpovídajícími otázkami
- [ ] Možnost manuálního mapování v GUI
- [x] Zobrazení struktury ve stromové hierarchii
- [x] Získávání detailů o otázkách
- [x] Vyhledávání podle ID
- [x] Získávání typu otázky
- [x] Získávání dostupných odpovědí
- [x] Získávání podotázek
- [x] Získávání názvu skupiny

### FR-004: Transformace dat [částečně implementováno]
- [x] Převedení dat do long formátu
- [x] Generování mapování otázek
- [x] Validace CSV struktury
- [x] Dekomprese CSV
- [ ] Agregace dat podle typu otázky
- [ ] Přejmenování sloupců pro konzistentní použití v grafech

### FR-005: Generování grafů [částečně implementováno]
- [x] Automatické přiřazení vhodného typu grafu
- [x] Generování grafů pro jednotlivé otázky
- [x] Export do PNG
- [x] Publikování grafů na Datawrapper
- [x] Správa složek na serveru
- [x] Nastavení metadat grafu (popisky, barvy, velikost, lokalizace)
- [x] Validace metadat podle schématu
- [x] Výchozí metadata pro různé typy grafů
- [x] GUI pro správu metadat (záložky, různé typy vstupů, výběr fontu a barev)
- [x] Export metadat do Datawrapper API
- [ ] Ukládání metadat do lokálního souboru
- [ ] Import metadat z existujících grafů
- [ ] Porovnání metadat mezi verzemi
- [x] Integrace s Datawrapper API
- [ ] Možnost vytvoření více grafů pro jednu otázku

### FR-006: Export výsledků [částečně implementováno]
- [x] Export grafů do PNG
- [ ] Export grafů do PDF
- [ ] Generování interaktivních odkazů
- [ ] Uložení datové struktury s mapováním otázek

## Nefunkční požadavky

### NF-001: Výkon
- Zpracování datových sad do 1 minuty pro 100 000 záznamů
- Asynchronní zpracování velkých datových sad

### NF-002: Bezpečnost
- Možnost anonymizace citlivých dat
- Šifrované ukládání přihlašovacích údajů k API

### NF-003: Škálovatelnost
- Podpora více souběžných uživatelů
- Možnost horizontálního škálování

### NF-004: Spolehlivost
- 99.9% dostupnost služby
- Automatické zálohování dat

### NF-005: Použitelnost
- Intuitivní uživatelské rozhraní
- Minimální nutnost čtení dokumentace
- Kontextová nápověda přímo v aplikaci
