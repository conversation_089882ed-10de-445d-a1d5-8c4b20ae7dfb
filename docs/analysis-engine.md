# Analysis Engine

Inteligentní systém pro automatické rozhodování o typech analýz a vizualizací na základě struktury LimeSurvey otázek.

## 📋 Přehled

Analysis Engine je modulární systém integrovaný z nedokončeného projektu `limesurvey-structure-metadata`, který poskytuje:

- **<PERSON><PERSON><PERSON> r<PERSON>** typů LimeSurvey otázek
- **Inteligentní doporučování** vhodných analýz
- **Optimální mapování** na vizualizace (Datawrapper + externí generátory)
- **Prioritizace implementace** podle složitosti a dostupnosti

## 🏗️ Architektura

### Hlavní komponenty:

1. **MetadataLoader** - Načítá metadata o typech otázek, analýz a vizualizací
2. **QuestionAnalyzer** - Analyzuje LimeSurvey otázky a mapuje je na typy
3. **AnalysisRecommender** - Doporučuje vhodné analýzy pro průzkum
4. **VisualizationMapper** - Mapuje analýzy na vizualizace

### Datové struktury:

- **28 typů otázek** (L, 5, A, B, C, D, E, F, G, H, I, K, N, O, P, Q, R, S, T, U, X, Y, !, :, ;, |, 1)
- **20 typů analýz** (question + section level)
- **66 typů vizualizací** s mapováním na Datawrapper typy
- **Komplexní mapování** otázka → analýza → vizualizace

## 🚀 Použití

### Základní workflow:

```python
from analysis_engine import MetadataLoader, QuestionAnalyzer, AnalysisRecommender, VisualizationMapper
from analysis_engine.question_analyzer import LimeSurveyQuestion

# 1. Načtení metadata
loader = MetadataLoader()
loader.load_all_metadata()

# 2. Analýza otázek
analyzer = QuestionAnalyzer(loader)
questions = [
    LimeSurveyQuestion(qid="Q1", question_code="satisfaction", question_text="Spokojenost", question_type="5"),
    LimeSurveyQuestion(qid="Q2", question_code="comments", question_text="Komentáře", question_type="T")
]
analyzed_questions = analyzer.analyze_survey_questions(questions)

# 3. Doporučení analýz
recommender = AnalysisRecommender(loader)
analysis_plan = recommender.recommend_analyses(analyzed_questions)

# 4. Mapování vizualizací
mapper = VisualizationMapper(loader)
chart_plan = mapper.map_analyses_to_visualizations(analysis_plan.question_level_analyses)
```

### Výstup:

```
📊 PLÁN ANALÝZ:
   Analýzy otázek: 5
   Analýzy sekcí: 0
   Cross-question analýzy: 1
   Odhadovaný čas: 330 minut
   Složitost: 2.8/10

🎨 PLÁN GRAFŮ:
   Primární vizualizace: 5
   Alternativní vizualizace: 10
   Datawrapper kompatibilita: 60%
```

## 📊 Podporované typy otázek

### Základní typy:
- **L** - List (Radio) - Výběr z možností
- **5** - 5 Point Choice - Likertova škála
- **T** - Long Free Text - Dlouhý text
- **N** - Numerical Input - Číselný vstup
- **Y** - Yes/No - Ano/Ne otázky

### Pokročilé typy:
- **A** - Array (5 Point) - Matice s 5bodovou škálou
- **M** - Multiple choice - Vícenásobný výběr
- **R** - Ranking - Řazení podle priority
- **D** - Date - Datum

## 📈 Typy analýz

### Question-level analýzy:
- **FRA** - Frequency Analysis (Frekvenční analýza)
- **STA** - Statistical Analysis (Statistická analýza)
- **DIA** - Distribution Analysis (Analýza distribuce)
- **THA** - Thematic Analysis (Tematická analýza)

### Section-level analýzy:
- **CRA** - Correlation Analysis (Korelační analýza)
- **CTA** - Contingency Analysis (Kontingenční analýza)
- **CMA** - Comparative Analysis (Komparativní analýza)
- **FAA** - Factor Analysis (Faktorová analýza)

## 🎨 Vizualizace

### Datawrapper kompatibilní:
- **BAR** → `d3-bars` - Sloupcový graf
- **PIE** → `d3-pies` - Koláčový graf
- **HIS** → `d3-bars` - Histogram
- **TAB** → `tables` - Tabulka
- **SCP** → `d3-scatter-plot` - Bodový graf

### Externí generátory:
- **WCS** - Word Cloud (wordcloud library)
- **NET** - Network diagram (networkx)
- **RAD** - Radar chart (plotly)
- **SAN** - Sankey diagram (plotly)

## 🔧 Konfigurace

### Metadata soubory:
- `data/analysis_metadata/otazky.md` - Definice typů otázek
- `data/analysis_metadata/analyzy-otazky.md` - Analýzy na úrovni otázek
- `data/analysis_metadata/analyzy-sekce.md` - Analýzy na úrovni sekcí
- `data/analysis_metadata/vizualizace.md` - Definice vizualizací
- `data/analysis_metadata/otazky2analyzy.md` - Mapování otázka → analýza → vizualizace

### Prioritizace:
- **Priorita 1-2**: Vysoká (základní grafy, jednoduché analýzy)
- **Priorita 3**: Střední (pokročilé analýzy)
- **Priorita 4-5**: Nízká (specializované vizualizace)

## 🎯 Integrace s aplikací

Analysis Engine je **IMPLEMENTOVÁN jako Menu 13** v hlavní aplikaci (leden 2025):

### ✅ Implementované funkce:
1. **Hierarchická analýza** → Respektuje pořadí skupin a otázek z LimeSurvey
2. **Vazba na aktuální průzkum** → Pracuje s vybranými daty z Menu 1-2
3. **Interaktivní HTML reporty** → Tooltips pro typy otázek a analýz
4. **Automatické nalezení souborů** → structure.lss a responses.csv
5. **Doporučení implementace** → Prioritizace podle složitosti

### 🚀 Použití:
```bash
# Spuštění Menu 13
python src/main.py
# 1. Vybrat server a průzkum (Menu 1)
# 2. Načíst data (Menu 2)  
# 3. Spustit hierarchickou analýzu (Menu 13)
```

### 📊 Výstup:
- **JSON report** s kompletní analýzou
- **HTML report** s interaktivními tooltips
- **Doporučení** pro automatické generování grafů

## 📚 Rozšíření

### Přidání nového typu otázky:
1. Přidat do `otazky.md`
2. Definovat mapování v `otazky2analyzy.md`
3. Aktualizovat `QuestionAnalyzer.limesurvey_mapping`

### Přidání nové analýzy:
1. Přidat do `analyzy-otazky.md` nebo `analyzy-sekce.md`
2. Definovat podporované vizualizace
3. Implementovat logiku v `AnalysisRecommender`

### Přidání nové vizualizace:
1. Přidat do `vizualizace.md`
2. Mapovat na Datawrapper typ v `VisualizationMapper.datawrapper_mapping`
3. Implementovat externí generátor (pokud potřeba)

## 🧪 Testování

```bash
# Spuštění testů Analysis Engine
python test/test_analysis_engine.py

# Test jednotlivých komponent
python -c "from analysis_engine import MetadataLoader; loader = MetadataLoader(); print(loader.load_all_metadata())"
```

## 🎉 Výhody

- **Automatizace** - Eliminuje manuální rozhodování o typech analýz
- **Konzistence** - Standardizované mapování napříč projekty
- **Škálovatelnost** - Snadno rozšiřitelné o nové typy
- **Integrace** - Kompatibilní s existující Datawrapper infrastrukturou
- **Prioritizace** - Inteligentní doporučení podle složitosti

Analysis Engine transformuje naši aplikaci na **inteligentní systém** pro automatizované zpracování průzkumů! 🚀
