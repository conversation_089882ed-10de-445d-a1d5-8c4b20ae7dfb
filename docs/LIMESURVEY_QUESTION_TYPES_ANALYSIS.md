# Analýza typů otá<PERSON>k <PERSON>Survey a návrh rozšíření grafů

## 🔍 Současný stav systému

### 1. Identifikované komponenty

**Načítání a analýza LSS:**
- ✅ `survey_manager.py` - správa průzkumů
- ✅ `structure_manager.py` - načítán<PERSON> LSS struktury
- ✅ `question_manager.py` - správa otá<PERSON>k
- ❌ **CHYBÍ**: Dedikovaný LSS parser pro typy otázek

**Příprava dat:**
- ✅ `data_transformer.py` - základn<PERSON> transformace
- ❌ **OMEZENO**: Pouze `single_choice`, `multiple_choice`, `numeric`

**Generování grafů:**
- ✅ `chart_generator.py` - základn<PERSON> generování
- ✅ `datawrapper_client.py` - komunikace s API
- ❌ **OMEZENO**: Pouze základní typy grafů

### 2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mapování typů otázek

```python
# Z chart_generator.py
self.chart_types = {
    'single_choice': 'd3-bars',           # Sloupcový graf
    'multiple_choice': 'd3-bars-stacked', # Skládaný sloupcový
    'scale': 'd3-bars',                   # Sloupcový graf
    'text': 'd3-bars',                    # Sloupcový graf
    'array': 'd3-bars-stacked'            # Skládaný sloupcový - PRO POLE!
}
```

### 3. Dostupné typy grafů v Datawrapper

**Základní grafy:**
- Bar Chart, Stacked Bars, Grouped Bars, Split Bars
- Column Chart, Stacked Columns, Grouped Columns
- Lines, Multiple Lines, Area Chart
- Scatter Plot, Dot Plot, Range Plot
- Pie Chart, Donut Chart, Multiple Pies/Donuts

## 📋 LimeSurvey typy otázek a jejich potřeby

### 1. Identifikované typy z LSS struktury

**Z analýzy `structure.lss`:**
- **Type "T"** - Text (krátký text)
- **Type "L"** - List (výběr ze seznamu) 
- **Type "M"** - Multiple choice (více možností)
- **Type "F"** - Array (pole otázek)
- **Type "1"** - Array dual scale (pole s dvojitou škálou)
- **Type "5"** - 5-point choice (5-bodová škála)
- **Type "A"** - Array (5-point choice) (pole s 5-bodovou škálou)
- **Type "B"** - Array (10-point choice) (pole s 10-bodovou škálou)
- **Type "H"** - Array (increase/same/decrease) (pole s nárůst/stejné/pokles)
- **Type "E"** - Array (yes/no/uncertain) (pole s ano/ne/nevím)
- **Type "C"** - Array (yes/no) (pole s ano/ne)
- **Type "X"** - Boilerplate question (informační text)
- **Type "D"** - Date (datum)
- **Type "N"** - Numerical input (číselný vstup)
- **Type "S"** - Short free text (krátký volný text)
- **Type "U"** - Huge free text (dlouhý volný text)
- **Type "Y"** - Yes/No (ano/ne)
- **Type "G"** - Gender (pohlaví)
- **Type "I"** - Language switch (přepínač jazyka)
- **Type "!"** - List - dropdown (rozbalovací seznam)
- **Type ":"** - Array (numbers) (pole s čísly)
- **Type ";"** - Array (texts) (pole s texty)
- **Type "O"** - List with comment (seznam s komentářem)
- **Type "P"** - Multiple choice with comments (více možností s komentáři)
- **Type "Q"** - Multiple short text (více krátkých textů)
- **Type "K"** - Multiple numerical input (více číselných vstupů)
- **Type "R"** - Ranking (řazení)

### 2. Návrh mapování na grafy

#### **Jednoduché výběry (Single Choice)**
```python
'L': 'column-chart',        # List - sloupcový
'!': 'column-chart',        # Dropdown - sloupcový  
'O': 'column-chart',        # List with comment - sloupcový
'Y': 'pie-chart',           # Yes/No - koláčový
'G': 'pie-chart',           # Gender - koláčový
'5': 'column-chart',        # 5-point choice - sloupcový
```

#### **Více možností (Multiple Choice)**
```python
'M': 'grouped-column-chart', # Multiple choice - seskupený sloupcový
'P': 'grouped-column-chart', # Multiple choice with comments - seskupený
```

#### **Pole otázek (Arrays)**
```python
'F': 'stacked-column-chart', # Array - skládaný sloupcový
'A': 'stacked-column-chart', # Array (5-point) - skládaný
'B': 'stacked-column-chart', # Array (10-point) - skládaný  
'H': 'stacked-column-chart', # Array (increase/same/decrease) - skládaný
'E': 'stacked-column-chart', # Array (yes/no/uncertain) - skládaný
'C': 'stacked-column-chart', # Array (yes/no) - skládaný
'1': 'grouped-column-chart', # Array dual scale - seskupený
':': 'line-chart',           # Array (numbers) - čárový
';': 'table',                # Array (texts) - tabulka
```

#### **Škály a hodnocení**
```python
'5': 'column-chart',         # 5-point choice - sloupcový
'A': 'stacked-column-chart', # Array (5-point) - skládaný
'B': 'stacked-column-chart', # Array (10-point) - skládaný
'H': 'diverging-bar-chart',  # Increase/same/decrease - divergentní
```

#### **Číselné hodnoty**
```python
'N': 'histogram',            # Numerical - histogram
'K': 'scatter-plot',         # Multiple numerical - bodový
':': 'line-chart',           # Array numbers - čárový
```

#### **Texty a komentáře**
```python
'S': 'word-cloud',           # Short text - mrak slov
'U': 'word-cloud',           # Long text - mrak slov  
';': 'table',                # Array texts - tabulka
'Q': 'table',                # Multiple short text - tabulka
```

#### **Speciální typy**
```python
'R': 'ranking-chart',        # Ranking - žebříček
'D': 'timeline',             # Date - časová osa
'X': None,                   # Boilerplate - bez grafu
'I': None,                   # Language switch - bez grafu
```

## 🚀 Návrh implementace

### 1. Rozšíření LSS parseru

```python
# src/lss_question_analyzer.py
class LSSQuestionAnalyzer:
    """Analyzuje typy otázek z LSS struktury"""
    
    QUESTION_TYPE_MAPPING = {
        # Jednoduché výběry
        'L': {'chart_type': 'column-chart', 'data_type': 'categorical'},
        '!': {'chart_type': 'column-chart', 'data_type': 'categorical'},
        'Y': {'chart_type': 'pie-chart', 'data_type': 'binary'},
        'G': {'chart_type': 'pie-chart', 'data_type': 'categorical'},
        
        # Více možností
        'M': {'chart_type': 'grouped-column-chart', 'data_type': 'multiple_choice'},
        'P': {'chart_type': 'grouped-column-chart', 'data_type': 'multiple_choice'},
        
        # Pole otázek
        'F': {'chart_type': 'stacked-column-chart', 'data_type': 'array'},
        'A': {'chart_type': 'stacked-column-chart', 'data_type': 'array_scale'},
        'B': {'chart_type': 'stacked-column-chart', 'data_type': 'array_scale'},
        'H': {'chart_type': 'diverging-bar-chart', 'data_type': 'array_scale'},
        'E': {'chart_type': 'stacked-column-chart', 'data_type': 'array_choice'},
        'C': {'chart_type': 'stacked-column-chart', 'data_type': 'array_binary'},
        '1': {'chart_type': 'grouped-column-chart', 'data_type': 'array_dual'},
        
        # Číselné
        'N': {'chart_type': 'histogram', 'data_type': 'numerical'},
        'K': {'chart_type': 'scatter-plot', 'data_type': 'multiple_numerical'},
        ':': {'chart_type': 'line-chart', 'data_type': 'array_numerical'},
        
        # Škály
        '5': {'chart_type': 'column-chart', 'data_type': 'scale'},
        
        # Texty
        'S': {'chart_type': 'word-cloud', 'data_type': 'text'},
        'U': {'chart_type': 'word-cloud', 'data_type': 'long_text'},
        'Q': {'chart_type': 'table', 'data_type': 'multiple_text'},
        ';': {'chart_type': 'table', 'data_type': 'array_text'},
        
        # Speciální
        'R': {'chart_type': 'ranking-chart', 'data_type': 'ranking'},
        'D': {'chart_type': 'timeline', 'data_type': 'date'},
        'O': {'chart_type': 'column-chart', 'data_type': 'categorical_comment'},
        
        # Bez grafu
        'X': {'chart_type': None, 'data_type': 'info'},
        'I': {'chart_type': None, 'data_type': 'control'},
        'T': {'chart_type': None, 'data_type': 'text_input'}
    }
    
    def analyze_question(self, question_data):
        """Analyzuje otázku a vrátí doporučený typ grafu"""
        question_type = question_data.get('type', '')
        mapping = self.QUESTION_TYPE_MAPPING.get(question_type, {})
        
        return {
            'limesurvey_type': question_type,
            'chart_type': mapping.get('chart_type'),
            'data_type': mapping.get('data_type'),
            'requires_special_processing': self._requires_special_processing(question_type),
            'subquestions': self._has_subquestions(question_data),
            'scale_info': self._get_scale_info(question_data)
        }
```

### 2. Rozšíření data transformeru

```python
# src/enhanced_data_transformer.py
class EnhancedDataTransformer(DataTransformer):
    """Rozšířený transformer pro různé typy otázek"""
    
    def prepare_chart_data_by_type(self, df, question_analysis):
        """Příprava dat podle typu otázky"""
        data_type = question_analysis['data_type']
        
        if data_type == 'array':
            return self._prepare_array_data(df, question_analysis)
        elif data_type == 'array_scale':
            return self._prepare_scale_array_data(df, question_analysis)
        elif data_type == 'multiple_choice':
            return self._prepare_multiple_choice_data(df, question_analysis)
        elif data_type == 'ranking':
            return self._prepare_ranking_data(df, question_analysis)
        elif data_type == 'numerical':
            return self._prepare_numerical_data(df, question_analysis)
        elif data_type == 'text':
            return self._prepare_text_data(df, question_analysis)
        else:
            return self._prepare_categorical_data(df, question_analysis)
    
    def _prepare_array_data(self, df, question_analysis):
        """Příprava dat pro pole otázek"""
        # Implementace pro pole otázek
        pass
    
    def _prepare_scale_array_data(self, df, question_analysis):
        """Příprava dat pro škálová pole"""
        # Implementace pro škálová pole
        pass
```

### 3. Rozšíření chart generatoru

```python
# src/enhanced_chart_generator.py
class EnhancedChartGenerator(ChartGenerator):
    """Rozšířený generátor grafů"""
    
    def __init__(self):
        super().__init__()
        self.chart_types.update({
            # Rozšířené mapování
            'grouped-column-chart': 'd3-bars-grouped',
            'stacked-column-chart': 'd3-bars-stacked', 
            'diverging-bar-chart': 'd3-bars-split',
            'pie-chart': 'd3-pies',
            'donut-chart': 'd3-donuts',
            'histogram': 'd3-bars',
            'scatter-plot': 'd3-scatter-plot',
            'line-chart': 'd3-lines',
            'word-cloud': 'word-cloud',
            'ranking-chart': 'd3-bars',
            'timeline': 'd3-lines',
            'table': 'table'
        })
    
    def generate_chart_by_analysis(self, question_analysis, data):
        """Generuje graf podle analýzy otázky"""
        chart_type = question_analysis['chart_type']
        
        if not chart_type:
            return None  # Bez grafu
            
        if chart_type == 'word-cloud':
            return self._generate_word_cloud(data)
        elif chart_type == 'ranking-chart':
            return self._generate_ranking_chart(data)
        elif chart_type == 'timeline':
            return self._generate_timeline_chart(data)
        else:
            return self._generate_standard_chart(chart_type, data)
```

## 📊 Prioritní implementace

### Fáze 1: Základní rozšíření (nejčastější typy)
1. **List (L)** - sloupcový graf
2. **Multiple choice (M)** - seskupený sloupcový
3. **Array 5-point (A)** - skládaný sloupcový
4. **Yes/No (Y)** - koláčový graf
5. **Numerical (N)** - histogram

### Fáze 2: Pokročilé typy
1. **Array dual scale (1)** - seskupený sloupcový
2. **Ranking (R)** - speciální žebříček
3. **Array numbers (:)** - čárový graf
4. **Diverging scales (H)** - divergentní sloupcový

### Fáze 3: Speciální typy
1. **Text analysis (S, U)** - mrak slov
2. **Date/Timeline (D)** - časová osa
3. **Multiple numerical (K)** - bodový graf
4. **Tables (;, Q)** - tabulky

## 🔧 Technické požadavky

### 1. Nové závislosti
```python
# Pro analýzu textu (word cloud)
pip install wordcloud

# Pro pokročilé grafy
# Datawrapper už podporuje většinu typů
```

### 2. Nové soubory
- `src/lss_question_analyzer.py` - analýza typů otázek
- `src/enhanced_data_transformer.py` - rozšířená transformace dat
- `src/enhanced_chart_generator.py` - rozšířený generátor grafů
- `src/chart_type_mapper.py` - mapování typů grafů

### 3. Rozšíření existujících
- `src/data_transformer.py` - přidání nových metod
- `src/chart_generator.py` - rozšíření mapování
- `src/question_manager.py` - analýza typů otázek

## 🎯 Očekávané výsledky

**Po implementaci bude systém schopen:**
1. ✅ Automaticky rozpoznat 20+ typů LimeSurvey otázek
2. ✅ Vygenerovat vhodný typ grafu pro každý typ otázky
3. ✅ Zpracovat pole otázek (arrays) se správným skládáním
4. ✅ Vytvořit škálové grafy pro hodnocení
5. ✅ Generovat koláčové grafy pro binární otázky
6. ✅ Vytvořit histogramy pro číselné hodnoty
7. ✅ Zpracovat ranking otázky
8. ✅ Vytvořit mraky slov pro textové odpovědi

**Výsledek:** Kompletní automatizace generování grafů pro všechny běžné typy LimeSurvey otázek! 🎉