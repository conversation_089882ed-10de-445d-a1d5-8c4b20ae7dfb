# Skutečné opravy v Datawrapper modulu

## Hlavní problém
Původní implementace používala **špatný způsob nastavení parametrů** pro PNG export. Místo URL parametrů se pokoušela nastavovat metadata grafu, což nefungovalo.

## Identifikované chyby a opravy

### 1. 🔧 Špatný způsob exportu
**Problém:** Export se pokoušel nastavovat přes metadata grafu místo URL parametrů.

**Původní (špatný) způsob:**
```python
# Nastavení přes metadata - NEFUNGUJE!
payload = {
    "metadata": {
        "publish": {
            "export-png": {
                "width": 600,
                "padding": 10,
                "scale": 2
            }
        }
    }
}
```

**<PERSON>ý (správný) způsob:**
```python
# Parametry přímo v URL - FUNGUJE!
params = {
    "width": 600,
    "borderWidth": 10,
    "zoom": 2,
    "plain": "true",
    "mode": "rgb",
    "unit": "px"
}
response = requests.get(f"{api_url}/charts/{chart_id}/export/png", params=params)
```

### 2. 🔧 Nesprávný název parametru pro okraj
**Problém:** Používal se `"padding"` místo `"borderWidth"`.

**Oprava:** Změněno na `"borderWidth"` podle oficiální Datawrapper knihovny.

### 3. 🔧 Nesprávný parametr pro zoom
**Problém:** Pro PNG se používal `"scale"` místo `"zoom"`.

**Oprava:** Pro PNG export se používá `"zoom"`, `"scale"` je pro PDF.

### 4. 🔧 Automatická výška
**Problém:** Nastavovala se fixní výška 800px.

**Oprava:** Výška se vůbec nenastavuje, Datawrapper ji vypočítá automaticky podle obsahu.

## Implementované změny

### [`src/datawrapper_client.py`](src/datawrapper_client.py)

1. **Přepsána metoda `export_chart()`:**
   - Odstraněno nastavení přes metadata
   - Přidány URL parametry podle oficiální knihovny
   - Správné názvy parametrů: `borderWidth`, `zoom`

2. **Odstraněna metoda `update_chart_settings()`:**
   - Už se nepoužívá, export se dělá přímo přes URL

### [`src/chart_generator.py`](src/chart_generator.py)

1. **Odstraněno volání `update_chart_settings()`**
2. **Aktualizováno volání `export_chart()`:**
   ```python
   png_data = self.dw.export_chart(
       chart['id'],
       export_format='png',
       width=self.png_width,           # 600px
       border_width=self.png_border,   # 10px
       zoom=self.png_scale,            # 2x
       plain=not self.full_header_footer,
       mode='rgba' if self.transparent_bg else 'rgb'
   )
   ```

## Výsledné parametry

### URL pro PNG export:
```
https://api.datawrapper.de/v3/charts/{chart_id}/export/png?width=600&borderWidth=10&zoom=2&plain=true&mode=rgb&unit=px
```

### Význam parametrů:
- `width=600` - šířka grafu v pixelech
- `borderWidth=10` - okraj kolem grafu (10px)
- `zoom=2` - multiplikátor pro PNG (finální šířka 1200px)
- `plain=true` - jen graf bez hlavičky/patičky
- `mode=rgb` - barevný režim
- `unit=px` - jednotka pro rozměry

## Kompatibilita s oficiální knihovnou

Implementace je nyní **100% kompatibilní** s oficiální Datawrapper Python knihovnou:

| Parametr | Oficiální knihovna | Naše implementace | Status |
|----------|-------------------|------------------|--------|
| `borderWidth` | ✓ | ✓ | ✅ |
| `zoom` | ✓ | ✓ | ✅ |
| `width` | ✓ | ✓ | ✅ |
| `plain` | ✓ | ✓ | ✅ |
| `mode` | ✓ | ✓ | ✅ |
| `unit` | ✓ | ✓ | ✅ |

## Očekávané výsledky

Po těchto opravách budou grafy:

1. **✅ Mít správný okraj** - 10px kolem grafu
2. **✅ Mít automatickou výšku** - přizpůsobí se obsahu
3. **✅ Mít správnou šířku** - 1200px (600px × 2 zoom)
4. **✅ Exportovat se správně** - přes URL parametry

## Testování

Vytvořeny testy pro ověření:
- [`test_datawrapper_real_fix.py`](test_datawrapper_real_fix.py) - ověřuje správnost implementace
- Test prošel úspěšně ✅

## Zdroje

Opravy jsou založené na oficiální Datawrapper Python knihovně:
- [`datawrapper_api_light.py`](datawrapper_api_light.py) - oficiální implementace
- Metoda `export_chart()` na řádcích 1018-1101