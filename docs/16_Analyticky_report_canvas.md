## Finální Implementač<PERSON><PERSON>: "Analytický Report Canvas"

**Verze:** 3.0 (<PERSON><PERSON>, Samostatný)
**Určeno pro:** Implementaci Frontend GUI a základní logiky
**Cíl:** Vytvořit MVP desktopové aplikace v Pythonu pro vizuální a interaktivní tvorbu analytických zpráv.

### I. Strategická Vize a Koncept

Vytváříme špičkov<PERSON> aplikaci "Analytický Report Canvas", která revolučním způsobem mění proces tvorby datových zpráv. Jádrem aplikace je vizuální editor (Canvas), kde uživatel nepracuje s abstraktními procesy, ale přímo **sestavuje finální report jako strom vizuálních uzlů**. Každý uzel reprezentuje kapitolu nebo sekci zprávy. Uživatel do těchto uzlů vklád<PERSON> data, p<PERSON><PERSON>e instrukce pro AI a generuje obsah, <PERSON><PERSON><PERSON><PERSON>, interaktivní a plně reprodukovatelný dokument.

### II. Podrobná Specifikace GUI a Interakcí (MVP)

Aplikace bude mít jedno hlavní okno, rozdělené do tří vertikálních panelů. Budeme používat Python s frameworkem **PyQt6** nebo **PySide6** pro robustní a moderní GUI. Pro vizualizaci Canvasu doporučuji využít knihovnu `pyqtgraph` nebo specializovaný `NodeGraph` framework, pokud existuje pro zvolenou platformu, pro jeho schopnosti efektivně renderovat grafové struktury.

#### A. Levý Panel: Data Explorer
Tento panel je trvale viditelný a slouží jako centrální zdroj všech datových entit.

*   **Vizuální komponenta:** `QTreeView` nebo podobný stromový widget.
*   **Struktura stromu (pro MVP):**
    *   **Kořenový uzel: "Datové Zdroje"**
        *   `Surová Data (CSV)`: Po načtení se zde zobrazí stromová struktura: `Skupina -> Otázka (sloupec)`.
        *   `Statistiky (XLS)`: Po načtení se zde zobrazí seznam listů a pojmenovaných rozsahů v Excelu.
        *   `Uživatelské Soubory`: Seznam souborů (TXT, DOCX, obrázky), které uživatel nahrál.
    *   **Kořenový uzel: "Výsledky z Reportu" (dynamický)**
        *   Tato sekce je na začátku prázdná.
        *   Jakmile je v jakémkoliv uzlu na Canvasu vygenerován výstup (text, graf), automaticky se zde objeví položka s jeho unikátním ID (např. `#KAPITOLA_UVOD`), kterou lze znovu použít.
*   **Interakce:**
    *   Uživatel může vybrat jednu nebo více položek (pomocí `Ctrl`/`Shift`).
    *   **Drag-and-Drop:** Uživatel může přetáhnout vybrané položky z Data Exploreru přímo na uzel na Canvasu, čímž se připojí jako jeho datové vstupy.

#### B. Střední Panel: Canvas Editor
Toto je hlavní pracovní plocha.

*   **Vizuální komponenta:** Specializovaný grafový widget.
*   **Funkcionalita:**
    *   **Přidávání uzlů:** Z kontextového menu (pravé tlačítko myši na Canvasu) může uživatel přidat nový `Uzel Kapitoly`.
    *   **Manipulace s uzly:** Uzly lze volně přesouvat, propojovat (rodič-dítě vztah pro strukturu reportu) a mazat.
    *   **Zoom a Pan:** Standardní funkce pro navigaci na velkém Canvasu.
*   **Vizuál uzlu `NODE_CHAPTER`:**
    *   **Hlavička:** Zobrazuje ID uzlu (`#KAP01`) a editovatelný nadpis kapitoly.
    *   **Tělo uzlu:** Má **dvě přepínatelné záložky**:
        1.  **"Obsah" (výchozí pohled):** Zobrazuje **vygenerovaný text/výstup**. Uživatel může tento text **přímo editovat** jako v běžném textovém editoru. Jakákoliv manuální úprava označí uzel jako "manuálně upravený".
        2.  **"Nastavení & Prompt":** Zobrazuje **všechny parametry** uzlu: seznam připojených datových vstupů, pole pro psaní instrukcí pro AI, výběr systémového promptu atd.
    *   **Tlačítka na uzlu:**
        *   `🔄 Generovat`: Spustí AI generování pro tento konkrétní uzel. Pokud je záložka "Obsah" manuálně upravená, zobrazí se varování: "Stávající obsah bude přepsán. Pokračovat?".
        *   `👁️ Přepnout pohled`: Rychlé přepínání mezi záložkami "Obsah" a "Nastavení & Prompt".

#### C. Pravý Panel: Inspektor
Tento panel poskytuje detailní pohled a alternativní způsob editace pro uzel vybraný na Canvasu. Zobrazuje přesně ty samé informace jako záložka "Nastavení & Prompt" na uzlu, ale ve větším a přehlednějším formátu.

### III. User Stories (Příklady pracovních postupů)

Zde je 10 realistických příběhů, které demonstrují, jak analytik pracuje v navrženém systému.

1.  **"Prázdný list":** Analytik otevře aplikaci, vytvoří pravým klikem na Canvasu první `Uzel Kapitoly`, v Inspektorovi ho pojmenuje "1. Úvod" a přímo na záložce "Obsah" začne psát první odstavce.
2.  **Vložení hotové tabulky:** Analytik přetáhne tabulku z Data Exploreru (kde má načtené statistiky z XLS) na uzel "2. Demografie". Tabulka se vloží do záložky "Obsah" jako formátovaný text. Poté ji ručně okomentuje.
3.  **Jednoduchá AI syntéza:** Analytik vytvoří uzel "3. Analýza spokojenosti", připojí k němu 5 relevantních tabulek jako datové vstupy, přepne na záložku "Nastavení & Prompt", napíše instrukci `"Shrň klíčová zjištění z připojených tabulek."` a klikne `Generovat`. Vygenerovaný text se objeví v záložce "Obsah".
4.  **Manuální úprava AI výstupu:** Analytikovi se nelíbí jedna věta ve vygenerovaném textu. Přepne na záložku "Obsah" a větu jednoduše přepíše. Uzel je nyní označen jako "manuálně upravený".
5.  **Restrukturalizace reportu:** Analytik se rozhodne, že kapitola 3 má být součástí kapitoly 2. Na Canvasu jednoduše přetáhne uzel `Kapitola 3` tak, aby se stal pod-uzlem (dítětem) uzlu `Kapitola 2`.
6.  **Vložení grafu:** Analytik má obrázek grafu (např. `graf_spokojenosti.png`). Nahraje ho do Data Exploreru. Poté na Canvasu vytvoří uzel `Kapitola 4`, přetáhne do něj obrázek z Data Exploreru a v promptu napíše `"Popiš graf vložený v datech. Jedná se o sloupcový graf spokojenosti. Zaměř se na nejvyšší a nejnižší hodnoty."`. AI vygeneruje textový popis grafu.
7.  **Řetězení AI operací:** Analytik má uzel `#SHRNUTI_TEMAT`, kde AI vygenerovala seznam témat. Vytvoří nový uzel "Doporučení", jako datový vstup do něj připojí `#SHRNUTI_TEMAT` a v promptu napíše: `"Na základě připojených témat, zejména 'špatná komunikace', navrhni 3 konkrétní doporučení pro management."`
8.  **Hromadné generování:** Analytik má rozpracovaný report s 10 kapitolami. Některé jsou hotové, některé mají jen připravené prompty. V hlavní liště aplikace klikne na tlačítko `"Generovat celý report"`. Systém projde všechny uzly a pro ty, které ještě nemají vygenerovaný obsah, spustí AI generování.
9.  **Verzování (budoucí, ale myšlenka je zde):** Analytik klikne na uzel `Kapitola 3` a v Inspektorovi vidí historii generování ("Verze 1 - 10:30", "Verze 2 - 11:15"). Může si zobrazit předchozí verzi, zkopírovat z ní část textu a vložit ji do aktuální verze.
10. **Použití AI k úpravě textu (budoucí):** Analytik v záložce "Obsah" označí odstavec, klikne pravým tlačítkem a z menu vybere "Způsob úpravy: Zkrať", "Způsob úpravy: Přeformuluj pro manažery", "Způsob úpravy: Přidej příklady".

### IV. Klíčové Požadavky pro Implementaci MVP

1.  **GUI Framework:** Python, PyQt6/PySide6.
2.  **Canvas:** Schopnost renderovat, přesouvat a propojovat uzly.
3.  **Uzel `NODE_CHAPTER`:** Musí implementovat přepínatelné pohledy ("Obsah" vs. "Nastavení & Prompt").
4.  **Data Explorer:** Základní stromový pohled s podporou načtení CSV/LSS a drag-and-drop.
5.  **AI Integrace:** Funkce `generate_chapter`, která je volána z uzlu a jejíž výstup se vkládá zpět do uzlu. Konfigurace API klíče musí být v externím souboru.
6.  **Persistence:** Ukládání a načítání celého stavu Canvasu (včetně obsahu všech uzlů a jejich nastavení) do/z jednoho JSON souboru.
7.  **Bezpečnost a autorizace:** V MVP se **neřeší**. Aplikace běží lokálně a pracuje se soubory na lokálním disku.

