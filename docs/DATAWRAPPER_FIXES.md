# Opravy v Datawrapper modulu

## Identifikované a opravené chyby

### 1. Border okraj není na obrázku nastaven (0px)

**Problém:** Parametr pro okraj nebyl správně nastaven v API volání.

**Oprava:** 
- Změněn parametr z `"border"` na `"padding"` v [`src/datawrapper_client.py:289`](src/datawrapper_client.py:289)
- Datawrapper API používá `"padding"` pro nastavení okraje kolem grafu

**Před:**
```python
png_settings = {
    "width": width,
    "border": border,  # Nesprávný parametr
    "scale": scale_factor,
    "logo": "auto",
    "mode": "rgb"
}
```

**Po:**
```python
png_settings = {
    "width": width,
    "padding": border,  # Správný parametr pro okraj
    "scale": scale_factor,
    "logo": "auto", 
    "mode": "rgb"
}
```

### 2. Generuje všechny grafy v rozlišení 1200x800px místo automatické výšky

**Problém:** Nastavovala se jak šířka tak výška, což způsobovalo fixní rozlišení 1200x800px.

**Oprava:**
- Odstraněno nastavení výšky - nastavuje se pouze `width`
- Výška se nyní počítá automaticky podle obsahu grafu
- Zachován multiplikační faktor 2 pro vysoké rozlišení (600px × 2 = 1200px šířka)

**Implementace:**
- V [`src/datawrapper_client.py:295-297`](src/datawrapper_client.py:295-297) se nenastavuje `height` parametr
- Parametr `auto_height=True` zajišťuje automatický výpočet výšky
- Finální šířka: 600px × 2 (scale) = 1200px
- Finální výška: automatická podle obsahu grafu

### 3. Nesprávný klíč pro PNG export

**Problém:** Používal se klíč `"export-pdf"` místo `"export-png"`.

**Oprava:**
- Změněn klíč z `"export-pdf"` na `"export-png"` v [`src/datawrapper_client.py:312`](src/datawrapper_client.py:312)

**Před:**
```python
payload = {
    "metadata": {
        "publish": {
            "export-pdf": png_settings  # Nesprávný klíč
        }
    }
}
```

**Po:**
```python
payload = {
    "metadata": {
        "publish": {
            "export-png": png_settings  # Správný klíč pro PNG export
        }
    }
}
```

## Výsledný payload pro PNG export

```json
{
  "metadata": {
    "publish": {
      "export-png": {
        "width": 600,
        "padding": 10,
        "scale": 2,
        "logo": "auto",
        "mode": "rgb"
      }
    }
  }
}
```

## Testování

Vytvořen test script [`test_datawrapper_fixes.py`](test_datawrapper_fixes.py) pro ověření oprav:

```bash
python3 test_datawrapper_fixes.py
```

**Výsledky testu:**
- ✓ PNG šířka: 600px (finální 1200px s scale 2x)
- ✓ PNG border: 10px (správně nastaven jako 'padding')
- ✓ Automatická výška: True
- ✓ Výška se nenastavuje - bude automatická
- ✓ Používá se 'padding' pro okraj
- ✓ Používá se správný klíč 'export-png'

## Očekávané výsledky

Po těchto opravách by měly grafy:
1. **Mít správný okraj** - 10px padding kolem grafu
2. **Mít automatickou výšku** - výška se přizpůsobí obsahu grafu
3. **Mít správnou šířku** - 1200px (600px × 2 scale factor)
4. **Být správně exportovány** - pomocí správného PNG export API