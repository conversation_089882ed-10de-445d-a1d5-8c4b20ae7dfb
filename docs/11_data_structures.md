# Data Structures Design

## 📊 **<PERSON><PERSON><PERSON><PERSON>ch Struktur**

Tento dokument definuje datové struktury pro Advanced Chart Architecture včetně virtuá<PERSON><PERSON><PERSON>, chart konfigurací a AI analýz.

## 🏗️ **Core Data Structures**

### **1. Chart Configuration Structure**

```json
{
  "question_id": "Q123",
  "question_text": "<PERSON>ak hodnotíte naše služby?",
  "data_type": "likert|text|choice|numeric",
  "hidden": false,
  "charts": [
    {
      "chart_type": "column|pie|donut|wordcloud|table",
      "generator": "datawrapper|internal_wordcloud|internal_table",
      "parameters": {
        "width": 800,
        "height": 400,
        "color_scheme": "default",
        "custom_param": "value"
      },
      "enabled": true,
      "priority": 1
    }
  ],
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### **2. Virtual Question Structure**

```json
{
  "question_id": "VIRTUAL_satisfaction",
  "question_text": "Celková spokojenost",
  "virtual": true,
  "hidden": false,
  "source_questions": ["Q1", "Q2", "Q3"],
  "merge_strategy": "concatenate|aggregate_numeric|combine_categories|cross_tabulate",
  "parameters": {
    "separator": " ",
    "remove_duplicates": true,
    "weight_factors": {"Q1": 1.0, "Q2": 0.8, "Q3": 1.2}
  },
  "computed_data": {
    "concatenated_text": "text data...",
    "aggregated_value": 4.2,
    "categories": ["cat1", "cat2"],
    "frequencies": {"word1": 10, "word2": 8},
    "total_responses": 150,
    "computation_timestamp": "2024-01-15T10:30:00Z"
  },
  "translation_mapping": {
    "cs": "Celková spokojenost",
    "en": "Overall Satisfaction"
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### **3. Enhanced Chart Data Structure**

```json
{
  "survey_id": "survey123",
  "version": "2.0",
  "generated_at": "2024-01-15T10:30:00Z",
  "questions": {
    "Q1": {
      "question_text": "Jak hodnotíte kvalitu?",
      "question_type": "likert",
      "data_type": "choice",
      "virtual": false,
      "hidden": false,
      "charts": {
        "column": {
          "generator": "datawrapper",
          "chart_id": "dw_12345",
          "chart_url": "https://datawrapper.dwcdn.net/12345/",
          "data": {
            "categories": ["Velmi dobré", "Dobré", "Průměrné"],
            "values": [25, 35, 15],
            "total_responses": 75
          },
          "ai_analysis": {
            "summary": "Převažují pozitivní hodnocení",
            "confidence": 0.85,
            "generated_at": "2024-01-15T10:30:00Z"
          }
        },
        "table": {
          "generator": "internal_table",
          "output_path": "charts/survey123/table/Q1_table.html",
          "data": {
            "category_counts": {"Velmi dobré": 25, "Dobré": 35},
            "percentages": {"Velmi dobré": 33.3, "Dobré": 46.7}
          }
        }
      },
      "translations": {
        "cs": {
          "question_text": "Jak hodnotíte kvalitu?",
          "categories": ["Velmi dobré", "Dobré", "Průměrné"]
        },
        "en": {
          "question_text": "How do you rate the quality?",
          "categories": ["Very good", "Good", "Average"]
        }
      }
    },
    "VIRTUAL_satisfaction": {
      "question_text": "Celková spokojenost",
      "question_type": "virtual",
      "data_type": "text",
      "virtual": true,
      "hidden": false,
      "source_questions": ["Q1", "Q2"],
      "merge_strategy": "concatenate",
      "charts": {
        "wordcloud": {
          "generator": "internal_wordcloud",
          "output_path": "charts/survey123/wordcloud/VIRTUAL_satisfaction_wordcloud.png",
          "data": {
            "frequencies": {"kvalita": 15, "služby": 12, "spokojenost": 10},
            "total_words": 150,
            "concatenated_text": "kvalita služby spokojenost..."
          },
          "ai_analysis": {
            "summary": "Klíčová témata: kvalita, služby, spokojenost",
            "key_insights": ["Pozitivní sentiment", "Kvalita je priorita"],
            "confidence": 0.92
          }
        }
      }
    }
  },
  "metadata": {
    "total_questions": 2,
    "virtual_questions": 1,
    "total_charts": 3,
    "ai_enhanced_charts": 2,
    "languages": ["cs", "en"],
    "generators_used": ["datawrapper", "internal_wordcloud", "internal_table"]
  }
}
```

### **4. AI Analysis Structure**

```json
{
  "analysis_id": "ai_analysis_12345",
  "chart_type": "wordcloud",
  "question_id": "Q1",
  "analysis_type": "automatic|manual|batch",
  "model_used": "gpt-4",
  "prompt_template": "wordcloud_analysis",
  "input_data": {
    "frequencies": {"kvalita": 15, "služby": 12},
    "total_responses": 50,
    "question_context": {
      "question_text": "Co se vám líbilo?",
      "question_type": "text"
    }
  },
  "results": {
    "summary": "Nejčastější témata jsou kvalita a služby",
    "key_insights": [
      "Kvalita je nejdůležitější faktor",
      "Služby jsou druhé nejčastější téma",
      "Pozitivní sentiment převažuje"
    ],
    "sentiment_score": 0.75,
    "confidence": 0.88,
    "metadata_text": "Analýza ukazuje vysokou spokojenost s kvalitou služeb",
    "chart_recommendations": [
      "Zvýraznit pozitivní aspekty",
      "Použít teplé barvy pro wordcloud"
    ],
    "statistical_notes": [
      "N=50 respondentů",
      "95% confidence interval"
    ]
  },
  "cost": 0.0234,
  "processing_time_ms": 1250,
  "generated_at": "2024-01-15T10:30:00Z",
  "cached": false
}
```

### **5. Generator Registry Structure**

```json
{
  "generators": {
    "datawrapper": {
      "name": "Datawrapper API",
      "type": "external_api",
      "supported_chart_types": ["column", "pie", "donut", "line", "area"],
      "configuration": {
        "api_key_required": true,
        "base_url": "https://api.datawrapper.de/v3",
        "rate_limit": "100/hour"
      },
      "status": "active"
    },
    "internal_wordcloud": {
      "name": "WordCloud Generator",
      "type": "internal",
      "supported_chart_types": ["wordcloud", "wordcloud_shaped"],
      "configuration": {
        "ai_enhanced": true,
        "fallback_library": "wordcloud",
        "output_formats": ["png", "svg"]
      },
      "status": "active"
    },
    "internal_table": {
      "name": "Table Generator", 
      "type": "internal",
      "supported_chart_types": ["table", "frequency_table", "crosstab"],
      "configuration": {
        "output_formats": ["html", "csv", "png"],
        "styling_options": ["default", "minimal", "bootstrap"]
      },
      "status": "active"
    },
    "internal_multi_chart": {
      "name": "Multi-Chart Generator",
      "type": "internal",
      "supported_chart_types": ["multi_donut", "multi_column", "dashboard"],
      "configuration": {
        "max_charts": 12,
        "layout_options": ["grid", "horizontal", "vertical"],
        "output_formats": ["html", "png"]
      },
      "status": "active"
    }
  }
}
```

## 🔄 **Data Flow Architecture**

### **1. Input Data Processing**

```
CSV/LSS Data → Data Validation → Question Classification → Chart Assignment
```

### **2. Virtual Question Processing**

```
Source Questions → Merge Strategy → Data Computation → Virtual Question Data
```

### **3. Chart Generation Flow**

```
Question Data → Generator Selection → Chart Creation → AI Analysis → Output
```

### **4. Translation Flow**

```
Original Data → Translation Dictionary → Language-Specific Data → Localized Charts
```

## 📁 **File Structure**

```
project_root/
├── data/
│   ├── survey_123/
│   │   ├── chart_data.json          # Enhanced chart data
│   │   ├── chart_config.json        # Chart configurations
│   │   ├── virtual_questions.json   # Virtual questions
│   │   └── ai_analyses.json         # AI analysis cache
│   └── translations/
│       ├── cs.json                  # Czech translations
│       └── en.json                  # English translations
├── charts/
│   └── survey_123/
│       ├── wordcloud/               # WordCloud outputs
│       ├── table/                   # Table outputs
│       ├── multi_chart/             # Multi-chart outputs
│       └── metadata/                # Chart metadata
└── config/
    ├── chart_defaults.json          # Default chart configurations
    ├── generator_registry.json      # Generator configurations
    └── ai_prompts.yaml              # AI prompt templates
```

## 🔧 **API Interfaces**

### **Chart Configuration Manager**

```python
class ChartConfigManager:
    def configure_question_charts(question_id, question_text, data_type) -> QuestionConfig
    def add_chart_to_question(question_id, chart_config) -> bool
    def get_question_config(question_id) -> QuestionConfig
    def get_all_configurations() -> Dict[str, QuestionConfig]
    def export_configuration(filename) -> bool
    def import_configuration(filename) -> bool
```

### **Virtual Question Manager**

```python
class VirtualQuestionManager:
    def create_virtual_question(question_id, source_questions, merge_strategy) -> VirtualQuestion
    def compute_virtual_data(question_id) -> Dict[str, Any]
    def toggle_visibility(question_id) -> bool
    def get_virtual_question(question_id) -> VirtualQuestion
    def delete_virtual_question(question_id) -> bool
```

### **AI Data Analyst**

```python
class AIDataAnalyst:
    def analyze_chart_data(chart_data, chart_type, question_context) -> AnalysisResult
    def analyze_multiple_charts(chart_data_list, chart_types) -> List[AnalysisResult]
    def get_analysis_statistics() -> Dict[str, Any]
    def clear_cache() -> None
```

## 🔒 **Data Validation Rules**

### **Chart Configuration Validation**

- `question_id`: Required, alphanumeric + underscore
- `chart_type`: Must be in supported types list
- `generator`: Must be registered generator
- `parameters`: Must match generator schema

### **Virtual Question Validation**

- `source_questions`: Must exist in survey
- `merge_strategy`: Must be supported strategy
- `question_id`: Must start with "VIRTUAL_"
- No circular dependencies

### **AI Analysis Validation**

- `confidence`: Float between 0.0 and 1.0
- `model_used`: Must be configured model
- `cost`: Non-negative float
- `generated_at`: Valid ISO timestamp

## 🚀 **Performance Considerations**

### **Caching Strategy**

- AI analyses cached by data hash
- Chart configurations cached in memory
- Virtual question data cached until source changes

### **Memory Management**

- Lazy loading of chart data
- Streaming for large datasets
- Garbage collection for unused analyses

### **Scalability**

- Horizontal scaling for AI processing
- Database sharding for large surveys
- CDN for chart assets

## 🔄 **Migration Strategy**

### **Backward Compatibility**

- Old chart_data.json format supported
- Automatic migration on first load
- Fallback to v1 structure if needed

### **Version Management**

- Schema versioning in data files
- Migration scripts for each version
- Rollback capability

This data structure design ensures scalability, maintainability, and backward compatibility while supporting all advanced features of the chart architecture.
