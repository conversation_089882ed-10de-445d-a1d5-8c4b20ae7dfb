# AI Integrace - Konkrétn<PERSON> úkoly

## 🎯 Přehled situace

**✅ HOTOVO:** AI komponenty úspěšně převzaty a integrovány do `src/ai/`
**🔄 NYNÍ:** Potřeba napojit AI na existující LimWrapp systém

## 📋 Konkrétní úkoly k dokončení

### 1. <PERSON><PERSON> (Priorita: VYSOKÁ)

#### Menu 10: AI WordCloud generování
```python
def menu_ai_wordcloud():
    """Generování WordCloud s AI enhancement"""
    # Implementace:
    # 1. Výběr survey ID
    # 2. Výběr textových otázek
    # 3. AI analýza a generování
    # 4. Uložení do charts/survey_id/wordclouds/
```

#### Menu 11: AI analýza textových odpovědí
```python
def menu_ai_text_analysis():
    """AI analýza textových odpovědí (sentiment, themes)"""
    # Implementace:
    # 1. <PERSON><PERSON><PERSON><PERSON><PERSON> survey a otázky
    # 2. <PERSON><PERSON> anal<PERSON>zy (sentiment/themes/general)
    # 3. AI zpracování
    # 4. Export výsledků
```

#### Menu 12: AI návrhy parametrů
```python
def menu_ai_parameter_suggestions():
    """AI návrhy optimálních parametrů pro analýzy"""
    # Implementace:
    # 1. Analýza charakteristik dat
    # 2. AI návrhy parametrů
    # 3. Aplikace návrhů
```

#### Menu 13: AI generování scénářů
```python
def menu_ai_scenario_generation():
    """AI generování analytických scénářů"""
    # Implementace:
    # 1. Analýza survey struktury
    # 2. Generování scénářů
    # 3. Výběr a spuštění scénáře
```

#### Menu 14: AI statistiky a správa
```python
def menu_ai_management():
    """AI usage statistiky a správa"""
    # Implementace:
    # 1. Zobrazení usage stats
    # 2. Cost tracking
    # 3. Správa API klíčů
    # 4. Reset statistik
```

### 2. Data Integration (Priorita: VYSOKÁ)

#### Napojení na existující data loading
```python
# V src/ai/integration.py - dokončit placeholder funkce:

def _get_survey_text_data(self, survey_id: str, question_ids: List[str] = None):
    """Napojit na existující CSV loading"""
    # TODO: Použít stávající data loading systém
    # TODO: Filtrovat textové odpovědi
    # TODO: Vrátit list textů

def _get_survey_characteristics(self, survey_id: str):
    """Napojit na existující LSS parsing"""
    # TODO: Použít stávající LSS parser
    # TODO: Extrahovat charakteristiky průzkumu
    # TODO: Vrátit SurveyCharacteristics
```

### 3. Configuration Setup (Priorita: STŘEDNÍ)

#### Environment variables
```bash
# Přidat do .env:
OPENAI_API_KEY=sk-proj-your-key-here
OPENAI_ORG_ID=your-org-id-here
REDIS_URL=redis://localhost:6379/0  # volitelné
DAILY_COST_LIMIT=50.0
MONTHLY_COST_LIMIT=500.0
```

#### Config integration
```python
# V src/core/config_manager.py - přidat AI sekci:
def load_ai_config(self):
    """Load AI configuration"""
    return {
        'openai_api_key': os.getenv('OPENAI_API_KEY'),
        'openai_org_id': os.getenv('OPENAI_ORG_ID'),
        'redis_url': os.getenv('REDIS_URL'),
        'daily_cost_limit': float(os.getenv('DAILY_COST_LIMIT', 50.0)),
        'monthly_cost_limit': float(os.getenv('MONTHLY_COST_LIMIT', 500.0))
    }
```

### 4. Testing a validace (Priorita: STŘEDNÍ)

#### Unit testy pro AI komponenty
```python
# tests/test_ai_integration.py
def test_ai_wordcloud_generation():
    """Test WordCloud generování"""
    
def test_ai_text_analysis():
    """Test AI analýzy textu"""
    
def test_ai_parameter_suggestions():
    """Test AI návrhů parametrů"""
```

#### Integration testy
```python
# tests/test_ai_menu_integration.py
def test_menu_ai_functions():
    """Test AI menu funkcí"""
    
def test_ai_data_loading():
    """Test napojení AI na data"""
```

### 5. Error Handling a Fallbacks (Priorita: STŘEDNÍ)

#### Graceful degradation
```python
# V menu_functions.py:
def check_ai_availability():
    """Kontrola dostupnosti AI funkcí"""
    if not is_ai_available():
        print("❌ AI funkce nejsou dostupné")
        print("💡 Zkontrolujte OPENAI_API_KEY v .env")
        return False
    return True
```

#### Cost limits
```python
def check_cost_limits():
    """Kontrola cost limitů před AI voláním"""
    stats = get_ai_integration().get_usage_statistics()
    if stats['openai']['total_cost'] > DAILY_LIMIT:
        print("⚠️ Denní cost limit překročen")
        return False
    return True
```

## 🚀 Implementační plán

### Týden 1: Menu a základní integrace
- [ ] Vytvořit Menu 10-14 v menu_functions.py
- [ ] Napojit AI integration na config_manager
- [ ] Základní error handling

### Týden 2: Data integration
- [ ] Dokončit placeholder funkce v integration.py
- [ ] Napojit na existující CSV a LSS loading
- [ ] Testovat s reálnými daty

### Týden 3: Testing a optimalizace
- [ ] Unit testy pro AI komponenty
- [ ] Integration testy
- [ ] Performance optimalizace
- [ ] Dokumentace

### Týden 4: Polish a deployment
- [ ] User experience improvements
- [ ] Error handling refinement
- [ ] Documentation updates
- [ ] Release preparation

## 📊 Očekávané výsledky

### Okamžitě dostupné funkce:
1. **WordCloud generování** - AI enhanced analýza textů
2. **Parameter suggestions** - AI návrhy optimálních nastavení
3. **Scenario generation** - AI plánování analýz
4. **Text analysis** - sentiment, themes, kategorizace
5. **Usage tracking** - cost monitoring a statistiky

### Integrace s existujícím workflow:
1. **Menu rozšíření** - nové AI funkce v CLI
2. **Data compatibility** - práce se stávajícími CSV/LSS
3. **Chart enhancement** - AI návrhy pro Datawrapper
4. **Cost control** - monitoring a limity

## 🔧 Technické poznámky

### Dependencies
```bash
# Nové závislosti (requirements_ai.txt):
pip install openai httpx tenacity wordcloud matplotlib pillow numpy redis pyyaml
```

### File structure
```
src/ai/                          # ✅ HOTOVO
├── __init__.py                  # ✅ AI module exports
├── enhanced_openai_client.py    # ✅ OpenAI client
├── wordcloud_generator.py       # ✅ WordCloud generator
├── parameter_manager.py         # ✅ Parameter management
├── prompt_manager.py            # ✅ Prompt management
├── scenario_generator.py        # ✅ Scenario generation
├── ai_manager.py               # ✅ Central coordinator
├── integration.py              # 🔄 Integration layer
└── prompts/                    # ✅ YAML templates
    ├── wordcloud_analysis.yaml
    └── survey_analysis.yaml

config/
└── ai_config.example.py        # ✅ Configuration example

docs/
├── AI_INTEGRATION.md           # ✅ Detailed docs
└── AI_COMPONENTS_SUMMARY.md    # ✅ Component summary
```

### Performance considerations
- Redis caching pro AI responses (volitelné)
- Rate limiting respektující OpenAI API limity
- Fallback mechanismy při chybách
- Cost tracking a monitoring

## 🎯 Success Criteria

### Minimální viable product:
- [ ] Menu 10 (WordCloud) funguje s reálnými daty
- [ ] AI integration je napojená na config_manager
- [ ] Basic error handling a cost limits
- [ ] Dokumentace je aktualizovaná

### Plná integrace:
- [ ] Všechna Menu 10-14 fungují
- [ ] AI funkce jsou napojené na všechny data typy
- [ ] Comprehensive testing
- [ ] Production-ready error handling
- [ ] User-friendly experience

## 💡 Tipy pro implementaci

1. **Začít s Menu 10** - WordCloud je nejjednodušší na implementaci
2. **Testovat postupně** - každou funkci otestovat před pokračováním
3. **Používat AI Manager** - centrální API pro všechny AI funkce
4. **Sledovat costs** - nastavit rozumné limity
5. **Dokumentovat změny** - aktualizovat docs průběžně

AI infrastruktura je připravená - stačí ji napojit! 🚀
