# Datawrapper Export Module - User Guide

## 📖 **Přehled**

Datawrapper Export Module umožňuje export všech grafů z Datawrapper složky do HTML souboru s filtrováním a plnou funkcionalitou sdílení.

## 🚀 **R<PERSON>lý start**

### **1. Příprava prostředí**

Ujistěte se, že máte nakonfigurované environment variables v `.env` souboru:

```bash
# Povinné
DATAWRAPPER_API_KEY=your_api_key_here

# Volitelné (doporučené)
DATAWRAPPER_TEAM_ID=57Zj-Xbm
DATAWRAPPER_LIMESURVEY_FOLDER_ID=329499
```

### **2. Instalace závislostí**

```bash
pip install jinja2>=3.1.0
```

### **3. Spuštění exportu**

1. Spusťte hlavní aplikaci: `python src/main.py`
2. Vyberte průzkum (Menu 1 a 2)
3. Zvolte **Menu 12 - Datawrapper Export**
4. Postupujte podle instrukcí

## 📋 **Menu 12 - Datawrapper Export**

### **Workflow procesu:**

```
1. 🔍 Test připojení k Datawrapper API
2. 📝 Konfigurace exportu:
   - Název průzkumu
   - Filtr podle jazyka (všechny/české/anglické)
   - Výstupní složka
   - Rekonfigurace grafů
3. 🚀 Spuštění exportu s progress indikátory
4. ✅ Zobrazení výsledků a statistik
```

### **Výstup:**

- **Soubor:** `priloha-grafy-[SURVEY_ID].html`
- **Umístění:** Zadaná výstupní složka (default: aktuální)
- **Obsah:** Všechny grafy s filtrováním a share funkcemi

## 🎯 **Funkce HTML výstupu**

### **Hlavní funkce:**

- **📊 Zobrazení všech grafů** ze složky
- **🔍 JavaScript filtrování** podle názvu grafu
- **📱 Responsive design** pro všechna zařízení
- **🔗 Share linky** pro každý graf
- **📥 Download funkce** (PNG, PDF, SVG)
- **🌍 Lokalizace** cs-CZ

### **Ovládání:**

- **Filtrování:** Zadejte text do vyhledávacího pole
- **Vymazání filtru:** Tlačítko "Vymazat filtr" nebo ESC
- **Klávesové zkratky:** Ctrl+F pro focus na filtr
- **Sdílení:** Klik na "🔗 Sdílet graf" u každého grafu

## ⚙️ **Pokročilé použití**

### **Programové použití:**

```python
from datawrapper_export import ExportManager

# Inicializace
export_manager = ExportManager()

# Export
result = export_manager.export_survey_charts(
    survey_id="123456",
    output_dir="exports",
    survey_name="Můj průzkum",
    language_filter="cs",  # nebo "en" nebo None
    force_reconfigure=True
)

if result.success:
    print(f"Export úspěšný: {result.output_file}")
else:
    print(f"Export selhal: {result.errors}")
```

### **Validace prostředí:**

```python
from datawrapper_export import validate_environment, display_validation_results

validation = validate_environment()
display_validation_results(validation)
```

## 🔧 **Konfigurace**

### **Environment Variables:**

| Proměnná | Povinná | Popis |
|----------|---------|-------|
| `DATAWRAPPER_API_KEY` | ✅ | API klíč pro Datawrapper |
| `DATAWRAPPER_TEAM_ID` | ⚠️ | ID týmu (doporučené) |
| `DATAWRAPPER_LIMESURVEY_FOLDER_ID` | ⚠️ | ID parent složky (doporučené) |

### **Metadata konfigurace grafů:**

Modul automaticky nastavuje:

```json
{
  "publish": {
    "locale": "cs-CZ",
    "chart-footer": {
      "data-download": true,
      "image-download": {
        "png": true,
        "pdf": true,
        "svg": true
      },
      "embed-link": true,
      "social-sharing": true
    }
  }
}
```

## 🐛 **Troubleshooting**

### **Časté problémy:**

#### **❌ "DATAWRAPPER_API_KEY není nastaven"**
- **Řešení:** Přidejte API klíč do `.env` souboru
- **Získání klíče:** Datawrapper → Account Settings → API tokens

#### **❌ "Složka pro survey nebyla nalezena"**
- **Řešení:** Zkontrolujte název složky v Datawrapper
- **Tip:** Složka musí obsahovat Survey ID v názvu

#### **❌ "Nebyly nalezeny žádné grafy"**
- **Řešení:** Ujistěte se, že složka obsahuje publikované grafy
- **Tip:** Zkontrolujte, zda jsou grafy ve správné složce

#### **❌ "Jinja2 není nainstalován"**
- **Řešení:** `pip install jinja2>=3.1.0`

#### **⚠️ "Graf není optimálně nakonfigurován"**
- **Řešení:** Povolte "Rekonfigurace grafů" v Menu 12
- **Alternativa:** Manuálně nastavte download options v Datawrapper

### **Debug kroky:**

1. **Validace prostředí:**
   ```python
   from datawrapper_export import validate_environment
   print(validate_environment())
   ```

2. **Test připojení:**
   ```python
   from datawrapper_export import ExportManager
   manager = ExportManager()
   print(manager.test_connection())
   ```

3. **Zobrazení složek:**
   ```python
   from datawrapper_export import DatawrapperExportClient
   client = DatawrapperExportClient()
   folders = client.get_folders()
   for folder in folders:
       print(f"{folder['name']} (ID: {folder['id']})")
   ```

## 📊 **Performance**

### **Doporučení:**

- **Malé projekty** (< 10 grafů): Rychlé zpracování
- **Střední projekty** (10-50 grafů): Optimální pro produkci
- **Velké projekty** (> 50 grafů): Zvažte filtrování podle jazyka

### **Optimalizace:**

- Použijte `language_filter` pro rychlejší zpracování
- Nastavte `force_reconfigure=False` pro rychlejší běh
- HTML používá lazy loading pro iframe elementy

## 🔒 **Bezpečnost**

### **API klíče:**
- Nikdy nesdílejte API klíče
- Používejte `.env` soubor (přidán do `.gitignore`)
- Pravidelně rotujte API klíče

### **Výstupní soubory:**
- HTML soubory neobsahují citlivá data
- Grafy jsou embedovány přes iframe z Datawrapper
- Share URL jsou veřejně přístupné

## 📈 **Monitoring**

### **Logy:**
Modul loguje do console a volitelně do souboru:

```python
from datawrapper_export import ExportLogger

logger = ExportLogger("my_export", "logs/export.log")
```

### **Metriky:**
- Počet zpracovaných grafů
- Doba zpracování
- Úspěšnost konfigurace
- Velikost výstupního souboru

## 🆘 **Podpora**

### **Kontakt:**
- **Issues:** GitHub repository
- **Dokumentace:** `docs/` složka
- **Příklady:** `test/` složka

### **Užitečné odkazy:**
- [Datawrapper API dokumentace](https://developer.datawrapper.de/)
- [Jinja2 dokumentace](https://jinja.palletsprojects.com/)
- [LimWrapp dokumentace](docs/)

---

**💡 Tip:** Pro nejlepší výsledky používejte konzistentní pojmenování grafů a složek v Datawrapper.
