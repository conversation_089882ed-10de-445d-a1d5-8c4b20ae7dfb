# Datawrapper Export Module - Task Breakdown

## 🎯 **Hlavní cíl**
Vytvoření modulu pro export všech grafů z Datawrapper složky do HTML souboru s filtrováním a plnou funkcionalitou sdílení.

## 📋 **Task List - 10 úkolů**

### **1. Datawrapper API Client** 
**UUID:** `xf7Vz4asnE33Xz4cV1U3MM`
- **Popis:** Vytvoření API klienta pro komunikaci s Datawrapper API
- **Deliverables:**
  - `src/datawrapper_export/datawrapper_client.py`
  - Metody: `get_folders()`, `get_charts_in_folder()`, `get_chart_metadata()`, `update_chart_metadata()`, `publish_chart()`, `unpublish_chart()`
  - Error handling a retry mechanismus
  - Rate limiting support

### **2. Chart Collector**
**UUID:** `wCvVmwKJGP1xpTmdmwK6gk`
- **Popis:** Modul pro sběr všech grafů z Datawrapper složky podle survey ID
- **Deliverables:**
  - `src/datawrapper_export/chart_collector.py`
  - Automatické zjištění folder ID podle survey ID
  - Sběr všech grafů ze složky
  - Filtrování podle typu/jazyka (volitelné)

### **3. Chart Configurator**
**UUID:** `gHuHT6VjWfZb57du6pHHG9`
- **Popis:** Konfigurace metadata grafů - locale cs-CZ, download options, sharing, social media
- **Deliverables:**
  - `src/datawrapper_export/chart_configurator.py`
  - Nastavení locale na cs-CZ
  - Aktivace download options (PNG, PDF, SVG)
  - Aktivace embed link a social sharing
  - Republish workflow (unpublish → configure → publish)

### **4. HTML Template Engine**
**UUID:** `4ynzvECsW8SBX4MzVpTumm`
- **Popis:** Vytvoření HTML template s responsive designem, JavaScript filtrováním a chart embedding
- **Deliverables:**
  - `src/datawrapper_export/templates/chart_export.html`
  - `src/datawrapper_export/html_generator.py`
  - Responsive CSS design
  - JavaScript filtrování grafů podle názvu
  - Iframe embedding s plnou funkcionalitou
  - Share URL linky

### **5. Export Manager**
**UUID:** `1bm5eLsfEXu3BbjYRa2b8m`
- **Popis:** Hlavní orchestrace - koordinace všech komponent a workflow řízení
- **Deliverables:**
  - `src/datawrapper_export/export_manager.py`
  - Hlavní workflow orchestrace
  - Progress reporting
  - Error aggregation
  - File output management

### **6. Menu 12 Integration**
**UUID:** `9M3HYQLkXHrjUMjgdUZkjt`
- **Popis:** Integrace do hlavního menu jako Menu 12 - Datawrapper Export s user workflow
- **Deliverables:**
  - Úprava `src/main.py` - přidání Menu 12
  - User interface pro zadání Survey ID
  - Progress indikátory
  - Success/error reporting
  - File location output

### **7. Test Phase 1 - Single Chart**
**UUID:** `5MXmSQd8P4NooNNHyj9vhY`
- **Popis:** Testování: vytvoření testovacího grafu, nastavení metadata, generování HTML s jedním grafem
- **Deliverables:**
  - Test script pro vytvoření testovacího grafu
  - Test konfigurace metadata
  - Test HTML generování s jedním grafem
  - Validace výstupního HTML
  - Test všech download funkcí

### **8. Test Phase 2 - Folder Integration**
**UUID:** `n7nwZrooSyHp7KBzBjgsXU`
- **Popis:** Testování: napojení na složku, cyklus přes grafy, filtrování, kompletní HTML výstup
- **Deliverables:**
  - Test s reálnou Datawrapper složkou
  - Test zpracování více grafů
  - Test JavaScript filtrování
  - Performance testing
  - End-to-end workflow test

### **9. Error Handling & Polish**
**UUID:** `6Wr59qADN5Qatnd94wqFVz`
- **Popis:** Implementace error handling, logging, user feedback a finální optimalizace
- **Deliverables:**
  - Comprehensive error handling
  - Logging systém
  - User-friendly error messages
  - Performance optimalizace
  - Code cleanup a dokumentace

### **10. Documentation & Examples**
**UUID:** `bZkimT8FNPBknFZ1GECgDa`
- **Popis:** Kompletní dokumentace, příklady použití a user guide pro modul
- **Deliverables:**
  - User guide pro Menu 12
  - API dokumentace
  - Příklady použití
  - Troubleshooting guide
  - Configuration reference

## 🔄 **Implementační pořadí**

### **Fáze 1: Core Infrastructure (Tasks 1-3)**
1. Datawrapper API Client
2. Chart Collector  
3. Chart Configurator

### **Fáze 2: Output Generation (Tasks 4-5)**
4. HTML Template Engine
5. Export Manager

### **Fáze 3: Integration (Task 6)**
6. Menu 12 Integration

### **Fáze 4: Testing (Tasks 7-8)**
7. Test Phase 1 - Single Chart
8. Test Phase 2 - Folder Integration

### **Fáze 5: Polish (Tasks 9-10)**
9. Error Handling & Polish
10. Documentation & Examples

## 🎯 **Success Criteria**

### **Funkční požadavky:**
- ✅ Menu 12 funguje a je přístupné z hlavního menu
- ✅ Automatické zjištění Datawrapper složky podle Survey ID
- ✅ Konfigurace všech grafů s požadovanými metadata
- ✅ Generování HTML s JavaScript filtrováním
- ✅ Všechny download a sharing funkce aktivní

### **Technické požadavky:**
- ✅ Robust error handling
- ✅ Performance optimalizace
- ✅ Clean code a dokumentace
- ✅ Comprehensive testing
- ✅ User-friendly interface

### **Výstupní soubor:**
- ✅ `priloha-grafy-[SURVEY_ID].html`
- ✅ Responsive design
- ✅ JavaScript filtrování
- ✅ Všechny grafy s plnou funkcionalitou
- ✅ Share URL pro každý graf

## 🚀 **Ready for Implementation**

Všechny tasky jsou připravené k implementaci. Můžeme začít novou session a postupně implementovat podle fází.

**Doporučené pořadí pro novou session:**
1. Začít s Task 1 (Datawrapper API Client)
2. Pokračovat Task 2 (Chart Collector)
3. Implementovat Task 3 (Chart Configurator)

Každý task má jasně definované deliverables a success criteria pro efektivní implementaci.
