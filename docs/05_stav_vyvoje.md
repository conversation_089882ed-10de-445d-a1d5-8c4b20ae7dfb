# Stav vývoje

## Aktu<PERSON>lní verze: 0.2.0 - AI Enhanced! 🚀

### 🎉 MAJOR UPDATE - Leden 2025: AI Komponenty Integrovány!

**Převzato z existujících projektů:**
- ✅ Enhanced OpenAI Client s rate limiting a cost tracking
- ✅ WordCloud Generator s AI enhancement (3 typy vizualizací)
- ✅ Parameter Manager s validací a historií změn
- ✅ Prompt Manager s YAML templates
- ✅ Scenario Generator pro plánování analýz
- ✅ AI Manager jako centr<PERSON><PERSON> koordin<PERSON>tor
- ✅ Kompletní dokumentace a konfigurace

**Nové možnosti:**
- 🎨 WordCloud generování s AI analýzou textu
- 🤖 Automatické návrhy parametrů pro analýzy
- 📊 Generování analytických scénářů
- 💰 Cost tracking a usage monitoring
- 🛡️ Fallback mechanismy a error handling

### Probíhající práce

- ✅ **Menu 13 - Hierarchická analýza dokončeno** (leden 2025)
- 🔄 Integrace AI komponent do existujícího menu systému
- 🔄 Testování AI funkcí s reálnými survey daty
- 🔄 Vytvoření AI menu položek (Menu 10-15)
- Implementace autentizace API (limesurvey_client, config_loader)
- Revize dokumentace (md soubory)

### Dokončené úkoly

- ✅ **Menu 13 - Hierarchická analýza průzkumů** (leden 2025)
  - Vazba na aktuální server a survey_id
  - Respektování hierarchie a pořadí skupin/otázek
  - HTML reporty s tooltips pro typy otázek a analýz
  - Oprava parsování group_order z LimeSurvey API
- ✅ **AI Infrastructure kompletně převzata a integrována**
- ✅ Inicializace Git repozitáře
- ✅ Vytvoření základní struktury projektu
- ✅ Implementace základní třídy pro komunikaci s API
- ✅ Ošetření načítání .env souboru v absence `python-dotenv`
- ✅ Ošetření importu pandas v absence `pandas`
- ✅ Ošetření EOFError v CLI menu
- ✅ Konfigurace CI/CD pipeline
- ✅ Základní implementace GUI modulů (PyQt5)

### Plánované úkoly - Fáze 1 (Konsolidace + AI Integrace)

**Okamžité (leden 2025):**
- ✅ **BONUS: Převzetí AI komponent** (HOTOVO!)
- [ ] Integrace AI do menu systému (Menu 10-15)
- [ ] Testování AI funkcí s reálnými daty
- [ ] Aktualizace dokumentace (README, user guide)
- [ ] Stabilizace CLI menu a oprava všech známých bugů
- [ ] Rozšíření unit testů na 80% pokrytí (včetně AI)

**Krátkodobé (únor-březen 2025):**
- [ ] AI překlad JSON struktur (infrastruktura hotová)
- [ ] AI komentáře grafů (infrastruktura hotová)
- [ ] WordCloud integrace do Datawrapper workflow
- [ ] Přidání cachování (response cache, Datawrapper cache)
- [ ] Filtrování průzkumů podle parametrů
- [ ] Implementace anonymizace citlivých dat

### Plánované úkoly - Fáze 2+ (Q2 2025+)

**Infrastruktura:**
- [ ] Podpora více LimeSurvey serverů
- [ ] Rozšířená metadata a konfigurace
- [ ] Optimalizace výkonu

**Rozšíření funkcí:**
- [ ] Nové typy grafů (tabulky, koláče, skupinové)
- ✅ **AI funkce základy** (infrastruktura hotová!)
  - [ ] AI překlad (připraveno k integraci)
  - [ ] AI komentáře (připraveno k integraci)
  - [ ] AI analýzy textů (připraveno k integraci)
- [ ] GUI rozšíření s AI chatbotem
- ✅ **WordCloud grafy** (hotové, připravené k integraci)

Detailní plán viz [06_plan_rozsireni.md](06_plan_rozsireni.md)

## Detailní popis aktuálního úkolu

### Refaktorizace a zajištění spustitelnosti aplikace

**Cíl:**

Zajistit, aby aplikace nepadala při chybějících závislostech a podporovala piped input, a aktualizovat stávající kód pro stabilní běh.

**Kroky implementace:**

1. Přidat lazy import pro `python-dotenv` a `pandas` s fallbackem.  
2. Odebrat anotace odkazující na `pd.DataFrame` pro kompatibilitu bez pandas.  
3. Ošetřit `EOFError` při čtení vstupu (`input()`) v src/main.py a funkcích CLI.  
4. Aktualizovat jednotkové testy (`test_transformer.py`, `test_cli.py` atd.) pro nové chování.  
5. Spustit CI pipeline a zajistit úspěšné spuštění všech testů.  

**Relevantní soubory:**

- src/main.py  
- src/data_transformer.py  
- src/limesurvey_client.py  
- src/config_loader.py  
- src/cli.py  
- test_transformer.py  
- test_cli.py  

**Testovací scénáře:**

- Spuštění CLI menu interaktivně i s piped input.  
- Volání funkcí bez nainstalovaného `python-dotenv` a `pandas`.  
- Ověření, že testy proběhnou bez chyb (`pytest`).  

**Příkazy pro aider:**

```
/edit src/data_transformer.py src/main.py src/limesurvey_client.py src/config_loader.py
/run pytest -q
