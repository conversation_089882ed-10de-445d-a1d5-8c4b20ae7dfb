# Privacy Filter - <PERSON><PERSON>rana soukromí při zpracování dat

## 🔒 <PERSON><PERSON><PERSON>led

Privacy Filter je centrální systém pro filtrování citlivých sloupců v CSV datech z LimeSurvey průzkumů. Umožňuje uživateli jednoduše vybrat sloupce, které se mají vyloučit ze zpracování kvůli ochraně soukromí.

## ✨ Klíčové vlastnosti

- **Jednoduché nastavení** - <PERSON><PERSON><PERSON><PERSON><PERSON> sloupc<PERSON> pomocí čísel a rozsahů
- **Centrální filtrování** - Automatická aplikace ve všech modulech
- **Flexibilní konfigurace** - Kdykoliv změnitelné nastavení
- **Bezpečnost by default** - Citlivá data se nedostanou do LLM ani grafů
- **Transparentní** - <PERSON><PERSON><PERSON><PERSON> vid<PERSON>, co se zpracovává

## 🚀 J<PERSON> p<PERSON>í<PERSON>

### 1. Základní workflow

1. **Vyberte průzkum** (Menu 1 a 2)
2. **Otevřete Privacy Filter** (Menu 16)
3. **Zobrazte sloupce** (Akce 1)
4. **Nakonfigurujte vyloučení** (Akce 2)
5. **Pokračujte se zpracováním dat** (Menu 5, 6, 8)

### 2. Konfigurace vyloučených sloupců

**Formát zadávání:**
```
1-4,8,64-78    # Rozsahy a jednotlivá čísla
1,3,5,7        # Pouze jednotlivá čísla  
10-20          # Pouze rozsah
               # Prázdný vstup = žádné vyloučení
```

**Příklady použití:**
- `1-4` - Vyloučí sloupce 1, 2, 3, 4 (obvykle id, lastpage, submitdate, startdate)
- `1-4,8,64-78` - Vyloučí systémové sloupce + konkrétní citlivé otázky
- `50-100` - Vyloučí celou sekci (např. časové údaje na konci)

### 3. Typické scénáře

**Vyloučení systémových sloupců:**
```
1-10    # Obvykle: id, lastpage, submitdate, startdate, datestamp, atd.
```

**Vyloučení citlivých otázek:**
```
15,23,45-50    # Konkrétní otázky s emaily, telefony, jmény
```

**Vyloučení časových údajů:**
```
80-120    # Časové údaje na konci CSV (grouptime1, grouptime2, atd.)
```

## 🔧 Technické detaily

### Architektura

```
privacy_filter.py
├── PrivacyFilter - Hlavní třída
├── get_privacy_filter() - Factory funkce
├── apply_privacy_filter() - Centrální aplikace
└── filter_csv_with_privacy() - CSV filtrování
```

### Integrace s moduly

**data_transformer.py:**
- `transform_to_long_format()` - Automatické filtrování CSV
- `generate_chart_data()` - Filtrování otázek v chart_data

**Ostatní moduly:**
- Použití centrálních funkcí `apply_privacy_filter()` a `get_privacy_filter()`

### Ukládání nastavení

**Soubor:** `data/{survey_id}/privacy_filter.json`

**Struktura:**
```json
{
  "survey_id": "827822",
  "excluded_columns": [1, 2, 3, 4, 8, 64, 65, 66, 67, 68],
  "created": "user_configured",
  "description": "Sloupce vyloučené z zpracování kvůli ochraně soukromí"
}
```

## 📊 Menu 16 - Ochrana soukromí

### Dostupné akce

1. **👁️ Zobrazit všechny sloupce**
   - Přehled všech sloupců s jejich čísly
   - Označení vyloučených sloupců

2. **⚙️ Nastavit vyloučené sloupce**
   - Konfigurace pomocí rozsahů
   - Orientační přehled sloupců
   - Okamžité uložení

3. **🧹 Vymazat všechna nastavení**
   - Reset na výchozí stav
   - Potvrzení před smazáním

4. **📄 Zobrazit souhrn nastavení**
   - Detailní informace o konfiguraci
   - Názvy vyloučených sloupců

5. **🧪 Test filtrování (náhled)**
   - Náhled efektu filtru
   - Bez změny dat

## 🛡️ Bezpečnostní aspekty

### Co privacy filter chrání

- **Osobní údaje** - Emaily, telefony, jména, adresy
- **Systémové informace** - ID respondentů, časové značky
- **Citlivé odpovědi** - Volné textové odpovědi s osobními údaji
- **Metadata** - Technické informace o vyplňování

### Kde se aplikuje

- ✅ **Transformace dat** - Filtrování při převodu na long formát
- ✅ **Generování grafů** - Vyloučení z chart_data.json
- ✅ **AI analýzy** - Citlivá data se nepošlou do LLM
- ✅ **Export** - Filtrované data v HTML reportech

### Kde se NEAPLIKUJE

- ❌ **Původní CSV** - Zůstává nezměněn
- ❌ **LSS struktura** - Struktura průzkumu se nemění
- ❌ **Překlady** - Translation manager pracuje se všemi daty

## 💡 Doporučené postupy

### 1. Standardní konfigurace

**Pro většinu průzkumů:**
```
1-4,64-78    # Systémové sloupce + časové údaje
```

**Pro citlivé průzkumy:**
```
1-10,15,23,45-50,64-100    # Rozšířené vyloučení
```

### 2. Identifikace citlivých sloupců

**Kontrolní seznam:**
- [ ] Sloupce s "email" v názvu
- [ ] Sloupce s "phone", "telefon" v názvu  
- [ ] Sloupce s "name", "jmeno" v názvu
- [ ] Dlouhé textové odpovědi
- [ ] Systémové sloupce (id, lastpage, submitdate)
- [ ] Časové údaje (grouptime1, grouptime2, atd.)

### 3. Testování konfigurace

1. **Použijte akci 5** pro náhled efektu
2. **Zkontrolujte chart_data.json** - měl by obsahovat jen bezpečné otázky
3. **Ověřte AI analýzy** - citlivá data by se neměla objevit

## 🔄 Workflow s privacy filtrem

```mermaid
graph TD
    A[Načtení dat - Menu 2] --> B[Konfigurace Privacy Filter - Menu 16]
    B --> C[Transformace dat - Menu 5]
    C --> D[Generování chart_data - Menu 6]
    D --> E[Generování grafů - Menu 8]
    E --> F[AI analýzy - Menu 20+]
    
    B --> G[Test filtrování - Akce 5]
    G --> B
```

## 🚨 Řešení problémů

### Časté problémy

**1. "Privacy filter není dostupný"**
- Zkontrolujte existenci `src/privacy_filter.py`
- Restartujte aplikaci

**2. "Nepodařilo se extrahovat survey_id"**
- Ujistěte se, že cesta obsahuje 6-místné číslo průzkumu
- Zkontrolujte formát cesty k souborům

**3. "Žádné sloupce nejsou vyloučeny"**
- Zkontrolujte nastavení v Menu 16, Akce 4
- Ověřte formát zadání rozsahu

### Debug informace

**Logy privacy filtru:**
```bash
grep "privacy_filter" logs/app.log
```

**Kontrola nastavení:**
```bash
cat data/{survey_id}/privacy_filter.json
```

## 📈 Výhody implementace

### Pro uživatele
- **Jednoduché ovládání** - Číslované sloupce místo složitých názvů
- **Flexibilita** - Kdykoliv změnitelné nastavení
- **Transparentnost** - Jasně viditelné, co se zpracovává
- **Bezpečnost** - Citlivá data se nedostanou ven

### Pro vývojáře
- **Centrální řešení** - Jedna konfigurace pro všechny moduly
- **Čistá integrace** - Minimální změny v existujícím kódu
- **Rozšiřitelnost** - Snadné přidání do nových modulů
- **Testovatelnost** - Kompletní test suite

## 🎯 Budoucí rozšíření

### Plánované funkce
- **Automatická detekce** - AI rozpoznávání citlivých sloupců
- **Šablony** - Přednastavené konfigurace pro různé typy průzkumů
- **Audit log** - Sledování změn v konfiguraci
- **Batch operace** - Aplikace na více průzkumů najednou

### Možná vylepšení
- **GUI konfigurace** - Grafické rozhraní pro výběr sloupců
- **Regex filtry** - Filtrování podle vzorů v názvech
- **Podmíněné filtrování** - Různé filtry pro různé operace
- **Export politik** - Sdílení konfigurací mezi projekty

---

*Privacy Filter v1.0 - Implementováno 2024 pro LimeSurvey Datawrapper Integration*