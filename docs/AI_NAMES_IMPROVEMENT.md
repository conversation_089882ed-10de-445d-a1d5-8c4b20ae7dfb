# AI Úprava Názvů pro Grafy

## P<PERSON>ehled

Nová funkce v Menu 10 (Správa překladů a úprav názvů) umožňuje automatickou AI úpravu názvů otázek tak, aby byly vhodné pro zobrazení v grafech.

## Funkce

### Přístup
- **Menu:** 10 → 7. 🤖 AI úprava názvů pro grafy
- **Požadavky:** OPENAI_API_KEY v .env souboru

### Možnosti

1. **Výběr jazyka:** Možnost vybrat cílový jazyk pro úpravu názvů
2. **Typ otázek:** 
   - <PERSON>uze hlav<PERSON> (bez podotázek)
   - Všechny názvy (včetně podotázek)
3. **AI optimalizace:** Automatická úprava názvů podle kritérií pro grafy

## Jak to funguje

### 1. Kontrola dostupnosti AI
```python
# Kontrola OPENAI_API_KEY a AI modulů
if not check_ai_availability():
    # Zobrazí návod na aktivaci
```

### 2. Analýza existujících názvů
```python
# Extrakce názvů z chart_data.json
strings = tm.extract_translatable_strings(chart_data_path)
question_names = strings.get('question_names', [])
```

### 3. AI zpracování
- **Dávkové zpracování:** Max 10 názvů najednou
- **Prompt optimalizace:** Specializovaný prompt pro úpravu názvů grafů
- **Kritéria úpravy:**
  - Stručnost (max 60 znaků)
  - Výstižnost
  - Gramatická správnost
  - Konzistentní terminologie
  - Vhodnost pro nadpisy grafů

### 4. Uložení a aplikace
- Uložení do `translations_{jazyk}.json`
- Možnost okamžité aplikace na `chart_data.json`
- Vytvoření souboru `chart_data_ai_improved_{jazyk}.json`

## Příklad použití

### Vstup
```
"Jak hodnotíte kvalitu našich služeb v oblasti zákaznického servisu a podpory klientů?"
```

### AI úprava
```
"Kvalita zákaznického servisu"
```

### Výsledek
- Kratší a výstižnější název
- Vhodný pro zobrazení v grafu
- Zachovává význam původní otázky

## Technické detaily

### AI Model
- **Výchozí:** gpt-4o-mini (ekonomický)
- **Fallback:** gpt-4o, gpt-4
- **Parametry:** temperature=0.3, max_tokens=2000

### Náklady
- **gpt-4o-mini:** ~$0.0001-0.0005 za dávku 10 názvů (velmi levné!)
- **gpt-4o:** ~$0.01-0.05 za dávku 10 názvů
- **Zobrazení:** Automatické zobrazení nákladů po dokončení

### Bezpečnost
- **Fallback:** Pokud AI selže, zachovají se původní názvy
- **Validace:** JSON parsování s error handlingem
- **Backup:** Původní data zůstávají nedotčena

## Integrace s existujícím systémem

### TranslationManager
```python
# Využívá existující infrastrukturu překladů
tm = TranslationManager(survey_id)
tm.translations['question_names'][original] = improved
tm.save_translations()
```

### Kompatibilita
- **Zpětná kompatibilita:** Funguje s existujícími překlady
- **Jazykové verze:** Podporuje všechny dostupné jazyky
- **Menu integrace:** Bezproblémové začlenění do Menu 10

## Konfigurace

### .env soubor
```bash
# Povinné pro AI funkce
OPENAI_API_KEY=sk-your-api-key-here

# Volitelné pro cache
REDIS_URL=redis://localhost:6379/0
```

### Závislosti
```bash
# AI moduly
pip install -r requirements_ai.txt

# Nebo jednotlivě
pip install openai httpx tenacity
```

## Řešení problémů

### AI není dostupné
```
❌ AI funkce nejsou dostupné
💡 Pro aktivaci AI funkcí:
   1. Nainstalujte závislosti: pip install -r requirements_ai.txt
   2. Nastavte OPENAI_API_KEY v .env souboru
   3. Restartujte aplikaci
```

### Chyba při zpracování
- **Dávkové zpracování:** Pokračuje i při chybě jedné dávky
- **Fallback:** Zachovává původní názvy při chybě
- **Logging:** Detailní error reporting

### Neočekávané výsledky
- **Kontrola:** Zobrazení ukázky úprav před uložením
- **Potvrzení:** Uživatel musí potvrdit aplikaci změn
- **Backup:** Původní data zůstávají zachována

## Budoucí rozšíření

### Možné vylepšení
1. **Vlastní prompty:** Možnost definovat vlastní kritéria úpravy
2. **Batch konfigurace:** Nastavení velikosti dávek
3. **Model selection:** Výběr AI modelu pro zpracování
4. **Kontextová úprava:** Zohlednění typu grafu při úpravě

### Integrace
- **Analysis Engine:** Propojení s metadata systémem
- **Chart Generator:** Automatická úprava při generování grafů
- **Export systém:** Integrace s Datawrapper exportem

## Testování

### Automatické testy
```bash
# Test AI funkcí
python test_ai_names_improvement.py

# Test integrace
python test_menu_integration.py
```

### Manuální test
1. Spusťte hlavní aplikaci: `python src/main.py`
2. Vyberte průzkum (Menu 1, 2)
3. Vygenerujte chart_data.json (Menu 6)
4. Přejděte do Menu 10 → 7
5. Postupujte podle instrukcí

## Závěr

AI úprava názvů je výkonný nástroj pro optimalizaci názvů otázek pro zobrazení v grafech. Využívá pokročilé AI modely pro vytvoření stručných, výstižných a gramaticky správných názvů, které jsou ideální pro vizualizace dat.

Funkce je plně integrována do existujícího systému překladů a zachovává zpětnou kompatibilitu se všemi stávajícími funkcemi.