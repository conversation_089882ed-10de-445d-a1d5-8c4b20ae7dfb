# 🚀 Nastavení LimWrapp na novém PC

## 📋 <PERSON><PERSON><PERSON><PERSON> start

### 1. Klonování projektu
```bash
git clone https://github.com/rousarp/limwrapp.git
cd limwrapp
```

### 2. Vytvoření virtual environment
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# nebo
.venv\Scripts\activate     # Windows
```

### 3. Instalace závislostí
```bash
pip install -r requirements.txt
```

### 4. Konfigurace prostředí
```bash
cp .env .env.local
# Edituj .env.local s tvými API klíči
```

### 5. Spuštění aplikace
```bash
cd src
python main.py
```

## 🔧 Konfigurace API klíčů

### Datawrapper API
```bash
# V .env.local
DATAWRAPPER_API_TOKEN=your_token_here
DATAWRAPPER_TEAM_ID=57Zj-Xbm
DATAWRAPPER_LIMESURVEY_FOLDER_ID=329499
```

### LimeSurvey API (volitelné)
```bash
# V .env.local
LIMESURVEY_URL=https://your-limesurvey.com/admin/remotecontrol
LIMESURVEY_USERNAME=your_username
LIMESURVEY_PASSWORD=your_password
```

## 📁 Struktura projektu

```
limwrapp/
├── src/                          # Hlavní zdrojové kódy
│   ├── main.py                   # CLI aplikace
│   ├── gui.py                    # GUI aplikace
│   ├── data_transformer.py       # Transformace dat
│   ├── enhanced_chart_generator.py # Generování grafů
│   ├── translation_manager.py    # Správa překladů
│   └── ...
├── data/                         # Data průzkumů
├── charts/                       # Vygenerované grafy
├── test_*.py                     # Testovací soubory
└── docs/                         # Dokumentace
```

## 🎯 Hlavní funkcionality

### CLI Menu (python main.py)
1. **Import CSV/LSS** - Import dat z LimeSurvey
2. **Transformace dat** - Převod do long formátu
3. **Mapování otázek** - Analýza struktury
4. **Generování grafů** - Vytvoření Datawrapper grafů
5. **Překlady** - Vícejazyčné grafy
6. **GUI** - Grafické rozhraní

### GUI aplikace (python gui.py)
- Grafické rozhraní pro všechny funkce
- Drag & drop pro soubory
- Vizuální správa průzkumů
- Integrované náhledy

## 🔥 Nejnovější funkce

### ✅ Opravené LSS pořadí legend
- Array otázky: rozhodně ano → spíše ano → spíše ne → rozhodně ne
- Single/Multiple choice: Ano → Ne (logické pořadí)

### ✅ Opravené řazení řádků v grafech
- Řazení podle prvního sloupce (sestupně)
- Nejvyšší hodnoty nahoře

### ✅ Selektivní generování grafů
- Zobrazení všech grafů s čísly
- Možnost výběru konkrétních grafů (např. '1,3,5')
- Rychlé testování

## 🧪 Testování

### Spuštění testů
```bash
# Test LSS pořadí
python test_ano_ne_order.py

# Test řazení řádků
python test_row_sorting_complete.py

# Test selektivního generování
python test_selective_chart_generation.py

# Test GUI
python test_gui.py
```

### Testovací data
- Průzkum 827822 je připraven pro testování
- Data v `src/data/827822/`
- Obsahuje všechny typy otázek

## 📊 Workflow pro grafy

### 1. Příprava dat
```bash
# Menu 1: Import CSV/LSS
# Menu 2: Transformace do long formátu
# Menu 3: Mapování otázek
# Menu 6: Generování chart_data.json
```

### 2. Generování grafů
```bash
# Menu 8: Enhanced Chart Generation
# - Zobrazí všechny grafy s čísly
# - Možnost výběru konkrétních grafů
# - Automatické PNG stažení
```

### 3. Překlady
```bash
# Menu 10: Translation Management
# - Vytvoření překladových slovníků
# - Aplikace překladů na grafy
# - Vícejazyčné verze
```

## 🔧 Řešení problémů

### Chybí pandas
```bash
pip install pandas
```

### Chybí tkinter (Linux)
```bash
sudo apt-get install python3-tk
```

### API chyby
- Zkontroluj API klíče v .env.local
- Ověř připojení k internetu
- Zkontroluj Datawrapper team ID

### Data chyby
- Zkontroluj formát CSV (UTF-8)
- Ověř strukturu LSS souboru
- Spusť Menu 1 pro reimport

## 📝 Poznámky

- Projekt používá Python 3.8+
- Všechny změny jsou automaticky uloženy
- Data průzkumů se neukládají do gitu
- Testovací soubory jsou pro vývoj

## 🎯 Pokračování práce

1. **Klonuj projekt** na novém PC
2. **Nastav virtual environment**
3. **Zkopíruj .env.local** s API klíči
4. **Spusť Menu 8** pro test grafů
5. **Pokračuj v práci** tam, kde jsi skončil

**Projekt je plně připraven pro pokračování!** 🚀
