# Soubor Promptů pro Metodiku Programování s AI

Tento soubor obsahuje sadu promptů pro AI, k<PERSON><PERSON> slouž<PERSON> k <PERSON>ízení vývoje softwaru podle definované metodiky.


## Prompt 0: Generování yml struktury pro AI
**Popis:** Tento prompt se používá pro vytvoření struktury projektu v formátu yml, který slouží jako vstup pro generování dokumentace.
```
Na základě následujícího slovního popisu projektu vygeneruj detailní parametry projektu ve formátu YAML nebo JSON. Zaměř se na extrahování informací o cíli a účelu programu, cílové platformě, programovacím jazyce, vzhledu, klíčových funkcionalitách a nefunkčních požadavcích. Pokud informace nejsou v popisu explicitně uvedeny, použij rozumné výchozí hodnoty nebo oz<PERSON>, že je potřeba upřesnit.

**Slovní popis projektu:** [Zde uveďte slovní popis projektu od uživatele]

**Výchozí hodnoty:**
*   Programovací jazyk: Python
*   Cílová platforma: Webový prohlížeč
*   Správa verzí: Git
*   Nástroj pro asistenci s kódem: cline
*   GUI: Moderní, trendové

**Formát výstupu:** YAML

**Klíče pro parametry:**
*   nazev_projektu:
*   cil_a_ucel:
*   cileova_platforma:
*   programovaci_jazyk:
*   vzhled_ui:
*   klicove_funkcionality:
*   nefunkcni_pozadavky:
    *   vykon:
    *   bezpecnost:
    *   skalovatelnost:
    *   spolehlivost:
    *   pouzitelnost:
*   externi_zavislosti:
*   konvence_pro_kodovani:
*   sprava_verzi:
*   asistent_kodovani:

```


## Prompt 1: Generování Základní Dokumentace pro Nový Projekt

**Popis:** Tento prompt se používá na začátku nového projektu. Na základě stručného popisu projektu vygeneruje AI základní sadu dokumentů, které definují cíle, požadavky, architekturu a plán vývoje.
```
Generuj sadu dokumentů pro řízení vývoje softwaru s následujícími parametry projektu:

**Parametry projektu (načtené z `project_params.yaml`):**
```yaml
nazev_projektu: [Název projektu]
cil_a_ucel: [Detailní popis cíle a účelu programu]
cileova_platforma: [Cílová platforma]
programovaci_jazyk: [Programovací jazyk]
vzhled_ui: [Popis vzhledu a uživatelského rozhraní]
klicove_funkcionality: [Seznam klíčových funkcionalit]
nefunkcni_pozadavky:
  vykon: [Požadavky na výkon]
  bezpecnost: [Požadavky na bezpečnost]
  skalovatelnost: [Požadavky na škálovatelnost]
  spolehlivost: [Požadavky na spolehlivost]
  pouzitelnost: [Požadavky na použitelnost]
externi_zavislosti: [Seznam externích závislostí]
konvence_pro_kodovani: [Preferované konvence pro kódování]
sprava_verzi: [Systém správy verzí]
asistent_kodovani: [Nástroj pro asistenci s kódem]
```
```
**Struktura generované dokumentace:**

Vytvořte následující soubory v Markdown formátu (.md):

1. **`readme.md`:**
    *   **Název projektu:** [nazev\_projektu]
    *   **Stručný popis:** Stručně shrňte cíl a účel projektu.
    *   **Základní informace o použití:**  Základní instrukce pro spuštění nebo použití programu (pokud je to relevantní v této fázi).
    *   **Odkaz na dokumentaci:** Odkaz na soubor `00_projekt.md` pro detailnější informace.

2. **`changelog.md`:**
    *   # Changelog
    *   Všechny významné změny v tomto projektu budou dokumentovány v tomto souboru.
    *   ## [0.1.0] - [Aktuální datum]
        *   Projekt inicializován.
        *   Vygenerována základní dokumentace.

3. **`00_projekt.md`:**
    *   **Název projektu:** [nazev\_projektu]
    *   **Datum vytvoření:** [Aktuální datum]
    *   **Popis projektu:** Rozpracujte `cil_a_ucel`.
    *   **Cílová skupina uživatelů:** Detailně popište cílovou skupinu uživatelů.
    *   **Přínosy projektu:** Popište, jaké přínosy projekt přinese.

4. **`01_poadavky.md`:**
    *   **Funkční požadavky:** Rozepište `klicove_funkcionality` do detailních funkčních požadavků.
    *   **Uživatelské případy (Use Cases):** Pro klíčové funkční požadavky vytvořte základní uživatelské případy.
    *   **Nefunkční požadavky:** Rozepište detaily z `nefunkcni_pozadavky`.

5. **`02_navrh_architektury.md`:**
    *   **Vysokoúrovňový návrh architektury:** Popište základní architekturu programu.
    *   **Návrh datového modelu (základní):** Popište základní entity a vztahy v datovém modelu.
    *   **Technologický zásobník:** Uveďte `programovaci_jazyk` a další relevantní technologie.

6. **`03_plan_vyvoje.md`:**
    *   **Rozdělení na fáze/iterace:** Navrhněte rozdělení vývoje na menší fáze nebo iterace.
    *   **Seznam úkolů pro první fázi:** Pro první fázi vývoje vytvořte detailní seznam úkolů.
    *   **Metodika sledování postupu:** Popište, jak bude sledován postup vývoje.

7. **`04_cline_pokyny.md`:**
    *   **Základní pracovní postup s `cline`:** Popište základní pracovní postup pro použití nástroje `cline`.
    *   **Doporučené příkazy `cline`:** Uveďte příklady doporučených příkazů `cline`.
    *   **Integrace s dokumentací:** Popište, jak by se měla dokumentace používat při práci s `cline`.

8. **`05_stav_vyvoje.md`:**
    *   **Aktuální fáze:** Uveďte aktuální fázi vývoje.
    *   **Dokončené úkoly:** Seznam dokončených úkolů.
    *   **Rozpracované úkoly:** Seznam aktuálně rozpracovaných úkolů.
    *   **Čekající úkoly:** Seznam úkolů, které čekají na implementaci.
    *   **Problémy a otevřené otázky:** Seznam aktuálních problémů a otevřených otázek.
```
## Prompt 3: Pokyny pro další postup (pro AI)
Popis: Po vygenerování úvodní dokumentace, v každém dalším kroku vývoje:
```
1. **Načti do paměti soubory:** `readme.md`, `changelog.md`, `00_projekt.md`, `01_poadavky.md`, `02_navrh_architektury.md`, `03_plan_vyvoje.md`, `04_cline_pokyny.md`, `05_stav_vyvoje.md`.
2. **Přečti soubor `03_plan_vyvoje.md` a identifikuj další úkol k implementaci.**
3. **Vytvoř detailní popis tohoto úkolu, včetně:**
    *   **Odkazu na relevantní funkční požadavky a části návrhu architektury.**
    *   **Konkrétních kroků, které je potřeba provést k implementaci úkolu.**
    *   **Návrhů na implementaci (např. jaké soubory vytvořit nebo upravit, jaké funkce implementovat).**
    *   **Případných testovacích scénářů pro ověření funkčnosti.**
    *   **Doporučených příkazů pro `cline` pro provedení úkolu.**
4. **Aktualizuj soubor `05_stav_vyvoje.md` s informacemi o zahájení práce na úkolu.**
5. **Po dokončení úkolu:**
    *   **Aktualizuj soubor `05_stav_vyvoje.md` s informací o dokončení úkolu a odkazem na relevantní změny v kódu.**
    *   **Zvaž, zda je potřeba aktualizovat další části dokumentace (např. `01_poadavky.md`, `02_navrh_architektury.md`) v závislosti na provedených změnách.**
    *   **Identifikuj další úkol z `03_plan_vyvoje.md` a opakuj proces.**

**Pokračování po přerušení vývoje:**

Pro pokračování vývoje po přerušení:

1. **Načti do paměti soubory:** `readme.md`, `changelog.md`, `00_projekt.md`, `01_poadavky.md`, `02_navrh_architektury.md`, `03_plan_vyvoje.md`, `04_cline_pokyny.md`, `05_stav_vyvoje.md`.
2. **Přečti soubor `05_stav_vyvoje.md` pro zjištění aktuálního stavu vývoje a rozpracovaných úkolů.**
3. **Pokračuj v implementaci rozpracovaného úkolu nebo vyber další úkol z `03_plan_vyvoje.md`.**


## Prompt 2: Migrace Existujícího Projektu na Novou Metodiku

**Popis:** Tento prompt se používá pro převedení existujícího softwarového projektu s neaktuální dokumentací na nově definovanou metodiku vývoje. Pomáhá sjednotit kód a dokumentaci a zahájit iterativní vývoj.


Vytvoř metodiku pro převedení existujícího softwarového projektu s neaktuální dokumentací na nový způsob vývoje řízený dokumentací, jak je popsáno v předchozích instrukcích. Cílem je sjednotit kód a dokumentaci a zahájit iterativní vývoj s pomocí nástrojů jako `aider` nebo `cline`.

**Vstupní informace:**

*   **Adresář s existujícím kódem projektu:** [Zde uveďte cestu k adresáři s kódem projektu]
*   **Adresář s existující dokumentací (pokud existuje):** [Zde uveďte cestu k adresáři s existující dokumentací, pokud existuje, jinak uveďte "žádná"]
*   **Slovní popis aktuálního stavu projektu:** [Stručně popište aktuální stav projektu, jeho hlavní funkcionality, a známé odchylky mezi kódem a dokumentací.]
*   **Technologie použité v projektu:** [Vyjmenujte programovací jazyky, frameworky, knihovny a další technologie použité v projektu.]
*   **Preferovaný nástroj pro asistenci s kódem:** [Uveďte preferovaný nástroj pro asistenci s kódem, např. `aider` nebo `cline`. Defaultně `cline`.]
*   **Strategie načítání souborů:** [Určete strategii načítání souborů pro analýzu. Možnosti:
    *   `všechny`: Načíst všechny soubory v adresáři s kódem.
    *   `podle_koncovek`: Načíst soubory s uvedenými koncovkami (např. `.py`, `.js`, `.html`). Uveďte seznam koncovek: [seznam koncovek].
    *   `podle_adresářů`: Načíst soubory v uvedených adresářích. Uveďte seznam adresářů: [seznam adresářů].
    *   `inteligentní`: Nechat AI rozhodnout, které soubory jsou relevantní pro analýzu (na základě typu souborů, obsahu, atd.).
]

**Kroky pro převedení projektu:**

1. **Analýza existujícího projektu:**
    *   Na základě zadané strategie načítání souborů načti relevantní soubory s kódem do paměti.
    *   Prozkoumej strukturu adresářů a souborů projektu.
    *   Identifikuj hlavní programovací jazyky a technologie použité v projektu.
    *   Pokus se automaticky extrahovat informace o hlavních funkcionalitách a komponentách projektu z kódu (např. názvy tříd, funkcí, modulů).
    *   Pokud existuje, načti existující dokumentaci a pokus se identifikovat její strukturu a obsah.

2. **Generování nové dokumentace (počáteční verze):**
    *   Vytvoř základní sadu dokumentace podle struktury definované v předchozích instrukcích (`readme.md`, `changelog.md`, `00_projekt.md`, `01_poadavky.md`, `02_navrh_architektury.md`, `03_plan_vyvoje.md`, `04_cline_pokyny.md`, `05_stav_vyvoje.md`).
    *   **`readme.md`:** Vygeneruj základní `readme.md` s názvem projektu (odvozeným z názvu adresáře nebo uživatelského popisu), stručným popisem (odvozeným z analýzy kódu nebo uživatelského popisu) a informací o převodu na nový způsob vývoje.
    *   **`changelog.md`:** Inicializuj `changelog.md` s informací o zahájení převodu projektu na nový způsob vývoje.
    *   **`00_projekt.md`:** Popiš projekt na základě analýzy kódu a uživatelského popisu. Zahrň informace o cíli a účelu (pokud lze odvodit), cílové skupině uživatelů (pokud je známa) a technologiích.
    *   **`01_poadavky.md`:** Pokus se odvodit funkční požadavky z analýzy kódu (např. identifikací hlavních funkcí a jejich parametrů). Pokud existuje stará dokumentace, porovnej ji s kódem a označ rozdíly.
    *   **`02_navrh_architektury.md`:** Na základě struktury kódu a identifikovaných komponent navrhni základní architekturu projektu. Může jít o vysokoúrovňový popis modulů, tříd nebo služeb.
    *   **`03_plan_vyvoje.md`:** Vytvoř počáteční plán vývoje zaměřený na sjednocení kódu a dokumentace. První úkoly by měly zahrnovat revizi a aktualizaci dokumentace na základě aktuálního stavu kódu.
    *   **`04_cline_pokyny.md`:** Vygeneruj pokyny pro použití preferovaného nástroje pro asistenci s kódem (`aider` nebo `cline`) v kontextu tohoto projektu.
    *   **`05_stav_vyvoje.md`:** Nastav počáteční stav vývoje, např. "Probíhá převod projektu na nový způsob vývoje".

3. **Identifikace rozdílů mezi kódem a dokumentací:**
    *   Porovnej nově vygenerovanou dokumentaci s existující dokumentací (pokud existuje) a s aktuálním kódem.
    *   Identifikuj konkrétní rozdíly a nesrovnalosti. Například:
        *   Funkce nebo moduly popsané v dokumentaci, které neexistují v kódu.
        *   Funkce nebo moduly existující v kódu, které nejsou popsány v dokumentaci.
        *   Rozdíly v popisu funkcí, parametrů, návratových hodnot, atd.
        *   Zastaralé informace v dokumentaci.
    *   Vytvoř seznam úkolů pro sjednocení kódu a dokumentace. Každý úkol by měl být konkrétní a akční (např. "Aktualizovat popis funkce `X` v `01_poadavky.md` na základě implementace v `soubor.py`").

4. **První fáze vývoje: Sjednocení kódu a dokumentace:**
    *   Naplánuj první fázi vývoje, která se zaměří na postupné sjednocování kódu a dokumentace.
    *   Vytvoř detailní úkoly pro programátora, které zahrnují:
        *   Revizi konkrétních částí dokumentace a kódu.
        *   Aktualizaci dokumentace na základě aktuálního kódu.
        *   Úpravu kódu tak, aby odpovídal dokumentaci (pokud je dokumentace považována za správnou).
        *   Psaní testů pro ověření funkčnosti a shody s dokumentací.
    *   Použij preferovaný nástroj pro asistenci s kódem (`aider` nebo `cline`) pro provádění těchto úkolů.

5. **Pokračování v iterativním vývoji:**
    *   Po dokončení první fáze sjednocování kódu a dokumentace přejdi na standardní iterativní vývoj popsaný v předchozích instrukcích.
    *   Plánuj další fáze vývoje na základě funkčních požadavků (aktualizovaných v předchozí fázi).
    *   Důsledně dodržuj nový způsob vývoje řízený dokumentací.
```

## Prompt 3: Validace Dokumentace

**Popis:** Tento prompt slouží k validaci existující dokumentace projektu. AI načte dokumentaci a prověří její logiku, aktuálnost a soulad s definovanou strukturou. Výstupem je zpráva s nalezenými problémy a doporučeními k úpravě.

```
Vytvoř metodiku pro validaci softwarové dokumentace generované podle dříve definované metodiky řízení vývoje. Cílem je, aby AI načetla existující dokumentaci a provedla její kontrolu z hlediska logiky, aktuálnosti a souladu s definovanou strukturou a principy.

**Vstupní informace:**

*   **Adresář s dokumentací projektu:** [Zde uveďte cestu k adresáři s dokumentací projektu]
*   **Seznam souborů dokumentace k validaci:** `readme.md`, `changelog.md`, `00_projekt.md`, `01_poadavky.md`, `02_navrh_architektury.md`, `03_plan_vyvoje.md`, `04_cline_pokyny.md`, `05_stav_vyvoje.md`
*   **Preferované oblasti validace (volitelné):** [Uživatel může uvést oblasti, na které se má validace primárně zaměřit, např. "funkční požadavky", "návrh architektury", "plán vývoje". Pokud není uvedeno, validují se všechny oblasti.]

**Kritéria validace:**

AI by měla prověřovat dokumentaci z hlediska následujících kritérií:

1. **Struktura a formát:**
    *   **Přítomnost povinných souborů:** Zkontroluj, zda existují všechny povinné soubory dokumentace (`00_projekt.md`, `01_poadavky.md`, `02_navrh_architektury.md`, `03_plan_vyvoje.md`, `04_cline_pokyny.md`, `05_stav_vyvoje.md`, `readme.md`, `changelog.md`).
    *   **Správnost formátování:** Zkontroluj základní formátování Markdown (nadpisy, seznamy, odkazy).
    *   **Přítomnost klíčových sekcí:** Zkontroluj, zda každý soubor obsahuje očekávané klíčové sekce (např. `01_poadavky.md` by měl obsahovat sekce "Funkční požadavky" a "Nefunkční požadavky").

2. **Logická konzistence a provázanost:**
    *   **Soulad mezi popisem projektu a požadavky:** Zkontroluj, zda funkční požadavky popsané v `01_poadavky.md` odpovídají cíli a účelu projektu popsanému v `00_projekt.md`.
    *   **Soulad mezi požadavky a návrhem architektury:** Zkontroluj, zda návrh architektury v `02_navrh_architektury.md` umožňuje implementaci funkčních a nefunkčních požadavků. Identifikuj případné požadavky, které nejsou v návrhu architektury zohledněny.
    *   **Soulad mezi požadavky a plánem vývoje:** Zkontroluj, zda plán vývoje v `03_plan_vyvoje.md` zahrnuje úkoly pro implementaci všech klíčových funkčních požadavků.
    *   **Aktuálnost stavu vývoje:** Zkontroluj, zda informace v `05_stav_vyvoje.md` (aktuální fáze, dokončené úkoly) jsou logické a odpovídají plánu vývoje.
    *   **Konzistence terminologie:** Zkontroluj, zda se v dokumentaci používá konzistentní terminologie pro klíčové koncepty a entity.

3. **Aktuálnost informací:**
    *   **Odkazy na neexistující části dokumentace:** Zkontroluj, zda odkazy uvnitř dokumentace odkazují na existující sekce a soubory.
    *   **Zastaralé informace (heuristicky):** Pokus se identifikovat potenciálně zastaralé informace. Například, pokud `05_stav_vyvoje.md` uvádí, že se pracuje na úkolu, který měl být podle `03_plan_vyvoje.md` již dokončen.
    *   **Soulad s `changelog.md`:** Zkontroluj, zda významné změny v dokumentaci jsou zaznamenány v `changelog.md`.

4. **Dodržování metodiky:**
    *   **Přítomnost a struktura úkolů v plánu vývoje:** Zkontroluj, zda `03_plan_vyvoje.md` obsahuje detailní a akční úkoly, které jsou v souladu s principy metodiky.
    *   **Využití nástroje pro asistenci s kódem:** Zkontroluj, zda dokumentace odkazuje na použití nástroje pro asistenci s kódem (např. `aider` nebo `cline`) a zda jsou v `04_cline_pokyny.md` uvedeny relevantní informace.

**Výstup validace:**

Výstupem validace by měl být zpráva, která obsahuje:

*   **Celkový stav dokumentace:** Stručné shrnutí, zda je dokumentace považována za aktuální a logicky konzistentní.
*   **Seznam nalezených problémů a nesrovnalostí:** Detailní seznam identifikovaných problémů, rozdělený podle kategorií (struktura, logika, aktuálnost, metodika). U každého problému uveďte:
    *   **Popis problému:** Jasný popis nesrovnalosti.
    *   **Dotčené soubory a sekce:** Odkaz na konkrétní místa v dokumentaci, kde se problém nachází.
    *   **Doporučení k úpravě:** Návrh na opravu nebo úpravu dokumentace.
*   **Doporučení pro další kroky:** Obecná doporučení pro zlepšení dokumentace.
```
