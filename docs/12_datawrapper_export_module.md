# Datawrapper Export Module

## 📊 **Přehled modulu**

Datawrapper Export Module vytváří HTML přehled všech grafů z Datawrapper složky s možnostmi sdílení, filtrování a stahování.

## 🎯 **Hlavní funkce**

### **Input:**
- Aktuální server (z .env)
- Survey ID (z uživatelského vstupu)
- Automatické zjištění Datawrapper folder ID

### **Output:**
- HTML soubor `priloha-grafy-[SURVEY_ID].html`
- Všechny grafy ze složky s plnou funkcionalitou
- JavaScript filtrování podle názvu grafu
- Embedded grafy s aktivními metadata funkcemi

## 🏗️ **Architektura modulu**

```
src/datawrapper_export/
├── __init__.py
├── datawrapper_client.py      # API komunikace s Datawrapper
├── chart_collector.py         # Sběr grafů ze složky
├── chart_configurator.py      # Nastavení sharing options a metadata
├── html_generator.py          # Generování HTML s JS filtrováním
├── export_manager.py          # Hlavní orchestrace
└── templates/
    └── chart_export.html      # HTML template
```

## 📋 **HTML Struktura**

```html
<!DOCTYPE html>
<html>
<head>
    <title>Grafy průzkumu [SURVEY_ID]</title>
    <style>/* Responsive CSS styling */</style>
</head>
<body>
    <!-- Nadpis -->
    <header>
        <h1>Přehled grafů - [SURVEY_NAME]</h1>
        <p>Generováno: [TIMESTAMP] | Celkem grafů: [COUNT]</p>
    </header>
    
    <!-- Filtr grafů -->
    <section class="filter-section">
        <input type="text" id="chartFilter" placeholder="Filtrovat grafy podle názvu...">
        <div class="filter-stats">
            Zobrazeno: <span id="visibleCount">[COUNT]</span> z <span id="totalCount">[COUNT]</span>
        </div>
    </section>
    
    <!-- Grafy -->
    <main id="chartsContainer">
        <!-- Pro každý graf -->
        <div class="chart-item" data-chart-name="[CHART_NAME]">
            <h2 class="chart-title">[CHART_TITLE]</h2>
            <div class="chart-embed">
                <iframe src="[CHART_URL]" width="100%" height="400"></iframe>
            </div>
            <div class="chart-share">
                <a href="[SHARE_URL]" target="_blank">🔗 Sdílet graf</a>
            </div>
        </div>
    </main>
    
    <script>
        // JavaScript filtrování grafů
        function filterCharts() { /* implementace */ }
    </script>
</body>
</html>
```

## ⚙️ **Datawrapper API Konfigurace**

### **Požadovaná metadata pro každý graf:**

```python
REQUIRED_CHART_SETTINGS = {
    'metadata': {
        'publish': {
            'locale': 'cs-CZ',
            'chart-footer': {
                'data-download': True,           # Stažení dat
                'image-download': {
                    'png': True,                 # PNG download
                    'pdf': True,                 # PDF download  
                    'svg': True                  # SVG download
                },
                'embed-link': True,              # Embed kód
                'social-sharing': True           # Sociální sítě
            }
        }
    }
}
```

## 🔄 **Workflow procesu**

### **1. Menu 12 - Datawrapper Export:**
```
1. Zadejte Survey ID: [INPUT]
2. 🔍 Hledám Datawrapper složku...
3. ✅ Nalezena složka: [FOLDER_NAME]
4. 📊 Nalezeno grafů: [COUNT]
5. 🔄 Konfiguruji metadata pro všechny grafy...
6. 🔄 Republishuji grafy s novými nastaveními...
7. 🔄 Generuji HTML s filtrováním...
8. ✅ Vytvořen soubor: priloha-grafy-[SURVEY_ID].html
```

### **2. Chart Processing Pipeline:**
```python
def process_charts(folder_id):
    charts = collect_charts_from_folder(folder_id)
    
    for chart in charts:
        # Konfigurace metadata
        configure_chart_metadata(chart.id, REQUIRED_SETTINGS)
        
        # Republish pro aktivaci nastavení
        new_url = republish_chart(chart.id)
        
        # Uložení nové URL
        chart.share_url = new_url
    
    return charts
```

### **3. HTML Generation:**
```python
def generate_html(charts, survey_id):
    template = load_template('chart_export.html')
    
    html_content = template.render(
        survey_id=survey_id,
        charts=charts,
        generated_at=datetime.now(),
        total_count=len(charts)
    )
    
    save_html_file(f'priloha-grafy-{survey_id}.html', html_content)
```

## 🧪 **Testing Strategie**

### **Fáze 1: Základní testování**
1. **Test graf creation** - vytvoření testovacího grafu
2. **Test metadata configuration** - nastavení sharing options
3. **Test HTML generation** - HTML s jedním grafem

### **Fáze 2: Integrační testování**
1. **Test folder integration** - napojení na složku
2. **Test chart cycling** - zpracování více grafů
3. **Test filtering** - JavaScript filtrování
4. **Test complete workflow** - end-to-end test

## 📱 **JavaScript Filtrování**

```javascript
function initChartFilter() {
    const filterInput = document.getElementById('chartFilter');
    const chartItems = document.querySelectorAll('.chart-item');
    const visibleCount = document.getElementById('visibleCount');
    const totalCount = document.getElementById('totalCount');
    
    totalCount.textContent = chartItems.length;
    
    filterInput.addEventListener('input', function() {
        const filterValue = this.value.toLowerCase();
        let visible = 0;
        
        chartItems.forEach(item => {
            const chartName = item.dataset.chartName.toLowerCase();
            const isVisible = chartName.includes(filterValue);
            
            item.style.display = isVisible ? 'block' : 'none';
            if (isVisible) visible++;
        });
        
        visibleCount.textContent = visible;
    });
}

// Inicializace po načtení stránky
document.addEventListener('DOMContentLoaded', initChartFilter);
```

## 🛡️ **Error Handling**

### **API Errors:**
- ❌ **Datawrapper API nedostupné** → Retry mechanismus
- ❌ **Neplatný API key** → Clear error message
- ❌ **Rate limit exceeded** → Exponential backoff

### **Chart Errors:**
- ❌ **Graf neexistuje** → Skip s warningem
- ❌ **Nelze republish** → Použít stávající URL
- ❌ **Metadata update failed** → Default nastavení

### **File Errors:**
- ❌ **Nelze vytvořit HTML** → Permission check
- ❌ **Template chybí** → Fallback template

## 🔧 **Konfigurace**

### **Environment variables:**
```bash
# V .env souboru
DATAWRAPPER_API_KEY=your_api_key
DATAWRAPPER_TEAM_ID=57Zj-Xbm
DATAWRAPPER_LIMESURVEY_FOLDER_ID=329499
```

### **Chart export settings:**
```python
# config/datawrapper_export.json
{
    "html_template": "chart_export.html",
    "output_directory": "exports/",
    "filename_pattern": "priloha-grafy-{survey_id}.html",
    "iframe_height": 400,
    "responsive_design": true,
    "include_filter": true
}
```

## 📊 **Výstupní soubor**

### **Název:** `priloha-grafy-[SURVEY_ID].html`
### **Umístění:** Root directory projektu
### **Velikost:** Optimalizováno pro rychlé načítání
### **Kompatibilita:** Moderní prohlížeče, responsive design

## 🚀 **Budoucí rozšíření**

1. **Export do PDF** - celý HTML jako PDF
2. **Batch export** - více surveys najednou
3. **Custom templates** - různé HTML šablony
4. **Chart grouping** - seskupování podle kategorií
5. **Advanced filtering** - filtrování podle typu grafu
6. **Chart statistics** - přehled metrik grafů

## 📋 **Dependencies**

```python
# requirements_datawrapper.txt
requests>=2.28.0
jinja2>=3.1.0
python-dotenv>=0.19.0
```

Tento modul poskytuje kompletní řešení pro export a prezentaci Datawrapper grafů s moderním uživatelským rozhraním a plnou funkcionalitou sdílení.
