# Datawrapper Export Module - API Reference

## 📚 **Přehled API**

Kompletní API reference pro Datawrapper Export Module s příklady použití.

## 🏗️ **Architektura**

```
datawrapper_export/
├── DatawrapperExportClient  # API komunikace
├── ChartCollector          # Sběr grafů
├── ChartConfigurator       # Konfigurace metadata
├── HTMLGenerator           # HTML generování
├── ExportManager           # Orchestrace
└── utils                   # Pomocné funkce
```

## 📡 **DatawrapperExportClient**

### **Inicializace:**

```python
from datawrapper_export import DatawrapperExportClient

client = DatawrapperExportClient()
```

### **Metody:**

#### **get_folders() → List[Dict]**
Získá seznam všech složek.

```python
folders = client.get_folders()
for folder in folders:
    print(f"{folder['name']} (ID: {folder['id']})")
```

#### **get_charts_in_folder(folder_id: str) → List[ChartInfo]**
Získá všechny grafy ze složky.

```python
charts = client.get_charts_in_folder("123456")
print(f"Nalezeno {len(charts)} grafů")
```

#### **get_chart_metadata(chart_id: str) → Optional[Dict]**
Získá metadata grafu.

```python
metadata = client.get_chart_metadata("abc123")
if metadata:
    print(f"Locale: {metadata.get('publish', {}).get('locale')}")
```

#### **update_chart_metadata(chart_id: str, metadata: Dict) → bool**
Aktualizuje metadata grafu.

```python
new_metadata = {
    "publish": {
        "locale": "cs-CZ",
        "chart-footer": {"data-download": True}
    }
}
success = client.update_chart_metadata("abc123", new_metadata)
```

#### **publish_chart(chart_id: str) → Optional[str]**
Publikuje graf a vrátí share URL.

```python
share_url = client.publish_chart("abc123")
if share_url:
    print(f"Graf publikován: {share_url}")
```

#### **unpublish_chart(chart_id: str) → bool**
Unpublikuje graf.

```python
success = client.unpublish_chart("abc123")
```

#### **find_survey_folder(survey_id: str) → Optional[str]**
Najde složku pro daný survey ID.

```python
folder_id = client.find_survey_folder("789")
if folder_id:
    print(f"Složka nalezena: {folder_id}")
```

## 📊 **ChartCollector**

### **Inicializace:**

```python
from datawrapper_export import ChartCollector, DatawrapperExportClient

client = DatawrapperExportClient()
collector = ChartCollector(client)
```

### **Metody:**

#### **collect_charts_for_survey(survey_id, language_filter=None, chart_type_filter=None) → List[ChartInfo]**

```python
# Všechny grafy
charts = collector.collect_charts_for_survey("789")

# Pouze české grafy
czech_charts = collector.collect_charts_for_survey("789", language_filter="cs")

# Pouze sloupcové grafy
bar_charts = collector.collect_charts_for_survey("789", chart_type_filter="d3-bars")
```

#### **get_collection_stats() → CollectionStats**

```python
stats = collector.get_collection_stats()
print(f"Celkem: {stats.total_charts}")
print(f"České: {stats.czech_charts}")
print(f"Anglické: {stats.english_charts}")
print(f"Typy: {stats.chart_types}")
```

## ⚙️ **ChartConfigurator**

### **Inicializace:**

```python
from datawrapper_export import ChartConfigurator, DatawrapperExportClient

client = DatawrapperExportClient()
configurator = ChartConfigurator(client)
```

### **Metody:**

#### **configure_charts_for_export(charts, force_republish=True) → List[ConfigurationResult]**

```python
results = configurator.configure_charts_for_export(charts)

for result in results:
    if result.success:
        print(f"✅ Graf {result.chart_id}: {result.new_share_url}")
    else:
        print(f"❌ Graf {result.chart_id}: {result.error_message}")
```

#### **validate_chart_configuration(chart) → Dict**

```python
validation = configurator.validate_chart_configuration(chart)
print(f"Platný: {validation['valid']}")
print(f"Problémy: {validation['issues']}")
```

#### **get_configuration_summary() → Dict**

```python
summary = configurator.get_configuration_summary()
print(f"Úspěšnost: {summary['success_rate']}")
```

## 🎨 **HTMLGenerator**

### **Inicializace:**

```python
from datawrapper_export import HTMLGenerator

generator = HTMLGenerator()
# nebo s custom template složkou
generator = HTMLGenerator(template_dir="my_templates")
```

### **Metody:**

#### **generate_export_html(charts, survey_id, output_path, survey_name=None) → bool**

```python
success = generator.generate_export_html(
    charts=charts,
    survey_id="789",
    output_path="export.html",
    survey_name="Můj průzkum"
)
```

#### **validate_charts_for_export(charts) → Dict**

```python
validation = generator.validate_charts_for_export(charts)
print(f"Platných grafů: {validation['valid_charts']}")
print(f"Neplatných grafů: {validation['invalid_charts']}")
```

#### **get_template_info(template_name) → Dict**

```python
info = generator.get_template_info("chart_export.html")
print(f"Existuje: {info['exists']}")
print(f"Velikost: {info['size']} bytů")
```

## 🎯 **ExportManager**

### **Inicializace:**

```python
from datawrapper_export import ExportManager, ExportProgress

def progress_callback(progress: ExportProgress):
    print(f"🔄 {progress.current_step} ({progress.progress_percentage:.1f}%)")

manager = ExportManager(progress_callback=progress_callback)
```

### **Metody:**

#### **export_survey_charts(survey_id, output_dir=".", survey_name=None, language_filter=None, force_reconfigure=True) → ExportResult**

```python
result = manager.export_survey_charts(
    survey_id="789",
    output_dir="exports",
    survey_name="Test Export",
    language_filter="cs",
    force_reconfigure=False
)

if result.success:
    print(f"✅ Export úspěšný: {result.output_file}")
    print(f"📊 Grafů: {result.charts_exported}")
    print(f"⏱️ Doba: {result.execution_time:.1f}s")
else:
    print(f"❌ Export selhal: {result.errors}")
```

#### **test_connection() → Dict**

```python
test = manager.test_connection()
if test['success']:
    print(f"✅ API dostupné ({test['folders_count']} složek)")
else:
    print(f"❌ API nedostupné: {test['error']}")
```

#### **get_export_summary(result) → Dict**

```python
summary = manager.get_export_summary(result)
print(f"Velikost souboru: {summary.get('file_size', 'N/A')}")
print(f"Statistiky: {summary.get('collection_stats', {})}")
```

## 🛠️ **Utility Functions**

### **Validace prostředí:**

```python
from datawrapper_export import validate_environment, display_validation_results

validation = validate_environment()
display_validation_results(validation)

if validation['valid']:
    print("✅ Prostředí je připraveno")
else:
    print("❌ Opravte chyby před pokračováním")
```

### **Logging:**

```python
from datawrapper_export import ExportLogger

logger = ExportLogger("my_export", "logs/export.log")
logger.info("Export začíná")
logger.error("Chyba při exportu", exc_info=True)
```

### **Progress tracking:**

```python
from datawrapper_export import ProgressTracker

tracker = ProgressTracker(total_steps=5)
tracker.update("Načítání dat")
tracker.update("Zpracování grafů")
tracker.finish("Export dokončen")
```

### **Pomocné funkce:**

```python
from datawrapper_export import (
    format_file_size, 
    format_duration, 
    safe_filename,
    user_confirm
)

print(format_file_size(1024000))  # "1000.0 KB"
print(format_duration(125.5))     # "2m 5.5s"
print(safe_filename("Graf<>?.txt"))  # "Graf___.txt"

if user_confirm("Pokračovat?", default=True):
    print("Pokračujem...")
```

## 📋 **Data Classes**

### **ChartInfo**

```python
@dataclass
class ChartInfo:
    id: str
    title: str
    type: str
    status: str
    url: str
    share_url: Optional[str] = None
    metadata: Optional[Dict] = None
```

### **ExportResult**

```python
@dataclass
class ExportResult:
    success: bool
    output_file: Optional[str] = None
    charts_exported: int = 0
    total_charts: int = 0
    collection_stats: Optional[CollectionStats] = None
    configuration_results: List[ConfigurationResult] = None
    errors: List[str] = None
    execution_time: float = 0.0
```

### **ExportProgress**

```python
@dataclass
class ExportProgress:
    current_step: str
    step_number: int
    total_steps: int
    charts_processed: int = 0
    total_charts: int = 0
    errors: List[str] = None
```

## 🔧 **Konfigurace**

### **Environment Variables:**

```python
import os

# Povinné
DATAWRAPPER_API_KEY = os.getenv('DATAWRAPPER_API_KEY')

# Volitelné
DATAWRAPPER_TEAM_ID = os.getenv('DATAWRAPPER_TEAM_ID')
DATAWRAPPER_LIMESURVEY_FOLDER_ID = os.getenv('DATAWRAPPER_LIMESURVEY_FOLDER_ID')
```

### **Rate Limiting:**

```python
# DatawrapperExportClient má vestavěné rate limiting
client = DatawrapperExportClient()
client.rate_limit_delay = 1.0  # 1 sekunda mezi requesty
client.max_retries = 5         # 5 pokusů při chybě
client.retry_delay = 3         # 3 sekundy mezi pokusy
```

## 🚨 **Error Handling**

### **Exception Types:**

- **ValueError:** Chybí API klíč nebo neplatné parametry
- **requests.RequestException:** Síťové chyby
- **FileNotFoundError:** Chybí template soubory
- **PermissionError:** Problémy s přístupem k souborům

### **Error Handling Pattern:**

```python
from datawrapper_export import handle_exceptions, ExportLogger

logger = ExportLogger("my_app")

@handle_exceptions(logger=logger, return_value=False, user_message="Export selhal")
def my_export_function():
    # Váš kód zde
    pass
```

---

**📝 Poznámka:** Všechny metody jsou thread-safe a mohou být používány v concurrent prostředí.
