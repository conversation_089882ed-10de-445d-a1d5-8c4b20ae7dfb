# Plán vývoje

## Fáze 1: Základní infrastruktura (1 týden)
1. Nastavení projektu
   - [x] Inicializace Git repozitáře
   - [x] Vytvoření základní struktury projektu
   - [ ] Konfigurace CI/CD pipeline

2. Implementace API Gateway
   - [ ] Základní třída pro komunikaci s API
   - [ ] Implementace autentizace
   - [ ] Přidání cachování

## Fáze 2: Datový pipeline (2 týdny)
1. <PERSON>išt<PERSON>n<PERSON> dat
   - [ ] Implementace základních transformací
   - [ ] Validace datových typů
   - [ ] Logování chyb

2. Transformace dat
   - [ ] Převedení do long formátu
   - [ ] Agregace dat
   - [ ] Přejmenování sloupců

## Fáze 3: GUI a integrace (3 týdny)
1. Uživatelské rozhraní
   - [ ] <PERSON><PERSON><PERSON><PERSON> okno aplikace
   - [ ] Výb<PERSON>r průzkumu
   - [ ] Zobrazení metadat

2. Integrace s Datawrapper
   - [ ] Generování základn<PERSON>ch grafů
   - [ ] Nastavení metadat grafu
   - [ ] Export výsledků

## Fáze 4: Rozšíření a AI funkce (6 měsíců)
1. Podpora více LimeSurvey serverů
   - [ ] Rozšíření konfigurace pro více serverů
   - [ ] Úprava souborové struktury
   - [ ] Řešení kolizí ID průzkumů

2. Rozšířená metadata a konfigurace
   - [ ] JSON struktura pro metadata
   - [ ] Jazykové mutace metadat
   - [ ] Integrace do generování grafů

3. AI funkce
   - [ ] AI překlad JSON struktur
   - [ ] AI komentáře grafů
   - [ ] AI zpracování textových otázek

## Fáze 5: Pokročilé funkce (6 měsíců)
1. Rozšíření typů grafů
   - [ ] Výběr typu grafu pro otázky
   - [ ] Tabulkové grafy
   - [ ] Vícenásobné a skupinové grafy

2. Speciální analýzy
   - [ ] WordCloud grafy
   - [ ] Analýzy kompletnosti vyplnění
   - [ ] Časové analýzy respondentů

## Metodika sledování postupu

- **K implementaci:** Úkol čeká na zahájení
- **V implementaci:** Úkol je v procesu řešení
- **K testování:** Úkol čeká na ověření
- **Hotovo:** Úkol byl dokončen a ověřen

Pro sledování použijeme GitHub Issues s příslušnými labely.

## Odkazy na dokumentaci

- [Detailní plán rozšíření](06_plan_rozsireni.md)
- [Masterplan](masterplan.md)
- [Aktuální stav vývoje](05_stav_vyvoje.md)
