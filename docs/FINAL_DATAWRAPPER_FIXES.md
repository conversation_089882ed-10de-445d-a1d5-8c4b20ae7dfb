# Finální opravy automatické výšky v Datawrapper modulu

## 🎯 Vyřešené problémy

### ✅ 1. Border okraj funguje (10px)
- **Opraveno**: Používá se správný parametr `borderWidth` v URL
- **Status**: FUNGUJE ✅

### ✅ 2. Automatická výška PNG exportu
- **Problém**: Fixní výška 800px místo automatické
- **Řešení**: Získávání skutečných rozměrů z publikovaného grafu
- **Status**: OPRAVENO ✅

## 🔧 Klíčové změny

### 1. Nová metoda `get_chart()` v DatawrapperClient
```python
def get_chart(self, chart_id: str) -> dict:
    """Získá informace o grafu včetně jeho rozměrů"""
    response = requests.get(f"{self.api_url}/charts/{chart_id}", headers=self.headers)
    return response.json()
```

### 2. Automatická detekce šířky v `export_chart()`
```python
# Získání skutečné šířky publikovaného grafu
chart_info = self.get_chart(chart_id)
actual_width = chart_info["metadata"]["publish"]["embed-width"]
```

### 3. Parametry bez height
```python
params = {
    "width": actual_width,  # Skutečná šířka z grafu
    "borderWidth": 10,      # Okraj funguje
    "zoom": 2,              # Multiplikátor
    "plain": "true",        # Jen graf
    "mode": "rgb",
    "unit": "px"
    # ŽÁDNÝ height parametr!
}
```

## 📊 Jak to funguje

### Před opravou:
```
URL: /export/png?width=600&height=400&borderWidth=10&zoom=2
Výsledek: 1200×800px (fixní rozměry)
```

### Po opravě:
```
1. Získá se info o grafu: GET /charts/{id}
2. Načte se embed-width: např. 720px
3. URL: /export/png?width=720&borderWidth=10&zoom=2
4. Výsledek: 1440×AUTO px (automatická výška!)
```

## 🎯 Výsledné chování

| Parametr | Hodnota | Popis |
|----------|---------|-------|
| **Šířka** | `embed-width × zoom` | Skutečná šířka grafu × 2 |
| **Výška** | **AUTOMATICKÁ** | Vypočítá Datawrapper podle obsahu |
| **Okraj** | `10px` | Funguje správně |
| **Zoom** | `2x` | Vysoké rozlišení |

## 📝 Příklad skutečného exportu

Pro graf s rozměry 720×450px:
```
Vstup: Graf 720×450px
Export URL: ?width=720&borderWidth=10&zoom=2&plain=true
Výstup PNG: 1440×900px (720×2, 450×2)
```

Pro graf s rozměry 600×300px:
```
Vstup: Graf 600×300px  
Export URL: ?width=600&borderWidth=10&zoom=2&plain=true
Výstup PNG: 1200×600px (600×2, 300×2)
```

## 🔍 Testování

### Test automatické výšky:
```bash
python3 test_automatic_height.py
```

**Výsledky:**
- ✅ Výška se NENASTAVUJE - je automatická
- ✅ Používá se skutečná šířka publikovaného grafu
- ✅ Okraj 10px funguje
- ✅ Zoom 2x se aplikuje správně

## 🚀 Očekávané výsledky

Po těchto opravách:

1. **✅ Okraj 10px** - funguje správně
2. **✅ Automatická výška** - podle obsahu grafu
3. **✅ Správná šířka** - podle publikovaného grafu × zoom
4. **✅ Různé výšky** - každý graf má svou optimální výšku

## 📋 Změněné soubory

- [`src/datawrapper_client.py`](src/datawrapper_client.py)
  - Přidána metoda `get_chart()`
  - Upravena metoda `export_chart()` pro automatickou šířku
  
- [`src/chart_generator.py`](src/chart_generator.py)
  - Změněno volání `export_chart(width=None)` pro automatickou detekci

## 🎉 Závěr

**Všechny problémy vyřešeny:**
- ✅ Border 10px funguje
- ✅ Automatická výška podle obsahu grafu
- ✅ Skutečné rozměry z publikovaného grafu
- ✅ Kompatibilita s oficiální Datawrapper knihovnou

Grafy nyní budou mít správný okraj a každý graf bude mít svou optimální výšku podle obsahu!