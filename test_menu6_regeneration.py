#!/usr/bin/env python3
"""
Test regenerace Menu 6 s opraveným LSS pořadím
"""

import sys
import os
import json

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_menu6_regeneration():
    """Test regenerace chart_data.json s opraveným pořadím"""
    print("🧪 Test regenerace Menu 6...")
    
    try:
        from data_transformer import generate_chart_data
        
        # Cesty k souborům
        long_path = "data/dotazniky.urad.online/827822/responses_long.csv"
        chart_data_path = "data/dotazniky.urad.online/827822/chart_data.json"
        
        if not os.path.exists(long_path):
            print("⚠️  responses_long.csv neexistuje - spusť nejprve Menu 5")
            return True
        
        print("🔄 Regeneruji chart_data.json...")
        
        # Spustíme generate_chart_data (Menu 6)
        success = generate_chart_data(long_path, chart_data_path)
        
        if success:
            print("✅ chart_data.json <PERSON> regenerován")
            
            # Načteme a zkontrolujeme výsledek
            with open(chart_data_path, 'r', encoding='utf-8') as f:
                chart_data = json.load(f)
            
            # Najdeme G6Q00001 a zkontrolujeme pořadí
            for item in chart_data:
                if item.get('code') == 'G6Q00001':
                    print(f"✅ Nalezena otázka G6Q00001: {item['name']}")
                    
                    if item['data']:
                        first_subq = item['data'][0]
                        responses = first_subq['responses']
                        response_keys = list(responses.keys())
                        
                        print(f"✅ Pořadí odpovědí: {response_keys}")
                        
                        # Kontrola správného pořadí
                        expected_order = ['rozhodně ano', 'spíše ano', 'spíše ne', 'rozhodně ne']
                        
                        # Zkontrolujeme, zda jsou první 4 odpovědi v správném pořadí
                        actual_order = response_keys[:4]
                        
                        if actual_order == expected_order:
                            print("✅ Pořadí odpovědí je SPRÁVNÉ!")
                            return True
                        else:
                            print(f"❌ Pořadí odpovědí je NESPRÁVNÉ")
                            print(f"   Očekávané: {expected_order}")
                            print(f"   Aktuální:  {actual_order}")
                            return False
                    else:
                        print("❌ Žádná data v otázce")
                        return False
            
            print("❌ Otázka G6Q00001 nenalezena v chart_data.json")
            return False
        else:
            print("❌ Regenerace chart_data.json selhala")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při regeneraci: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test regenerace Menu 6 s opraveným LSS pořadím")
    print("=" * 60)
    
    if test_menu6_regeneration():
        print("\n✅ ÚSPĚCH! Oprava LSS pořadí funguje!")
        print("\n🎯 Výsledek:")
        print("   • chart_data.json má správné pořadí odpovědí")
        print("   • G6Q00001 má škálové pořadí: rozhodně ano → spíše ano → spíše ne → rozhodně ne")
        print("   • Varování 'Nerozpoznán škálový vzor' by měla být minimální")
        print("\n🎯 Další kroky:")
        print("   1. Spusť Menu 8 pro generování grafů")
        print("   2. Zkontroluj legendy grafů - měly by být v logickém pořadí")
        print("   3. Ověř konkrétně graf G6Q00001")
        return True
    else:
        print("\n❌ PROBLÉM! Oprava nefunguje správně")
        print("\n🔧 Co zkontrolovat:")
        print("   1. Zda existuje responses_long.csv")
        print("   2. Zda existuje structure.lss")
        print("   3. Zda G6Q00001 má answeroptions v LSS")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)