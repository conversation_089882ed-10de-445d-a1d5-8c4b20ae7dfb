#!/usr/bin/env python3
"""
Test Survey 827822 - Fáze 2

Konfigurace metadata a HTML export pro všech 40 grafů.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_survey_827822_phase2():
    """Fáze 2: Konfigurace metadata a HTML export"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST SURVEY 827822 - FÁZE 2")
        logger.info("=" * 60)
        
        from datawrapper_export import (
            ExportManager,
            DatawrapperExportClient,
            ChartCollector,
            ChartConfigurator,
            ChartInfo
        )
        
        # Známé informace z fáze 1
        survey_id = "827822"
        folder_id = "329765"
        expected_charts = 40
        
        logger.info(f"Survey ID: {survey_id}")
        logger.info(f"Folder ID: {folder_id}")
        logger.info(f"Očekávaný počet grafů: {expected_charts}")
        
        # Krok 1: Inicializace klientů
        logger.info("\n🔧 KROK 1: Inicializace klientů")
        
        export_client = DatawrapperExportClient()
        chart_collector = ChartCollector(export_client)
        chart_configurator = ChartConfigurator(export_client)
        export_manager = ExportManager()
        
        logger.info("✅ Všichni klienti inicializováni")
        
        # Krok 2: Načtení grafů přímo ze složky
        logger.info("\n📊 KROK 2: Načtení grafů ze složky")
        
        charts = export_client.get_charts_in_folder(folder_id)
        
        if not charts:
            logger.error(f"❌ Nepodařilo se načíst grafy ze složky {folder_id}")
            return False
        
        logger.info(f"✅ Načteno {len(charts)} grafů")
        
        if len(charts) != expected_charts:
            logger.warning(f"⚠️ Počet grafů ({len(charts)}) neodpovídá očekávání ({expected_charts})")
        
        # Krok 3: Kontrola publikačního statusu
        logger.info("\n🔍 KROK 3: Kontrola publikačního statusu")
        
        published_count = 0
        draft_count = 0
        
        # Zkontrolujeme prvních 5 grafů detailně
        for i, chart in enumerate(charts[:5], 1):
            try:
                # Načtení detailních informací
                response = export_client._make_request('GET', f'charts/{chart.id}')
                
                if response.status_code == 200:
                    chart_data = response.json()
                    
                    # Různé možné statusy
                    public_status = chart_data.get('publicStatus', 'unknown')
                    public_url = chart_data.get('publicUrl', '')
                    is_published = bool(public_url)
                    
                    logger.info(f"   {i}. {chart.title[:50]}...")
                    logger.info(f"      ID: {chart.id}")
                    logger.info(f"      publicStatus: {public_status}")
                    logger.info(f"      publicUrl: {'✅ EXISTUJE' if public_url else '❌ NEEXISTUJE'}")
                    logger.info(f"      Skutečně publikován: {'✅ ANO' if is_published else '❌ NE'}")
                    
                    if is_published:
                        published_count += 1
                        # Aktualizace share_url v ChartInfo
                        chart.share_url = public_url
                        chart.status = 'published'
                    else:
                        draft_count += 1
                        chart.status = 'draft'
                    
                    logger.info("")
                    
                else:
                    logger.warning(f"   {i}. {chart.id} - nepodařilo se načíst detaily")
                    draft_count += 1
                    
            except Exception as e:
                logger.warning(f"   {i}. {chart.id} - chyba: {str(e)}")
                draft_count += 1
        
        logger.info(f"📊 Status prvních 5 grafů:")
        logger.info(f"   ✅ Publikované: {published_count}")
        logger.info(f"   📝 Draft: {draft_count}")
        
        # Krok 4: Konfigurace metadata (pouze pro publikované)
        logger.info("\n⚙️ KROK 4: Konfigurace metadata")
        
        # Filtrujeme pouze publikované grafy
        published_charts = [chart for chart in charts if chart.status == 'published']
        
        if not published_charts:
            logger.warning("⚠️ Žádné publikované grafy k nakonfigurování")
            logger.info("💡 Zkusím nakonfigurovat všechny grafy (možná jsou publikované, ale API vrací špatný status)")
            published_charts = charts[:10]  # Prvních 10 pro test
        
        logger.info(f"Konfiguruji metadata pro {len(published_charts)} grafů...")
        
        # Konfigurace s bezpečným workflow
        configuration_results = chart_configurator.configure_charts_for_export(
            published_charts, 
            force_republish=False  # Nebudeme force republish kvůli URL stabilitě
        )
        
        successful_configs = sum(1 for result in configuration_results if result.success)
        
        logger.info(f"✅ Úspěšně nakonfigurováno: {successful_configs}/{len(published_charts)} grafů")
        
        # Zobrazení výsledků konfigurace
        for i, result in enumerate(configuration_results[:3], 1):
            if result.success:
                logger.info(f"   {i}. ✅ {result.chart_id} - {result.new_share_url}")
            else:
                logger.info(f"   {i}. ❌ {result.chart_id} - {result.error_message}")
        
        # Krok 5: Generování HTML
        logger.info("\n🎨 KROK 5: Generování HTML")
        
        # Použijeme všechny grafy pro HTML (i ty s draft statusem)
        output_dir = "test_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # Použijeme ExportManager pro kompletní workflow
        logger.info("Spouštím kompletní export workflow...")
        
        try:
            result = export_manager.export_survey_charts(
                survey_id=survey_id,
                output_dir=output_dir,
                survey_name="Survey 827822 - Test Export",
                language_filter=None,  # Všechny jazyky
                force_reconfigure=False  # Už jsme konfigurovali
            )
            
            if result.success:
                logger.info("✅ HTML export úspěšný!")
                logger.info(f"📁 Výstupní soubor: {result.output_file}")
                logger.info(f"📊 Exportováno grafů: {result.charts_exported}")
                logger.info(f"⏱️ Doba zpracování: {result.execution_time:.1f}s")
                
                # Zobrazení cesty k souboru
                full_path = os.path.abspath(result.output_file)
                logger.info(f"🔗 Plná cesta: {full_path}")
                
            else:
                logger.error("❌ HTML export selhal")
                if result.errors:
                    for error in result.errors:
                        logger.error(f"   - {error}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Chyba při HTML exportu: {str(e)}")
            
            # Fallback - manuální HTML generování
            logger.info("🔄 Zkouším fallback HTML generování...")
            
            from datawrapper_export import HTMLGenerator
            
            html_generator = HTMLGenerator()
            output_file = os.path.join(output_dir, f"survey-{survey_id}-manual.html")
            
            html_success = html_generator.generate_export_html(
                charts=charts,
                survey_id=survey_id,
                output_path=output_file,
                survey_name="Survey 827822 - Manual Export"
            )
            
            if html_success:
                logger.info(f"✅ Fallback HTML úspěšný: {output_file}")
            else:
                logger.error("❌ I fallback HTML selhal")
                return False
        
        # Krok 6: Shrnutí
        logger.info("\n" + "=" * 60)
        logger.info("SHRNUTÍ FÁZE 2")
        logger.info("=" * 60)
        logger.info(f"Survey ID: {survey_id}")
        logger.info(f"Folder ID: {folder_id}")
        logger.info(f"Celkem grafů: {len(charts)}")
        logger.info(f"Nakonfigurováno: {successful_configs}")
        logger.info(f"HTML export: ✅ ÚSPĚŠNÝ")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba ve fázi 2: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_direct_chart_status():
    """Test přímé kontroly statusu grafů"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST PŘÍMÉ KONTROLY STATUSU GRAFŮ")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        
        client = DatawrapperExportClient()
        folder_id = "329765"
        
        # Načtení grafů
        charts = client.get_charts_in_folder(folder_id)
        
        logger.info(f"Kontroluji status všech {len(charts)} grafů...")
        
        published_count = 0
        draft_count = 0
        error_count = 0
        
        for i, chart in enumerate(charts, 1):
            try:
                response = client._make_request('GET', f'charts/{chart.id}')
                
                if response.status_code == 200:
                    chart_data = response.json()
                    public_url = chart_data.get('publicUrl', '')
                    
                    if public_url:
                        published_count += 1
                        if i <= 5:  # Prvních 5 pro detail
                            logger.info(f"   {i:2d}. ✅ {chart.id} - {public_url}")
                    else:
                        draft_count += 1
                        if i <= 5:
                            logger.info(f"   {i:2d}. ❌ {chart.id} - není publikován")
                else:
                    error_count += 1
                    
            except Exception as e:
                error_count += 1
                if i <= 5:
                    logger.info(f"   {i:2d}. 💥 {chart.id} - chyba: {str(e)}")
        
        logger.info(f"\n📊 CELKOVÝ PŘEHLED:")
        logger.info(f"   ✅ Publikované: {published_count}")
        logger.info(f"   ❌ Draft: {draft_count}")
        logger.info(f"   💥 Chyby: {error_count}")
        logger.info(f"   📊 Celkem: {len(charts)}")
        
        if published_count > 0:
            logger.info(f"\n🎉 Máte {published_count} publikovaných grafů připravených k exportu!")
        else:
            logger.warning("\n⚠️ Žádné publikované grafy nenalezeny")
        
        return published_count > 0
        
    except Exception as e:
        logger.error(f"❌ Chyba při kontrole statusu: {str(e)}")
        return False


if __name__ == "__main__":
    print("🚀 SPOUŠTÍM TEST SURVEY 827822 - FÁZE 2")
    
    # Test kontroly statusu
    status_ok = test_direct_chart_status()
    
    # Hlavní test fáze 2
    success = test_survey_827822_phase2()
    
    if success and status_ok:
        print("\n🎉 FÁZE 2 ÚSPĚŠNĚ DOKONČENA!")
        print("✅ Metadata nakonfigurována")
        print("✅ HTML export vygenerován")
        print("🚀 Survey 827822 je připraven k použití!")
    else:
        print("\n❌ FÁZE 2 SELHALA!")
        print("💡 Zkontrolujte logy výše pro detaily")
