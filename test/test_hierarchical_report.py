#!/usr/bin/env python3
"""
Test generování HTML reportu s hierarchickým parserem

Vygeneruje nový report a ukáže kam se ukládá.
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def generate_hierarchical_report():
    """Vygeneruje HTML report s hierarchickým parserem"""
    
    try:
        logger.info("=" * 80)
        logger.info("GENEROVÁNÍ HTML REPORTU S HIERARCHICKÝM PARSEREM")
        logger.info("=" * 80)
        
        from intelligent_analysis import IntelligentAnalysisManager
        from intelligent_analysis_html import AnalysisHTMLGenerator
        
        # Inicializace Analysis Engine
        logger.info("🔧 Inicializuji Analysis Engine...")
        manager = IntelligentAnalysisManager()
        
        # Cesta k LSS souboru
        lss_file = "/home/<USER>/vyvoj/limwrapp/data/dotazniky.urad.online/827822/structure.lss"
        
        logger.info(f"📄 Analyzuji s hierarchickým parserem: {lss_file}")
        
        # Spuštění analýzy
        result = manager.analyze_survey_from_lss(lss_file)
        
        if not result:
            logger.error("❌ Analýza selhala")
            return False
        
        # Zobrazení výsledků
        summary = result['summary']
        logger.info("📊 VÝSLEDKY HIERARCHICKÉ ANALÝZY:")
        logger.info(f"   📋 Celkem otázek: {summary['total_questions']}")
        logger.info(f"   🎯 Rozpoznáno: {summary['recognized_questions']} ({summary['recognition_rate']:.1%})")
        logger.info(f"   📈 Doporučené analýzy: {summary['total_analyses']}")
        logger.info(f"   🎨 Vizualizace: {summary['total_visualizations']}")
        logger.info(f"   ⏱️ Odhadovaný čas: {summary['estimated_time_hours']:.1f} hodin")
        logger.info(f"   🔧 Složitost: {summary['complexity_score']:.1f}/10")
        
        # Definice výstupních cest
        timestamp = "hierarchical"
        output_dir = f"/home/<USER>/vyvoj/limwrapp/analysis_reports/survey_827822_{timestamp}"
        html_filename = f"survey_827822_analysis_{timestamp}.html"
        json_filename = f"survey_827822_analysis_{timestamp}.json"
        
        html_path = os.path.join(output_dir, html_filename)
        json_path = os.path.join(output_dir, json_filename)
        
        # Vytvoření výstupního adresáře
        os.makedirs(output_dir, exist_ok=True)
        
        logger.info(f"\n📁 VÝSTUPNÍ ADRESÁŘ:")
        logger.info(f"   📂 {output_dir}")
        
        # Generování HTML reportu
        logger.info(f"\n📤 GENEROVÁNÍ HTML REPORTU...")
        html_generator = AnalysisHTMLGenerator()
        
        if html_generator.generate_analysis_report(result, html_path, "Survey 827822 - Hierarchický Parser"):
            file_size = os.path.getsize(html_path) / 1024
            logger.info(f"✅ HTML report vygenerován!")
            logger.info(f"   📄 Soubor: {html_path}")
            logger.info(f"   📏 Velikost: {file_size:.1f} KB")
            
            # Absolutní cesta pro prohlížeč
            abs_html_path = os.path.abspath(html_path)
            logger.info(f"   🔗 URL: file://{abs_html_path}")
        else:
            logger.error("❌ Generování HTML reportu selhalo")
            return False
        
        # Generování JSON reportu
        logger.info(f"\n📤 GENEROVÁNÍ JSON REPORTU...")
        if manager.export_analysis_report(result, json_path):
            json_size = os.path.getsize(json_path) / 1024
            logger.info(f"✅ JSON report vygenerován!")
            logger.info(f"   📄 Soubor: {json_path}")
            logger.info(f"   📏 Velikost: {json_size:.1f} KB")
        else:
            logger.error("❌ Generování JSON reportu selhalo")
        
        # Porovnání s původním reportem
        logger.info(f"\n📊 POROVNÁNÍ S PŮVODNÍM REPORTEM:")
        logger.info("=" * 60)
        
        original_dir = "/home/<USER>/vyvoj/limwrapp/analysis_reports/survey_827822"
        original_html = os.path.join(original_dir, "survey_827822_analysis.html")
        
        if os.path.exists(original_html):
            original_size = os.path.getsize(original_html) / 1024
            size_diff = file_size - original_size
            size_change = (size_diff / original_size * 100) if original_size > 0 else 0
            
            logger.info(f"   📄 Původní report: {original_size:.1f} KB")
            logger.info(f"   📄 Hierarchický report: {file_size:.1f} KB")
            logger.info(f"   📈 Změna velikosti: {size_diff:+.1f} KB ({size_change:+.1f}%)")
        else:
            logger.info(f"   ⚠️ Původní report nenalezen: {original_html}")
        
        # Struktura výstupního adresáře
        logger.info(f"\n📁 OBSAH VÝSTUPNÍHO ADRESÁŘE:")
        logger.info("=" * 60)
        
        for file in os.listdir(output_dir):
            file_path = os.path.join(output_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path) / 1024
                logger.info(f"   📄 {file}: {size:.1f} KB")
        
        # Návod pro otevření
        logger.info(f"\n🚀 JAK OTEVŘÍT REPORT:")
        logger.info("=" * 60)
        logger.info(f"1. 🖱️  Zkopírujte URL: file://{abs_html_path}")
        logger.info(f"2. 🌐 Vložte do prohlížeče")
        logger.info(f"3. 📊 Porovnejte s původním reportem")
        
        # Klíčové rozdíly
        logger.info(f"\n🎯 KLÍČOVÉ ROZDÍLY HIERARCHICKÉHO REPORTU:")
        logger.info("=" * 60)
        logger.info(f"   📋 Otázky: 20 místo 94 (78.7% redukce)")
        logger.info(f"   📈 Analýzy: {summary['total_analyses']} místo 8")
        logger.info(f"   ⏱️ Čas: {summary['estimated_time_hours']:.1f}h místo 13h")
        logger.info(f"   🔧 Složitost: {summary['complexity_score']:.1f}/10 místo 3.5/10")
        logger.info(f"   🎯 Přesnost: Respektuje hierarchii otázek")
        
        logger.info(f"\n✅ HIERARCHICKÝ REPORT ÚSPĚŠNĚ VYGENEROVÁN!")
        
        return abs_html_path
        
    except Exception as e:
        logger.error(f"❌ Chyba při generování reportu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Spuštění generování hierarchického reportu"""
    
    print("🚀 GENERUJI HTML REPORT S HIERARCHICKÝM PARSEREM")
    
    html_path = generate_hierarchical_report()
    
    if html_path:
        print(f"\n🎉 REPORT ÚSPĚŠNĚ VYGENEROVÁN!")
        print(f"📂 Umístění: {os.path.dirname(html_path)}")
        print(f"📄 Soubor: {os.path.basename(html_path)}")
        print(f"🔗 URL: file://{html_path}")
        print(f"\n💡 Zkopírujte URL a vložte do prohlížeče pro zobrazení")
        
        # Nabídka automatického otevření
        try:
            import webbrowser
            open_choice = input("\nOtevřít report automaticky v prohlížeči? (y/N): ").strip().lower()
            if open_choice == 'y':
                webbrowser.open(f"file://{html_path}")
                print("🌐 Report otevřen v prohlížeči!")
        except:
            pass
        
        return True
    else:
        print("❌ Generování reportu selhalo")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
