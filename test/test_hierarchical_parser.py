#!/usr/bin/env python3
"""
Test hierarchického LSS parseru

Testuje vylepšený parser, k<PERSON><PERSON> rozumí hierarchii otázek a subotázek.
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_hierarchical_parsing():
    """Test hierarchického parsování s reálnými daty"""
    
    try:
        logger.info("=" * 80)
        logger.info("TEST HIERARCHICKÉHO LSS PARSERU")
        logger.info("=" * 80)
        
        from lss_parser import LSSParser
        
        # Test s reálným LSS souborem
        lss_file = "/home/<USER>/vyvoj/limwrapp/data/dotazniky.urad.online/827822/structure.lss"
        
        if not os.path.exists(lss_file):
            logger.error(f"❌ LSS soubor neexistuje: {lss_file}")
            return False
        
        logger.info(f"📄 Testuji hierarchické parsování: {lss_file}")
        
        # Parsování s vylepšeným parserem
        parser = LSSParser()
        survey_data = parser.parse_lss_file(lss_file)
        
        if not survey_data:
            logger.error("❌ Parsování selhalo")
            return False
        
        # Statistiky
        stats = parser.get_survey_statistics(survey_data)
        logger.info("📊 HIERARCHICKÉ STATISTIKY:")
        logger.info(f"   Survey ID: {stats['survey_id']}")
        logger.info(f"   Hlavní otázky: {stats['total_questions']}")
        logger.info(f"   Subotázky: {stats['total_subquestions']}")
        logger.info(f"   Možnosti odpovědí: {stats['total_answer_options']}")
        logger.info(f"   Skupiny: {stats['total_groups']}")
        logger.info(f"   Hierarchické parsování: {stats['hierarchical_parsing']}")
        
        # Analýza typů otázek
        logger.info(f"\n📋 TYPY HLAVNÍCH OTÁZEK:")
        for q_type, count in sorted(stats['question_types'].items()):
            logger.info(f"   {q_type}: {count} otázek")
        
        # Detailní analýza prvních 5 otázek
        questions = survey_data['questions']
        logger.info(f"\n🔍 DETAILNÍ ANALÝZA PRVNÍCH 5 OTÁZEK:")
        logger.info("=" * 60)
        
        for i, question in enumerate(questions[:5]):
            logger.info(f"\n📝 Otázka {i+1}:")
            logger.info(f"   QID: {question.qid}")
            logger.info(f"   Kód: {question.question_code}")
            logger.info(f"   Typ: {question.question_type}")
            logger.info(f"   Text: {question.question_text[:80]}...")
            logger.info(f"   Skupina: {question.group_id}")
            logger.info(f"   Pořadí: {question.question_order}")
            logger.info(f"   Parent QID: {question.parent_qid}")
            logger.info(f"   Je hlavní: {question.is_main_question}")
            
            if question.subquestions:
                logger.info(f"   📋 Subotázky ({len(question.subquestions)}):")
                for j, subq in enumerate(question.subquestions[:3]):  # Max 3
                    logger.info(f"      {j+1}. {subq['code']}: {subq['text'][:50]}...")
                if len(question.subquestions) > 3:
                    logger.info(f"      ... a {len(question.subquestions) - 3} dalších")
            
            if question.answer_options:
                logger.info(f"   ✅ Možnosti odpovědí ({len(question.answer_options)}):")
                for j, opt in enumerate(question.answer_options[:3]):  # Max 3
                    logger.info(f"      {j+1}. {opt['code']}: {opt['text'][:50]}...")
                if len(question.answer_options) > 3:
                    logger.info(f"      ... a {len(question.answer_options) - 3} dalších")
            
            if question.help_text:
                logger.info(f"   💡 Nápověda: {question.help_text[:60]}...")
        
        # Porovnání se starým parserem
        logger.info(f"\n📊 POROVNÁNÍ S PŮVODNÍM PARSEREM:")
        logger.info("=" * 60)
        
        # Simulace starého parseru (všechny otázky jako samostatné)
        old_style_count = 0
        for group in survey_data.get('groups', []):
            old_style_count += len(group.get('questions', []))
        
        improvement = old_style_count - stats['total_questions']
        logger.info(f"   Původní parser: {old_style_count} 'otázek'")
        logger.info(f"   Hierarchický parser: {stats['total_questions']} hlavních otázek")
        logger.info(f"   Zlepšení: -{improvement} redundantních položek ({improvement/old_style_count*100:.1f}% redukce)")
        
        # Kontrola konkrétní otázky (multiple choice)
        logger.info(f"\n🎯 KONTROLA KONKRÉTNÍ MULTIPLE CHOICE OTÁZKY:")
        logger.info("=" * 60)
        
        # Najít otázku typu M
        m_question = None
        for question in questions:
            if question.question_type == 'M':
                m_question = question
                break
        
        if m_question:
            logger.info(f"   📝 Otázka: {m_question.question_text}")
            logger.info(f"   🔢 QID: {m_question.qid}")
            logger.info(f"   📋 Možnosti odpovědí: {len(m_question.answer_options)}")
            
            for i, option in enumerate(m_question.answer_options):
                logger.info(f"      {i+1}. {option['code']}: {option['text']}")
            
            logger.info(f"   ✅ Správně rozpoznáno jako jedna otázka s možnostmi!")
        else:
            logger.warning("   ⚠️ Nenalezena žádná multiple choice otázka")
        
        logger.info(f"\n✅ Hierarchické parsování úspěšné!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu hierarchického parseru: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_menu13_with_hierarchical_parser():
    """Test Menu 13 s hierarchickým parserem"""
    
    try:
        logger.info("\n" + "=" * 80)
        logger.info("TEST MENU 13 S HIERARCHICKÝM PARSEREM")
        logger.info("=" * 80)
        
        from intelligent_analysis import IntelligentAnalysisManager
        
        # Inicializace Analysis Engine
        manager = IntelligentAnalysisManager()
        
        # Test s reálným LSS souborem
        lss_file = "/home/<USER>/vyvoj/limwrapp/data/dotazniky.urad.online/827822/structure.lss"
        
        logger.info(f"📄 Analyzuji s hierarchickým parserem: {lss_file}")
        
        # Spuštění analýzy
        result = manager.analyze_survey_from_lss(lss_file)
        
        if not result:
            logger.error("❌ Analýza s hierarchickým parserem selhala")
            return False
        
        # Porovnání výsledků
        summary = result['summary']
        logger.info("📊 VÝSLEDKY S HIERARCHICKÝM PARSEREM:")
        logger.info(f"   📋 Celkem otázek: {summary['total_questions']}")
        logger.info(f"   🎯 Rozpoznáno: {summary['recognized_questions']} ({summary['recognition_rate']:.1%})")
        logger.info(f"   📈 Doporučené analýzy: {summary['total_analyses']}")
        logger.info(f"   🎨 Vizualizace: {summary['total_visualizations']}")
        logger.info(f"   ⏱️ Odhadovaný čas: {summary['estimated_time_hours']:.1f} hodin")
        logger.info(f"   🔧 Složitost: {summary['complexity_score']:.1f}/10")
        
        # Kontrola kvality analýzy
        questions = result['question_analysis']['questions']
        
        # Najít multiple choice otázku
        mc_questions = [q for q in questions if q['limesurvey_type'] == 'M']
        logger.info(f"\n🎯 MULTIPLE CHOICE OTÁZKY ({len(mc_questions)}):")
        
        for mc_q in mc_questions[:2]:  # Prvních 2
            logger.info(f"   📝 {mc_q['question_code']}: {mc_q['question_text'][:60]}...")
            logger.info(f"   🎯 Rozpoznáno jako: {mc_q.get('matched_type', {}).get('name', 'Nerozpoznáno')}")
            logger.info(f"   📈 Analýzy: {', '.join(mc_q.get('recommended_analyses', []))}")
        
        # Array otázky
        array_questions = [q for q in questions if q['limesurvey_type'] == 'F']
        logger.info(f"\n📊 ARRAY OTÁZKY ({len(array_questions)}):")
        
        for arr_q in array_questions[:2]:  # Prvních 2
            logger.info(f"   📝 {arr_q['question_code']}: {arr_q['question_text'][:60]}...")
            logger.info(f"   🎯 Rozpoznáno jako: {arr_q.get('matched_type', {}).get('name', 'Nerozpoznáno')}")
            logger.info(f"   📈 Analýzy: {', '.join(arr_q.get('recommended_analyses', []))}")
        
        logger.info(f"\n✅ Menu 13 s hierarchickým parserem funguje!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu Menu 13: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Spuštění všech testů hierarchického parseru"""
    
    print("🚀 SPOUŠTÍM TESTY HIERARCHICKÉHO LSS PARSERU")
    
    tests = [
        ("Hierarchické parsování", test_hierarchical_parsing),
        ("Menu 13 s hierarchickým parserem", test_menu13_with_hierarchical_parser)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Spouštím test: {test_name}")
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ Test '{test_name}' úspěšný")
            else:
                logger.error(f"❌ Test '{test_name}' selhal")
                
        except Exception as e:
            logger.error(f"💥 Test '{test_name}' vyvolal výjimku: {str(e)}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 80)
    logger.info("SHRNUTÍ TESTŮ HIERARCHICKÉHO PARSERU")
    logger.info("=" * 80)
    
    successful = 0
    for test_name, success in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        logger.info(f"{status}: {test_name}")
        if success:
            successful += 1
    
    logger.info(f"\nÚspěšnost: {successful}/{len(results)} testů")
    
    if successful == len(results):
        print("\n🎉 VŠECHNY TESTY HIERARCHICKÉHO PARSERU ÚSPĚŠNÉ!")
        print("✅ Hierarchické parsování funguje správně")
        print("✅ Menu 13 rozumí struktuře otázek")
        print("✅ Subotázky a možnosti odpovědí správně přiřazeny")
        print("🚀 Parser je připraven pro produkční použití!")
    else:
        print(f"\n⚠️ {len(results) - successful} testů selhalo")
        print("💡 Zkontrolujte logy výše pro detaily")
    
    return successful == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
