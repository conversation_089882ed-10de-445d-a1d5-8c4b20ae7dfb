#!/usr/bin/env python3
"""
Test sticky filtru

Testuje CSS sticky positioning a Ctrl+F funkcionalitu.
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_sticky_filter_css():
    """Test CSS sticky positioning pro filtr"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST CSS STICKY POSITIONING")
        logger.info("=" * 60)
        
        from datawrapper_export import HTMLGenerator, ChartInfo
        
        # Vytvoření více testovacích grafů pro scrollování
        test_charts = []
        for i in range(10):
            chart = ChartInfo(
                id=f"test{i:03d}",
                title=f"Test Graf {i+1} - Dlouhý název pro testování filtru",
                type="d3-bars",
                status="published",
                url="",
                share_url=f"https://datawrapper.dwcdn.net/test{i:03d}/"
            )
            test_charts.append(chart)
        
        # Generování HTML
        generator = HTMLGenerator()
        output_file = "test_output/test-sticky-filter.html"
        
        os.makedirs("test_output", exist_ok=True)
        
        success = generator.generate_export_html(
            charts=test_charts,
            survey_id="test",
            output_path=output_file,
            survey_name="Test Sticky Filter"
        )
        
        if success:
            # Kontrola CSS
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Kontroly CSS sticky positioning
            checks = [
                ("Sticky position", "position: sticky" in content),
                ("Top positioning", "top: 0" in content),
                ("Z-index", "z-index: 1000" in content),
                ("Filter section", ".filter-section" in content),
                ("Ctrl+F handler", "e.ctrlKey && e.key === 'f'" in content),
                ("ScrollIntoView", "scrollIntoView" in content),
                ("Smooth behavior", "behavior: 'smooth'" in content),
                ("Focus timeout", "setTimeout" in content and "focus()" in content)
            ]
            
            all_passed = True
            for check_name, check_result in checks:
                if check_result:
                    logger.info(f"✅ {check_name}: OK")
                else:
                    logger.error(f"❌ {check_name}: CHYBÍ")
                    all_passed = False
            
            # Kontrola velikosti souboru
            file_size = os.path.getsize(output_file) / 1024
            logger.info(f"📏 Velikost souboru: {file_size:.1f} KB")
            
            if all_passed:
                logger.info("✅ CSS sticky positioning správně implementován!")
                
                # Absolutní cesta pro uživatele
                abs_path = os.path.abspath(output_file)
                logger.info(f"🔗 Test soubor: {abs_path}")
                
                print("\n" + "🎉" * 60)
                print("STICKY FILTER ÚSPĚŠNĚ IMPLEMENTOVÁN!")
                print(f"📁 Test soubor: {abs_path}")
                print("✅ CSS sticky positioning")
                print("✅ Ctrl+F scroll to filter")
                print("✅ Smooth scrolling")
                print("✅ Auto focus a select")
                print("🎉" * 60)
                
                return True
            else:
                logger.error("❌ Některé CSS kontroly selhaly")
                return False
        else:
            logger.error("❌ HTML generování selhalo")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu CSS: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_filter_functionality():
    """Test funkcionality filtru"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST FUNKCIONALITY FILTRU")
        logger.info("=" * 60)
        
        # Načtení template pro kontrolu JS
        template_path = Path(__file__).parent.parent / "src" / "datawrapper_export" / "templates" / "chart_export.html"
        
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Kontroly JavaScript funkcionality
        js_checks = [
            ("Filter function", "function filterCharts()" in template_content),
            ("Clear filter function", "function clearFilter()" in template_content),
            ("Event listeners", "addEventListener('input', filterCharts)" in template_content),
            ("Escape key handler", "e.key === 'Escape'" in template_content),
            ("Ctrl+F prevention", "e.preventDefault()" in template_content),
            ("Visible count update", "visibleCount.textContent" in template_content),
            ("Chart visibility toggle", "item.style.display" in template_content),
            ("No results message", "noResults.style.display" in template_content)
        ]
        
        all_passed = True
        for check_name, check_result in js_checks:
            if check_result:
                logger.info(f"✅ {check_name}: OK")
            else:
                logger.error(f"❌ {check_name}: CHYBÍ")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu JS: {str(e)}")
        return False


def test_responsive_design():
    """Test responsive designu pro sticky filter"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST RESPONSIVE DESIGN")
        logger.info("=" * 60)
        
        # Načtení template
        template_path = Path(__file__).parent.parent / "src" / "datawrapper_export" / "templates" / "chart_export.html"
        
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Kontroly responsive CSS
        responsive_checks = [
            ("Media queries", "@media" in template_content),
            ("Mobile breakpoint", "max-width: 768px" in template_content),
            ("Filter input group", ".filter-input-group" in template_content),
            ("Flex layout", "display: flex" in template_content),
            ("Gap spacing", "gap:" in template_content)
        ]
        
        all_passed = True
        for check_name, check_result in responsive_checks:
            if check_result:
                logger.info(f"✅ {check_name}: OK")
            else:
                logger.warning(f"⚠️ {check_name}: CHYBÍ (může být OK)")
        
        # Sticky positioning by měl fungovat i na mobilech
        sticky_mobile_checks = [
            ("Sticky position", "position: sticky" in template_content),
            ("Top positioning", "top: 0" in template_content),
            ("Z-index", "z-index: 1000" in template_content)
        ]
        
        for check_name, check_result in sticky_mobile_checks:
            if check_result:
                logger.info(f"✅ {check_name} (mobile): OK")
            else:
                logger.error(f"❌ {check_name} (mobile): CHYBÍ")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu responsive: {str(e)}")
        return False


def main():
    """Spuštění všech testů sticky filtru"""
    
    print("🚀 SPOUŠTÍM TESTY STICKY FILTRU")
    
    tests = [
        ("CSS sticky positioning", test_sticky_filter_css),
        ("Funkcionalita filtru", test_filter_functionality),
        ("Responsive design", test_responsive_design)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Spouštím test: {test_name}")
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ Test '{test_name}' úspěšný")
            else:
                logger.error(f"❌ Test '{test_name}' selhal")
                
        except Exception as e:
            logger.error(f"💥 Test '{test_name}' vyvolal výjimku: {str(e)}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SHRNUTÍ TESTŮ STICKY FILTRU")
    logger.info("=" * 60)
    
    successful = 0
    for test_name, success in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        logger.info(f"{status}: {test_name}")
        if success:
            successful += 1
    
    logger.info(f"\nÚspěšnost: {successful}/{len(results)} testů")
    
    if successful == len(results):
        print("\n🎉 VŠECHNY TESTY ÚSPĚŠNÉ!")
        print("✅ Sticky positioning implementován")
        print("✅ Ctrl+F scroll to filter")
        print("✅ Smooth scrolling a auto focus")
        print("✅ Responsive design zachován")
        print("📱 Filtr zůstává viditelný při scrollování")
        print("⌨️ Ctrl+F automaticky scrolluje k filtru")
    else:
        print(f"\n⚠️ {len(results) - successful} testů selhalo")
        print("💡 Zkontrolujte logy výše pro detaily")
    
    return successful == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
