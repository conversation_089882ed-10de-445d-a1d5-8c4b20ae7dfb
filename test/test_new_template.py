#!/usr/bin/env python3
"""
Test nové HTML šablony

Testuje aktualizovanou šablonu s:
- Link ke sd<PERSON><PERSON><PERSON> m<PERSON>
- <PERSON><PERSON><PERSON> nápověda s profesionálním vzhledem
- Stabilní URL bez verzí
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_new_template():
    """Test nové HTML šablony"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST NOVÉ HTML ŠABLONY")
        logger.info("=" * 60)
        
        from datawrapper_export import HTMLGenerator, ChartInfo
        
        # Vytvoření testovacích grafů
        test_charts = [
            ChartInfo(
                id="14J5J",
                title="Phases of Involvement in the Legislative Process (EU & CZ)",
                type="d3-bars-stacked",
                status="published",
                url="",
                share_url="https://datawrapper.dwcdn.net/14J5J/"  # Bez verze
            ),
            ChartInfo(
                id="1rX16",
                title="Level of Agreement with Stated Opinions",
                type="d3-bars-stacked",
                status="published",
                url="",
                share_url="https://datawrapper.dwcdn.net/1rX16/"  # Bez verze
            ),
            ChartInfo(
                id="2fvEE",
                title="Zájem o zapojení do konzultace výstupů projektu",
                type="d3-bars",
                status="published",
                url="",
                share_url="https://datawrapper.dwcdn.net/2fvEE/"  # Bez verze
            )
        ]
        
        # Generování HTML s novou šablonou
        generator = HTMLGenerator()
        output_file = "test_output/survey-827822-new-template.html"
        
        os.makedirs("test_output", exist_ok=True)
        
        success = generator.generate_export_html(
            charts=test_charts,
            survey_id="827822",
            output_path=output_file,
            survey_name="Survey 827822 - Nová šablona s nápovědou"
        )
        
        if success:
            logger.info(f"✅ HTML vygenerován: {output_file}")
            
            # Kontrola obsahu
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Kontroly obsahu
            checks = [
                ("Sekce nápověda", "📊 Nápověda k práci s grafy" in content),
                ("Link ke sdílení", "Link ke sdílení:" in content),
                ("Filtrování grafů", "Filtrování grafů" in content),
                ("Stažení PNG", "Stažení PNG obrázku" in content),
                ("Embed kód", "Sdílení přes embed kód" in content),
                ("Sociální sítě", "Sdílení na sociálních sítích" in content),
                ("Stažení dat", "Stažení dat grafu" in content),
                ("Stabilní URL", "https://datawrapper.dwcdn.net/14J5J/" in content),
                ("Bez verzí", "/14J5J/2/" not in content and "/1rX16/1/" not in content)
            ]
            
            all_passed = True
            for check_name, check_result in checks:
                if check_result:
                    logger.info(f"✅ {check_name}: OK")
                else:
                    logger.error(f"❌ {check_name}: CHYBÍ")
                    all_passed = False
            
            # Kontrola velikosti souboru
            file_size = os.path.getsize(output_file)
            file_size_kb = file_size / 1024
            
            logger.info(f"📏 Velikost souboru: {file_size_kb:.1f} KB")
            
            if all_passed:
                logger.info("✅ Všechny kontroly prošly")
                
                # Absolutní cesta pro uživatele
                abs_path = os.path.abspath(output_file)
                logger.info(f"🔗 Plná cesta: {abs_path}")
                
                print("\n" + "🎉" * 60)
                print("NOVÁ ŠABLONA ÚSPĚŠNĚ VYGENEROVÁNA!")
                print(f"📁 Soubor: {abs_path}")
                print(f"📊 Grafů: {len(test_charts)}")
                print(f"📏 Velikost: {file_size_kb:.1f} KB")
                print("✅ Obsahuje sekci nápověda")
                print("✅ Link ke sdílení místo tlačítka")
                print("✅ Stabilní URL bez verzí")
                print("🎉" * 60)
                
                return True
            else:
                logger.error("❌ Některé kontroly selhaly")
                return False
        else:
            logger.error("❌ HTML generování selhalo")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu šablony: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_template_content_details():
    """Detailní test obsahu šablony"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("DETAILNÍ TEST OBSAHU ŠABLONY")
        logger.info("=" * 60)
        
        # Načtení šablony
        template_path = Path(__file__).parent.parent / "src" / "datawrapper_export" / "templates" / "chart_export.html"
        
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Kontroly šablony
        template_checks = [
            ("Help section CSS", ".help-section" in template_content),
            ("Help grid CSS", ".help-grid" in template_content),
            ("Help item CSS", ".help-item" in template_content),
            ("Chart share info CSS", ".chart-share-info" in template_content),
            ("Help header", "📊 Nápověda k práci s grafy" in template_content),
            ("Filtrování help", "Filtrování grafů" in template_content),
            ("PNG help", "Stažení PNG obrázku" in template_content),
            ("Embed help", "Sdílení přes embed kód" in template_content),
            ("Social help", "Sdílení na sociálních sítích" in template_content),
            ("Data help", "Stažení dat grafu" in template_content),
            ("Link format", "Link ke sdílení:" in template_content),
            ("Responsive help", "help-grid" in template_content and "grid-template-columns: 1fr" in template_content)
        ]
        
        all_passed = True
        for check_name, check_result in template_checks:
            if check_result:
                logger.info(f"✅ {check_name}: OK")
            else:
                logger.error(f"❌ {check_name}: CHYBÍ")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Chyba při detailním testu: {str(e)}")
        return False


def test_url_removal_in_template():
    """Test odstranění verzí z URL v šabloně"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST ODSTRANĚNÍ VERZÍ V ŠABLONĚ")
        logger.info("=" * 60)
        
        from datawrapper_export import HTMLGenerator, ChartInfo
        
        # Graf s verzovanou URL
        test_chart = ChartInfo(
            id="test123",
            title="Test Graf s verzovanou URL",
            type="d3-bars",
            status="published",
            url="",
            share_url="https://datawrapper.dwcdn.net/test123/5/"  # S verzí
        )
        
        generator = HTMLGenerator()
        output_file = "test_output/test-url-removal.html"
        
        success = generator.generate_export_html(
            charts=[test_chart],
            survey_id="test",
            output_path=output_file,
            survey_name="Test URL Removal"
        )
        
        if success:
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Kontrola, že verze byla odstraněna
            if "https://datawrapper.dwcdn.net/test123/" in content and "/test123/5/" not in content:
                logger.info("✅ Verze z URL úspěšně odstraněna")
                return True
            else:
                logger.error("❌ Verze z URL nebyla odstraněna")
                logger.error(f"Obsah obsahuje: {'/test123/5/' in content}")
                return False
        else:
            logger.error("❌ HTML generování selhalo")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu URL: {str(e)}")
        return False


def main():
    """Spuštění všech testů"""
    
    print("🚀 SPOUŠTÍM TESTY NOVÉ HTML ŠABLONY")
    
    tests = [
        ("Obsah šablony", test_template_content_details),
        ("Odstranění verzí URL", test_url_removal_in_template),
        ("Kompletní HTML generování", test_new_template)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Spouštím test: {test_name}")
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ Test '{test_name}' úspěšný")
            else:
                logger.error(f"❌ Test '{test_name}' selhal")
                
        except Exception as e:
            logger.error(f"💥 Test '{test_name}' vyvolal výjimku: {str(e)}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SHRNUTÍ TESTŮ NOVÉ ŠABLONY")
    logger.info("=" * 60)
    
    successful = 0
    for test_name, success in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        logger.info(f"{status}: {test_name}")
        if success:
            successful += 1
    
    logger.info(f"\nÚspěšnost: {successful}/{len(results)} testů")
    
    if successful == len(results):
        print("\n🎉 VŠECHNY TESTY ÚSPĚŠNÉ!")
        print("✅ Nová šablona je připravena")
        print("📊 Sekce nápověda implementována")
        print("🔗 Link ke sdílení místo tlačítka")
        print("🌐 Stabilní URL bez verzí")
        print("📱 Responsive design pro mobily")
    else:
        print(f"\n⚠️ {len(results) - successful} testů selhalo")
        print("💡 Zkontrolujte logy výše pro detaily")
    
    return successful == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
