#!/usr/bin/env python3
"""
Test optimalizace Menu 12

Testuje skutečný workflow Menu 12 s optimalizacemi.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_find_survey_folder_optimization():
    """Test optimalizace find_survey_folder"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST OPTIMALIZACE find_survey_folder")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        
        client = DatawrapperExportClient()
        survey_id = "827822"
        
        # Test 1: Prvn<PERSON> volán<PERSON> (m<PERSON><PERSON><PERSON> b<PERSON><PERSON>oma<PERSON>)
        logger.info("🔍 Test 1: První volání find_survey_folder")
        start_time = time.time()
        
        folder_id_1 = client.find_survey_folder(survey_id)
        
        first_call_time = time.time() - start_time
        
        logger.info(f"✅ První volání: {first_call_time:.2f}s, folder_id: {folder_id_1}")
        
        # Test 2: Druhé volání (mělo by být rychlé díky cache)
        logger.info("🚀 Test 2: Druhé volání (cache)")
        start_time = time.time()
        
        folder_id_2 = client.find_survey_folder(survey_id)
        
        second_call_time = time.time() - start_time
        
        logger.info(f"✅ Druhé volání: {second_call_time:.2f}s, folder_id: {folder_id_2}")
        
        # Kontrola výsledků
        if folder_id_1 == folder_id_2 and folder_id_1:
            cache_speedup = first_call_time / second_call_time if second_call_time > 0 else float('inf')
            logger.info(f"🚀 Cache zrychlení: {cache_speedup:.1f}x")
            
            # Úspěch pokud cache je alespoň 10x rychlejší
            if cache_speedup > 10:
                logger.info("✅ Cache optimalizace úspěšná!")
                return True, first_call_time, second_call_time
            else:
                logger.warning("⚠️ Cache zrychlení není dostatečné")
                return False, first_call_time, second_call_time
        else:
            logger.error("❌ Nekonzistentní výsledky find_survey_folder")
            return False, first_call_time, second_call_time
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu find_survey_folder: {str(e)}")
        return False, 0, 0


def test_complete_menu12_workflow():
    """Test kompletního Menu 12 workflow"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST KOMPLETNÍHO MENU 12 WORKFLOW")
        logger.info("=" * 60)
        
        from datawrapper_export import ExportManager, ExportProgress
        
        # Progress callback pro sledování
        progress_messages = []
        
        def progress_callback(progress: ExportProgress):
            percentage = progress.progress_percentage
            charts_info = ""
            if progress.total_charts > 0:
                charts_info = f" | Grafy: {progress.charts_processed}/{progress.total_charts}"
            
            message = f"🔄 {progress.current_step} ({percentage:.1f}%){charts_info}"
            logger.info(message)
            progress_messages.append(message)
        
        # Měření celkového času
        start_time = time.time()
        
        # Simulace Menu 12 workflow
        logger.info("🚀 Spouštím simulaci Menu 12...")
        
        export_manager = ExportManager(progress_callback=progress_callback)
        
        # Test připojení
        logger.info("🔗 Test připojení...")
        connection_test = export_manager.test_connection()
        
        if not connection_test['success']:
            logger.error("❌ Připojení selhalo")
            return False, 0, []
        
        # Parametry jako v Menu 12
        survey_id = "827822"
        survey_name = "Survey 827822 - Optimalizovaný test"
        language_filter = None  # Všechny jazyky
        output_dir = "test_output"
        force_reconfigure = False  # Nebudeme rekonfigurovat
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Spuštění exportu (stejně jako Menu 12)
        logger.info(f"📊 Spouštím export pro survey {survey_id}...")
        
        result = export_manager.export_survey_charts(
            survey_id=survey_id,
            output_dir=output_dir,
            survey_name=survey_name,
            language_filter=language_filter,
            force_reconfigure=force_reconfigure
        )
        
        total_time = time.time() - start_time
        
        # Analýza výsledků
        logger.info(f"\n📊 VÝSLEDKY MENU 12 WORKFLOW:")
        logger.info(f"⏱️ Celkový čas: {total_time:.1f}s")
        logger.info(f"✅ Úspěch: {result.success}")
        
        if result.success:
            logger.info(f"📁 Výstupní soubor: {result.output_file}")
            logger.info(f"📊 Exportováno grafů: {result.charts_exported}")
            logger.info(f"⏱️ Doba zpracování: {result.execution_time:.1f}s")
            
            # Kontrola velikosti souboru
            if os.path.exists(result.output_file):
                file_size = os.path.getsize(result.output_file) / 1024  # KB
                logger.info(f"📏 Velikost souboru: {file_size:.1f} KB")
        else:
            logger.error("❌ Export selhal")
            if result.errors:
                for error in result.errors:
                    logger.error(f"   - {error}")
        
        # Analýza progress zpráv
        logger.info(f"\n📈 Progress reporting:")
        logger.info(f"   Celkem zpráv: {len(progress_messages)}")
        for msg in progress_messages:
            logger.info(f"   {msg}")
        
        # Úspěch pokud je rychlé a úspěšné
        success = result.success and total_time < 15  # Méně než 15 sekund
        
        if success:
            logger.info("✅ Menu 12 workflow optimalizace úspěšná!")
        else:
            logger.error("❌ Menu 12 workflow stále pomalý nebo neúspěšný")
        
        return success, total_time, progress_messages
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu Menu 12 workflow: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, 0, []


def test_known_mappings():
    """Test známých mapování survey -> folder"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST ZNÁMÝCH MAPOVÁNÍ")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        
        client = DatawrapperExportClient()
        
        # Test známého mapování
        survey_id = "827822"
        expected_folder_id = "329765"
        
        logger.info(f"🔍 Testuji známé mapování: {survey_id} → {expected_folder_id}")
        
        start_time = time.time()
        found_folder_id = client.find_survey_folder(survey_id)
        lookup_time = time.time() - start_time
        
        logger.info(f"⏱️ Čas hledání: {lookup_time:.3f}s")
        logger.info(f"📁 Nalezené ID: {found_folder_id}")
        logger.info(f"📁 Očekávané ID: {expected_folder_id}")
        
        if found_folder_id == expected_folder_id:
            logger.info("✅ Známé mapování funguje správně!")
            
            # Mělo by být velmi rychlé (< 0.1s)
            if lookup_time < 0.1:
                logger.info("🚀 Hledání je velmi rychlé!")
                return True
            else:
                logger.warning("⚠️ Hledání je pomalejší než očekáváno")
                return False
        else:
            logger.error("❌ Známé mapování neodpovídá skutečnosti")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu známých mapování: {str(e)}")
        return False


def main():
    """Spuštění všech testů optimalizace Menu 12"""
    
    print("🚀 SPOUŠTÍM TESTY OPTIMALIZACE MENU 12")
    
    results = {}
    
    # Test 1: Známá mapování
    logger.info("\n🧪 Test 1: Známá mapování")
    mappings_success = test_known_mappings()
    results['mappings'] = mappings_success
    
    # Test 2: Cache optimalizace
    logger.info("\n🧪 Test 2: Cache optimalizace")
    cache_success, first_time, second_time = test_find_survey_folder_optimization()
    results['cache'] = {'success': cache_success, 'first_time': first_time, 'second_time': second_time}
    
    # Test 3: Kompletní Menu 12 workflow
    logger.info("\n🧪 Test 3: Kompletní Menu 12 workflow")
    workflow_success, workflow_time, progress_msgs = test_complete_menu12_workflow()
    results['workflow'] = {'success': workflow_success, 'time': workflow_time, 'progress_count': len(progress_msgs)}
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SHRNUTÍ TESTŮ OPTIMALIZACE MENU 12")
    logger.info("=" * 60)
    
    successful_tests = 0
    total_tests = 3
    
    # Známá mapování
    if results['mappings']:
        logger.info("✅ ÚSPĚCH: Známá mapování")
        successful_tests += 1
    else:
        logger.info("❌ SELHÁNÍ: Známá mapování")
    
    # Cache
    cache_result = results['cache']
    if cache_result['success']:
        speedup = cache_result['first_time'] / cache_result['second_time'] if cache_result['second_time'] > 0 else float('inf')
        logger.info(f"✅ ÚSPĚCH: Cache optimalizace ({speedup:.1f}x rychlejší)")
        successful_tests += 1
    else:
        logger.info("❌ SELHÁNÍ: Cache optimalizace")
    
    # Workflow
    workflow_result = results['workflow']
    if workflow_result['success']:
        logger.info(f"✅ ÚSPĚCH: Menu 12 workflow ({workflow_result['time']:.1f}s)")
        successful_tests += 1
    else:
        logger.info(f"❌ SELHÁNÍ: Menu 12 workflow ({workflow_result['time']:.1f}s)")
    
    logger.info(f"\nÚspěšnost: {successful_tests}/{total_tests} testů")
    
    # Srovnání s původním výkonem
    if workflow_result['success']:
        original_time = 60  # Původní čas v minutách → sekundy
        new_time = workflow_result['time']
        speedup = original_time / new_time if new_time > 0 else 1
        
        print(f"\n🎯 VÝSLEDKY OPTIMALIZACE MENU 12:")
        print(f"⏱️ Původní čas: ~{original_time/60:.1f} minut")
        print(f"⚡ Nový čas: {new_time:.1f}s")
        print(f"🚀 Zrychlení: {speedup:.1f}x rychlejší")
        print(f"📈 Progress zpráv: {workflow_result['progress_count']}")
    
    if successful_tests == total_tests:
        print("\n🎉 VŠECHNY OPTIMALIZACE MENU 12 ÚSPĚŠNÉ!")
        print("✅ Známá mapování implementována")
        print("✅ Cache optimalizace funguje")
        print("✅ Kompletní workflow optimalizován")
        print("🚀 Menu 12 je nyní použitelné v produkci!")
    else:
        print(f"\n⚠️ {total_tests - successful_tests} optimalizací selhalo")
        print("💡 Zkontrolujte logy výše pro detaily")
    
    return successful_tests == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
