#!/usr/bin/env python3
"""
Test LSS Parser

Testuje nový LSS parser modul.
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_lss_parser():
    """Test LSS Parser"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST LSS PARSER")
        logger.info("=" * 60)
        
        from lss_parser import LSSParser
        
        # Inicializace parseru
        parser = LSSParser()
        
        # Test s existujícím LSS souborem
        lss_file = "/home/<USER>/vyvoj/limwrapp/data/dotazniky.urad.online/827822/structure.lss"
        
        if os.path.exists(lss_file):
            logger.info(f"📄 Testuji s LSS souborem: {lss_file}")
            
            # Parsování
            survey_data = parser.parse_lss_file(lss_file)
            
            if survey_data:
                logger.info("✅ LSS soubor úspěšně parsován!")
                
                # Statistiky
                stats = parser.get_survey_statistics(survey_data)
                logger.info(f"📊 Statistiky:")
                logger.info(f"   Survey ID: {stats['survey_id']}")
                logger.info(f"   Celkem otázek: {stats['total_questions']}")
                logger.info(f"   Celkem skupin: {stats['total_groups']}")
                logger.info(f"   Formát: {stats['format']}")
                
                # Typy otázek
                if stats['question_types']:
                    logger.info(f"   Typy otázek:")
                    for q_type, count in stats['question_types'].items():
                        logger.info(f"     {q_type}: {count}")
                
                # Test konkrétní otázky
                questions = survey_data['questions']
                if questions:
                    first_question = questions[0]
                    logger.info(f"📝 První otázka:")
                    logger.info(f"   QID: {first_question.qid}")
                    logger.info(f"   Kód: {first_question.question_code}")
                    logger.info(f"   Text: {first_question.question_text[:100]}...")
                    logger.info(f"   Typ: {first_question.question_type}")
                    logger.info(f"   Subotázky: {len(first_question.subquestions)}")
                    logger.info(f"   Možnosti odpovědí: {len(first_question.answer_options)}")
                
                return True
            else:
                logger.error("❌ Parsování LSS souboru selhalo")
                return False
        else:
            logger.warning(f"⚠️ LSS soubor neexistuje: {lss_file}")
            logger.info("🔧 Testuji s dummy daty...")
            
            # Test s dummy JSON daty
            return test_with_dummy_data(parser)
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu LSS parser: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_with_dummy_data(parser):
    """Test s dummy JSON daty"""
    
    try:
        # Vytvoření dummy JSON LSS souboru
        dummy_data = {
            "survey_id": "test_survey",
            "questions": [
                {
                    "qid": "1",
                    "title": "satisfaction",
                    "question": "Jak jste spokojeni s naší službou?",
                    "type": "5",
                    "gid": "1",
                    "subquestions": [],
                    "answeroptions": [
                        {"code": "1", "answer": "Velmi nespokojen"},
                        {"code": "2", "answer": "Nespokojen"},
                        {"code": "3", "answer": "Neutrální"},
                        {"code": "4", "answer": "Spokojen"},
                        {"code": "5", "answer": "Velmi spokojen"}
                    ],
                    "attributes": {}
                },
                {
                    "qid": "2",
                    "title": "comments",
                    "question": "Máte nějaké komentáře?",
                    "type": "T",
                    "gid": "1",
                    "subquestions": [],
                    "answeroptions": [],
                    "attributes": {}
                }
            ],
            "groups": [
                {
                    "gid": "1",
                    "group_name": "Hlavní sekce",
                    "description": "Základní otázky",
                    "group_order": "1"
                }
            ]
        }
        
        # Uložení dummy dat
        dummy_file = "test_dummy_survey.json"
        import json
        with open(dummy_file, 'w', encoding='utf-8') as f:
            json.dump(dummy_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 Testuji s dummy JSON: {dummy_file}")
        
        # Parsování dummy dat
        survey_data = parser.parse_lss_file(dummy_file)
        
        if survey_data:
            logger.info("✅ Dummy JSON úspěšně parsován!")
            
            # Statistiky
            stats = parser.get_survey_statistics(survey_data)
            logger.info(f"📊 Dummy statistiky:")
            logger.info(f"   Survey ID: {stats['survey_id']}")
            logger.info(f"   Celkem otázek: {stats['total_questions']}")
            logger.info(f"   Celkem skupin: {stats['total_groups']}")
            
            # Úklid
            os.remove(dummy_file)
            
            return True
        else:
            logger.error("❌ Parsování dummy JSON selhalo")
            if os.path.exists(dummy_file):
                os.remove(dummy_file)
            return False
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu s dummy daty: {str(e)}")
        return False


def test_menu13_integration():
    """Test integrace s Menu 13"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST INTEGRACE MENU 13")
        logger.info("=" * 60)
        
        from intelligent_analysis import IntelligentAnalysisManager
        
        # Inicializace manageru
        manager = IntelligentAnalysisManager()
        
        # Test s dummy LSS souborem
        dummy_data = {
            "survey_id": "test_integration",
            "questions": [
                {
                    "qid": "Q1",
                    "title": "satisfaction",
                    "question": "Spokojenost s produktem",
                    "type": "5",
                    "gid": "G1",
                    "subquestions": [],
                    "answeroptions": [
                        {"code": "1", "answer": "Velmi nespokojen"},
                        {"code": "5", "answer": "Velmi spokojen"}
                    ],
                    "attributes": {}
                }
            ],
            "groups": []
        }
        
        # Uložení dummy dat
        dummy_file = "test_integration_survey.json"
        import json
        with open(dummy_file, 'w', encoding='utf-8') as f:
            json.dump(dummy_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 Testuji integraci s: {dummy_file}")
        
        # Test analýzy
        result = manager.analyze_survey_from_lss(dummy_file)
        
        if result:
            logger.info("✅ Integrace s Menu 13 funguje!")
            
            summary = result['summary']
            logger.info(f"📊 Výsledky integrace:")
            logger.info(f"   Celkem otázek: {summary['total_questions']}")
            logger.info(f"   Rozpoznáno: {summary['recognized_questions']}")
            logger.info(f"   Úspěšnost: {summary['recognition_rate']:.1%}")
            
            # Úklid
            os.remove(dummy_file)
            
            return True
        else:
            logger.error("❌ Integrace s Menu 13 selhala")
            if os.path.exists(dummy_file):
                os.remove(dummy_file)
            return False
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu integrace: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Spuštění všech testů LSS Parser"""
    
    print("🚀 SPOUŠTÍM TESTY LSS PARSER")
    
    tests = [
        ("LSS Parser", test_lss_parser),
        ("Integrace Menu 13", test_menu13_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Spouštím test: {test_name}")
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ Test '{test_name}' úspěšný")
            else:
                logger.error(f"❌ Test '{test_name}' selhal")
                
        except Exception as e:
            logger.error(f"💥 Test '{test_name}' vyvolal výjimku: {str(e)}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SHRNUTÍ TESTŮ LSS PARSER")
    logger.info("=" * 60)
    
    successful = 0
    for test_name, success in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        logger.info(f"{status}: {test_name}")
        if success:
            successful += 1
    
    logger.info(f"\nÚspěšnost: {successful}/{len(results)} testů")
    
    if successful == len(results):
        print("\n🎉 VŠECHNY TESTY LSS PARSER ÚSPĚŠNÉ!")
        print("✅ LSS Parser funguje správně")
        print("✅ Integrace s Menu 13 dokončena")
        print("🚀 Menu 13 je nyní plně funkční!")
    else:
        print(f"\n⚠️ {len(results) - successful} testů selhalo")
        print("💡 Zkontrolujte logy výše pro detaily")
    
    return successful == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
