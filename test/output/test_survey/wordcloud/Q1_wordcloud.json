{"chart_info": {"generator": "wordcloud", "chart_type": "wordcloud", "created_at": "2025-06-27T13:22:23.082769", "file_path": "test/output/test_survey/wordcloud/Q1_wordcloud.png"}, "config": {"chart_type": "wordcloud", "generator": "internal_wordcloud", "parameters": {"max_words": 150, "color_scheme": "blue", "width": 1000, "height": 500, "background_color": "white", "use_ai": true, "prompt_template": "general_keywords"}, "survey_id": "test_survey", "question_id": "Q1"}, "data_summary": {"data_keys": ["text_length", "frequencies"], "total_items": 6}, "result": {"success": true, "generator": "wordcloud", "chart_type": "wordcloud", "output_path": "test/output/test_survey/wordcloud/Q1_wordcloud.png", "file_size": 48211, "timestamp": "2025-06-27T13:22:23.082731", "frequencies": {"kvalita": 1.0, "služby": 1.0, "rychlé": 1.0, "vyřízení": 1.0, "spokojenost": 1.0, "zákazníci": 1.0}, "wordcloud_config": {"max_words": 150, "min_word_length": 3, "width": 1000, "height": 500, "background_color": "white", "color_scheme": "blue", "output_format": "png", "dpi": 300, "use_ai": true, "language": "cs", "prompt_template": "general_keywords", "visualization_type": "WCS"}, "ai_enhanced": false}}