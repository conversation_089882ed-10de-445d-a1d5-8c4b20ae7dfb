#!/usr/bin/env python3
"""
Analýza výkonu Datawrapper export

Měř<PERSON> časy jednotlivých operací a identifikuje bottlenecky.
"""

import os
import sys
import time
import logging
from pathlib import Path
from typing import List, Dict

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PerformanceTimer:
    """Měření výkonu s detailními časovými značkami"""
    
    def __init__(self):
        self.times = {}
        self.start_time = None
        self.current_operation = None
    
    def start(self, operation: str):
        """Začátek měření operace"""
        if self.current_operation:
            self.end()
        
        self.current_operation = operation
        self.start_time = time.time()
        logger.info(f"🔄 Začínám: {operation}")
    
    def end(self):
        """Konec měření aktuální operace"""
        if self.current_operation and self.start_time:
            duration = time.time() - self.start_time
            self.times[self.current_operation] = duration
            logger.info(f"✅ Dokončeno: {self.current_operation} ({duration:.2f}s)")
            self.current_operation = None
            self.start_time = None
    
    def checkpoint(self, checkpoint_name: str):
        """Mezičas v rámci operace"""
        if self.start_time:
            elapsed = time.time() - self.start_time
            logger.info(f"   ⏱️ {checkpoint_name}: {elapsed:.2f}s")
    
    def get_summary(self) -> Dict[str, float]:
        """Vrátí souhrn všech časů"""
        if self.current_operation:
            self.end()
        return self.times.copy()


def test_folder_loading_performance():
    """Test rychlosti načítání složky"""
    
    timer = PerformanceTimer()
    
    try:
        logger.info("=" * 60)
        logger.info("ANALÝZA VÝKONU - NAČÍTÁNÍ SLOŽKY")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        
        # Inicializace
        timer.start("Inicializace klienta")
        client = DatawrapperExportClient()
        timer.end()
        
        folder_id = "329765"  # Survey 827822
        
        # Test 1: Načtení základních informací o složce
        timer.start("Načtení základních informací složky")
        response = client._make_request('GET', f'folders/{folder_id}')
        if response.status_code == 200:
            data = response.json()
            chart_ids = [item['id'] for item in data.get('charts', [])]
            timer.checkpoint(f"Získáno {len(chart_ids)} ID grafů")
        timer.end()
        
        # Test 2: Načtení detailů grafů (současný přístup)
        timer.start("Načtení detailů všech grafů (současný přístup)")
        charts_detailed = []
        
        for i, chart_id in enumerate(chart_ids, 1):
            if i % 10 == 0:  # Každých 10 grafů
                timer.checkpoint(f"Načteno {i}/{len(chart_ids)} grafů")
            
            try:
                chart_response = client._make_request('GET', f'charts/{chart_id}')
                if chart_response.status_code == 200:
                    chart_data = chart_response.json()
                    charts_detailed.append({
                        'id': chart_id,
                        'title': chart_data.get('title', 'Bez názvu'),
                        'share_url': chart_data.get('publicUrl', '')
                    })
            except Exception as e:
                logger.warning(f"Chyba při načítání {chart_id}: {str(e)}")
        
        timer.end()
        
        # Test 3: Měření jednotlivých API volání
        timer.start("Měření rychlosti jednotlivých API volání")
        
        sample_ids = chart_ids[:5]  # Prvních 5 pro test
        api_times = []
        
        for chart_id in sample_ids:
            start = time.time()
            response = client._make_request('GET', f'charts/{chart_id}')
            duration = time.time() - start
            api_times.append(duration)
            logger.info(f"   API volání {chart_id}: {duration:.3f}s")
        
        avg_api_time = sum(api_times) / len(api_times)
        estimated_total = avg_api_time * len(chart_ids)
        
        timer.checkpoint(f"Průměrný čas API volání: {avg_api_time:.3f}s")
        timer.checkpoint(f"Odhadovaný celkový čas: {estimated_total:.1f}s")
        timer.end()
        
        return timer.get_summary(), {
            'total_charts': len(chart_ids),
            'avg_api_time': avg_api_time,
            'estimated_total': estimated_total,
            'charts_loaded': len(charts_detailed)
        }
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu výkonu: {str(e)}")
        return timer.get_summary(), {}


def test_optimized_loading():
    """Test optimalizovaného načítání"""
    
    timer = PerformanceTimer()
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST OPTIMALIZOVANÉHO NAČÍTÁNÍ")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        import concurrent.futures
        import threading
        
        client = DatawrapperExportClient()
        folder_id = "329765"
        
        # Získání ID grafů
        timer.start("Získání seznamu grafů")
        response = client._make_request('GET', f'folders/{folder_id}')
        chart_ids = [item['id'] for item in response.json().get('charts', [])]
        timer.end()
        
        # Test paralelního načítání
        timer.start("Paralelní načítání grafů (ThreadPoolExecutor)")
        
        def load_chart_details(chart_id):
            """Načte detaily jednoho grafu"""
            try:
                response = client._make_request('GET', f'charts/{chart_id}')
                if response.status_code == 200:
                    data = response.json()
                    return {
                        'id': chart_id,
                        'title': data.get('title', 'Bez názvu'),
                        'share_url': data.get('publicUrl', ''),
                        'success': True
                    }
            except Exception as e:
                logger.warning(f"Chyba při načítání {chart_id}: {str(e)}")
            
            return {'id': chart_id, 'success': False}
        
        # Paralelní zpracování s progress reportingem
        charts_parallel = []
        completed = 0
        total = len(chart_ids)
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            # Spuštění všech úloh
            future_to_id = {executor.submit(load_chart_details, chart_id): chart_id 
                           for chart_id in chart_ids}
            
            # Zpracování výsledků s progress reportingem
            for future in concurrent.futures.as_completed(future_to_id):
                result = future.result()
                charts_parallel.append(result)
                completed += 1
                
                if completed % 5 == 0 or completed == total:
                    progress = (completed / total) * 100
                    timer.checkpoint(f"Dokončeno {completed}/{total} grafů ({progress:.1f}%)")
        
        timer.end()
        
        successful_parallel = sum(1 for chart in charts_parallel if chart.get('success'))
        
        return timer.get_summary(), {
            'total_charts': len(chart_ids),
            'successful_parallel': successful_parallel,
            'parallel_success_rate': successful_parallel / len(chart_ids) * 100
        }
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu optimalizace: {str(e)}")
        return timer.get_summary(), {}


def test_export_workflow_performance():
    """Test výkonu celého export workflow"""
    
    timer = PerformanceTimer()
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("ANALÝZA VÝKONU - CELÝ WORKFLOW")
        logger.info("=" * 60)
        
        from datawrapper_export import ExportManager
        
        # Simulace export workflow s měřením
        timer.start("Inicializace ExportManager")
        export_manager = ExportManager()
        timer.end()
        
        timer.start("Test připojení")
        connection_test = export_manager.test_connection()
        timer.end()
        
        if connection_test['success']:
            logger.info(f"✅ Připojení úspěšné: {connection_test['folders_count']} složek")
        else:
            logger.error("❌ Připojení selhalo")
            return timer.get_summary(), {}
        
        # Test find_survey_folder
        timer.start("Hledání složky survey")
        from datawrapper_export import DatawrapperExportClient
        client = DatawrapperExportClient()
        folder_id = client.find_survey_folder("827822")
        timer.end()
        
        if not folder_id:
            logger.error("❌ Složka nenalezena")
            return timer.get_summary(), {}
        
        # Test načtení grafů
        timer.start("Načtení všech grafů ze složky")
        charts = client.get_charts_in_folder(folder_id)
        timer.end()
        
        # Test validace
        timer.start("Validace grafů")
        from datawrapper_export import HTMLGenerator
        html_generator = HTMLGenerator()
        validation = html_generator.validate_charts_for_export(charts)
        timer.end()
        
        # Test HTML generování
        timer.start("Generování HTML")
        output_file = "test_output/performance-test.html"
        os.makedirs("test_output", exist_ok=True)
        
        html_success = html_generator.generate_export_html(
            charts=charts,
            survey_id="827822",
            output_path=output_file,
            survey_name="Performance Test"
        )
        timer.end()
        
        return timer.get_summary(), {
            'charts_loaded': len(charts),
            'validation_passed': validation['validation_passed'],
            'html_generated': html_success,
            'valid_charts': validation['valid_charts']
        }
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu workflow: {str(e)}")
        return timer.get_summary(), {}


def main():
    """Spuštění analýzy výkonu"""
    
    print("🚀 SPOUŠTÍM ANALÝZU VÝKONU DATAWRAPPER EXPORT")
    
    all_times = {}
    all_stats = {}
    
    # Test 1: Načítání složky
    logger.info("\n🧪 Test 1: Analýza načítání složky")
    times1, stats1 = test_folder_loading_performance()
    all_times.update({f"Test1_{k}": v for k, v in times1.items()})
    all_stats['folder_loading'] = stats1
    
    # Test 2: Optimalizované načítání
    logger.info("\n🧪 Test 2: Optimalizované načítání")
    times2, stats2 = test_optimized_loading()
    all_times.update({f"Test2_{k}": v for k, v in times2.items()})
    all_stats['optimized_loading'] = stats2
    
    # Test 3: Celý workflow
    logger.info("\n🧪 Test 3: Celý workflow")
    times3, stats3 = test_export_workflow_performance()
    all_times.update({f"Test3_{k}": v for k, v in times3.items()})
    all_stats['full_workflow'] = stats3
    
    # Analýza výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SOUHRN ANALÝZY VÝKONU")
    logger.info("=" * 60)
    
    # Časové analýzy
    logger.info("⏱️ ČASOVÉ ANALÝZY:")
    for operation, duration in sorted(all_times.items(), key=lambda x: x[1], reverse=True):
        logger.info(f"   {operation}: {duration:.2f}s")
    
    # Statistiky
    logger.info("\n📊 STATISTIKY:")
    for test_name, stats in all_stats.items():
        logger.info(f"   {test_name}:")
        for key, value in stats.items():
            logger.info(f"      {key}: {value}")
    
    # Doporučení
    logger.info("\n💡 DOPORUČENÍ PRO OPTIMALIZACI:")
    
    # Analýza bottlenecků
    if 'folder_loading' in all_stats:
        stats = all_stats['folder_loading']
        if 'avg_api_time' in stats:
            avg_time = stats['avg_api_time']
            total_charts = stats.get('total_charts', 0)
            
            if avg_time > 0.5:
                logger.info("   🐌 API volání jsou pomalá (>0.5s)")
                logger.info("   💡 Doporučení: Paralelizace nebo batch requesty")
            
            if total_charts > 20:
                logger.info("   📊 Velký počet grafů")
                logger.info("   💡 Doporučení: Progress reporting každých 5 grafů")
    
    # Srovnání sekvenčního vs paralelního
    if 'folder_loading' in all_times and 'optimized_loading' in all_times:
        sequential_key = next((k for k in all_times.keys() if 'Test1_Načtení detailů' in k), None)
        parallel_key = next((k for k in all_times.keys() if 'Test2_Paralelní načítání' in k), None)
        
        if sequential_key and parallel_key:
            sequential_time = all_times[sequential_key]
            parallel_time = all_times[parallel_key]
            speedup = sequential_time / parallel_time if parallel_time > 0 else 1
            
            logger.info(f"   ⚡ Paralelizace: {speedup:.1f}x rychlejší")
            if speedup > 2:
                logger.info("   ✅ Paralelizace výrazně pomáhá!")
            else:
                logger.info("   ⚠️ Paralelizace pomáhá méně (možná API limit)")
    
    print("\n🎯 ANALÝZA DOKONČENA!")
    print("📋 Zkontrolujte logy výše pro detailní doporučení")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
