#!/usr/bin/env python3
"""
Test kompletní integrace všech komponent

Testuje všechny implementované komponenty z nedokončeného projektu.
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_json_descriptions_integration():
    """Test integrace JSON popisů"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST JSON POPISŮ")
        logger.info("=" * 60)
        
        from analysis_engine import MetadataLoader
        
        loader = MetadataLoader()
        success = loader.load_all_metadata()
        
        if not success:
            logger.error("❌ Načítání metadata selhalo")
            return False
        
        # Test detailních popisů
        analysis_descriptions = len(loader.analysis_descriptions)
        viz_descriptions = len(loader.visualization_descriptions)
        
        logger.info(f"📊 Detailní popisy analýz: {analysis_descriptions}")
        logger.info(f"🎨 Detailní popisy vizualizací: {viz_descriptions}")
        
        # Test konkrétních popisů
        test_analysis = loader.get_detailed_analysis_description('FRA')
        test_viz = loader.get_detailed_visualization_description('BAR')
        
        logger.info(f"✅ Test popis analýzy FRA: {'Dostupný' if test_analysis else 'Chybí'}")
        logger.info(f"✅ Test popis vizualizace BAR: {'Dostupný' if test_viz else 'Chybí'}")
        
        # Test rozšířených informací
        analysis_details = loader.get_analysis_with_details('FRA')
        viz_details = loader.get_visualization_with_details('BAR')
        
        success = (analysis_descriptions > 0 and viz_descriptions > 0 and 
                  analysis_details is not None and viz_details is not None)
        
        if success:
            logger.info("✅ JSON popisy úspěšně integrovány!")
        else:
            logger.error("❌ JSON popisy nejsou správně integrovány")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu JSON popisů: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_metadata_manager():
    """Test Metadata Manager"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST METADATA MANAGER")
        logger.info("=" * 60)
        
        from analysis_engine import MetadataLoader, MetadataManager
        
        # Inicializace
        loader = MetadataLoader()
        loader.load_all_metadata()
        
        manager = MetadataManager(loader)
        
        # Test validace
        logger.info("🔍 Test validace konzistence...")
        errors = manager.validate_metadata_consistency()
        total_errors = sum(len(v) for v in errors.values())
        logger.info(f"   Nalezeno {total_errors} problémů")
        
        # Test generování registrů
        logger.info("📁 Test generování registru analýz...")
        analysis_registry_success = manager.generate_analysis_registry()
        
        logger.info("🎨 Test generování registru vizualizací...")
        viz_registry_success = manager.generate_visualization_registry()
        
        # Test exportu souhrnu
        logger.info("📤 Test exportu souhrnu...")
        summary = manager.export_metadata_summary()
        summary_success = len(summary) > 0
        
        # Test přidání nového typu
        logger.info("➕ Test přidání nové analýzy...")
        test_analysis = {
            'id': 'TEST',
            'name': 'Test Analýza',
            'description': 'Testovací analýza',
            'analysis_type': 'question',
            'priority': 3
        }
        add_analysis_success = manager.add_new_analysis_type(test_analysis)
        
        # Test přidání nové vizualizace
        logger.info("🖼️ Test přidání nové vizualizace...")
        test_viz = {
            'id': 'TEST',
            'name': 'Test Vizualizace',
            'description': 'Testovací vizualizace',
            'priority': 3,
            'datawrapper_type': 'd3-bars'
        }
        add_viz_success = manager.add_new_visualization_type(test_viz)
        
        # Vyhodnocení
        all_tests = [
            analysis_registry_success,
            viz_registry_success,
            summary_success,
            add_analysis_success,
            add_viz_success
        ]
        
        success = all(all_tests)
        
        logger.info(f"📊 Výsledky testů:")
        logger.info(f"   Registr analýz: {'✅' if analysis_registry_success else '❌'}")
        logger.info(f"   Registr vizualizací: {'✅' if viz_registry_success else '❌'}")
        logger.info(f"   Export souhrnu: {'✅' if summary_success else '❌'}")
        logger.info(f"   Přidání analýzy: {'✅' if add_analysis_success else '❌'}")
        logger.info(f"   Přidání vizualizace: {'✅' if add_viz_success else '❌'}")
        
        if success:
            logger.info("✅ Metadata Manager funguje správně!")
        else:
            logger.error("❌ Některé funkce Metadata Manager selhaly")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu Metadata Manager: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_yaml_metadata():
    """Test YAML metadata"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST YAML METADATA")
        logger.info("=" * 60)
        
        from analysis_engine import MetadataLoader
        
        loader = MetadataLoader()
        loader.load_all_metadata()
        
        # Test YAML souhrnu
        yaml_summary = loader.get_yaml_metadata_summary()
        
        logger.info(f"📋 YAML konfigurace:")
        logger.info(f"   Celkem konfigurací: {yaml_summary['total_configs']}")
        logger.info(f"   Konfigurace vizualizací: {yaml_summary['visualization_configs_count']}")
        logger.info(f"   Registr: {len(yaml_summary['registries'])}")
        
        # Test pokročilých parametrů
        logger.info("🔧 Test pokročilých parametrů...")
        advanced_params = loader.get_advanced_parameters('BAR')
        
        logger.info(f"   Pokročilé parametry pro BAR: {'Dostupné' if advanced_params else 'Nedostupné'}")
        
        # Test YAML konfigurace pro vizualizaci
        yaml_config = loader.get_yaml_config_for_visualization('BAR')
        logger.info(f"   YAML konfigurace pro BAR: {'Dostupná' if yaml_config else 'Nedostupná'}")
        
        # Test implementation status
        impl_status = loader.get_implementation_status_from_yaml('BAR', 'visualization')
        logger.info(f"   Implementation status pro BAR: {impl_status or 'Nedostupný'}")
        
        success = yaml_summary['total_configs'] >= 0  # Může být 0 pokud YAML soubory neexistují
        
        if success:
            logger.info("✅ YAML metadata systém funguje!")
        else:
            logger.error("❌ YAML metadata systém nefunguje")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu YAML metadata: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_menu14_integration():
    """Test integrace Menu 14"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST INTEGRACE MENU 14")
        logger.info("=" * 60)
        
        # Test importu hlavní aplikace
        try:
            import main
            logger.info("✅ Import main.py úspěšný")
        except ImportError as e:
            logger.error(f"❌ Import main.py selhal: {e}")
            return False
        
        # Test existence funkce metadata_management_menu
        if hasattr(main, 'metadata_management_menu'):
            logger.info("✅ Funkce metadata_management_menu existuje")
        else:
            logger.error("❌ Funkce metadata_management_menu neexistuje")
            return False
        
        # Test importu všech potřebných modulů
        try:
            from analysis_engine import MetadataLoader, MetadataManager
            logger.info("✅ Import všech Analysis Engine modulů úspěšný")
        except ImportError as e:
            logger.error(f"❌ Import Analysis Engine modulů selhal: {e}")
            return False
        
        logger.info("✅ Menu 14 je připraveno k použití!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu integrace Menu 14: {str(e)}")
        return False


def test_file_structure():
    """Test struktury souborů"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST STRUKTURY SOUBORŮ")
        logger.info("=" * 60)
        
        # Kontrola existence klíčových souborů
        files_to_check = [
            # JSON popisy
            "data/analysis_metadata/descriptions_analysis.json",
            "data/analysis_metadata/descriptions_visualization.json",
            
            # Metadata Manager
            "src/analysis_engine/metadata_manager.py",
            
            # Metadata Generator
            "src/metadata_generator/generator.py",
            "src/metadata_generator/config.py",
            
            # YAML metadata
            "data/analysis_metadata/yaml_metadata",
            
            # Dokumentace
            "docs/analysis-engine.md"
        ]
        
        all_exist = True
        for file_path in files_to_check:
            if os.path.exists(file_path):
                logger.info(f"✅ {file_path}")
            else:
                logger.error(f"❌ {file_path} CHYBÍ")
                all_exist = False
        
        # Kontrola generovaných adresářů
        generated_dirs = [
            "data/analysis_metadata/generated",
            "analysis_reports"
        ]
        
        for dir_path in generated_dirs:
            os.makedirs(dir_path, exist_ok=True)
            if os.path.exists(dir_path):
                logger.info(f"✅ Adresář: {dir_path}")
            else:
                logger.error(f"❌ Adresář: {dir_path} NELZE VYTVOŘIT")
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu struktury: {str(e)}")
        return False


def main():
    """Spuštění všech testů kompletní integrace"""
    
    print("🚀 SPOUŠTÍM TESTY KOMPLETNÍ INTEGRACE")
    
    tests = [
        ("Struktura souborů", test_file_structure),
        ("JSON popisy", test_json_descriptions_integration),
        ("Metadata Manager", test_metadata_manager),
        ("YAML metadata", test_yaml_metadata),
        ("Integrace Menu 14", test_menu14_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Spouštím test: {test_name}")
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ Test '{test_name}' úspěšný")
            else:
                logger.error(f"❌ Test '{test_name}' selhal")
                
        except Exception as e:
            logger.error(f"💥 Test '{test_name}' vyvolal výjimku: {str(e)}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SHRNUTÍ TESTŮ KOMPLETNÍ INTEGRACE")
    logger.info("=" * 60)
    
    successful = 0
    for test_name, success in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        logger.info(f"{status}: {test_name}")
        if success:
            successful += 1
    
    logger.info(f"\nÚspěšnost: {successful}/{len(results)} testů")
    
    if successful == len(results):
        print("\n🎉 VŠECHNY TESTY KOMPLETNÍ INTEGRACE ÚSPĚŠNÉ!")
        print("✅ JSON popisy integrovány")
        print("✅ Metadata Manager funkční")
        print("✅ YAML metadata systém připraven")
        print("✅ Menu 14 implementováno")
        print("✅ Struktura souborů kompletní")
        print("🚀 Kompletní integrace nedokončeného projektu dokončena!")
        print("\n💡 Nyní máte k dispozici:")
        print("   - Menu 13: Inteligentní analýza průzkumu")
        print("   - Menu 14: Správa metadata Analysis Engine")
        print("   - Detailní popisy všech analýz a vizualizací")
        print("   - Pokročilé YAML konfigurace")
        print("   - Nástroje pro správu a rozšiřování metadata")
    else:
        print(f"\n⚠️ {len(results) - successful} testů selhalo")
        print("💡 Zkontrolujte logy výše pro detaily")
    
    return successful == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
