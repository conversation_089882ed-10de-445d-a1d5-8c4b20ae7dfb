#!/usr/bin/env python3
"""
Test vytvoření grafu a konfigurace metadata

Workflow:
1. Vytvoření funkčního grafu s daty
2. Publikování grafu BEZ export metadata
3. Zobrazení URL grafu pro kontrolu
4. Přenastavení metadata pro export
5. Republish s novými nastaveními
6. Generování HTML s grafem
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from datawrapper_client import DatawrapperClient
from datawrapper_export import (
    DatawrapperExportClient,
    ChartConfigurator,
    HTMLGenerator,
    ChartInfo
)

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ChartCreationAndMetadataTest:
    """Test vytvoření grafu a konfigurace metadata"""
    
    def __init__(self):
        self.dw_client = None
        self.export_client = None
        self.test_chart_id = None
        self.initial_share_url = None
        self.final_share_url = None
        
    def step_1_create_chart(self):
        """Krok 1: Vytvoření funkčního grafu"""
        logger.info("=" * 60)
        logger.info("KROK 1: Vytvoření funkčního grafu")
        logger.info("=" * 60)
        
        try:
            # Inicializace klienta
            self.dw_client = DatawrapperClient()
            logger.info("✅ DatawrapperClient inicializován")
            
            # Vytvoření grafu
            chart_data = self.dw_client.create_chart(
                title="Test Graf - Metadata Workflow",
                chart_type="d3-bars",
                description="Test grafu pro metadata workflow",
                data_source="LimWrapp Test",
                byline="Test Data"
            )
            
            if not chart_data or 'id' not in chart_data:
                logger.error("❌ Nepodařilo se vytvořit graf")
                return False
            
            self.test_chart_id = chart_data['id']
            logger.info(f"✅ Graf vytvořen s ID: {self.test_chart_id}")
            
            # Přidání správných dat pro sloupcový graf
            csv_data = """Odpověď,Počet respondentů
Velmi spokojen,45
Spokojen,32
Neutrální,18
Nespokojen,8
Velmi nespokojen,3"""
            
            logger.info("📊 Přidávám data do grafu...")
            data_success = self.dw_client.update_chart_data(self.test_chart_id, csv_data)
            
            if not data_success:
                logger.error("❌ Nepodařilo se přidat data")
                return False
            
            logger.info("✅ Data úspěšně přidána")
            
            # Počkáme na zpracování
            time.sleep(3)
            
            # Nastavení základních metadata pro správné zobrazení
            basic_metadata = {
                "data": {
                    "column-format": {
                        "Odpověď": {"type": "text", "input-format": "auto"},
                        "Počet respondentů": {"type": "number", "input-format": "auto"}
                    }
                },
                "visualize": {
                    "chart": {
                        "margin": {"top": 20, "right": 20, "bottom": 40, "left": 60}
                    },
                    "show-color-key": False
                }
            }
            
            logger.info("⚙️ Nastavuji základní metadata...")
            metadata_success = self.dw_client.update_chart_metadata(self.test_chart_id, basic_metadata)
            
            if metadata_success:
                logger.info("✅ Základní metadata nastavena")
            else:
                logger.warning("⚠️ Nepodařilo se nastavit metadata")
            
            time.sleep(2)
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při vytváření grafu: {str(e)}")
            return False
    
    def step_2_initial_publish(self):
        """Krok 2: Publikování grafu BEZ export metadata"""
        logger.info("\n" + "=" * 60)
        logger.info("KROK 2: Publikování grafu BEZ export metadata")
        logger.info("=" * 60)
        
        try:
            # Publikování grafu
            logger.info("📤 Publikuji graf...")
            share_url = self.dw_client.publish_chart(self.test_chart_id)
            
            if not share_url:
                logger.error("❌ Nepodařilo se publikovat graf")
                return False
            
            self.initial_share_url = share_url
            logger.info(f"✅ Graf publikován: {share_url}")
            
            # Zobrazení informací o grafu
            chart_info = self.dw_client.get_chart(self.test_chart_id)
            if chart_info:
                logger.info(f"📊 Název: {chart_info.get('title', 'N/A')}")
                logger.info(f"📊 Typ: {chart_info.get('type', 'N/A')}")
                logger.info(f"📊 Status: {chart_info.get('publicStatus', 'N/A')}")
            
            print("\n" + "🔗" * 60)
            print(f"ZKONTROLUJTE GRAF V PROHLÍŽEČI:")
            print(f"URL: {share_url}")
            print("🔗" * 60)
            
            # Čekání na potvrzení uživatele
            input("\n👀 Otevřete URL v prohlížeči a zkontrolujte, že graf je zobrazitelný.")
            input("📝 Zkontrolujte také v Datawrapper editoru, že graf má správná data.")
            input("✅ Stiskněte Enter když je graf v pořádku...")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při publikování: {str(e)}")
            return False
    
    def step_3_check_current_metadata(self):
        """Krok 3: Kontrola aktuálních metadata"""
        logger.info("\n" + "=" * 60)
        logger.info("KROK 3: Kontrola aktuálních metadata")
        logger.info("=" * 60)
        
        try:
            # Inicializace export klienta
            self.export_client = DatawrapperExportClient()
            
            # Získání aktuálních metadata
            metadata = self.export_client.get_chart_metadata(self.test_chart_id)
            
            if not metadata:
                logger.error("❌ Nepodařilo se načíst metadata")
                return False
            
            # Kontrola publish nastavení
            publish_meta = metadata.get('publish', {})
            chart_footer = publish_meta.get('chart-footer', {})
            
            logger.info("📋 Aktuální publish metadata:")
            logger.info(f"  Locale: {publish_meta.get('locale', 'NENÍ NASTAVENO')}")
            logger.info(f"  Data download: {chart_footer.get('data-download', 'NENÍ NASTAVENO')}")
            
            image_download = chart_footer.get('image-download', {})
            logger.info(f"  PNG download: {image_download.get('png', 'NENÍ NASTAVENO')}")
            logger.info(f"  PDF download: {image_download.get('pdf', 'NENÍ NASTAVENO')}")
            logger.info(f"  SVG download: {image_download.get('svg', 'NENÍ NASTAVENO')}")
            
            logger.info(f"  Embed link: {chart_footer.get('embed-link', 'NENÍ NASTAVENO')}")
            logger.info(f"  Social sharing: {chart_footer.get('social-sharing', 'NENÍ NASTAVENO')}")
            
            # Validace pomocí ChartConfigurator
            configurator = ChartConfigurator(self.export_client)
            chart_info = ChartInfo(
                id=self.test_chart_id,
                title="Test Graf - Metadata Workflow",
                type="d3-bars",
                status="published",
                url="",
                share_url=self.initial_share_url
            )
            
            validation = configurator.validate_chart_configuration(chart_info)
            logger.info(f"\n🔍 Validace pro export: {'✅ PLATNÝ' if validation['valid'] else '❌ NEPLATNÝ'}")
            
            if not validation['valid']:
                logger.info("❌ Problémy:")
                for issue in validation['issues']:
                    logger.info(f"  - {issue}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při kontrole metadata: {str(e)}")
            return False
    
    def step_4_configure_export_metadata(self):
        """Krok 4: Konfigurace export metadata"""
        logger.info("\n" + "=" * 60)
        logger.info("KROK 4: Konfigurace export metadata")
        logger.info("=" * 60)
        
        try:
            # Vytvoření ChartInfo objektu
            chart_info = ChartInfo(
                id=self.test_chart_id,
                title="Test Graf - Metadata Workflow",
                type="d3-bars",
                status="published",
                url="",
                share_url=self.initial_share_url
            )
            
            # Konfigurace pomocí ChartConfigurator
            configurator = ChartConfigurator(self.export_client)
            
            logger.info("⚙️ Konfiguruji export metadata...")
            logger.info("  - Nastavuji locale na cs-CZ")
            logger.info("  - Povolují download options (PNG, PDF, SVG)")
            logger.info("  - Povolují embed link a social sharing")
            
            # Konfigurace s force_republish=True
            results = configurator.configure_charts_for_export([chart_info], force_republish=True)
            
            if not results or len(results) == 0:
                logger.error("❌ Konfigurace selhala - žádné výsledky")
                return False
            
            result = results[0]
            
            if result.success:
                logger.info("✅ Export metadata úspěšně nakonfigurována")
                logger.info(f"✅ Nová share URL: {result.new_share_url}")
                self.final_share_url = result.new_share_url
                
                # Aktualizace chart_info
                chart_info.share_url = result.new_share_url
                
                return True
            else:
                logger.error(f"❌ Konfigurace selhala: {result.error_message}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Chyba při konfiguraci metadata: {str(e)}")
            return False
    
    def step_5_verify_metadata(self):
        """Krok 5: Ověření nových metadata"""
        logger.info("\n" + "=" * 60)
        logger.info("KROK 5: Ověření nových metadata")
        logger.info("=" * 60)
        
        try:
            # Počkáme na zpracování
            time.sleep(3)
            
            # Znovu načteme metadata
            metadata = self.export_client.get_chart_metadata(self.test_chart_id)
            
            if not metadata:
                logger.error("❌ Nepodařilo se načíst aktualizovaná metadata")
                return False
            
            # Kontrola nových nastavení
            publish_meta = metadata.get('publish', {})
            chart_footer = publish_meta.get('chart-footer', {})
            
            logger.info("📋 Aktualizovaná publish metadata:")
            logger.info(f"  Locale: {publish_meta.get('locale', 'NENÍ NASTAVENO')}")
            logger.info(f"  Data download: {chart_footer.get('data-download', 'NENÍ NASTAVENO')}")
            
            image_download = chart_footer.get('image-download', {})
            logger.info(f"  PNG download: {image_download.get('png', 'NENÍ NASTAVENO')}")
            logger.info(f"  PDF download: {image_download.get('pdf', 'NENÍ NASTAVENO')}")
            logger.info(f"  SVG download: {image_download.get('svg', 'NENÍ NASTAVENO')}")
            
            logger.info(f"  Embed link: {chart_footer.get('embed-link', 'NENÍ NASTAVENO')}")
            logger.info(f"  Social sharing: {chart_footer.get('social-sharing', 'NENÍ NASTAVENO')}")
            
            print("\n" + "🔗" * 60)
            print(f"ZKONTROLUJTE AKTUALIZOVANÝ GRAF:")
            print(f"Původní URL: {self.initial_share_url}")
            print(f"Nová URL: {self.final_share_url}")
            print("🔗" * 60)
            
            input("\n👀 Otevřete novou URL a zkontrolujte download funkce...")
            input("✅ Stiskněte Enter když jsou download funkce aktivní...")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při ověření metadata: {str(e)}")
            return False
    
    def step_6_generate_html(self):
        """Krok 6: Generování HTML s grafem"""
        logger.info("\n" + "=" * 60)
        logger.info("KROK 6: Generování HTML s grafem")
        logger.info("=" * 60)
        
        try:
            # Vytvoření finálního ChartInfo objektu
            chart_info = ChartInfo(
                id=self.test_chart_id,
                title="Test Graf - Metadata Workflow",
                type="d3-bars",
                status="published",
                url="",
                share_url=self.final_share_url
            )
            
            # Generování HTML
            html_generator = HTMLGenerator()
            output_file = "test_metadata_workflow.html"
            
            logger.info(f"🎨 Generuji HTML: {output_file}")
            
            success = html_generator.generate_export_html(
                charts=[chart_info],
                survey_id="METADATA_TEST",
                output_path=output_file,
                survey_name="Test Metadata Workflow"
            )
            
            if success:
                logger.info(f"✅ HTML úspěšně vygenerován: {output_file}")
                
                # Informace o souboru
                if os.path.exists(output_file):
                    file_size = os.path.getsize(output_file) / 1024
                    logger.info(f"📏 Velikost souboru: {file_size:.1f} KB")
                
                print("\n" + "📄" * 60)
                print(f"HTML SOUBOR VYTVOŘEN:")
                print(f"Soubor: {os.path.abspath(output_file)}")
                print("📄" * 60)
                
                input("\n👀 Otevřete HTML soubor v prohlížeči...")
                input("✅ Zkontrolujte, že graf je zobrazen a funkční...")
                
                return True
            else:
                logger.error("❌ Generování HTML selhalo")
                return False
                
        except Exception as e:
            logger.error(f"❌ Chyba při generování HTML: {str(e)}")
            return False
    
    def cleanup(self):
        """Úklid"""
        logger.info("\n" + "=" * 60)
        logger.info("ÚKLID")
        logger.info("=" * 60)
        
        try:
            # Nabídka smazání grafu
            if self.test_chart_id:
                delete_chart = input(f"Smazat testovací graf {self.test_chart_id}? (y/N): ").strip().lower()
                if delete_chart == 'y':
                    logger.info("ℹ️ Mazání grafu není implementováno - smažte manuálně v Datawrapper")
            
            # Nabídka smazání HTML
            html_file = "test_metadata_workflow.html"
            if os.path.exists(html_file):
                delete_html = input(f"Smazat {html_file}? (y/N): ").strip().lower()
                if delete_html == 'y':
                    os.remove(html_file)
                    logger.info(f"✅ Soubor {html_file} smazán")
            
        except Exception as e:
            logger.error(f"⚠️ Chyba při úklidu: {str(e)}")
    
    def run_full_workflow(self):
        """Spuštění celého workflow"""
        logger.info("🚀 SPOUŠTÍM METADATA WORKFLOW TEST")
        logger.info("=" * 60)
        
        steps = [
            ("Vytvoření grafu", self.step_1_create_chart),
            ("Publikování BEZ export metadata", self.step_2_initial_publish),
            ("Kontrola aktuálních metadata", self.step_3_check_current_metadata),
            ("Konfigurace export metadata", self.step_4_configure_export_metadata),
            ("Ověření nových metadata", self.step_5_verify_metadata),
            ("Generování HTML", self.step_6_generate_html)
        ]
        
        for i, (step_name, step_func) in enumerate(steps, 1):
            logger.info(f"\n🔄 Krok {i}/{len(steps)}: {step_name}")
            
            if not step_func():
                logger.error(f"❌ Krok {i} selhal: {step_name}")
                return False
        
        logger.info("\n" + "🎉" * 60)
        logger.info("METADATA WORKFLOW TEST ÚSPĚŠNĚ DOKONČEN!")
        logger.info("🎉" * 60)
        
        return True


if __name__ == "__main__":
    test = ChartCreationAndMetadataTest()
    
    try:
        success = test.run_full_workflow()
        test.cleanup()
        
        if success:
            print("\n✅ Test úspěšně dokončen!")
        else:
            print("\n❌ Test selhal!")
            
    except KeyboardInterrupt:
        print("\n⚠️ Test přerušen uživatelem")
        test.cleanup()
    except Exception as e:
        print(f"\n💥 Neočekávaná chyba: {e}")
        test.cleanup()
