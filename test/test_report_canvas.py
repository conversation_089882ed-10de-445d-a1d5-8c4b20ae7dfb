"""
Test pro Report Canvas modul
Základní testy funkčnosti bez GUI
"""

import unittest
import sys
import os
from pathlib import Path

# Přidání src do path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

class TestReportCanvasImports(unittest.TestCase):
    """Test importů Report Canvas modulů"""
    
    def test_import_main_module(self):
        """Test importu hlavního modulu"""
        try:
            import report_canvas
            self.assertTrue(hasattr(report_canvas, '__version__'))
            self.assertTrue(hasattr(report_canvas, 'PYQT_AVAILABLE'))
            # Pokud PyQt6 není dostupné, GUI komponenty by m<PERSON><PERSON> být <PERSON>
            if not report_canvas.PYQT_AVAILABLE:
                self.assertIsNone(report_canvas.ReportCanvasMainWindow)
        except ImportError as e:
            self.skipTest(f"Report Canvas modul není dostupný: {e}")
    
    def test_import_cli_launcher(self):
        """Test importu CLI launcheru"""
        try:
            # Import přímo bez __init__.py
            import importlib.util
            spec = importlib.util.spec_from_file_location(
                "cli_launcher", 
                str(Path(__file__).parent.parent / "src" / "report_canvas" / "cli_launcher.py")
            )
            cli_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(cli_module)
            deps = cli_module.check_dependencies()
            self.assertIsInstance(deps, list)
        except ImportError as e:
            self.skipTest(f"CLI launcher není dostupný: {e}")
    
    def test_import_ai_integration(self):
        """Test importu AI integrace"""
        try:
            # Import přímo bez __init__.py
            import importlib.util
            spec = importlib.util.spec_from_file_location(
                "ai_integration", 
                str(Path(__file__).parent.parent / "src" / "report_canvas" / "ai_integration.py")
            )
            ai_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(ai_module)
            ai = ai_module.AIIntegration()
            self.assertIsNotNone(ai)
        except ImportError as e:
            self.skipTest(f"AI integrace není dostupná: {e}")

class TestAIIntegration(unittest.TestCase):
    """Test AI integrace bez PyQt"""
    
    def setUp(self):
        """Nastavení testu"""
        try:
            # Import přímo bez __init__.py
            import importlib.util
            spec = importlib.util.spec_from_file_location(
                "ai_integration", 
                str(Path(__file__).parent.parent / "src" / "report_canvas" / "ai_integration.py")
            )
            ai_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(ai_module)
            self.ai = ai_module.AIIntegration()
        except ImportError:
            self.skipTest("AI integrace není dostupná")
    
    def test_api_key_loading(self):
        """Test načítání API klíče"""
        # Test by měl projít i bez API klíče
        status = self.ai.get_status_message()
        self.assertIsInstance(status, str)
        self.assertGreater(len(status), 0)
    
    def test_availability_check(self):
        """Test kontroly dostupnosti"""
        available = self.ai.is_available()
        self.assertIsInstance(available, bool)

class TestDataStructures(unittest.TestCase):
    """Test datových struktur"""
    
    def test_node_data_structure(self):
        """Test struktury dat uzlu"""
        node_data = {
            'node_id': 'KAP01',
            'title': 'Test kapitola',
            'content': 'Test obsah',
            'prompt': 'Test prompt',
            'data_sources': [],
            'is_manually_edited': False,
            'creation_time': '2024-01-01T00:00:00',
            'system_prompt': 'Analytik - obecný'
        }
        
        # Kontrola povinných polí
        required_fields = ['node_id', 'title', 'content']
        for field in required_fields:
            self.assertIn(field, node_data)
            
    def test_project_data_structure(self):
        """Test struktury projektu"""
        project_data = {
            'version': '1.0',
            'canvas': {
                'nodes': {},
                'connections': []
            },
            'data_sources': {},
            'metadata': {
                'created': '2024-01-01T00:00:00',
                'modified': '2024-01-01T00:00:00'
            }
        }
        
        # Kontrola struktury
        self.assertIn('version', project_data)
        self.assertIn('canvas', project_data)
        self.assertIn('data_sources', project_data)
        self.assertIn('metadata', project_data)

class TestCLIIntegration(unittest.TestCase):
    """Test CLI integrace"""
    
    def test_dependencies_check(self):
        """Test kontroly závislostí"""
        try:
            # Import přímo bez __init__.py
            import importlib.util
            spec = importlib.util.spec_from_file_location(
                "cli_launcher", 
                str(Path(__file__).parent.parent / "src" / "report_canvas" / "cli_launcher.py")
            )
            cli_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(cli_module)
            deps = cli_module.check_dependencies()
            self.assertIsInstance(deps, list)
            
            # PyQt6 by mělo být v seznamu chybějících závislostí
            # (pokud není nainstalováno)
            try:
                import PyQt6
                # Pokud je PyQt6 dostupné, seznam by měl být kratší
            except ImportError:
                self.assertIn("PyQt6", str(deps))
                
        except ImportError:
            self.skipTest("CLI launcher není dostupný")

def run_tests():
    """Spuštění testů"""
    print("=" * 60)
    print("TESTY REPORT CANVAS MODULU")
    print("=" * 60)
    
    # Vytvoření test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Přidání testů
    suite.addTests(loader.loadTestsFromTestCase(TestReportCanvasImports))
    suite.addTests(loader.loadTestsFromTestCase(TestAIIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestDataStructures))
    suite.addTests(loader.loadTestsFromTestCase(TestCLIIntegration))
    
    # Spuštění testů
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Souhrn
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ Všechny testy prošly úspěšně!")
    else:
        print(f"❌ {len(result.failures)} testů selhalo")
        print(f"⚠️  {len(result.errors)} testů skončilo chybou")
        print(f"⏭️  {len(result.skipped)} testů bylo přeskočeno")
    print("=" * 60)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)