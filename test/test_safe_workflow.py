#!/usr/bin/env python3
"""
Test bezpečného workflow pro existující grafy

Testuje opravenou verzi s bezpečným unpublish workflow.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from datawrapper_export import (
    DatawrapperExportClient,
    ChartConfigurator,
    ChartInfo
)

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_safe_workflow():
    """Test bezpečného workflow"""
    
    chart_id = input("Zadejte Chart ID existujícího grafu: ").strip()
    
    if not chart_id:
        logger.error("❌ Chart ID je povinné")
        return False
    
    try:
        logger.info("=" * 60)
        logger.info(f"BEZPEČNÝ WORKFLOW TEST PRO GRAF: {chart_id}")
        logger.info("=" * 60)
        
        # Inicializace klientů
        export_client = DatawrapperExportClient()
        configurator = ChartConfigurator(export_client)
        
        # Krok 1: Kontrola aktuálního stavu
        logger.info("\n🔍 KROK 1: Kontrola aktuálního stavu")
        
        response = export_client._make_request('GET', f'charts/{chart_id}')
        if response.status_code != 200:
            logger.error(f"❌ Graf {chart_id} nenalezen: {response.status_code}")
            return False
        
        chart_data = response.json()
        current_url = chart_data.get('publicUrl', '')
        is_published = bool(current_url)
        
        logger.info(f"📊 Graf existuje")
        logger.info(f"📊 Publikován: {is_published}")
        logger.info(f"📊 Aktuální URL: {current_url}")
        
        # Analýza metadata
        metadata = chart_data.get('metadata', {})
        publish_meta = metadata.get('publish', {})
        blocks = publish_meta.get('blocks', {})
        
        logger.info("\n📋 Aktuální BLOCKS nastavení:")
        logger.info(f"  embed: {blocks.get('embed', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-image: {blocks.get('download-image', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-pdf: {blocks.get('download-pdf', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-svg: {blocks.get('download-svg', 'NENÍ NASTAVENO')}")
        
        visualize_meta = metadata.get('visualize', {})
        sharing = visualize_meta.get('sharing', {})
        
        logger.info("📋 Aktuální SHARING nastavení:")
        logger.info(f"  enabled: {sharing.get('enabled', 'NENÍ NASTAVENO')}")
        logger.info(f"  auto: {sharing.get('auto', 'NENÍ NASTAVENO')}")
        
        # Krok 2: Vytvoření ChartInfo objektu
        chart_info = ChartInfo(
            id=chart_id,
            title=chart_data.get('title', 'Unknown'),
            type=chart_data.get('type', 'unknown'),
            status="published" if is_published else "draft",
            url="",
            share_url=current_url
        )
        
        # Krok 3: Aplikace bezpečného workflow
        logger.info("\n⚙️ KROK 2: Aplikace bezpečného workflow")
        logger.info("Konfiguruji graf s bezpečným workflow...")
        
        # Použití force_republish=True pro testování bezpečného unpublish
        results = configurator.configure_charts_for_export([chart_info], force_republish=True)
        
        if not results or len(results) == 0:
            logger.error("❌ Konfigurace selhala - žádné výsledky")
            return False
        
        result = results[0]
        
        if result.success:
            logger.info("✅ Bezpečný workflow úspěšný")
            logger.info(f"✅ Nová share URL: {result.new_share_url}")
            
            # Kontrola stability URL
            if current_url and current_url != result.new_share_url:
                logger.warning(f"⚠️ URL se změnila: {current_url} → {result.new_share_url}")
                logger.warning("⚠️ Klienti budou potřebovat aktualizaci URL!")
            elif current_url == result.new_share_url:
                logger.info("✅ URL zůstala stabilní - klienti nebudou ovlivněni")
            
        else:
            logger.error(f"❌ Bezpečný workflow selhal: {result.error_message}")
            return False
        
        # Krok 4: Ověření finálního stavu
        logger.info("\n🔍 KROK 3: Ověření finálního stavu")
        
        time.sleep(5)
        
        # Znovu načteme metadata
        response = export_client._make_request('GET', f'charts/{chart_id}')
        updated_data = response.json()
        updated_metadata = updated_data.get('metadata', {})
        
        # Kontrola nových nastavení
        updated_publish = updated_metadata.get('publish', {})
        updated_blocks = updated_publish.get('blocks', {})
        
        logger.info("📋 FINÁLNÍ BLOCKS nastavení:")
        logger.info(f"  embed: {updated_blocks.get('embed', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-image: {updated_blocks.get('download-image', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-pdf: {updated_blocks.get('download-pdf', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-svg: {updated_blocks.get('download-svg', 'NENÍ NASTAVENO')}")
        logger.info(f"  get-the-data: {updated_blocks.get('get-the-data', 'NENÍ NASTAVENO')}")
        
        updated_visualize = updated_metadata.get('visualize', {})
        updated_sharing = updated_visualize.get('sharing', {})
        
        logger.info("📋 FINÁLNÍ SHARING nastavení:")
        logger.info(f"  enabled: {updated_sharing.get('enabled', 'NENÍ NASTAVENO')}")
        logger.info(f"  auto: {updated_sharing.get('auto', 'NENÍ NASTAVENO')}")
        
        # Krok 5: Validace konfigurace
        logger.info("\n🔍 KROK 4: Validace konfigurace")
        
        validation = configurator.validate_chart_configuration(chart_info)
        
        logger.info(f"📋 Validace: {'✅ PLATNÁ' if validation['valid'] else '❌ NEPLATNÁ'}")
        
        if not validation['valid']:
            logger.info("❌ Zbývající problémy:")
            for issue in validation['issues']:
                logger.info(f"    - {issue}")
        
        # Výsledek
        print("\n" + "🔗" * 60)
        print("FINÁLNÍ GRAF S BEZPEČNÝM WORKFLOW:")
        print(f"Chart ID: {chart_id}")
        print(f"Původní URL: {current_url}")
        print(f"Nová URL: {result.new_share_url}")
        print(f"URL stabilní: {'✅ ANO' if current_url == result.new_share_url else '❌ NE'}")
        print("🔗" * 60)
        
        print("\n📋 PROSÍM ZKONTROLUJTE:")
        print("1. Otevřete graf v Datawrapper editoru")
        print("2. Zkontrolujte Layout → Share buttons")
        print("3. Otevřete graf v prohlížeči")
        print("4. Zkontrolujte všechny download funkce")
        
        user_report = input("\n👀 Finální stav - je vše správně nakonfigurováno? ")
        logger.info(f"USER REPORT: {user_report}")
        
        # Shrnutí pro produkční použití
        logger.info("\n📊 SHRNUTÍ PRO PRODUKČNÍ POUŽITÍ:")
        logger.info(f"  Graf ID: {chart_id}")
        logger.info(f"  Konfigurace: {'✅ ÚSPĚŠNÁ' if validation['valid'] else '❌ PROBLEMATICKÁ'}")
        logger.info(f"  URL stabilita: {'✅ STABILNÍ' if current_url == result.new_share_url else '⚠️ ZMĚNĚNA'}")
        logger.info(f"  Bezpečný workflow: {'✅ FUNGUJE' if result.success else '❌ SELHAL'}")
        
        return validation['valid'] and result.success
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_safe_workflow()
    
    if success:
        print("\n✅ Bezpečný workflow test úspěšný!")
        print("🚀 Modul je připraven pro produkční použití s existujícími grafy!")
    else:
        print("\n❌ Bezpečný workflow test selhal!")
        print("⚠️ Potřebuje další ladění před produkčním použitím!")
