{"total_tests": 7, "passed": 4, "failed": 3, "success_rate": 57.14285714285714, "results": [{"test_id": "UF1", "test_name": "Merge Text Questions to WordCloud", "success": true, "error": null, "details": {"virtual_question": {"question_id": "VIRTUAL_combined_feedback", "question_text": "Sloučená zpětná vazba (Q1 + Q2)", "type": "virtual", "source_questions": ["Q1", "Q2"], "merge_strategy": "concatenate", "merge_parameters": {"separator": " ", "remove_duplicates": true}, "hidden": false, "created_at": "2025-06-27T13:22:21.066631", "computed_data": {}, "data_type": "text"}, "combined_responses": 8, "wordcloud_config": {"chart_type": "wordcloud", "generator": "internal_wordcloud", "parameters": {"max_words": 50, "color_scheme": "default", "extract_nouns_only": true, "language": "cs"}, "survey_id": "test_survey", "question_id": "VIRTUAL_combined_feedback"}, "generation_result": {"success": true, "generator": "wordcloud", "chart_type": "wordcloud", "output_path": "test/output/test_survey/wordcloud/VIRTUAL_combined_feedback_wordcloud.png", "file_size": 104001, "timestamp": "2025-06-27T13:22:22.601543", "frequencies": {"služeb": 1.0, "Kvalita": 0.5, "je": 0.5, "výborná": 0.5, "Rychlé": 0.5, "vyřízení": 0.5, "požadavků": 0.5, "Příjemný": 0.5, "profesionální": 0.5, "personál": 0.5, "Dobré": 0.5, "ceny": 0.5, "za": 0.5, "poskytované": 0.5, "služby": 0.5, "Více": 0.5, "možností": 0.5, "platby": 0.5, "Lepší": 0.5, "komunikace": 0.5, "s": 0.5, "klienty": 0.5, "Rychlejší": 0.5, "odpovědi": 0.5, "na": 0.5, "dotazy": 0.5, "Rozšíření": 0.5, "o": 0.5, "víkendy": 0.5}, "wordcloud_config": {"max_words": 50, "min_word_length": 3, "width": 800, "height": 400, "background_color": "white", "color_scheme": "default", "output_format": "png", "dpi": 300, "use_ai": true, "language": "cs", "extract_nouns_only": true, "visualization_type": "WCS"}, "ai_enhanced": false}, "features_tested": ["Virtuální otázka vytvořena", "<PERSON> sloučena", "WordCloud konfigurován", "<PERSON>"]}}, {"test_id": "UF2", "test_name": "Multi Donuts from 6 Questions", "success": true, "error": null, "details": {"datasets_count": 6, "layout": "3x2", "multi_config": {"chart_type": "multi_donut", "generator": "internal_multi_chart", "parameters": {"layout": "grid", "grid_columns": 3, "grid_rows": 2, "chart_width": 300, "chart_height": 300, "spacing": 20, "show_individual_titles": true, "show_main_title": true, "main_title": "Analýza spokojenosti - Multi Donuts 3x2", "output_format": "html"}, "survey_id": "test_survey", "question_id": "multi_donuts_satisfaction"}, "generation_result": {"success": true, "generator": "multi_chart", "chart_type": "multi_donut", "output_path": "test/output/test_survey/multi_chart/multi_chart_multi_donut.html", "file_size": 3318, "timestamp": "2025-06-27T13:22:22.603385", "layout_info": {"layout": "grid", "cols": 3, "rows": 2, "total_width": 940, "total_height": 670, "chart_width": 300, "chart_height": 300, "spacing": 20, "positions": [{"x": 0, "y": 50}, {"x": 320, "y": 50}, {"x": 640, "y": 50}, {"x": 0, "y": 370}, {"x": 320, "y": 370}, {"x": 640, "y": 370}]}, "individual_charts_count": 6, "output_format": "html", "multi_config": {"layout": "grid", "grid_columns": 3, "grid_rows": 2, "chart_width": 300, "chart_height": 300, "spacing": 20, "title_height": 50, "output_format": "html", "background_color": "white", "show_individual_titles": true, "show_main_title": true, "main_title": "Analýza spokojenosti - Multi Donuts 3x2", "individual_chart_type": "donut"}}, "datawrapper_attributes": ["chart_width", "chart_height", "layout", "spacing"], "features_tested": ["6 datasetů zpracováno", "Multi donuts konfigurace", "3x2 grid layout", "HTML výstup", "Datawrapper atributy"]}}, {"test_id": "UF3", "test_name": "Named and Translated WordCloud", "success": true, "error": null, "details": {"virtual_question": {"question_id": "VIRTUAL_overall_satisfaction", "question_text": "Celkové hodnocení spokojenosti", "type": "virtual", "source_questions": ["Q1", "Q2", "Q3"], "merge_strategy": "concatenate", "merge_parameters": {"separator": " ", "remove_duplicates": true}, "hidden": false, "created_at": "2025-06-27T13:22:22.603515", "computed_data": {}, "data_type": "text"}, "custom_names": {"cs": "Celkové hodnocení spokojenosti", "en": "Overall Satisfaction Assessment"}, "source_questions_count": 3, "combined_responses": 9, "translation_dict": {"cs": {"question_names": {"VIRTUAL_overall_satisfaction": "Celkové hodnocení spokojenosti"}, "keywords": {"kvalita": "<PERSON><PERSON><PERSON>", "služby": "služby", "rychlé": "<PERSON><PERSON><PERSON>", "prostředí": "prostředí", "spokojenost": "spokojenost"}}, "en": {"question_names": {"VIRTUAL_overall_satisfaction": "Overall Satisfaction Assessment"}, "keywords": {"kvalita": "quality", "služby": "services", "rychlé": "fast", "prostředí": "environment", "spokojenost": "satisfaction"}}}, "cs_result": {"success": true, "generator": "wordcloud", "chart_type": "wordcloud", "output_path": "test/output/test_survey/wordcloud/VIRTUAL_overall_satisfaction_wordcloud.png", "file_size": 92697, "timestamp": "2025-06-27T13:22:22.785677", "frequencies": {"Výborná": 1.0, "kvalita": 1.0, "služeb": 1.0, "Rychlé": 1.0, "vyřízení": 1.0, "požadavků": 1.0, "Profesionální": 1.0, "přístup": 1.0, "Příjemné": 1.0, "prostředí": 1.0, "Čisté": 1.0, "upravené": 1.0, "Moderní": 1.0, "vybavení": 1.0, "Doporučuji": 1.0, "ostatním": 1.0, "Spokojený": 1.0, "zákazník": 1.0, "Skvělá": 1.0, "zkušenost": 1.0}, "wordcloud_config": {"max_words": 100, "min_word_length": 3, "width": 800, "height": 400, "background_color": "white", "color_scheme": "default", "output_format": "png", "dpi": 300, "use_ai": true, "language": "cs", "custom_title": "Celkové hodnocení spokojenosti", "visualization_type": "WCS"}, "ai_enhanced": false}, "en_result": {"success": true, "generator": "wordcloud", "chart_type": "wordcloud", "output_path": "test/output/test_survey/wordcloud/VIRTUAL_overall_satisfaction_wordcloud.png", "file_size": 94088, "timestamp": "2025-06-27T13:22:23.005777", "frequencies": {"Výborná": 1.0, "quality": 1.0, "služeb": 1.0, "Rychlé": 1.0, "vyřízení": 1.0, "požadavků": 1.0, "Profesionální": 1.0, "přístup": 1.0, "Příjemné": 1.0, "environment": 1.0, "Čisté": 1.0, "upravené": 1.0, "Moderní": 1.0, "vybavení": 1.0, "Doporučuji": 1.0, "ostatním": 1.0, "Spokojený": 1.0, "zákazník": 1.0, "Skvělá": 1.0, "zkušenost": 1.0}, "wordcloud_config": {"max_words": 100, "min_word_length": 3, "width": 800, "height": 400, "background_color": "white", "color_scheme": "default", "output_format": "png", "dpi": 300, "use_ai": true, "language": "en", "custom_title": "Overall Satisfaction Assessment", "visualization_type": "WCS"}, "ai_enhanced": false}, "features_tested": ["Virtuální otázka s vlastním názvem", "Data ze 3 zdrojových otázek", "WordCloud v češtině", "WordCloud v angličtině", "Překladový slovník", "Jazykov<PERSON> <PERSON><PERSON><PERSON>"]}}, {"test_id": "UF4", "test_name": "Persistent Settings", "success": false, "error": "Persistence features not working correctly", "details": {"step": "persistence_validation"}}, {"test_id": "UF5", "test_name": "Add Tables and Donuts", "success": true, "error": null, "details": {"existing_charts": {"Q1": {"chart_type": "column", "generator": "datawrapper"}, "Q2": {"chart_type": "pie", "generator": "datawrapper"}, "Q3": {"chart_type": "wordcloud", "generator": "internal_wordcloud"}, "Q4": {"chart_type": "column", "generator": "datawrapper"}, "Q5": {"chart_type": "pie", "generator": "datawrapper"}, "Q6": {"chart_type": "wordcloud", "generator": "internal_wordcloud"}}, "tables_added": 3, "donuts_added": 3, "selected_for_tables": ["Q1", "Q2", "Q4"], "selected_for_donuts": ["Q3", "Q5", "Q6"], "final_configurations": {"Q1": ["wordcloud", "table", "wordcloud", "table"], "Q2": ["column", "pie", "table"], "Q3": ["column", "pie", "donut"], "Q4": ["column", "pie", "table"], "Q5": ["column", "pie", "donut"], "Q6": ["column", "pie", "donut"]}, "generation_success_rate": 100.0, "features_tested": ["Tabulky přidány k 3 grafům", "Donut grafy přidány ke 3 grafům", "<PERSON><PERSON><PERSON><PERSON><PERSON> konfigurace <PERSON>", "Všechny typy grafů generovatelné", "Zachována původní funkcionalita"]}}, {"test_id": "UF6", "test_name": "Reset to Defaults", "success": false, "error": "Not implemented yet", "details": {"status": "placeholder"}}, {"test_id": "UF7", "test_name": "Date Histogram", "success": false, "error": "Not implemented yet", "details": {"status": "placeholder"}}]}