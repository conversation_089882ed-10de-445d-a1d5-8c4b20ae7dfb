#!/usr/bin/env python3
"""
Test Phase 2 - Folder Integration
Testování Datawrapper Export modulu s reálnou složkou

Test workflow:
1. Napojení na reálnou Datawrapper složku
2. Sběr všech grafů ze složky
3. Konfigurace více grafů najednou
4. Generování kompletního HTML výstupu
5. Test JavaScript filtrování
6. Performance testing
7. End-to-end workflow test
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from datawrapper_export import ExportManager, ExportProgress

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestPhase2:
    """Test Phase 2 - Folder Integration Testing"""
    
    def __init__(self):
        self.export_manager = None
        self.test_survey_id = None
        self.test_results = {}
        self.output_file = None
        
    def setup(self):
        """Inicializace testu"""
        logger.info("=== PHASE 2 TEST SETUP ===")
        
        try:
            # Progress callback
            def progress_callback(progress: ExportProgress):
                percentage = progress.progress_percentage
                charts_info = ""
                if progress.total_charts > 0:
                    charts_info = f" | Grafy: {progress.charts_processed}/{progress.total_charts}"
                logger.info(f"🔄 {progress.current_step} ({percentage:.1f}%){charts_info}")
            
            # Inicializace Export Manager
            self.export_manager = ExportManager(progress_callback=progress_callback)
            logger.info("✅ ExportManager inicializován")
            
            # Test připojení
            connection_test = self.export_manager.test_connection()
            if not connection_test['success']:
                logger.error(f"❌ API připojení selhalo: {connection_test.get('error')}")
                return False
            
            logger.info(f"✅ API připojení úspěšné ({connection_test['folders_count']} složek)")
            
            # Získání test survey ID od uživatele
            print("\n" + "=" * 60)
            print("PHASE 2 TEST - Folder Integration")
            print("=" * 60)
            print("Pro test potřebuji Survey ID s existujícími grafy v Datawrapper.")
            print("Doporučuji použít survey s alespoň 3-5 grafy pro komplexní test.")
            print()
            
            while True:
                survey_id = input("Zadejte Survey ID pro test (nebo 'skip' pro přeskočení): ").strip()
                
                if survey_id.lower() == 'skip':
                    logger.info("Test přeskočen uživatelem")
                    return False
                
                if survey_id:
                    self.test_survey_id = survey_id
                    logger.info(f"Test Survey ID nastaven: {survey_id}")
                    break
                else:
                    print("❌ Zadejte platný Survey ID")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Setup selhal: {str(e)}")
            return False
    
    def test_1_folder_discovery(self):
        """Test 1: Zjištění složky pro survey"""
        logger.info("\n=== TEST 1: Zjištění složky pro survey ===")
        
        try:
            # Použijeme chart collector pro zjištění složky
            from datawrapper_export import ChartCollector, DatawrapperExportClient
            
            client = DatawrapperExportClient()
            collector = ChartCollector(client)
            
            # Pokusíme se najít složku
            folder_id = client.find_survey_folder(self.test_survey_id)
            
            if folder_id:
                logger.info(f"✅ Složka nalezena: {folder_id}")
                self.test_folder_id = folder_id
                self.test_results['folder_discovery'] = True
                return True
            else:
                logger.error(f"❌ Složka pro survey {self.test_survey_id} nebyla nalezena")
                
                # Zobrazíme dostupné složky pro debug
                folders = client.get_folders()
                logger.info("Dostupné složky:")
                for folder in folders[:10]:  # Prvních 10
                    logger.info(f"  - {folder.get('name', 'Bez názvu')} (ID: {folder.get('id')})")
                
                self.test_results['folder_discovery'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ Test 1 selhal: {str(e)}")
            self.test_results['folder_discovery'] = False
            return False
    
    def test_2_chart_collection(self):
        """Test 2: Sběr grafů ze složky"""
        logger.info("\n=== TEST 2: Sběr grafů ze složky ===")
        
        if not hasattr(self, 'test_folder_id'):
            logger.error("❌ Chybí folder ID")
            return False
        
        try:
            from datawrapper_export import ChartCollector, DatawrapperExportClient
            
            client = DatawrapperExportClient()
            collector = ChartCollector(client)
            
            # Sběr grafů
            charts = collector.collect_charts_for_survey(self.test_survey_id)
            
            if charts:
                logger.info(f"✅ Nalezeno {len(charts)} grafů")
                
                # Statistiky
                stats = collector.get_collection_stats()
                logger.info(f"📊 České grafy: {stats.czech_charts}")
                logger.info(f"📊 Anglické grafy: {stats.english_charts}")
                logger.info(f"📊 Ostatní jazyky: {stats.other_language_charts}")
                
                # Typy grafů
                logger.info("📊 Typy grafů:")
                for chart_type, count in stats.chart_types.items():
                    logger.info(f"  - {chart_type}: {count}")
                
                self.test_charts = charts
                self.test_results['chart_collection'] = True
                return True
            else:
                logger.error("❌ Nebyly nalezeny žádné grafy")
                self.test_results['chart_collection'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ Test 2 selhal: {str(e)}")
            self.test_results['chart_collection'] = False
            return False
    
    def test_3_chart_configuration(self):
        """Test 3: Konfigurace více grafů"""
        logger.info("\n=== TEST 3: Konfigurace více grafů ===")
        
        if not hasattr(self, 'test_charts'):
            logger.error("❌ Chybí seznam grafů")
            return False
        
        try:
            from datawrapper_export import ChartConfigurator, DatawrapperExportClient
            
            client = DatawrapperExportClient()
            configurator = ChartConfigurator(client)
            
            # Konfigurace pouze prvních 3 grafů pro rychlost
            charts_to_configure = self.test_charts[:3]
            logger.info(f"Konfiguruji {len(charts_to_configure)} grafů (z {len(self.test_charts)})")
            
            start_time = time.time()
            results = configurator.configure_charts_for_export(charts_to_configure, force_republish=False)
            end_time = time.time()
            
            # Statistiky
            successful = sum(1 for r in results if r.success)
            logger.info(f"✅ Konfigurace dokončena: {successful}/{len(charts_to_configure)} úspěšných")
            logger.info(f"⏱️ Doba konfigurace: {end_time - start_time:.1f}s")
            
            if successful > 0:
                self.test_results['chart_configuration'] = True
                return True
            else:
                logger.error("❌ Žádný graf nebyl úspěšně nakonfigurován")
                self.test_results['chart_configuration'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ Test 3 selhal: {str(e)}")
            self.test_results['chart_configuration'] = False
            return False
    
    def test_4_html_generation(self):
        """Test 4: Generování kompletního HTML"""
        logger.info("\n=== TEST 4: Generování kompletního HTML ===")
        
        if not hasattr(self, 'test_charts'):
            logger.error("❌ Chybí seznam grafů")
            return False
        
        try:
            from datawrapper_export import HTMLGenerator
            
            html_generator = HTMLGenerator()
            
            # Generování HTML
            self.output_file = f"test_export_phase2_{self.test_survey_id}.html"
            
            start_time = time.time()
            success = html_generator.generate_export_html(
                charts=self.test_charts,
                survey_id=self.test_survey_id,
                output_path=self.output_file,
                survey_name=f"Test Phase 2 - {self.test_survey_id}"
            )
            end_time = time.time()
            
            if success:
                file_size = os.path.getsize(self.output_file) / 1024
                logger.info(f"✅ HTML vygenerován: {self.output_file}")
                logger.info(f"📏 Velikost: {file_size:.1f} KB")
                logger.info(f"⏱️ Doba generování: {end_time - start_time:.1f}s")
                
                self.test_results['html_generation'] = True
                return True
            else:
                logger.error("❌ Generování HTML selhalo")
                self.test_results['html_generation'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ Test 4 selhal: {str(e)}")
            self.test_results['html_generation'] = False
            return False
    
    def test_5_html_validation(self):
        """Test 5: Validace HTML a JavaScript filtrování"""
        logger.info("\n=== TEST 5: Validace HTML a JavaScript ===")
        
        if not self.output_file or not os.path.exists(self.output_file):
            logger.error("❌ HTML soubor neexistuje")
            return False
        
        try:
            with open(self.output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Pokročilé kontroly
            checks = [
                ("<!DOCTYPE html>", "HTML5 DOCTYPE"),
                ("chart-item", "Chart containers"),
                ("chartFilter", "Filter input"),
                ("initChartFilter", "Filter initialization"),
                ("iframe", "Chart iframes"),
                ("chart-share-link", "Share links"),
                ("visibleCount", "Visible count display"),
                ("totalCount", "Total count display"),
                ("no-results", "No results message"),
                ("responsive", "Responsive design"),
                (f"Celkem grafů: {len(self.test_charts)}", "Correct chart count")
            ]
            
            passed_checks = 0
            for check_text, description in checks:
                if check_text in content:
                    logger.info(f"✅ {description}")
                    passed_checks += 1
                else:
                    logger.warning(f"⚠️ {description} - nenalezeno")
            
            # Kontrola počtu chart-item elementů
            chart_item_count = content.count('class="chart-item"')
            expected_charts = len([c for c in self.test_charts if c.share_url])
            
            if chart_item_count == expected_charts:
                logger.info(f"✅ Správný počet chart-item elementů: {chart_item_count}")
                passed_checks += 1
            else:
                logger.warning(f"⚠️ Nesprávný počet chart-item: {chart_item_count} vs {expected_charts}")
            
            success_rate = (passed_checks / (len(checks) + 1)) * 100
            logger.info(f"📊 Validace: {passed_checks}/{len(checks) + 1} kontrol prošlo ({success_rate:.1f}%)")
            
            if success_rate >= 75:
                logger.info("✅ HTML validace úspěšná")
                self.test_results['html_validation'] = True
                return True
            else:
                logger.error("❌ HTML validace selhala")
                self.test_results['html_validation'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ Test 5 selhal: {str(e)}")
            self.test_results['html_validation'] = False
            return False

    def test_6_end_to_end_workflow(self):
        """Test 6: End-to-end workflow test"""
        logger.info("\n=== TEST 6: End-to-end workflow test ===")

        try:
            # Kompletní workflow pomocí ExportManager
            start_time = time.time()

            result = self.export_manager.export_survey_charts(
                survey_id=self.test_survey_id,
                output_dir="test_output",
                survey_name=f"E2E Test - {self.test_survey_id}",
                language_filter=None,  # Všechny jazyky
                force_reconfigure=False  # Rychlejší test
            )

            end_time = time.time()

            if result.success:
                logger.info(f"✅ End-to-end workflow úspěšný")
                logger.info(f"📁 Výstupní soubor: {result.output_file}")
                logger.info(f"📊 Exportováno grafů: {result.charts_exported}")
                logger.info(f"⏱️ Celková doba: {end_time - start_time:.1f}s")

                # Souhrn
                summary = self.export_manager.get_export_summary(result)
                if 'collection_stats' in summary:
                    stats = summary['collection_stats']
                    logger.info(f"🇨🇿 České: {stats['czech_charts']}, 🇬🇧 Anglické: {stats['english_charts']}")

                self.e2e_output_file = result.output_file
                self.test_results['end_to_end_workflow'] = True
                return True
            else:
                logger.error("❌ End-to-end workflow selhal")
                if result.errors:
                    for error in result.errors:
                        logger.error(f"  - {error}")
                self.test_results['end_to_end_workflow'] = False
                return False

        except Exception as e:
            logger.error(f"❌ Test 6 selhal: {str(e)}")
            self.test_results['end_to_end_workflow'] = False
            return False

    def test_7_performance_analysis(self):
        """Test 7: Performance analýza"""
        logger.info("\n=== TEST 7: Performance analýza ===")

        try:
            if not hasattr(self, 'test_charts'):
                logger.warning("⚠️ Chybí data pro performance analýzu")
                self.test_results['performance_analysis'] = False
                return False

            chart_count = len(self.test_charts)

            # Analýza velikosti souborů
            files_to_check = []
            if self.output_file and os.path.exists(self.output_file):
                files_to_check.append(("Phase 2 HTML", self.output_file))

            if hasattr(self, 'e2e_output_file') and os.path.exists(self.e2e_output_file):
                files_to_check.append(("E2E HTML", self.e2e_output_file))

            logger.info("📏 Analýza velikosti souborů:")
            for name, file_path in files_to_check:
                size_kb = os.path.getsize(file_path) / 1024
                size_per_chart = size_kb / chart_count if chart_count > 0 else 0
                logger.info(f"  {name}: {size_kb:.1f} KB ({size_per_chart:.1f} KB/graf)")

            # Performance metriky
            logger.info("⚡ Performance metriky:")
            logger.info(f"  Počet grafů: {chart_count}")

            if chart_count > 0:
                if chart_count <= 5:
                    logger.info("✅ Malý dataset - optimální pro rychlé testování")
                elif chart_count <= 20:
                    logger.info("✅ Střední dataset - vhodný pro produkční použití")
                else:
                    logger.info("⚠️ Velký dataset - může vyžadovat optimalizaci")

            # Doporučení
            logger.info("💡 Doporučení:")
            if chart_count > 50:
                logger.info("  - Zvažte filtrování podle jazyka pro rychlejší zpracování")
                logger.info("  - Implementujte lazy loading pro iframe elementy")

            logger.info("  - HTML soubor je optimalizován pro moderní prohlížeče")
            logger.info("  - JavaScript filtrování funguje client-side bez serveru")

            self.test_results['performance_analysis'] = True
            return True

        except Exception as e:
            logger.error(f"❌ Test 7 selhal: {str(e)}")
            self.test_results['performance_analysis'] = False
            return False

    def cleanup(self):
        """Úklid po testech"""
        logger.info("\n=== CLEANUP ===")

        try:
            files_to_cleanup = []

            if self.output_file and os.path.exists(self.output_file):
                files_to_cleanup.append(self.output_file)

            if hasattr(self, 'e2e_output_file') and os.path.exists(self.e2e_output_file):
                files_to_cleanup.append(self.e2e_output_file)

            if files_to_cleanup:
                cleanup_choice = input(f"Smazat {len(files_to_cleanup)} testovacích souborů? (y/N): ").strip().lower()

                if cleanup_choice == 'y':
                    for file_path in files_to_cleanup:
                        os.remove(file_path)
                        logger.info(f"✅ Soubor {file_path} smazán")
                else:
                    logger.info("ℹ️ Testovací soubory ponechány")
                    for file_path in files_to_cleanup:
                        logger.info(f"  📁 {file_path}")

            # Cleanup test_output složky
            test_output_dir = Path("test_output")
            if test_output_dir.exists():
                cleanup_dir = input("Smazat test_output složku? (y/N): ").strip().lower()
                if cleanup_dir == 'y':
                    import shutil
                    shutil.rmtree(test_output_dir)
                    logger.info("✅ test_output složka smazána")

        except Exception as e:
            logger.error(f"⚠️ Chyba při úklidu: {str(e)}")

    def run_all_tests(self):
        """Spuštění všech testů"""
        logger.info("🚀 SPOUŠTÍM PHASE 2 TESTY")
        logger.info("=" * 60)

        if not self.setup():
            return False

        tests = [
            self.test_1_folder_discovery,
            self.test_2_chart_collection,
            self.test_3_chart_configuration,
            self.test_4_html_generation,
            self.test_5_html_validation,
            self.test_6_end_to_end_workflow,
            self.test_7_performance_analysis
        ]

        passed_tests = 0
        for test in tests:
            if test():
                passed_tests += 1
            time.sleep(1)  # Pauza mezi testy

        # Výsledky
        logger.info("\n" + "=" * 60)
        logger.info("📊 VÝSLEDKY PHASE 2 TESTŮ")
        logger.info("=" * 60)

        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{test_name}: {status}")

        success_rate = (passed_tests / len(tests)) * 100
        logger.info(f"\nCelková úspěšnost: {passed_tests}/{len(tests)} ({success_rate:.1f}%)")

        if success_rate >= 70:  # Nižší práh kvůli složitosti
            logger.info("🎉 PHASE 2 TESTY ÚSPĚŠNÉ!")
            return True
        else:
            logger.error("💥 PHASE 2 TESTY SELHALY!")
            return False


if __name__ == "__main__":
    test = TestPhase2()
    success = test.run_all_tests()
    test.cleanup()

    sys.exit(0 if success else 1)
