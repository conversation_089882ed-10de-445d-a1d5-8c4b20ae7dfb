#!/usr/bin/env python3
"""
Test produkčních oprav

Testuje opravy filtrování a optimalizace v Menu 12.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_language_detection():
    """Test rychlé detekce jazyka z názvů"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST DETEKCE JAZYKA Z NÁZVŮ")
        logger.info("=" * 60)
        
        from datawrapper_export import ChartCollector, DatawrapperExportClient, ChartInfo
        
        client = DatawrapperExportClient()
        collector = ChartCollector(client)
        
        # Testovací grafy s českými a anglickými názvy
        test_charts = [
            ChartInfo(id="cs1", title="Zájem o zapojení do konzultace výstupů projektu", type="d3-bars", status="published", url="", share_url=""),
            ChartInfo(id="cs2", title="Míra ztotožnění s uvedenými názory", type="d3-bars", status="published", url="", share_url=""),
            ChartInfo(id="en1", title="Phases of Involvement in the Legislative Process", type="d3-bars", status="published", url="", share_url=""),
            ChartInfo(id="en2", title="Level of Agreement with Stated Opinions", type="d3-bars", status="published", url="", share_url=""),
            ChartInfo(id="cs3", title="Hodnocení právních předpisů", type="d3-bars", status="published", url="", share_url=""),
        ]
        
        # Test detekce jazyka
        charts_with_language = collector._detect_language_from_titles(test_charts)
        
        # Kontrola výsledků
        results = {}
        for chart in charts_with_language:
            results[chart.id] = chart.language
            logger.info(f"   {chart.id}: '{chart.title[:50]}...' → {chart.language}")
        
        # Ověření správnosti
        expected = {
            "cs1": "cs", "cs2": "cs", "cs3": "cs",
            "en1": "en", "en2": "en"
        }
        
        all_correct = True
        for chart_id, expected_lang in expected.items():
            actual_lang = results.get(chart_id)
            if actual_lang == expected_lang:
                logger.info(f"✅ {chart_id}: správně detekován jako {actual_lang}")
            else:
                logger.error(f"❌ {chart_id}: očekáván {expected_lang}, detekován {actual_lang}")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu detekce jazyka: {str(e)}")
        return False


def test_language_filtering():
    """Test filtrování podle jazyka"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST FILTROVÁNÍ PODLE JAZYKA")
        logger.info("=" * 60)
        
        from datawrapper_export import ChartCollector, DatawrapperExportClient, ChartInfo
        
        client = DatawrapperExportClient()
        collector = ChartCollector(client)
        
        # Testovací grafy s jazyky
        test_charts = [
            ChartInfo(id="cs1", title="Zájem o zapojení", type="d3-bars", status="published", url="", share_url=""),
            ChartInfo(id="cs2", title="Míra ztotožnění", type="d3-bars", status="published", url="", share_url=""),
            ChartInfo(id="en1", title="Level of Agreement", type="d3-bars", status="published", url="", share_url=""),
            ChartInfo(id="en2", title="Involvement Process", type="d3-bars", status="published", url="", share_url=""),
        ]
        
        # Přidání jazyků
        charts_with_language = collector._detect_language_from_titles(test_charts)
        
        # Test filtrování pouze českých
        logger.info("🔍 Test filtrování pouze českých grafů...")
        czech_only = collector._apply_filters(charts_with_language, 'cs', None)
        
        logger.info(f"   Původní grafy: {len(charts_with_language)}")
        logger.info(f"   České grafy: {len(czech_only)}")
        
        for chart in czech_only:
            logger.info(f"   - {chart.id}: {chart.title} (jazyk: {chart.language})")
        
        # Test filtrování pouze anglických
        logger.info("🔍 Test filtrování pouze anglických grafů...")
        english_only = collector._apply_filters(charts_with_language, 'en', None)
        
        logger.info(f"   Anglické grafy: {len(english_only)}")
        
        for chart in english_only:
            logger.info(f"   - {chart.id}: {chart.title} (jazyk: {chart.language})")
        
        # Ověření správnosti
        czech_correct = all(chart.language == 'cs' for chart in czech_only)
        english_correct = all(chart.language == 'en' for chart in english_only)
        
        if czech_correct and english_correct and len(czech_only) > 0 and len(english_only) > 0:
            logger.info("✅ Filtrování podle jazyka funguje správně!")
            return True
        else:
            logger.error("❌ Filtrování podle jazyka nefunguje správně")
            return False
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu filtrování: {str(e)}")
        return False


def test_optimized_export_speed():
    """Test rychlosti optimalizovaného exportu"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST RYCHLOSTI OPTIMALIZOVANÉHO EXPORTU")
        logger.info("=" * 60)
        
        from datawrapper_export import ExportManager
        
        # Měření času
        start_time = time.time()
        
        export_manager = ExportManager()
        
        # Test pouze sběru grafů (bez konfigurace metadata)
        logger.info("🚀 Test sběru grafů pro survey 827822...")
        
        # Simulace collect_charts_for_survey
        from datawrapper_export import ChartCollector, DatawrapperExportClient
        
        client = DatawrapperExportClient()
        collector = ChartCollector(client)
        
        # Test s filtrem českých grafů
        charts = collector.collect_charts_for_survey("827822", "cs")
        
        collection_time = time.time() - start_time
        
        logger.info(f"⏱️ Čas sběru grafů: {collection_time:.1f}s")
        logger.info(f"📊 Načteno grafů: {len(charts)}")
        
        # Analýza jazyků
        czech_count = sum(1 for chart in charts if getattr(chart, 'language', '') == 'cs')
        english_count = sum(1 for chart in charts if getattr(chart, 'language', '') == 'en')
        
        logger.info(f"🇨🇿 České grafy: {czech_count}")
        logger.info(f"🇬🇧 Anglické grafy: {english_count}")
        
        # Úspěch pokud je rychlé a filtruje správně
        if collection_time < 15 and czech_count > 0:
            logger.info("✅ Optimalizovaný export je rychlý a filtruje správně!")
            return True, collection_time, len(charts), czech_count
        else:
            logger.error("❌ Export je stále pomalý nebo filtrování nefunguje")
            return False, collection_time, len(charts), czech_count
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu rychlosti: {str(e)}")
        return False, 0, 0, 0


def test_real_survey_data():
    """Test na skutečných datech survey 827822"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST NA SKUTEČNÝCH DATECH SURVEY 827822")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        
        client = DatawrapperExportClient()
        
        # Test načtení skutečných grafů
        logger.info("📊 Načítám skutečné grafy ze složky 827822...")
        
        folder_id = client.find_survey_folder("827822")
        if not folder_id:
            logger.error("❌ Složka pro survey 827822 nenalezena")
            return False
        
        logger.info(f"✅ Složka nalezena: {folder_id}")
        
        # Načtení grafů s optimalizací
        start_time = time.time()
        charts = client.get_charts_in_folder(folder_id, use_parallel=True)
        loading_time = time.time() - start_time
        
        logger.info(f"⏱️ Čas načítání: {loading_time:.1f}s")
        logger.info(f"📊 Načteno grafů: {len(charts)}")
        
        # Test detekce jazyka na skutečných datech
        from datawrapper_export import ChartCollector
        collector = ChartCollector(client)
        
        charts_with_language = collector._detect_language_from_titles(charts)
        
        # Analýza jazyků
        czech_count = sum(1 for chart in charts_with_language if chart.language == 'cs')
        english_count = sum(1 for chart in charts_with_language if chart.language == 'en')
        
        logger.info(f"🇨🇿 České grafy: {czech_count}")
        logger.info(f"🇬🇧 Anglické grafy: {english_count}")
        
        # Ukázka detekce
        logger.info("\n📋 Ukázka detekce jazyka:")
        for i, chart in enumerate(charts_with_language[:5], 1):
            logger.info(f"   {i}. {chart.language}: {chart.title[:60]}...")
        
        # Úspěch pokud je rychlé a detekuje jazyky
        if loading_time < 15 and (czech_count > 0 or english_count > 0):
            logger.info("✅ Test na skutečných datech úspěšný!")
            return True
        else:
            logger.error("❌ Test na skutečných datech selhal")
            return False
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu skutečných dat: {str(e)}")
        return False


def main():
    """Spuštění všech testů produkčních oprav"""
    
    print("🚀 SPOUŠTÍM TESTY PRODUKČNÍCH OPRAV")
    
    tests = [
        ("Detekce jazyka z názvů", test_language_detection),
        ("Filtrování podle jazyka", test_language_filtering),
        ("Rychlost optimalizovaného exportu", test_optimized_export_speed),
        ("Test na skutečných datech", test_real_survey_data)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Spouštím test: {test_name}")
        
        try:
            if test_name == "Rychlost optimalizovaného exportu":
                success, time_taken, charts_count, czech_count = test_func()
                results.append((test_name, success, {'time': time_taken, 'charts': charts_count, 'czech': czech_count}))
            else:
                success = test_func()
                results.append((test_name, success, {}))
            
            if success:
                logger.info(f"✅ Test '{test_name}' úspěšný")
            else:
                logger.error(f"❌ Test '{test_name}' selhal")
                
        except Exception as e:
            logger.error(f"💥 Test '{test_name}' vyvolal výjimku: {str(e)}")
            results.append((test_name, False, {}))
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SHRNUTÍ TESTŮ PRODUKČNÍCH OPRAV")
    logger.info("=" * 60)
    
    successful = 0
    for test_name, success, extra in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        
        if test_name == "Rychlost optimalizovaného exportu" and extra:
            logger.info(f"{status}: {test_name} ({extra['time']:.1f}s, {extra['czech']} českých grafů)")
        else:
            logger.info(f"{status}: {test_name}")
        
        if success:
            successful += 1
    
    logger.info(f"\nÚspěšnost: {successful}/{len(results)} testů")
    
    if successful == len(results):
        print("\n🎉 VŠECHNY PRODUKČNÍ OPRAVY ÚSPĚŠNÉ!")
        print("✅ Detekce jazyka z názvů funguje")
        print("✅ Filtrování podle jazyka opraveno")
        print("✅ Optimalizace rychlosti implementována")
        print("✅ Test na skutečných datech prošel")
        print("🚀 Menu 12 je nyní rychlé a filtruje správně!")
    else:
        print(f"\n⚠️ {len(results) - successful} oprav selhalo")
        print("💡 Zkontrolujte logy výše pro detaily")
    
    return successful == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
