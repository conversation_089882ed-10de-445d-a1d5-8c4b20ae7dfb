#!/usr/bin/env python3
"""
Test finální integrace

Testuje opravenou find_survey_folder, URL bez verzí, a multi-server support.
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_find_survey_folder_fix():
    """Test opravené find_survey_folder"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST OPRAVENÉ find_survey_folder")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        
        client = DatawrapperExportClient()
        
        # Test pro survey 827822
        survey_id = "827822"
        
        logger.info(f"Testuji find_survey_folder pro survey: {survey_id}")
        
        folder_id = client.find_survey_folder(survey_id)
        
        if folder_id:
            logger.info(f"✅ find_survey_folder úspěšná: {folder_id}")
            
            # Test načtení grafů
            charts = client.get_charts_in_folder(folder_id)
            logger.info(f"✅ Načteno {len(charts)} grafů ze složky")
            
            return True
        else:
            logger.error("❌ find_survey_folder selhala")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu find_survey_folder: {str(e)}")
        return False


def test_url_version_removal():
    """Test odstranění verzí z URL"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST ODSTRANĚNÍ VERZÍ Z URL")
        logger.info("=" * 60)
        
        from datawrapper_export import HTMLGenerator
        
        generator = HTMLGenerator()
        
        # Test cases
        test_urls = [
            ("https://datawrapper.dwcdn.net/14J5J/2/", "https://datawrapper.dwcdn.net/14J5J/"),
            ("https://datawrapper.dwcdn.net/1rX16/1/", "https://datawrapper.dwcdn.net/1rX16/"),
            ("https://datawrapper.dwcdn.net/abc123/10/", "https://datawrapper.dwcdn.net/abc123/"),
            ("https://datawrapper.dwcdn.net/xyz/", "https://datawrapper.dwcdn.net/xyz/"),  # Už bez verze
            ("https://example.com/other", "https://example.com/other")  # Jiná URL
        ]
        
        all_passed = True
        
        for original, expected in test_urls:
            result = generator._remove_version_from_url(original)
            
            if result == expected:
                logger.info(f"✅ {original} → {result}")
            else:
                logger.error(f"❌ {original} → {result} (očekáváno: {expected})")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu URL: {str(e)}")
        return False


def test_path_manager_integration():
    """Test integrace path_manager"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST PATH_MANAGER INTEGRACE")
        logger.info("=" * 60)
        
        from path_manager import path_manager
        
        # Test nastavení serveru a survey
        test_survey_id = "827822"
        
        # Simulace nastavení (bez skutečné změny globálních proměnných)
        logger.info(f"Testuji cesty pro survey: {test_survey_id}")
        
        # Test cest
        data_path = path_manager.get_data_path(test_survey_id)
        charts_path = path_manager.get_charts_path(test_survey_id)
        
        logger.info(f"✅ Data path: {data_path}")
        logger.info(f"✅ Charts path: {charts_path}")
        
        # Test validace
        validation = path_manager.validate_survey_data(test_survey_id)
        logger.info(f"✅ Validace dat: {validation}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu path_manager: {str(e)}")
        return False


def test_html_generation_with_stable_urls():
    """Test HTML generování se stabilními URL"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST HTML GENEROVÁNÍ SE STABILNÍMI URL")
        logger.info("=" * 60)
        
        from datawrapper_export import HTMLGenerator, ChartInfo
        
        # Vytvoření testovacích grafů s verzovanými URL
        test_charts = [
            ChartInfo(
                id="14J5J",
                title="Test Graf 1",
                type="d3-bars",
                status="published",
                url="",
                share_url="https://datawrapper.dwcdn.net/14J5J/2/"
            ),
            ChartInfo(
                id="1rX16",
                title="Test Graf 2",
                type="d3-bars",
                status="published",
                url="",
                share_url="https://datawrapper.dwcdn.net/1rX16/1/"
            )
        ]
        
        # Generování HTML
        generator = HTMLGenerator()
        output_file = "test_output/test_stable_urls.html"
        
        os.makedirs("test_output", exist_ok=True)
        
        success = generator.generate_export_html(
            charts=test_charts,
            survey_id="827822",
            output_path=output_file,
            survey_name="Test Survey - Stabilní URL"
        )
        
        if success:
            logger.info(f"✅ HTML vygenerován: {output_file}")
            
            # Kontrola obsahu
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Kontrola, že URL jsou bez verzí
            if "/14J5J/" in content and "/14J5J/2/" not in content:
                logger.info("✅ URL bez verzí správně aplikovány")
                return True
            else:
                logger.error("❌ URL stále obsahují verze")
                return False
        else:
            logger.error("❌ HTML generování selhalo")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu HTML: {str(e)}")
        return False


def test_complete_workflow():
    """Test kompletního workflow"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST KOMPLETNÍHO WORKFLOW")
        logger.info("=" * 60)
        
        from datawrapper_export import ExportManager
        
        # Simulace kompletního exportu
        export_manager = ExportManager()
        
        # Test připojení
        connection_test = export_manager.test_connection()
        
        if connection_test['success']:
            logger.info(f"✅ Připojení k Datawrapper: {connection_test['folders_count']} složek")
            
            # Test by pokračoval exportem, ale pro test stačí připojení
            return True
        else:
            logger.error(f"❌ Připojení selhalo: {connection_test.get('error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu workflow: {str(e)}")
        return False


def main():
    """Spuštění všech testů"""
    
    print("🚀 SPOUŠTÍM TESTY FINÁLNÍ INTEGRACE")
    
    tests = [
        ("Opravená find_survey_folder", test_find_survey_folder_fix),
        ("Odstranění verzí z URL", test_url_version_removal),
        ("Path Manager integrace", test_path_manager_integration),
        ("HTML se stabilními URL", test_html_generation_with_stable_urls),
        ("Kompletní workflow", test_complete_workflow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Spouštím test: {test_name}")
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ Test '{test_name}' úspěšný")
            else:
                logger.error(f"❌ Test '{test_name}' selhal")
                
        except Exception as e:
            logger.error(f"💥 Test '{test_name}' vyvolal výjimku: {str(e)}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SHRNUTÍ TESTŮ FINÁLNÍ INTEGRACE")
    logger.info("=" * 60)
    
    successful = 0
    for test_name, success in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        logger.info(f"{status}: {test_name}")
        if success:
            successful += 1
    
    logger.info(f"\nÚspěšnost: {successful}/{len(results)} testů")
    
    if successful == len(results):
        print("\n🎉 VŠECHNY TESTY ÚSPĚŠNÉ!")
        print("✅ Finální integrace je připravena k použití")
        print("🚀 Menu 9 a Menu 12 jsou aktualizovány")
        print("📊 URL jsou stabilní bez verzí")
        print("🗂️ Multi-server support implementován")
    else:
        print(f"\n⚠️ {len(results) - successful} testů selhalo")
        print("💡 Zkontrolujte logy výše pro detaily")
    
    return successful == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
