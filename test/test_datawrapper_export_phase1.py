#!/usr/bin/env python3
"""
Test Phase 1 - Single Chart
Testování Datawrapper Export modulu s jedním grafem

Test workflow:
1. Vytvoření testovacího grafu
2. Konfigurace metadata
3. Generování HTML s jedním grafem
4. Validace výstupního HTML
5. Test všech download funkcí
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from datawrapper_export import (
    DatawrapperExportClient, 
    ChartCollector, 
    ChartConfigurator, 
    HTMLGenerator, 
    ExportManager,
    ChartInfo
)

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestPhase1:
    """Test Phase 1 - Single Chart Testing"""
    
    def __init__(self):
        self.client = None
        self.test_chart_id = None
        self.test_results = {}
        
    def setup(self):
        """Inicializace testu"""
        logger.info("=== PHASE 1 TEST SETUP ===")
        
        try:
            # Inicializace klienta
            self.client = DatawrapperExportClient()
            logger.info("✅ DatawrapperExportClient inicializován")
            
            # Test připojení
            folders = self.client.get_folders()
            logger.info(f"✅ API připojení úspěšné ({len(folders)} složek)")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Setup selhal: {str(e)}")
            return False
    
    def test_1_create_test_chart(self):
        """Test 1: Vytvoření testovacího grafu"""
        logger.info("\n=== TEST 1: Vytvoření testovacího grafu ===")
        
        try:
            # Použijeme existující DatawrapperClient pro vytvoření grafu
            from datawrapper_client import DatawrapperClient
            dw_client = DatawrapperClient()
            
            # Vytvoření testovacího grafu
            chart_data = dw_client.create_chart(
                title="Test Export Chart - Phase 1",
                chart_type="d3-bars",
                description="Testovací graf pro Datawrapper Export modul",
                data_source="Test Data",
                byline="LimWrapp Test"
            )
            
            if chart_data and 'id' in chart_data:
                self.test_chart_id = chart_data['id']
                logger.info(f"✅ Testovací graf vytvořen: {self.test_chart_id}")
                
                # Přidání testovacích dat - správný formát pro sloupcový graf
                test_csv_data = "Kategorie,Hodnota\nKategorie A,10\nKategorie B,20\nKategorie C,15\nKategorie D,25\nKategorie E,30"

                # Nejprve přidáme data pomocí update_chart_data
                data_success = dw_client.update_chart_data(self.test_chart_id, test_csv_data)
                logger.info(f"Data update success: {data_success}")

                if not data_success:
                    logger.error("❌ Nepodařilo se aktualizovat data grafu")
                    self.test_results['create_chart'] = False
                    return False

                # Počkáme chvilku na zpracování
                time.sleep(3)

                # Nastavíme správné typy sloupců pomocí update_chart_metadata
                metadata_update = {
                    "data": {
                        "column-format": {
                            "Kategorie": {"type": "text", "input-format": "auto"},
                            "Hodnota": {"type": "number", "input-format": "auto"}
                        }
                    },
                    "visualize": {
                        "chart": {
                            "margin": {"top": 10, "right": 10, "bottom": 10, "left": 10}
                        },
                        "show-color-key": True
                    }
                }

                # Aktualizujeme metadata
                metadata_success = dw_client.update_chart_metadata(self.test_chart_id, metadata_update)
                logger.info(f"Metadata update success: {metadata_success}")

                if not metadata_success:
                    logger.warning("⚠️ Nepodařilo se aktualizovat metadata, pokračujem")

                # Další pauza pro zpracování
                time.sleep(2)
                logger.info("✅ Testovací data přidána")
                
                self.test_results['create_chart'] = True
                return True
            else:
                logger.error("❌ Vytvoření grafu selhalo")
                self.test_results['create_chart'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ Test 1 selhal: {str(e)}")
            self.test_results['create_chart'] = False
            return False
    
    def test_2_configure_metadata(self):
        """Test 2: Konfigurace metadata"""
        logger.info("\n=== TEST 2: Konfigurace metadata ===")
        
        if not self.test_chart_id:
            logger.error("❌ Chybí test chart ID")
            return False
        
        try:
            configurator = ChartConfigurator(self.client)
            
            # Vytvoření ChartInfo objektu
            test_chart = ChartInfo(
                id=self.test_chart_id,
                title="Test Export Chart - Phase 1",
                type="d3-bars",
                status="draft",
                url=""
            )
            
            # Konfigurace grafu
            results = configurator.configure_charts_for_export([test_chart])
            
            if results and len(results) > 0 and results[0].success:
                logger.info("✅ Metadata úspěšně nakonfigurována")
                logger.info(f"✅ Nová share URL: {results[0].new_share_url}")
                
                # Aktualizace share URL
                test_chart.share_url = results[0].new_share_url
                self.test_chart_info = test_chart
                
                self.test_results['configure_metadata'] = True
                return True
            else:
                error_msg = results[0].error_message if results else "Neznámá chyba"
                logger.error(f"❌ Konfigurace metadata selhala: {error_msg}")
                self.test_results['configure_metadata'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ Test 2 selhal: {str(e)}")
            self.test_results['configure_metadata'] = False
            return False
    
    def test_3_generate_html(self):
        """Test 3: Generování HTML s jedním grafem"""
        logger.info("\n=== TEST 3: Generování HTML ===")
        
        if not hasattr(self, 'test_chart_info'):
            logger.error("❌ Chybí nakonfigurovaný graf")
            return False
        
        try:
            html_generator = HTMLGenerator()
            
            # Generování HTML
            output_file = "test_export_phase1.html"
            success = html_generator.generate_export_html(
                charts=[self.test_chart_info],
                survey_id="TEST_PHASE1",
                output_path=output_file,
                survey_name="Test Phase 1 Export"
            )
            
            if success:
                logger.info(f"✅ HTML úspěšně vygenerován: {output_file}")
                
                # Validace souboru
                if os.path.exists(output_file):
                    file_size = os.path.getsize(output_file)
                    logger.info(f"✅ Velikost souboru: {file_size / 1024:.1f} KB")
                    
                    # Základní validace obsahu
                    with open(output_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    if self.test_chart_info.share_url in content:
                        logger.info("✅ Share URL nalezena v HTML")
                    else:
                        logger.warning("⚠️ Share URL nenalezena v HTML")
                    
                    if "Test Export Chart - Phase 1" in content:
                        logger.info("✅ Název grafu nalezen v HTML")
                    else:
                        logger.warning("⚠️ Název grafu nenalezen v HTML")
                    
                    self.test_results['generate_html'] = True
                    self.output_file = output_file
                    return True
                else:
                    logger.error("❌ HTML soubor nebyl vytvořen")
                    self.test_results['generate_html'] = False
                    return False
            else:
                logger.error("❌ Generování HTML selhalo")
                self.test_results['generate_html'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ Test 3 selhal: {str(e)}")
            self.test_results['generate_html'] = False
            return False
    
    def test_4_validate_html(self):
        """Test 4: Validace výstupního HTML"""
        logger.info("\n=== TEST 4: Validace HTML ===")
        
        if not hasattr(self, 'output_file'):
            logger.error("❌ Chybí výstupní HTML soubor")
            return False
        
        try:
            with open(self.output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Kontroly
            checks = [
                ("DOCTYPE html", "HTML5 DOCTYPE"),
                ("chart-item", "Chart item container"),
                ("chartFilter", "Filter input"),
                ("iframe", "Chart iframe"),
                ("chart-share-link", "Share link"),
                ("initChartFilter", "JavaScript filter function"),
                ("responsive", "Responsive design")
            ]
            
            passed_checks = 0
            for check_text, description in checks:
                if check_text in content:
                    logger.info(f"✅ {description}")
                    passed_checks += 1
                else:
                    logger.warning(f"⚠️ {description} - nenalezeno")
            
            success_rate = (passed_checks / len(checks)) * 100
            logger.info(f"📊 Validace: {passed_checks}/{len(checks)} kontrol prošlo ({success_rate:.1f}%)")
            
            if success_rate >= 80:
                logger.info("✅ HTML validace úspěšná")
                self.test_results['validate_html'] = True
                return True
            else:
                logger.error("❌ HTML validace selhala")
                self.test_results['validate_html'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ Test 4 selhal: {str(e)}")
            self.test_results['validate_html'] = False
            return False
    
    def test_5_download_functions(self):
        """Test 5: Test download funkcí"""
        logger.info("\n=== TEST 5: Test download funkcí ===")
        
        if not self.test_chart_id:
            logger.error("❌ Chybí test chart ID")
            return False
        
        try:
            # Validace konfigurace grafu
            configurator = ChartConfigurator(self.client)
            validation = configurator.validate_chart_configuration(self.test_chart_info)
            
            logger.info("📋 Validace konfigurace grafu:")
            logger.info(f"  Platný: {validation['valid']}")
            logger.info(f"  Locale: {validation.get('current_locale', 'N/A')}")
            
            if validation['valid']:
                logger.info("✅ Graf je správně nakonfigurován pro download")
                self.test_results['download_functions'] = True
                return True
            else:
                logger.warning("⚠️ Graf není optimálně nakonfigurován")
                logger.warning(f"  Problémy: {', '.join(validation.get('issues', []))}")
                self.test_results['download_functions'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ Test 5 selhal: {str(e)}")
            self.test_results['download_functions'] = False
            return False
    
    def cleanup(self):
        """Úklid po testech"""
        logger.info("\n=== CLEANUP ===")
        
        try:
            # Smazání testovacího grafu (volitelné)
            cleanup_choice = input("Smazat testovací graf? (y/N): ").strip().lower()
            
            if cleanup_choice == 'y':
                # Zde by bylo mazání grafu přes API
                logger.info("ℹ️ Mazání grafu přeskočeno (implementovat podle potřeby)")
            
            # Smazání HTML souboru
            if hasattr(self, 'output_file') and os.path.exists(self.output_file):
                cleanup_html = input(f"Smazat {self.output_file}? (y/N): ").strip().lower()
                if cleanup_html == 'y':
                    os.remove(self.output_file)
                    logger.info(f"✅ Soubor {self.output_file} smazán")
            
        except Exception as e:
            logger.error(f"⚠️ Chyba při úklidu: {str(e)}")
    
    def run_all_tests(self):
        """Spuštění všech testů"""
        logger.info("🚀 SPOUŠTÍM PHASE 1 TESTY")
        logger.info("=" * 60)
        
        if not self.setup():
            return False
        
        tests = [
            self.test_1_create_test_chart,
            self.test_2_configure_metadata,
            self.test_3_generate_html,
            self.test_4_validate_html,
            self.test_5_download_functions
        ]
        
        passed_tests = 0
        for test in tests:
            if test():
                passed_tests += 1
            time.sleep(1)  # Pauza mezi testy
        
        # Výsledky
        logger.info("\n" + "=" * 60)
        logger.info("📊 VÝSLEDKY PHASE 1 TESTŮ")
        logger.info("=" * 60)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{test_name}: {status}")
        
        success_rate = (passed_tests / len(tests)) * 100
        logger.info(f"\nCelková úspěšnost: {passed_tests}/{len(tests)} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            logger.info("🎉 PHASE 1 TESTY ÚSPĚŠNÉ!")
            return True
        else:
            logger.error("💥 PHASE 1 TESTY SELHALY!")
            return False


if __name__ == "__main__":
    test = TestPhase1()
    success = test.run_all_tests()
    test.cleanup()
    
    sys.exit(0 if success else 1)
