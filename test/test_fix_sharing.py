#!/usr/bin/env python3
"""
Test opravy sharing nastavení

Opraví pouze sharing nastavení u existujícího grafu.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from datawrapper_export import DatawrapperExportClient

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_fix_sharing():
    """Test opravy sharing nastavení"""
    
    chart_id = input("Zadejte Chart ID grafu k opravě: ").strip()
    
    if not chart_id:
        logger.error("❌ Chart ID je povinné")
        return False
    
    try:
        logger.info("=" * 60)
        logger.info(f"OPRAVA SHARING NASTAVENÍ PRO GRAF: {chart_id}")
        logger.info("=" * 60)
        
        # Inicializace klienta
        client = DatawrapperExportClient()
        
        # Krok 1: Kontrola aktuálního stavu
        logger.info("\n🔍 KROK 1: Kontrola aktuálního stavu")
        
        response = client._make_request('GET', f'charts/{chart_id}')
        if response.status_code != 200:
            logger.error(f"❌ Graf {chart_id} nenalezen")
            return False
        
        full_data = response.json()
        metadata = full_data.get('metadata', {})
        
        visualize_meta = metadata.get('visualize', {})
        sharing = visualize_meta.get('sharing', {})
        
        logger.info("📋 Aktuální SHARING nastavení:")
        logger.info(f"  enabled: {sharing.get('enabled', 'NENÍ NASTAVENO')}")
        logger.info(f"  auto: {sharing.get('auto', 'NENÍ NASTAVENO')}")
        logger.info(f"  url: {sharing.get('url', 'NENÍ NASTAVENO')}")
        
        # Krok 2: Testování různých hodnot
        logger.info("\n⚙️ KROK 2: Testování sharing nastavení")
        
        # Zkusíme auto: true (možná to znamená "auto-detect site URL")
        test_metadata = metadata.copy()
        
        if 'visualize' not in test_metadata:
            test_metadata['visualize'] = {}
        
        test_metadata['visualize']['sharing'] = {
            'enabled': True,
            'auto': True  # Zkusíme true místo false
        }
        
        logger.info("📝 Testuji nastavení:")
        logger.info("  sharing.enabled = True")
        logger.info("  sharing.auto = True")
        
        # Krok 3: Unpublish → Update → Publish
        logger.info("\n📤 KROK 3: Unpublish grafu")
        
        unpublish_success = client.unpublish_chart(chart_id)
        if unpublish_success:
            logger.info("✅ Graf unpublished")
        else:
            logger.warning("⚠️ Unpublish selhal, pokračujem")
        
        time.sleep(2)
        
        # Aktualizace metadata
        logger.info("\n⚙️ KROK 4: Aktualizace sharing metadata")
        
        metadata_success = client.update_chart_metadata(chart_id, test_metadata)
        
        if not metadata_success:
            logger.error("❌ Nepodařilo se aktualizovat metadata")
            return False
        
        logger.info("✅ Sharing metadata aktualizována")
        time.sleep(3)
        
        # Republish
        logger.info("\n📤 KROK 5: Republish grafu")
        
        share_url = client.publish_chart(chart_id)
        
        if not share_url:
            logger.error("❌ Nepodařilo se publikovat graf")
            return False
        
        logger.info(f"✅ Graf publikován: {share_url}")
        
        # Krok 6: Ověření
        logger.info("\n🔍 KROK 6: Ověření výsledku")
        
        time.sleep(5)
        
        # Znovu načteme metadata
        response = client._make_request('GET', f'charts/{chart_id}')
        updated_full_data = response.json()
        updated_metadata = updated_full_data.get('metadata', {})
        
        updated_visualize = updated_metadata.get('visualize', {})
        updated_sharing = updated_visualize.get('sharing', {})
        
        logger.info("📋 NOVÁ SHARING nastavení:")
        logger.info(f"  enabled: {updated_sharing.get('enabled', 'NENÍ NASTAVENO')}")
        logger.info(f"  auto: {updated_sharing.get('auto', 'NENÍ NASTAVENO')}")
        logger.info(f"  url: {updated_sharing.get('url', 'NENÍ NASTAVENO')}")
        
        # Výsledek
        print("\n" + "🔗" * 60)
        print("GRAF S OPRAVENÝM SHARING:")
        print(f"Chart ID: {chart_id}")
        print(f"URL: {share_url}")
        print("🔗" * 60)
        
        print("\n📋 PROSÍM ZKONTROLUJTE:")
        print("1. Otevřete graf v Datawrapper editoru")
        print("2. Přejděte na Layout → Share buttons")
        print("3. Zkontrolujte, zda je vybrána možnost:")
        print("   'Share site chart is embedded in' (místo 'Share chart URL')")
        
        user_report = input("\n👀 Jaká možnost je nyní vybrána v Share buttons? ")
        logger.info(f"USER REPORT: {user_report}")
        
        # Pokud stále není správně, zkusíme auto: false
        if "chart url" in user_report.lower() or "chart" in user_report.lower():
            logger.info("\n🔄 Zkouším auto: false")
            
            test_metadata['visualize']['sharing']['auto'] = False
            
            # Znovu unpublish → update → publish
            client.unpublish_chart(chart_id)
            time.sleep(2)
            client.update_chart_metadata(chart_id, test_metadata)
            time.sleep(3)
            share_url = client.publish_chart(chart_id)
            time.sleep(5)
            
            logger.info(f"✅ Zkušeno s auto: false, URL: {share_url}")
            
            user_report2 = input("\n👀 A nyní jaká možnost je vybrána? ")
            logger.info(f"USER REPORT 2: {user_report2}")
        
        logger.info("\n🎉 TEST DOKONČEN!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_fix_sharing()
    
    if success:
        print("\n✅ Test opravy sharing úspěšný!")
    else:
        print("\n❌ Test opravy sharing selhal!")
