#!/usr/bin/env python3
"""
Test pro zobrazení všech Datawrapper složek
"""

import os
import sys
import json
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from datawrapper_export import DatawrapperExportClient

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def list_all_folders():
    """Zobrazí všechny Datawrapper složky"""
    try:
        logger.info("=" * 60)
        logger.info("SEZNAM VŠECH DATAWRAPPER SLOŽEK")
        logger.info("=" * 60)
        
        client = DatawrapperExportClient()
        
        # Získání všech složek
        folders = client.get_folders()
        
        logger.info(f"Celkem nalezeno složek: {len(folders)}")
        
        print("\n📁 VŠECHNY SLOŽKY:")
        print("=" * 60)
        
        for i, folder in enumerate(folders, 1):
            folder_id = folder.get('id', 'N/A')
            folder_name = folder.get('name', 'Bez názvu')
            parent_id = folder.get('parentId', 'Root')
            
            print(f"{i}. ID: {folder_id}")
            print(f"   Název: {folder_name}")
            print(f"   Parent: {parent_id}")
            
            # Zkusíme načíst grafy ze složky
            try:
                charts = client.get_charts_in_folder(str(folder_id))
                print(f"   Grafů: {len(charts)}")
                
                if charts:
                    # Zobrazíme první 3 grafy
                    for j, chart in enumerate(charts[:3]):
                        print(f"     - {chart.title} (ID: {chart.id})")
                    
                    if len(charts) > 3:
                        print(f"     ... a dalších {len(charts) - 3} grafů")
                        
            except Exception as e:
                print(f"   Grafů: Chyba při načítání ({str(e)})")
            
            print()
        
        # Hledání složek obsahujících čísla (možné survey ID)
        print("\n🔍 SLOŽKY S ČÍSLY (možné survey ID):")
        print("=" * 60)
        
        numeric_folders = []
        for folder in folders:
            folder_name = folder.get('name', '')
            folder_id = folder.get('id', '')
            
            # Hledáme čísla v názvu
            import re
            numbers = re.findall(r'\d+', folder_name)
            
            if numbers:
                numeric_folders.append({
                    'id': folder_id,
                    'name': folder_name,
                    'numbers': numbers
                })
        
        if numeric_folders:
            for folder in numeric_folders:
                print(f"📁 {folder['name']} (ID: {folder['id']})")
                print(f"   Čísla v názvu: {', '.join(folder['numbers'])}")
                print()
        else:
            print("❌ Žádné složky s čísly v názvu")
        
        # Návrh pro survey 827822
        print("\n💡 NÁVRHY PRO SURVEY 827822:")
        print("=" * 60)
        print("1. Vytvořte složku v Datawrapper s názvem obsahujícím '827822'")
        print("2. Nebo použijte existující složku a přejmenujte ji")
        print("3. Nebo použijte jiné survey ID, které má odpovídající složku")
        
        return folders
        
    except Exception as e:
        logger.error(f"❌ Chyba při načítání složek: {str(e)}")
        import traceback
        traceback.print_exc()
        return []


def test_folder_creation():
    """Test vytvoření složky pro survey"""
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST VYTVOŘENÍ SLOŽKY PRO SURVEY")
        logger.info("=" * 60)
        
        from datawrapper_client import DatawrapperClient
        
        client = DatawrapperClient()
        
        # Test survey ID
        test_survey_id = "827822"
        folder_name = f"Survey {test_survey_id}"
        
        logger.info(f"Zkouším vytvořit složku: {folder_name}")
        
        # Simulace (bez skutečného vytvoření)
        print(f"\n📁 SIMULACE VYTVOŘENÍ SLOŽKY:")
        print(f"Název: {folder_name}")
        print(f"Parent ID: {client.limesurvey_folder_id}")
        print(f"Team ID: {client.team_id}")
        
        print("\n⚠️ POZNÁMKA: Pro skutečné vytvoření složky:")
        print("1. Přejděte do Datawrapper")
        print("2. Vytvořte novou složku v LimeSurvey parent složce")
        print(f"3. Pojmenujte ji '{folder_name}' nebo podobně")
        print("4. Přidejte do ní grafy pro testování")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu vytvoření složky: {str(e)}")
        return False


if __name__ == "__main__":
    print("🚀 ANALÝZA DATAWRAPPER SLOŽEK")
    
    # Seznam všech složek
    folders = list_all_folders()
    
    # Test vytvoření složky
    test_folder_creation()
    
    print("\n✅ Analýza dokončena!")
    print("💡 Použijte informace výše pro konfiguraci správných survey ID")
