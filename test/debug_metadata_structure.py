#!/usr/bin/env python3
"""
Debug script pro zjištění správné struktury metadata
"""

import os
import sys
import json
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from datawrapper_export import DatawrapperExportClient

def debug_metadata_structure():
    """Zjistí skutečnou strukturu metadata grafu tQ2P3"""
    
    try:
        # Inicializace klienta
        client = DatawrapperExportClient()
        
        # Získání metadata testovacího grafu
        chart_id = "tQ2P3"
        print(f"Získávám metadata grafu {chart_id}...")
        
        # Použijeme GET /charts/{id} místo jen metadata
        response = client._make_request('GET', f'charts/{chart_id}')
        
        if response.status_code == 200:
            full_data = response.json()
            
            print("=" * 80)
            print("KOMPLETNÍ STRUKTURA GRAFU:")
            print("=" * 80)
            print(json.dumps(full_data, indent=2, ensure_ascii=False))
            
            print("\n" + "=" * 80)
            print("PUBLISH METADATA:")
            print("=" * 80)
            publish_meta = full_data.get('metadata', {}).get('publish', {})
            print(json.dumps(publish_meta, indent=2, ensure_ascii=False))
            
            print("\n" + "=" * 80)
            print("BLOCKS STRUKTURA:")
            print("=" * 80)
            blocks = publish_meta.get('blocks', {})
            print(json.dumps(blocks, indent=2, ensure_ascii=False))
            
            print("\n" + "=" * 80)
            print("ANALÝZA LAYOUT OPTIONS:")
            print("=" * 80)
            
            # Analýza současných nastavení
            print("Aktuální nastavení:")
            print(f"  embed: {blocks.get('embed', 'NENÍ NASTAVENO')}")
            print(f"  download-image: {blocks.get('download-image', 'NENÍ NASTAVENO')}")
            print(f"  download-pdf: {blocks.get('download-pdf', 'NENÍ NASTAVENO')}")
            print(f"  download-svg: {blocks.get('download-svg', 'NENÍ NASTAVENO')}")
            print(f"  get-the-data: {blocks.get('get-the-data', 'NENÍ NASTAVENO')}")
            print(f"  logo: {blocks.get('logo', {}).get('enabled', 'NENÍ NASTAVENO')}")
            
            # Hledání social sharing
            visualize_meta = full_data.get('metadata', {}).get('visualize', {})
            sharing = visualize_meta.get('sharing', {})
            print(f"  social sharing enabled: {sharing.get('enabled', 'NENÍ NASTAVENO')}")
            
            return True
            
        else:
            print(f"Chyba při načítání grafu: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"Chyba: {str(e)}")
        return False

if __name__ == "__main__":
    debug_metadata_structure()
