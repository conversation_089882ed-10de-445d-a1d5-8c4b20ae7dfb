#!/usr/bin/env python3
"""
Test Menu 13 s reáln<PERSON><PERSON> daty

<PERSON>uje Menu 13 s existujícím LSS souborem 827822.
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_menu13_with_real_lss():
    """Test Menu 13 s reálným LSS souborem 827822"""
    
    try:
        logger.info("=" * 80)
        logger.info("TEST MENU 13 S REÁLNÝMI DATY - SURVEY 827822")
        logger.info("=" * 80)
        
        from intelligent_analysis import IntelligentAnalysisManager
        from intelligent_analysis_html import AnalysisHTMLGenerator
        
        # Cesta k reálnému LSS souboru
        lss_file = "/home/<USER>/vyvoj/limwrapp/data/dotazniky.urad.online/827822/structure.lss"
        
        if not os.path.exists(lss_file):
            logger.error(f"❌ LSS soubor neexistuje: {lss_file}")
            return False
        
        logger.info(f"📄 Analyzuji reálný LSS soubor: {lss_file}")
        
        # Inicializace Analysis Engine
        logger.info("🔧 Inicializuji Analysis Engine...")
        manager = IntelligentAnalysisManager()
        
        # Spuštění analýzy
        logger.info("🚀 Spouštím inteligentní analýzu reálných dat...")
        result = manager.analyze_survey_from_lss(lss_file)
        
        if not result:
            logger.error("❌ Analýza reálných dat selhala")
            return False
        
        # Zobrazení výsledků
        logger.info("✅ ANALÝZA REÁLNÝCH DAT DOKONČENA!")
        logger.info("=" * 80)
        
        summary = result['summary']
        logger.info("📊 SOUHRN ANALÝZY:")
        logger.info(f"   📋 Celkem otázek: {summary['total_questions']}")
        logger.info(f"   🎯 Rozpoznáno: {summary['recognized_questions']} ({summary['recognition_rate']:.1%})")
        logger.info(f"   📈 Doporučené analýzy: {summary['total_analyses']}")
        logger.info(f"   🎨 Vizualizace: {summary['total_visualizations']}")
        logger.info(f"   ⏱️ Odhadovaný čas: {summary['estimated_time_hours']:.1f} hodin")
        logger.info(f"   🔧 Složitost: {summary['complexity_score']:.1f}/10")
        logger.info(f"   📊 Datawrapper kompatibilita: {summary['datawrapper_compatibility']:.1%}")
        logger.info(f"   🚀 Priorita implementace: {summary['implementation_priority']}")
        
        # Detailní analýza otázek
        logger.info("\n📝 DETAILNÍ ANALÝZA OTÁZEK:")
        logger.info("=" * 50)
        
        question_analysis = result['question_analysis']
        questions = question_analysis['questions']
        
        # Zobrazit prvních 10 otázek
        for i, question in enumerate(questions[:10]):
            logger.info(f"\n🔍 Otázka {i+1}:")
            logger.info(f"   QID: {question['qid']}")
            logger.info(f"   Kód: {question['question_code']}")
            logger.info(f"   Text: {question['question_text'][:100]}...")
            logger.info(f"   LimeSurvey typ: {question['limesurvey_type']}")
            
            if question.get('matched_type'):
                matched = question['matched_type']
                logger.info(f"   ✅ Rozpoznáno jako: {matched['name']} (spolehlivost: {question['confidence']:.1%})")
            else:
                logger.info(f"   ❌ Nerozpoznáno")
            
            if question.get('recommended_analyses'):
                logger.info(f"   📈 Doporučené analýzy: {', '.join(question['recommended_analyses'])}")
        
        if len(questions) > 10:
            logger.info(f"\n... a {len(questions) - 10} dalších otázek")
        
        # Analýza podle typů
        logger.info("\n📊 ANALÝZA PODLE TYPŮ OTÁZEK:")
        logger.info("=" * 50)
        
        type_stats = {}
        for question in questions:
            q_type = question['limesurvey_type']
            if q_type not in type_stats:
                type_stats[q_type] = {'count': 0, 'recognized': 0}
            type_stats[q_type]['count'] += 1
            if question.get('matched_type'):
                type_stats[q_type]['recognized'] += 1
        
        for q_type, stats in sorted(type_stats.items()):
            recognition_rate = stats['recognized'] / stats['count'] * 100 if stats['count'] > 0 else 0
            logger.info(f"   Typ {q_type}: {stats['count']} otázek, {stats['recognized']} rozpoznáno ({recognition_rate:.1f}%)")
        
        # Plán analýz
        logger.info("\n📈 PLÁN ANALÝZ:")
        logger.info("=" * 50)
        
        analysis_plan = result['analysis_plan']
        
        # Question level analýzy
        question_analyses = analysis_plan['question_level_analyses']
        logger.info(f"🔍 Analýzy na úrovni otázek: {len(question_analyses)}")
        for analysis in question_analyses[:5]:  # Prvních 5
            logger.info(f"   📊 {analysis['analysis_name']}: {len(analysis['applicable_questions'])} otázek")
            logger.info(f"      Priorita: {analysis['priority_score']:.2f}, Složitost: {analysis['estimated_complexity']}")
        
        # Section level analýzy
        section_analyses = analysis_plan['section_level_analyses']
        logger.info(f"📋 Analýzy na úrovni sekcí: {len(section_analyses)}")
        for analysis in section_analyses[:3]:  # Prvních 3
            logger.info(f"   📊 {analysis['analysis_name']}")
        
        # Cross-question analýzy
        cross_analyses = analysis_plan['cross_question_analyses']
        logger.info(f"🔗 Cross-question analýzy: {len(cross_analyses)}")
        for analysis in cross_analyses[:3]:  # Prvních 3
            logger.info(f"   📊 {analysis['analysis_name']}")
        
        # Plán vizualizací
        logger.info("\n🎨 PLÁN VIZUALIZACÍ:")
        logger.info("=" * 50)
        
        chart_plan = result['chart_plan']
        
        # Primární vizualizace
        primary_viz = chart_plan['primary_visualizations']
        logger.info(f"🎯 Primární vizualizace: {len(primary_viz)}")
        
        viz_stats = {}
        for viz in primary_viz:
            viz_type = viz['datawrapper_type'] or 'Externí generátor'
            if viz_type not in viz_stats:
                viz_stats[viz_type] = 0
            viz_stats[viz_type] += 1
        
        for viz_type, count in sorted(viz_stats.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"   📊 {viz_type}: {count} grafů")
        
        # Datawrapper kompatibilita
        datawrapper_compatible = len([v for v in primary_viz if v['datawrapper_type']])
        total_viz = len(primary_viz)
        compatibility_rate = datawrapper_compatible / total_viz * 100 if total_viz > 0 else 0
        
        logger.info(f"\n📊 Datawrapper kompatibilita: {datawrapper_compatible}/{total_viz} ({compatibility_rate:.1f}%)")
        logger.info(f"🔧 Externí generátory potřebné: {total_viz - datawrapper_compatible}")
        
        # Export HTML reportu
        logger.info("\n📤 EXPORT HTML REPORTU:")
        logger.info("=" * 50)
        
        html_generator = AnalysisHTMLGenerator()
        output_dir = "/home/<USER>/vyvoj/limwrapp/analysis_reports/survey_827822"
        os.makedirs(output_dir, exist_ok=True)
        
        html_path = os.path.join(output_dir, "survey_827822_analysis.html")
        
        if html_generator.generate_analysis_report(result, html_path, "Survey 827822"):
            file_size = os.path.getsize(html_path) / 1024
            logger.info(f"✅ HTML report vygenerován: {html_path}")
            logger.info(f"📏 Velikost souboru: {file_size:.1f} KB")
            
            # Absolutní cesta pro uživatele
            abs_path = os.path.abspath(html_path)
            logger.info(f"🔗 Otevřít v prohlížeči: file://{abs_path}")
        else:
            logger.error("❌ Generování HTML reportu selhalo")
        
        # Export JSON reportu
        json_path = os.path.join(output_dir, "survey_827822_analysis.json")
        if manager.export_analysis_report(result, json_path):
            json_size = os.path.getsize(json_path) / 1024
            logger.info(f"✅ JSON report vygenerován: {json_path}")
            logger.info(f"📏 Velikost JSON: {json_size:.1f} KB")
        
        logger.info("\n" + "=" * 80)
        logger.info("🎉 TEST MENU 13 S REÁLNÝMI DATY ÚSPĚŠNÝ!")
        logger.info("=" * 80)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu s reálnými daty: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_lss_structure_analysis():
    """Analýza struktury LSS souboru"""
    
    try:
        logger.info("\n" + "=" * 80)
        logger.info("ANALÝZA STRUKTURY LSS SOUBORU")
        logger.info("=" * 80)
        
        from lss_parser import LSSParser
        
        lss_file = "/home/<USER>/vyvoj/limwrapp/data/dotazniky.urad.online/827822/structure.lss"
        
        parser = LSSParser()
        survey_data = parser.parse_lss_file(lss_file)
        
        if not survey_data:
            logger.error("❌ Parsování LSS selhalo")
            return False
        
        # Základní statistiky
        stats = parser.get_survey_statistics(survey_data)
        logger.info("📊 ZÁKLADNÍ STATISTIKY:")
        logger.info(f"   Survey ID: {stats['survey_id']}")
        logger.info(f"   Formát: {stats['format']}")
        logger.info(f"   Celkem otázek: {stats['total_questions']}")
        logger.info(f"   Celkem skupin: {stats['total_groups']}")
        
        # Analýza skupin
        groups = survey_data.get('groups', [])
        logger.info(f"\n📋 ANALÝZA SKUPIN ({len(groups)}):")
        for i, group in enumerate(groups[:5]):  # Prvních 5 skupin
            group_name = group.get('group_name', 'Bez názvu')
            questions_count = len(group.get('questions', []))
            logger.info(f"   {i+1}. {group_name}: {questions_count} otázek")
        
        if len(groups) > 5:
            logger.info(f"   ... a {len(groups) - 5} dalších skupin")
        
        # Analýza otázek ve skupinách
        total_questions_in_groups = 0
        question_types_in_groups = {}
        
        for group in groups:
            questions = group.get('questions', [])
            total_questions_in_groups += len(questions)
            
            for question in questions:
                q_type = question.get('type', 'unknown')
                if q_type not in question_types_in_groups:
                    question_types_in_groups[q_type] = 0
                question_types_in_groups[q_type] += 1
        
        logger.info(f"\n📝 ANALÝZA OTÁZEK VE SKUPINÁCH:")
        logger.info(f"   Celkem otázek ve skupinách: {total_questions_in_groups}")
        logger.info(f"   Typy otázek:")
        
        for q_type, count in sorted(question_types_in_groups.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"     {q_type}: {count} otázek")
        
        # Ukázka první otázky z první skupiny
        if groups and groups[0].get('questions'):
            first_question = groups[0]['questions'][0]
            logger.info(f"\n🔍 UKÁZKA PRVNÍ OTÁZKY:")
            logger.info(f"   QID: {first_question.get('qid', 'N/A')}")
            logger.info(f"   Title: {first_question.get('title', 'N/A')}")
            logger.info(f"   Type: {first_question.get('type', 'N/A')}")
            logger.info(f"   Question: {first_question.get('question', 'N/A')[:100]}...")
            
            # Subotázky
            subquestions = first_question.get('subquestions', [])
            logger.info(f"   Subotázky: {len(subquestions)}")
            
            # Možnosti odpovědí
            answeroptions = first_question.get('answeroptions', [])
            logger.info(f"   Možnosti odpovědí: {len(answeroptions)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při analýze struktury: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Spuštění testů s reálnými daty"""
    
    print("🚀 SPOUŠTÍM TESTY MENU 13 S REÁLNÝMI DATY")
    
    tests = [
        ("Analýza struktury LSS", test_lss_structure_analysis),
        ("Menu 13 s reálnými daty", test_menu13_with_real_lss)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Spouštím test: {test_name}")
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ Test '{test_name}' úspěšný")
            else:
                logger.error(f"❌ Test '{test_name}' selhal")
                
        except Exception as e:
            logger.error(f"💥 Test '{test_name}' vyvolal výjimku: {str(e)}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 80)
    logger.info("SHRNUTÍ TESTŮ S REÁLNÝMI DATY")
    logger.info("=" * 80)
    
    successful = 0
    for test_name, success in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        logger.info(f"{status}: {test_name}")
        if success:
            successful += 1
    
    logger.info(f"\nÚspěšnost: {successful}/{len(results)} testů")
    
    if successful == len(results):
        print("\n🎉 VŠECHNY TESTY S REÁLNÝMI DATY ÚSPĚŠNÉ!")
        print("✅ LSS struktura úspěšně analyzována")
        print("✅ Menu 13 funguje s reálnými daty")
        print("✅ HTML a JSON reporty vygenerovány")
        print("🚀 Analysis Engine je připraven pro produkční použití!")
    else:
        print(f"\n⚠️ {len(results) - successful} testů selhalo")
        print("💡 Zkontrolujte logy výše pro detaily")
    
    return successful == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
