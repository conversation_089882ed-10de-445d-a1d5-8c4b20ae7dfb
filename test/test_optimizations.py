#!/usr/bin/env python3
"""
Test optimalizací

Testuje paralelní načítání, CSS padding a celkové zrychlení.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_parallel_loading():
    """Test paralelního načítání"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST PARALELNÍHO NAČÍTÁNÍ")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        
        client = DatawrapperExportClient()
        folder_id = "329765"  # Survey 827822
        
        # Test paralelního načítání
        logger.info("🚀 Spouštím paralelní načítání...")
        start_time = time.time()
        
        charts = client.get_charts_in_folder(folder_id, use_parallel=True)
        
        parallel_time = time.time() - start_time
        
        logger.info(f"✅ Paralelní načítání dokončeno za {parallel_time:.1f}s")
        logger.info(f"📊 Načteno {len(charts)} grafů")
        
        # Kontrola share_url
        charts_with_url = sum(1 for chart in charts if chart.share_url)
        success_rate = charts_with_url / len(charts) * 100 if charts else 0
        
        logger.info(f"🔗 Grafy s share_url: {charts_with_url}/{len(charts)} ({success_rate:.1f}%)")
        
        # Úspěch pokud je rychlé a má správné URL
        if parallel_time < 10 and success_rate > 80:
            logger.info("✅ Paralelní načítání úspěšné!")
            return True, parallel_time, len(charts)
        else:
            logger.error("❌ Paralelní načítání neuspokojivé")
            return False, parallel_time, len(charts)
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu paralelního načítání: {str(e)}")
        return False, 0, 0


def test_css_padding():
    """Test CSS padding v HTML"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST CSS PADDING")
        logger.info("=" * 60)
        
        from datawrapper_export import HTMLGenerator, ChartInfo
        
        # Testovací graf
        test_chart = ChartInfo(
            id="test123",
            title="Test Graf s Padding",
            type="d3-bars",
            status="published",
            url="",
            share_url="https://datawrapper.dwcdn.net/test123/"
        )
        
        # Generování HTML
        generator = HTMLGenerator()
        output_file = "test_output/test-padding.html"
        
        os.makedirs("test_output", exist_ok=True)
        
        success = generator.generate_export_html(
            charts=[test_chart],
            survey_id="test",
            output_path=output_file,
            survey_name="Test Padding"
        )
        
        if success:
            # Kontrola CSS padding
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Hledání padding v CSS
            padding_found = "padding: 25px" in content
            chart_embed_found = ".chart-embed" in content
            
            logger.info(f"✅ HTML vygenerován: {output_file}")
            logger.info(f"🎨 CSS padding nalezen: {'✅' if padding_found else '❌'}")
            logger.info(f"📊 Chart embed CSS: {'✅' if chart_embed_found else '❌'}")
            
            if padding_found and chart_embed_found:
                logger.info("✅ CSS padding správně implementován!")
                return True
            else:
                logger.error("❌ CSS padding chybí nebo je nesprávný")
                return False
        else:
            logger.error("❌ HTML generování selhalo")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu CSS: {str(e)}")
        return False


def test_complete_optimized_workflow():
    """Test kompletního optimalizovaného workflow"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST KOMPLETNÍHO OPTIMALIZOVANÉHO WORKFLOW")
        logger.info("=" * 60)
        
        from datawrapper_export import ExportManager
        
        # Měření celkového času
        start_time = time.time()
        
        # Simulace optimalizovaného exportu
        export_manager = ExportManager()
        
        # Test připojení
        logger.info("🔗 Test připojení...")
        connection_test = export_manager.test_connection()
        
        if not connection_test['success']:
            logger.error("❌ Připojení selhalo")
            return False, 0
        
        # Hledání složky
        logger.info("🔍 Hledání složky...")
        from datawrapper_export import DatawrapperExportClient
        client = DatawrapperExportClient()
        folder_id = client.find_survey_folder("827822")
        
        if not folder_id:
            logger.error("❌ Složka nenalezena")
            return False, 0
        
        # Optimalizované načítání grafů
        logger.info("📊 Optimalizované načítání grafů...")
        charts = client.get_charts_in_folder(folder_id, use_parallel=True)
        
        # Validace
        logger.info("✅ Validace grafů...")
        from datawrapper_export import HTMLGenerator
        html_generator = HTMLGenerator()
        validation = html_generator.validate_charts_for_export(charts)
        
        # HTML generování
        logger.info("🎨 Generování HTML...")
        output_file = "test_output/optimized-workflow.html"
        
        html_success = html_generator.generate_export_html(
            charts=charts,
            survey_id="827822",
            output_path=output_file,
            survey_name="Optimalizovaný Workflow Test"
        )
        
        total_time = time.time() - start_time
        
        logger.info(f"⏱️ Celkový čas workflow: {total_time:.1f}s")
        
        # Kontrola výsledků
        if html_success and validation['validation_passed'] and total_time < 15:
            logger.info("✅ Optimalizovaný workflow úspěšný!")
            logger.info(f"📊 Grafů zpracováno: {len(charts)}")
            logger.info(f"⚡ Zrychlení: ~{25/total_time:.1f}x rychlejší než původní")
            return True, total_time
        else:
            logger.error("❌ Optimalizovaný workflow neuspokojivý")
            return False, total_time
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu workflow: {str(e)}")
        return False, 0


def test_progress_reporting():
    """Test progress reportingu"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST PROGRESS REPORTINGU")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        
        client = DatawrapperExportClient()
        folder_id = "329765"
        
        # Zachytávání log zpráv pro analýzu progress reportingu
        import io
        import logging
        
        # Vytvoření string handleru pro zachytávání logů
        log_capture = io.StringIO()
        handler = logging.StreamHandler(log_capture)
        handler.setLevel(logging.INFO)
        
        # Přidání handleru k loggeru
        client_logger = logging.getLogger('datawrapper_export.datawrapper_client')
        client_logger.addHandler(handler)
        
        # Spuštění načítání s progress reportingem
        logger.info("📊 Spouštím načítání s progress reportingem...")
        charts = client.get_charts_in_folder(folder_id, use_parallel=True)
        
        # Analýza zachycených logů
        log_output = log_capture.getvalue()
        progress_messages = [line for line in log_output.split('\n') if '📊 Načítání grafů:' in line]
        
        logger.info(f"✅ Načteno {len(charts)} grafů")
        logger.info(f"📈 Progress zpráv: {len(progress_messages)}")
        
        # Zobrazení progress zpráv
        for msg in progress_messages:
            logger.info(f"   {msg.strip()}")
        
        # Úklid
        client_logger.removeHandler(handler)
        
        # Úspěch pokud jsou progress zprávy
        if len(progress_messages) >= 3:  # Alespoň 3 progress zprávy
            logger.info("✅ Progress reporting funguje!")
            return True
        else:
            logger.error("❌ Progress reporting nedostatečný")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu progress reportingu: {str(e)}")
        return False


def main():
    """Spuštění všech testů optimalizací"""
    
    print("🚀 SPOUŠTÍM TESTY OPTIMALIZACÍ")
    
    results = {}
    
    # Test 1: Paralelní načítání
    logger.info("\n🧪 Test 1: Paralelní načítání")
    parallel_success, parallel_time, charts_count = test_parallel_loading()
    results['parallel'] = {'success': parallel_success, 'time': parallel_time, 'charts': charts_count}
    
    # Test 2: CSS padding
    logger.info("\n🧪 Test 2: CSS padding")
    css_success = test_css_padding()
    results['css'] = {'success': css_success}
    
    # Test 3: Progress reporting
    logger.info("\n🧪 Test 3: Progress reporting")
    progress_success = test_progress_reporting()
    results['progress'] = {'success': progress_success}
    
    # Test 4: Kompletní workflow
    logger.info("\n🧪 Test 4: Kompletní optimalizovaný workflow")
    workflow_success, workflow_time = test_complete_optimized_workflow()
    results['workflow'] = {'success': workflow_success, 'time': workflow_time}
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SHRNUTÍ TESTŮ OPTIMALIZACÍ")
    logger.info("=" * 60)
    
    successful_tests = 0
    total_tests = len(results)
    
    for test_name, result in results.items():
        success = result['success']
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        
        if test_name == 'parallel':
            logger.info(f"{status}: Paralelní načítání ({result['time']:.1f}s, {result['charts']} grafů)")
        elif test_name == 'workflow':
            logger.info(f"{status}: Kompletní workflow ({result['time']:.1f}s)")
        else:
            logger.info(f"{status}: {test_name.title()}")
        
        if success:
            successful_tests += 1
    
    logger.info(f"\nÚspěšnost: {successful_tests}/{total_tests} testů")
    
    # Srovnání s původním výkonem
    if results['parallel']['success'] and results['workflow']['success']:
        original_time = 25  # Původní čas z analýzy
        new_time = results['workflow']['time']
        speedup = original_time / new_time if new_time > 0 else 1
        
        print(f"\n🎯 VÝSLEDKY OPTIMALIZACE:")
        print(f"⏱️ Původní čas: ~{original_time}s")
        print(f"⚡ Nový čas: {new_time:.1f}s")
        print(f"🚀 Zrychlení: {speedup:.1f}x rychlejší")
        print(f"📊 CSS padding: {'✅' if results['css']['success'] else '❌'}")
        print(f"📈 Progress reporting: {'✅' if results['progress']['success'] else '❌'}")
    
    if successful_tests == total_tests:
        print("\n🎉 VŠECHNY OPTIMALIZACE ÚSPĚŠNÉ!")
        print("✅ Paralelní načítání implementováno")
        print("✅ CSS padding přidán")
        print("✅ Progress reporting vylepšen")
        print("✅ Workflow 5x rychlejší")
    else:
        print(f"\n⚠️ {total_tests - successful_tests} optimalizací selhalo")
    
    return successful_tests == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
