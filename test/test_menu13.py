#!/usr/bin/env python3
"""
Test Menu 13 - Inteligentní analýza průzkumu

Testuje kompletní workflow Menu 13.
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_intelligent_analysis_manager():
    """Test IntelligentAnalysisManager"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST INTELLIGENT ANALYSIS MANAGER")
        logger.info("=" * 60)
        
        from intelligent_analysis import IntelligentAnalysisManager
        
        # Inicializace manageru
        manager = IntelligentAnalysisManager()
        
        if manager.question_analyzer is None:
            logger.error("❌ Question analyzer není inicializo<PERSON>")
            return False
        
        logger.info("✅ IntelligentAnalysisManager úspěšně inicializován")
        logger.info(f"📊 Metadata načtena:")
        logger.info(f"   - Typy otázek: {len(manager.metadata_loader.question_types)}")
        logger.info(f"   - Typy analýz: {len(manager.metadata_loader.analysis_types)}")
        logger.info(f"   - Typy vizualizací: {len(manager.metadata_loader.visualization_types)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu IntelligentAnalysisManager: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_html_generator():
    """Test AnalysisHTMLGenerator"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST HTML GENERATOR")
        logger.info("=" * 60)
        
        from intelligent_analysis_html import AnalysisHTMLGenerator
        
        # Vytvoření testovacích dat
        test_result = {
            'survey_info': {
                'lss_file': 'test.lss',
                'total_questions': 5,
                'analyzed_questions': 5,
                'recognition_rate': 1.0
            },
            'question_analysis': {
                'summary': {
                    'total_questions': 5,
                    'recognized_questions': 5,
                    'recognition_rate': 1.0
                },
                'questions': [
                    {
                        'qid': 'Q1',
                        'question_code': 'satisfaction',
                        'question_text': 'Jak jste spokojeni s naší službou?',
                        'limesurvey_type': '5',
                        'matched_type': {
                            'id': '5',
                            'name': '5 Point Choice',
                            'description': 'Likertova škála'
                        },
                        'confidence': 1.0,
                        'recommended_analyses': ['FRA', 'STA'],
                        'notes': 'Mapováno na typ: 5 Point Choice | Spolehlivost: 100.0%'
                    }
                ]
            },
            'analysis_plan': {
                'question_level_analyses': [
                    {
                        'analysis_id': 'FRA',
                        'analysis_name': 'Frequency Analysis',
                        'analysis_description': 'Frekvenční analýza odpovědí',
                        'applicable_questions': ['Q1'],
                        'priority_score': 0.8,
                        'estimated_complexity': 2,
                        'reasoning': 'Vhodné pro škálové otázky',
                        'prerequisites': ['Číselná data']
                    }
                ],
                'section_level_analyses': [],
                'cross_question_analyses': [],
                'total_estimated_time': 60,
                'complexity_score': 2.5
            },
            'chart_plan': {
                'primary_visualizations': [
                    {
                        'visualization_id': 'BAR',
                        'visualization_name': 'Sloupcový graf',
                        'visualization_description': 'Základní sloupcový graf',
                        'analysis_id': 'FRA',
                        'datawrapper_type': 'd3-bars',
                        'priority_score': 0.9,
                        'data_requirements': ['Kategoriální data'],
                        'configuration_hints': {'sort_bars': 'true'},
                        'fallback_options': ['TAB']
                    }
                ],
                'alternative_visualizations': [],
                'unsupported_analyses': [],
                'total_charts_estimate': 5,
                'datawrapper_compatibility': 0.8
            },
            'summary': {
                'total_questions': 5,
                'recognized_questions': 5,
                'recognition_rate': 1.0,
                'total_analyses': 1,
                'total_visualizations': 1,
                'estimated_time_hours': 1.0,
                'complexity_score': 2.5,
                'datawrapper_compatibility': 0.8,
                'implementation_priority': 'Vysoká'
            }
        }
        
        # Test generování HTML
        generator = AnalysisHTMLGenerator()
        output_path = "test_output/test_analysis_report.html"
        
        os.makedirs("test_output", exist_ok=True)
        
        success = generator.generate_analysis_report(
            test_result, 
            output_path, 
            "Test Survey"
        )
        
        if success:
            # Kontrola existence souboru
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / 1024
                logger.info(f"✅ HTML report vygenerován: {output_path}")
                logger.info(f"📏 Velikost souboru: {file_size:.1f} KB")
                
                # Kontrola obsahu
                with open(output_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                checks = [
                    ("HTML struktura", "<!DOCTYPE html>" in content),
                    ("CSS styly", ".container" in content),
                    ("JavaScript", "addEventListener" in content),
                    ("Navigace", "nav-link" in content),
                    ("Statistiky", "stat-card" in content),
                    ("Otázky", "question-card" in content),
                    ("Analýzy", "analysis-card" in content),
                    ("Vizualizace", "visualization-card" in content),
                    ("Implementace", "implementation-summary" in content)
                ]
                
                all_passed = True
                for check_name, check_result in checks:
                    if check_result:
                        logger.info(f"✅ {check_name}: OK")
                    else:
                        logger.error(f"❌ {check_name}: CHYBÍ")
                        all_passed = False
                
                if all_passed:
                    logger.info("✅ HTML generator funguje správně!")
                    
                    # Absolutní cesta pro uživatele
                    abs_path = os.path.abspath(output_path)
                    logger.info(f"🔗 Test HTML: {abs_path}")
                    
                    return True
                else:
                    logger.error("❌ Některé HTML kontroly selhaly")
                    return False
            else:
                logger.error("❌ HTML soubor nebyl vytvořen")
                return False
        else:
            logger.error("❌ HTML generování selhalo")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu HTML generator: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_menu13_integration():
    """Test integrace Menu 13 do hlavní aplikace"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST INTEGRACE MENU 13")
        logger.info("=" * 60)
        
        # Test importu hlavní aplikace
        try:
            import main
            logger.info("✅ Import main.py úspěšný")
        except ImportError as e:
            logger.error(f"❌ Import main.py selhal: {e}")
            return False
        
        # Test existence funkce intelligent_analysis_menu
        if hasattr(main, 'intelligent_analysis_menu'):
            logger.info("✅ Funkce intelligent_analysis_menu existuje")
        else:
            logger.error("❌ Funkce intelligent_analysis_menu neexistuje")
            return False
        
        # Test importu modulů v menu funkci
        try:
            from intelligent_analysis import IntelligentAnalysisManager
            from intelligent_analysis_html import AnalysisHTMLGenerator
            logger.info("✅ Import Analysis Engine modulů úspěšný")
        except ImportError as e:
            logger.error(f"❌ Import Analysis Engine modulů selhal: {e}")
            return False
        
        logger.info("✅ Menu 13 je připraveno k použití!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu integrace: {str(e)}")
        return False


def test_directory_structure():
    """Test struktury adresářů pro Menu 13"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST STRUKTURY ADRESÁŘŮ")
        logger.info("=" * 60)
        
        # Kontrola existence klíčových adresářů a souborů
        checks = [
            ("Analysis Engine", "src/analysis_engine/__init__.py"),
            ("Metadata", "data/analysis_metadata/otazky.md"),
            ("Intelligent Analysis", "src/intelligent_analysis.py"),
            ("HTML Generator", "src/intelligent_analysis_html.py"),
            ("Dokumentace", "docs/analysis-engine.md"),
            ("Test adresář", "test/test_analysis_engine.py")
        ]
        
        all_passed = True
        for check_name, file_path in checks:
            if os.path.exists(file_path):
                logger.info(f"✅ {check_name}: {file_path}")
            else:
                logger.error(f"❌ {check_name}: {file_path} CHYBÍ")
                all_passed = False
        
        # Kontrola výstupních adresářů
        output_dirs = [
            "analysis_reports",
            "data/surveys",
            "test_output"
        ]
        
        for dir_path in output_dirs:
            os.makedirs(dir_path, exist_ok=True)
            if os.path.exists(dir_path):
                logger.info(f"✅ Výstupní adresář: {dir_path}")
            else:
                logger.error(f"❌ Výstupní adresář: {dir_path} NELZE VYTVOŘIT")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu struktury: {str(e)}")
        return False


def main():
    """Spuštění všech testů Menu 13"""
    
    print("🚀 SPOUŠTÍM TESTY MENU 13")
    
    tests = [
        ("Struktura adresářů", test_directory_structure),
        ("Intelligent Analysis Manager", test_intelligent_analysis_manager),
        ("HTML Generator", test_html_generator),
        ("Integrace Menu 13", test_menu13_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Spouštím test: {test_name}")
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ Test '{test_name}' úspěšný")
            else:
                logger.error(f"❌ Test '{test_name}' selhal")
                
        except Exception as e:
            logger.error(f"💥 Test '{test_name}' vyvolal výjimku: {str(e)}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SHRNUTÍ TESTŮ MENU 13")
    logger.info("=" * 60)
    
    successful = 0
    for test_name, success in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        logger.info(f"{status}: {test_name}")
        if success:
            successful += 1
    
    logger.info(f"\nÚspěšnost: {successful}/{len(results)} testů")
    
    if successful == len(results):
        print("\n🎉 VŠECHNY TESTY MENU 13 ÚSPĚŠNÉ!")
        print("✅ Struktura adresářů připravena")
        print("✅ Analysis Engine funkční")
        print("✅ HTML generator funguje")
        print("✅ Integrace do hlavní aplikace dokončena")
        print("🚀 Menu 13 je připraveno k produkčnímu použití!")
        print("\n💡 Spusťte aplikaci a vyberte Menu 13 pro testování")
    else:
        print(f"\n⚠️ {len(results) - successful} testů selhalo")
        print("💡 Zkontrolujte logy výše pro detaily")
    
    return successful == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
