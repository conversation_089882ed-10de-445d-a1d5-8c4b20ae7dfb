#!/usr/bin/env python3
"""
Clean Metadata Workflow Test

Vyt<PERSON><PERSON><PERSON> graf od za<PERSON>ku a postupně nakonfiguruje metadata.
Uživatel bude hlásit co vidí v Datawrapper editoru.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from datawrapper_client import DatawrapperClient
from datawrapper_export import (
    DatawrapperExportClient,
    ChartConfigurator,
    ChartInfo
)

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CleanMetadataWorkflowTest:
    """Clean metadata workflow test"""
    
    def __init__(self):
        self.dw_client = None
        self.export_client = None
        self.test_chart_id = None
        self.share_url = None
        
    def step_1_create_clean_chart(self):
        """Krok 1: Vytvoření čistého grafu"""
        logger.info("=" * 60)
        logger.info("KROK 1: Vytvoření čistého grafu")
        logger.info("=" * 60)
        
        try:
            # Inicializace klienta
            self.dw_client = DatawrapperClient()
            logger.info("✅ DatawrapperClient inicializován")
            
            # Vytvoření grafu
            chart_data = self.dw_client.create_chart(
                title="Clean Test Graf - Social Sharing",
                chart_type="d3-bars",
                description="Test grafu pro social sharing metadata",
                data_source="LimWrapp Clean Test",
                byline="Clean Test Data"
            )
            
            if not chart_data or 'id' not in chart_data:
                logger.error("❌ Nepodařilo se vytvořit graf")
                return False
            
            self.test_chart_id = chart_data['id']
            logger.info(f"✅ Graf vytvořen s ID: {self.test_chart_id}")
            
            # Přidání dat
            csv_data = """Kategorie,Hodnota
Velmi spokojen,45
Spokojen,32
Neutrální,18
Nespokojen,8
Velmi nespokojen,3"""
            
            logger.info("📊 Přidávám data do grafu...")
            data_success = self.dw_client.update_chart_data(self.test_chart_id, csv_data)
            
            if not data_success:
                logger.error("❌ Nepodařilo se přidat data")
                return False
            
            logger.info("✅ Data úspěšně přidána")
            time.sleep(3)
            
            # Základní metadata pro zobrazení
            basic_metadata = {
                "data": {
                    "column-format": {
                        "Kategorie": {"type": "text", "input-format": "auto"},
                        "Hodnota": {"type": "number", "input-format": "auto"}
                    }
                },
                "visualize": {
                    "chart": {
                        "margin": {"top": 20, "right": 20, "bottom": 40, "left": 60}
                    }
                }
            }
            
            logger.info("⚙️ Nastavuji základní metadata...")
            self.dw_client.update_chart_metadata(self.test_chart_id, basic_metadata)
            time.sleep(2)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při vytváření grafu: {str(e)}")
            return False
    
    def step_2_publish_clean_chart(self):
        """Krok 2: Publikování čistého grafu"""
        logger.info("\n" + "=" * 60)
        logger.info("KROK 2: Publikování čistého grafu")
        logger.info("=" * 60)
        
        try:
            logger.info("📤 Publikuji čistý graf...")
            share_url = self.dw_client.publish_chart(self.test_chart_id)
            
            if not share_url:
                logger.error("❌ Nepodařilo se publikovat graf")
                return False
            
            self.share_url = share_url
            logger.info(f"✅ Graf publikován: {share_url}")
            
            print("\n" + "🔗" * 60)
            print(f"GRAF VYTVOŘEN A PUBLIKOVÁN:")
            print(f"Chart ID: {self.test_chart_id}")
            print(f"URL: {share_url}")
            print("🔗" * 60)
            
            print("\n📋 PROSÍM ZKONTROLUJTE V DATAWRAPPER EDITORU:")
            print(f"1. Otevřete graf {self.test_chart_id} v editoru")
            print("2. Přejděte na Layout tab")
            print("3. Hlaste co vidíte v sekci 'Share buttons'")
            
            user_report = input("\n👀 Co vidíte v Layout → Share buttons? (popište): ")
            logger.info(f"USER REPORT: {user_report}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při publikování: {str(e)}")
            return False
    
    def step_3_check_metadata_structure(self):
        """Krok 3: Kontrola metadata struktury"""
        logger.info("\n" + "=" * 60)
        logger.info("KROK 3: Kontrola metadata struktury")
        logger.info("=" * 60)
        
        try:
            # Inicializace export klienta
            self.export_client = DatawrapperExportClient()
            
            # Získání kompletních metadata
            response = self.export_client._make_request('GET', f'charts/{self.test_chart_id}')
            
            if response.status_code != 200:
                logger.error("❌ Nepodařilo se načíst metadata")
                return False
            
            full_data = response.json()
            metadata = full_data.get('metadata', {})
            
            # Analýza publish metadata
            publish_meta = metadata.get('publish', {})
            blocks = publish_meta.get('blocks', {})
            
            logger.info("📋 Aktuální BLOCKS nastavení:")
            logger.info(f"  embed: {blocks.get('embed', 'NENÍ NASTAVENO')}")
            logger.info(f"  download-image: {blocks.get('download-image', 'NENÍ NASTAVENO')}")
            logger.info(f"  download-pdf: {blocks.get('download-pdf', 'NENÍ NASTAVENO')}")
            logger.info(f"  download-svg: {blocks.get('download-svg', 'NENÍ NASTAVENO')}")
            logger.info(f"  get-the-data: {blocks.get('get-the-data', 'NENÍ NASTAVENO')}")
            
            # Analýza visualize metadata
            visualize_meta = metadata.get('visualize', {})
            sharing = visualize_meta.get('sharing', {})
            
            logger.info("📋 Aktuální SHARING nastavení:")
            logger.info(f"  enabled: {sharing.get('enabled', 'NENÍ NASTAVENO')}")
            logger.info(f"  auto: {sharing.get('auto', 'NENÍ NASTAVENO')}")
            logger.info(f"  url: {sharing.get('url', 'NENÍ NASTAVENO')}")
            
            print("\n📋 PROSÍM POROVNEJTE S DATAWRAPPER EDITOREM:")
            print("Layout → Share buttons - jaké jsou aktuální nastavení?")
            
            user_report = input("\n👀 Popište aktuální nastavení Share buttons: ")
            logger.info(f"USER REPORT: {user_report}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při kontrole metadata: {str(e)}")
            return False
    
    def step_4_configure_social_sharing(self):
        """Krok 4: Konfigurace social sharing"""
        logger.info("\n" + "=" * 60)
        logger.info("KROK 4: Konfigurace social sharing")
        logger.info("=" * 60)
        
        try:
            # Vytvoření ChartInfo objektu
            chart_info = ChartInfo(
                id=self.test_chart_id,
                title="Clean Test Graf - Social Sharing",
                type="d3-bars",
                status="published",
                url="",
                share_url=self.share_url
            )
            
            # Konfigurace pomocí ChartConfigurator
            configurator = ChartConfigurator(self.export_client)
            
            logger.info("⚙️ Aplikuji konfiguraci social sharing...")
            
            # Použití force_republish=True
            results = configurator.configure_charts_for_export([chart_info], force_republish=True)
            
            if not results or len(results) == 0:
                logger.error("❌ Konfigurace selhala")
                return False
            
            result = results[0]
            
            if result.success:
                logger.info("✅ Konfigurace úspěšná")
                logger.info(f"✅ Nová share URL: {result.new_share_url}")
                self.share_url = result.new_share_url
            else:
                logger.error(f"❌ Konfigurace selhala: {result.error_message}")
                return False
            
            print("\n📋 PROSÍM ZKONTROLUJTE ZMĚNY V EDITORU:")
            print("1. Refresh stránku editoru")
            print("2. Přejděte na Layout → Share buttons")
            print("3. Hlaste co se změnilo")
            
            user_report = input("\n👀 Co se změnilo v Share buttons po konfiguraci? ")
            logger.info(f"USER REPORT: {user_report}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při konfiguraci: {str(e)}")
            return False
    
    def step_5_verify_final_state(self):
        """Krok 5: Ověření finálního stavu"""
        logger.info("\n" + "=" * 60)
        logger.info("KROK 5: Ověření finálního stavu")
        logger.info("=" * 60)
        
        try:
            # Počkáme na zpracování
            time.sleep(5)
            
            # Znovu načteme metadata
            response = self.export_client._make_request('GET', f'charts/{self.test_chart_id}')
            full_data = response.json()
            metadata = full_data.get('metadata', {})
            
            # Analýza finálních nastavení
            publish_meta = metadata.get('publish', {})
            blocks = publish_meta.get('blocks', {})
            
            logger.info("📋 FINÁLNÍ BLOCKS nastavení:")
            logger.info(f"  embed: {blocks.get('embed', 'NENÍ NASTAVENO')}")
            logger.info(f"  download-image: {blocks.get('download-image', 'NENÍ NASTAVENO')}")
            logger.info(f"  download-pdf: {blocks.get('download-pdf', 'NENÍ NASTAVENO')}")
            logger.info(f"  download-svg: {blocks.get('download-svg', 'NENÍ NASTAVENO')}")
            logger.info(f"  get-the-data: {blocks.get('get-the-data', 'NENÍ NASTAVENO')}")
            
            visualize_meta = metadata.get('visualize', {})
            sharing = visualize_meta.get('sharing', {})
            
            logger.info("📋 FINÁLNÍ SHARING nastavení:")
            logger.info(f"  enabled: {sharing.get('enabled', 'NENÍ NASTAVENO')}")
            logger.info(f"  auto: {sharing.get('auto', 'NENÍ NASTAVENO')}")
            logger.info(f"  url: {sharing.get('url', 'NENÍ NASTAVENO')}")
            
            print("\n" + "🔗" * 60)
            print(f"FINÁLNÍ GRAF:")
            print(f"URL: {self.share_url}")
            print("🔗" * 60)
            
            print("\n📋 FINÁLNÍ KONTROLA:")
            print("1. Otevřete graf v prohlížeči")
            print("2. Zkontrolujte, zda jsou vidět social sharing buttons")
            print("3. Zkontrolujte download options")
            
            user_report = input("\n👀 Finální stav - co vidíte na grafu? ")
            logger.info(f"USER REPORT: {user_report}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při ověření: {str(e)}")
            return False
    
    def run_workflow(self):
        """Spuštění celého workflow"""
        logger.info("🚀 SPOUŠTÍM CLEAN METADATA WORKFLOW")
        logger.info("=" * 60)
        
        steps = [
            ("Vytvoření čistého grafu", self.step_1_create_clean_chart),
            ("Publikování čistého grafu", self.step_2_publish_clean_chart),
            ("Kontrola metadata struktury", self.step_3_check_metadata_structure),
            ("Konfigurace social sharing", self.step_4_configure_social_sharing),
            ("Ověření finálního stavu", self.step_5_verify_final_state)
        ]
        
        for i, (step_name, step_func) in enumerate(steps, 1):
            logger.info(f"\n🔄 Krok {i}/{len(steps)}: {step_name}")
            
            if not step_func():
                logger.error(f"❌ Krok {i} selhal: {step_name}")
                return False
        
        logger.info("\n" + "🎉" * 60)
        logger.info("CLEAN METADATA WORKFLOW DOKONČEN!")
        logger.info("🎉" * 60)
        
        return True


if __name__ == "__main__":
    test = CleanMetadataWorkflowTest()
    
    try:
        success = test.run_workflow()
        
        if success:
            print("\n✅ Clean workflow úspěšně dokončen!")
        else:
            print("\n❌ Clean workflow selhal!")
            
    except KeyboardInterrupt:
        print("\n⚠️ Test přerušen uživatelem")
    except Exception as e:
        print(f"\n💥 Neočekávaná chyba: {e}")
        import traceback
        traceback.print_exc()
