#!/usr/bin/env python3
"""
Test pro survey 827822 - fáze 1

Najde složku 827822 v Datawrapper, spo<PERSON><PERSON><PERSON><PERSON> grafy a vyp<PERSON><PERSON><PERSON> j<PERSON>ich n<PERSON>.
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_survey_827822_folder():
    """Test fáze 1: Najít složku 827822 a vypsat grafy"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST SURVEY 827822 - FÁZE 1")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        from datawrapper_client import DatawrapperClient
        from config_loader import load_config
        
        # Načtení konfigurace
        config = load_config()
        parent_folder_id = config.get('DATAWRAPPER_LIMESURVEY_FOLDER_ID')
        team_id = config.get('DATAWRAPPER_TEAM_ID')
        
        logger.info(f"Parent folder ID (LimeSurvey): {parent_folder_id}")
        logger.info(f"Team ID: {team_id}")
        
        # Survey info
        survey_id = "827822"

        logger.info(f"Survey ID: {survey_id}")
        logger.info("Hledám složku podle názvu v parent složce")
        
        # Inicializace klientů
        export_client = DatawrapperExportClient()
        dw_client = DatawrapperClient()
        
        # Krok 1: Ověření parent složky
        logger.info("\n🔍 KROK 1: Ověření parent složky")

        # Použijeme přímé API volání pro získání struktury
        response = export_client._make_request('GET', f'folders/{parent_folder_id}')

        if response.status_code == 200:
            parent_data = response.json()

            # API vrací strukturu s 'children' (podsložky) a 'charts' (grafy)
            children_ids = [child.get('id') for child in parent_data.get('children', [])]
            charts_count = len(parent_data.get('charts', []))

            logger.info(f"✅ Parent složka obsahuje:")
            logger.info(f"   📁 Podsložek: {len(children_ids)}")
            logger.info(f"   📊 Grafů: {charts_count}")

            # Načtení detailů podsložek
            logger.info("\n📁 Načítám detaily podsložek:")
            target_folder_id = None

            for child_id in children_ids:
                logger.info(f"🔍 Načítám složku ID: {child_id}")

                try:
                    # Načtení detailů složky přes API
                    child_response = export_client._make_request('GET', f'folders/{child_id}')

                    if child_response.status_code == 200:
                        child_data = child_response.json()
                        folder_name = child_data.get('name', 'Bez názvu')
                        charts_in_child = len(child_data.get('charts', []))

                        logger.info(f"   - ID: {child_id}, Název: '{folder_name}', Grafů: {charts_in_child}")

                        # Hledání podle názvu
                        if survey_id in folder_name or folder_name == survey_id:
                            logger.info(f"     🎯 NALEZENA CÍLOVÁ SLOŽKA PRO SURVEY {survey_id}!")
                            target_folder_id = str(child_id)
                    else:
                        logger.warning(f"   - ID: {child_id} - API vrátila {child_response.status_code}")

                except Exception as e:
                    logger.warning(f"   - ID: {child_id} - chyba při načítání: {str(e)}")
        else:
            logger.error(f"❌ Nepodařilo se načíst parent složku {parent_folder_id}: {response.status_code}")
            return False
        
        # Kontrola, zda jsme našli cílovou složku
        if not target_folder_id:
            logger.error(f"❌ Složka pro survey {survey_id} nebyla nalezena v parent složce")
            return False

        # Krok 2: Načtení cílové složky
        logger.info(f"\n📊 KROK 2: Načtení složky {target_folder_id}")

        target_response = export_client._make_request('GET', f'folders/{target_folder_id}')

        if target_response.status_code != 200:
            logger.error(f"❌ Složka {target_folder_id} neexistuje nebo není dostupná")
            return False

        target_data = target_response.json()

        # Analýza obsahu (API vrací strukturu s 'charts' a 'children')
        charts_in_folder = target_data.get('charts', [])
        subfolders = target_data.get('children', [])

        logger.info(f"✅ Složka {target_folder_id} obsahuje:")
        logger.info(f"   📊 Grafů: {len(charts_in_folder)}")
        logger.info(f"   📁 Podsložek: {len(subfolders)}")

        # Ověření počtu podle screenshotu (mělo by být 40 grafů)
        expected_count = 40
        if len(charts_in_folder) == expected_count:
            logger.info(f"✅ Počet grafů odpovídá očekávání: {expected_count}")
        else:
            logger.warning(f"⚠️ Počet grafů ({len(charts_in_folder)}) neodpovídá očekávání ({expected_count})")
        
        # Krok 3: Detailní informace o grafech
        logger.info(f"\n📋 KROK 3: Seznam grafů ve složce")
        
        if charts_in_folder:
            logger.info(f"Nalezeno {len(charts_in_folder)} grafů:")

            # API vrací pouze ID grafů, potřebujeme načíst detaily
            for i, chart in enumerate(charts_in_folder[:10], 1):  # Prvních 10 pro rychlost
                chart_id = chart.get('id', 'N/A')

                try:
                    # Načtení detailů grafu
                    chart_details = export_client._make_request('GET', f'charts/{chart_id}')

                    if chart_details.status_code == 200:
                        chart_data = chart_details.json()
                        chart_title = chart_data.get('title', 'Bez názvu')
                        chart_type = chart_data.get('type', 'unknown')
                        chart_status = chart_data.get('publicStatus', 'draft')

                        logger.info(f"   {i:2d}. {chart_title}")
                        logger.info(f"       ID: {chart_id}")
                        logger.info(f"       Typ: {chart_type}")
                        logger.info(f"       Status: {chart_status}")

                        # Pokud je publikován, zkusíme získat URL
                        if chart_status == 'published':
                            public_url = chart_data.get('publicUrl', 'N/A')
                            logger.info(f"       URL: {public_url}")
                    else:
                        logger.info(f"   {i:2d}. ID: {chart_id} (nepodařilo se načíst detaily)")

                except Exception as e:
                    logger.info(f"   {i:2d}. ID: {chart_id} (chyba: {str(e)})")

                logger.info("")

            if len(charts_in_folder) > 10:
                logger.info(f"   ... a dalších {len(charts_in_folder) - 10} grafů")
        else:
            logger.warning("⚠️ Ve složce nejsou žádné grafy")
        
        # Krok 4: Test pomocí DatawrapperExportClient metod
        logger.info(f"\n🧪 KROK 4: Test pomocí export klienta")
        
        # Test find_survey_folder
        found_folder_id = export_client.find_survey_folder(survey_id)
        
        if found_folder_id:
            logger.info(f"✅ find_survey_folder() nalezla: {found_folder_id}")

            if found_folder_id == target_folder_id:
                logger.info("✅ Shoda s nalezeným ID!")
            else:
                logger.warning(f"⚠️ Rozdíl: nalezeno {target_folder_id}, find_survey_folder vrátila {found_folder_id}")
        else:
            logger.warning("⚠️ find_survey_folder() nenalezla složku")

        # Test get_charts_in_folder
        charts_via_method = export_client.get_charts_in_folder(target_folder_id)
        
        logger.info(f"✅ get_charts_in_folder() nalezla: {len(charts_via_method)} grafů")
        
        if charts_via_method:
            logger.info("📋 Grafy přes export metodu:")
            for i, chart in enumerate(charts_via_method[:5], 1):  # Prvních 5
                logger.info(f"   {i}. {chart.title} (ID: {chart.id}, Status: {chart.status})")
            
            if len(charts_via_method) > 5:
                logger.info(f"   ... a dalších {len(charts_via_method) - 5} grafů")
        
        # Shrnutí
        logger.info("\n" + "=" * 60)
        logger.info("SHRNUTÍ FÁZE 1")
        logger.info("=" * 60)
        logger.info(f"Survey ID: {survey_id}")
        logger.info(f"Folder ID: {target_folder_id}")
        logger.info(f"Celkem grafů: {len(charts_in_folder)}")
        logger.info(f"Publikovaných grafů: {sum(1 for c in charts_in_folder if c.get('publicStatus') == 'published')}")
        logger.info(f"Export metoda funguje: {'✅ ANO' if charts_via_method else '❌ NE'}")
        logger.info(f"Očekávaný počet: 40, skutečný: {len(charts_in_folder)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_alternative_folder_discovery():
    """Test alternativních metod hledání složky"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST ALTERNATIVNÍCH METOD HLEDÁNÍ")
        logger.info("=" * 60)
        
        from datawrapper_client import DatawrapperClient
        from config_loader import load_config
        
        config = load_config()
        parent_folder_id = config.get('DATAWRAPPER_LIMESURVEY_FOLDER_ID')
        
        client = DatawrapperClient()
        survey_id = "827822"
        
        # Test get_folder_contents na parent složce
        logger.info(f"🔍 Testování get_folder_contents na parent složce {parent_folder_id}")
        
        contents = client.get_folder_contents(parent_folder_id)
        
        if contents:
            logger.info(f"✅ Načteno {len(contents)} položek z parent složky")
            
            # Hledání podle názvu
            for item in contents:
                if item.get('type') == 'folder':
                    folder_name = item.get('title', item.get('name', ''))
                    folder_id = item.get('id')
                    
                    if survey_id in folder_name:
                        logger.info(f"🎯 Nalezena složka podle názvu: '{folder_name}' (ID: {folder_id})")
        else:
            logger.warning("⚠️ get_folder_contents nevrátila žádný obsah")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při alternativním testu: {str(e)}")
        return False


if __name__ == "__main__":
    print("🚀 SPOUŠTÍM TEST SURVEY 827822 - FÁZE 1")
    
    # Test hlavní funkcionality
    success1 = test_survey_827822_folder()
    
    # Test alternativních metod
    success2 = test_alternative_folder_discovery()
    
    if success1 and success2:
        print("\n🎉 FÁZE 1 ÚSPĚŠNĚ DOKONČENA!")
        print("✅ Složka 827822 nalezena a analyzována")
        print("✅ Grafy spočítány a vypsány")
        print("🚀 Připraveno pro fázi 2: Konfigurace metadata a HTML export")
    else:
        print("\n❌ FÁZE 1 SELHALA!")
        print("💡 Zkontrolujte konfiguraci a připojení k Datawrapper")
