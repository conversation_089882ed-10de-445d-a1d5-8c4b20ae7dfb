#!/usr/bin/env python3
"""
Test opravy share_url

Testuje opravenou get_charts_in_folder a unpublish workflow.
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_get_charts_with_share_url():
    """Test načtení grafů s share_url"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST NAČTENÍ GRAFŮ S SHARE_URL")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        
        client = DatawrapperExportClient()
        folder_id = "329765"  # Survey 827822
        
        logger.info(f"Načítám grafy ze s<PERSON> {folder_id}")
        charts = client.get_charts_in_folder(folder_id)
        
        if not charts:
            logger.error("❌ Žádné grafy nenačteny")
            return False
        
        logger.info(f"✅ Načteno {len(charts)} grafů")
        
        # Kontrola share_url
        charts_with_url = 0
        charts_without_url = 0
        
        for i, chart in enumerate(charts[:10], 1):  # Prvních 10
            if chart.share_url:
                charts_with_url += 1
                logger.info(f"   {i:2d}. ✅ {chart.id}: {chart.title[:50]}... → {chart.share_url}")
            else:
                charts_without_url += 1
                logger.warning(f"   {i:2d}. ❌ {chart.id}: {chart.title[:50]}... → CHYBÍ URL")
        
        if len(charts) > 10:
            logger.info(f"   ... a dalších {len(charts) - 10} grafů")
        
        logger.info(f"\n📊 Statistiky:")
        logger.info(f"   ✅ S share_url: {charts_with_url}")
        logger.info(f"   ❌ Bez share_url: {charts_without_url}")
        
        # Úspěch pokud většina má URL
        success_rate = charts_with_url / len(charts[:10]) if len(charts) > 0 else 0
        
        if success_rate >= 0.8:  # 80% úspěšnost
            logger.info(f"✅ Test úspěšný: {success_rate*100:.1f}% grafů má share_url")
            return True
        else:
            logger.error(f"❌ Test selhal: pouze {success_rate*100:.1f}% grafů má share_url")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu: {str(e)}")
        return False


def test_unpublish_404_handling():
    """Test zpracování 404 při unpublish"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST ZPRACOVÁNÍ 404 PŘI UNPUBLISH")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        
        client = DatawrapperExportClient()
        
        # Test s neexistujícím grafem
        fake_chart_id = "NEEXISTUJE123"
        
        logger.info(f"Testuji unpublish neexistujícího grafu: {fake_chart_id}")
        
        result = client.unpublish_chart(fake_chart_id)
        
        if result:
            logger.info("✅ Unpublish vrátil True pro 404 (správné chování)")
            return True
        else:
            logger.error("❌ Unpublish vrátil False pro 404")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu unpublish: {str(e)}")
        return False


def test_validation_with_share_url():
    """Test validace s načtenými share_url"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST VALIDACE S NAČTENÝMI SHARE_URL")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient, HTMLGenerator
        
        client = DatawrapperExportClient()
        html_generator = HTMLGenerator()
        
        folder_id = "329765"  # Survey 827822
        
        # Načtení grafů s opravenými share_url
        charts = client.get_charts_in_folder(folder_id)
        
        if not charts:
            logger.error("❌ Žádné grafy nenačteny")
            return False
        
        logger.info(f"Načteno {len(charts)} grafů")
        
        # Validace
        validation_result = html_generator.validate_charts_for_export(charts)
        
        logger.info(f"📊 Výsledky validace:")
        logger.info(f"   📊 Celkem grafů: {validation_result['total_charts']}")
        logger.info(f"   ✅ Platných: {validation_result['valid_charts']}")
        logger.info(f"   ❌ Neplatných: {validation_result['invalid_charts']}")
        logger.info(f"   🎯 Validace prošla: {validation_result['validation_passed']}")
        
        # Zobrazení problémů
        if validation_result['invalid_chart_details']:
            logger.info(f"\n❌ Problémy s grafy:")
            for invalid in validation_result['invalid_chart_details'][:5]:  # Prvních 5
                chart = invalid['chart']
                issues = invalid['issues']
                logger.info(f"   - {chart.id}: {', '.join(issues)}")
        
        # Úspěch pokud je validace lepší než před opravou
        valid_rate = validation_result['valid_charts'] / validation_result['total_charts']
        
        if valid_rate >= 0.5:  # Alespoň 50% platných
            logger.info(f"✅ Validace zlepšena: {valid_rate*100:.1f}% platných grafů")
            return True
        else:
            logger.error(f"❌ Validace stále špatná: pouze {valid_rate*100:.1f}% platných grafů")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu validace: {str(e)}")
        return False


def test_complete_export_workflow():
    """Test kompletního export workflow s opravami"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST KOMPLETNÍHO EXPORT WORKFLOW")
        logger.info("=" * 60)
        
        from datawrapper_export import ExportManager
        
        export_manager = ExportManager()
        
        # Test připojení
        connection_test = export_manager.test_connection()
        
        if not connection_test['success']:
            logger.error(f"❌ Připojení selhalo: {connection_test.get('error')}")
            return False
        
        logger.info(f"✅ Připojení úspěšné: {connection_test['folders_count']} složek")
        
        # Simulace exportu (bez skutečného spuštění)
        survey_id = "827822"
        
        logger.info(f"Simuluji export pro survey: {survey_id}")
        
        # Test find_survey_folder
        from datawrapper_export import DatawrapperExportClient
        client = DatawrapperExportClient()
        
        folder_id = client.find_survey_folder(survey_id)
        
        if folder_id:
            logger.info(f"✅ Složka nalezena: {folder_id}")
            
            # Test načtení grafů
            charts = client.get_charts_in_folder(folder_id)
            logger.info(f"✅ Načteno {len(charts)} grafů")
            
            # Test validace
            from datawrapper_export import HTMLGenerator
            html_generator = HTMLGenerator()
            validation = html_generator.validate_charts_for_export(charts)
            
            valid_rate = validation['valid_charts'] / validation['total_charts'] if validation['total_charts'] > 0 else 0
            logger.info(f"✅ Validace: {valid_rate*100:.1f}% platných grafů")
            
            if valid_rate >= 0.5:
                logger.info("✅ Workflow test úspěšný")
                return True
            else:
                logger.error("❌ Příliš mnoho neplatných grafů")
                return False
        else:
            logger.error("❌ Složka nenalezena")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu workflow: {str(e)}")
        return False


def main():
    """Spuštění všech testů"""
    
    print("🚀 SPOUŠTÍM TESTY OPRAVY SHARE_URL")
    
    tests = [
        ("Načtení grafů s share_url", test_get_charts_with_share_url),
        ("Zpracování 404 při unpublish", test_unpublish_404_handling),
        ("Validace s share_url", test_validation_with_share_url),
        ("Kompletní workflow", test_complete_export_workflow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Spouštím test: {test_name}")
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ Test '{test_name}' úspěšný")
            else:
                logger.error(f"❌ Test '{test_name}' selhal")
                
        except Exception as e:
            logger.error(f"💥 Test '{test_name}' vyvolal výjimku: {str(e)}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SHRNUTÍ TESTŮ OPRAVY SHARE_URL")
    logger.info("=" * 60)
    
    successful = 0
    for test_name, success in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        logger.info(f"{status}: {test_name}")
        if success:
            successful += 1
    
    logger.info(f"\nÚspěšnost: {successful}/{len(results)} testů")
    
    if successful == len(results):
        print("\n🎉 VŠECHNY TESTY ÚSPĚŠNÉ!")
        print("✅ Share_url se načítají správně")
        print("✅ Unpublish 404 se zpracovává správně")
        print("✅ Validace funguje s share_url")
        print("✅ Kompletní workflow je opravený")
    else:
        print(f"\n⚠️ {len(results) - successful} testů selhalo")
        print("💡 Zkontrolujte logy výše pro detaily")
    
    return successful == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
