#!/usr/bin/env python3
"""
Test opravené konfigurace metadata

Testuje opravenou konfiguraci s blocks strukturou pro Layout options.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from datawrapper_export import (
    DatawrapperExportClient,
    ChartConfigurator,
    ChartInfo
)

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_fixed_metadata_configuration():
    """Test opravené konfigurace metadata pro graf tQ2P3"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST OPRAVENÉ KONFIGURACE METADATA")
        logger.info("=" * 60)
        
        # Inicializace klientů
        export_client = DatawrapperExportClient()
        configurator = ChartConfigurator(export_client)
        
        # Test graf ID
        chart_id = "tQ2P3"
        
        # Vytvoření ChartInfo objektu
        chart_info = ChartInfo(
            id=chart_id,
            title="Test Graf - Metadata Workflow",
            type="d3-bars",
            status="published",
            url="",
            share_url="https://datawrapper.dwcdn.net/tQ2P3/2/"
        )
        
        logger.info(f"Testuji graf: {chart_id}")
        
        # Krok 1: Kontrola aktuálního stavu
        logger.info("\n🔍 KROK 1: Kontrola aktuálního stavu")
        validation_before = configurator.validate_chart_configuration(chart_info)
        
        logger.info("Aktuální stav:")
        logger.info(f"  Platný: {'✅' if validation_before['valid'] else '❌'}")
        
        if not validation_before['valid']:
            logger.info("  Problémy:")
            for issue in validation_before['issues']:
                logger.info(f"    - {issue}")
        
        # Zobrazení aktuálních nastavení
        blocks = validation_before.get('blocks_settings', {})
        logger.info("\n📋 Aktuální blocks nastavení:")
        logger.info(f"  embed: {blocks.get('embed', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-image: {blocks.get('download-image', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-pdf: {blocks.get('download-pdf', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-svg: {blocks.get('download-svg', 'NENÍ NASTAVENO')}")
        logger.info(f"  get-the-data: {blocks.get('get-the-data', 'NENÍ NASTAVENO')}")
        
        sharing = validation_before.get('sharing_settings', {})
        logger.info(f"  social sharing: {sharing.get('enabled', 'NENÍ NASTAVENO')}")
        
        # Krok 2: Aplikace opravené konfigurace
        logger.info("\n⚙️ KROK 2: Aplikace opravené konfigurace")
        logger.info("Konfiguruji graf s opravenou blocks strukturou...")
        
        # Použití force_republish=True pro unpublish → configure → publish workflow
        results = configurator.configure_charts_for_export([chart_info], force_republish=True)
        
        if not results or len(results) == 0:
            logger.error("❌ Konfigurace selhala - žádné výsledky")
            return False
        
        result = results[0]
        
        if result.success:
            logger.info("✅ Konfigurace úspěšná")
            logger.info(f"✅ Nová share URL: {result.new_share_url}")
            
            # Aktualizace chart_info
            chart_info.share_url = result.new_share_url
        else:
            logger.error(f"❌ Konfigurace selhala: {result.error_message}")
            return False
        
        # Krok 3: Ověření nové konfigurace
        logger.info("\n🔍 KROK 3: Ověření nové konfigurace")
        
        # Počkáme na zpracování
        time.sleep(5)
        
        validation_after = configurator.validate_chart_configuration(chart_info)
        
        logger.info("Stav po konfiguraci:")
        logger.info(f"  Platný: {'✅' if validation_after['valid'] else '❌'}")
        
        if not validation_after['valid']:
            logger.info("  Zbývající problémy:")
            for issue in validation_after['issues']:
                logger.info(f"    - {issue}")
        
        # Zobrazení nových nastavení
        blocks_after = validation_after.get('blocks_settings', {})
        logger.info("\n📋 Nová blocks nastavení:")
        logger.info(f"  embed: {blocks_after.get('embed', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-image: {blocks_after.get('download-image', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-pdf: {blocks_after.get('download-pdf', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-svg: {blocks_after.get('download-svg', 'NENÍ NASTAVENO')}")
        logger.info(f"  get-the-data: {blocks_after.get('get-the-data', 'NENÍ NASTAVENO')}")
        
        sharing_after = validation_after.get('sharing_settings', {})
        logger.info(f"  social sharing: {sharing_after.get('enabled', 'NENÍ NASTAVENO')}")
        
        # Krok 4: Porovnání před a po
        logger.info("\n📊 KROK 4: Porovnání změn")
        
        changes = []
        
        # Porovnání blocks
        for key in ['embed', 'download-image', 'download-pdf', 'download-svg', 'get-the-data']:
            before = blocks.get(key, False)
            after = blocks_after.get(key, False)
            
            if before != after:
                status = "✅ OPRAVENO" if after else "❌ STÁLE ŠPATNĚ"
                changes.append(f"  {key}: {before} → {after} ({status})")
        
        # Porovnání sharing
        before_sharing = sharing.get('enabled', False)
        after_sharing = sharing_after.get('enabled', False)
        
        if before_sharing != after_sharing:
            status = "✅ OPRAVENO" if after_sharing else "❌ STÁLE ŠPATNĚ"
            changes.append(f"  social sharing: {before_sharing} → {after_sharing} ({status})")
        
        if changes:
            logger.info("Změny:")
            for change in changes:
                logger.info(change)
        else:
            logger.info("Žádné změny v nastavení")
        
        # Krok 5: Test v prohlížeči
        logger.info("\n🌐 KROK 5: Test v prohlížeči")
        
        print("\n" + "🔗" * 60)
        print("ZKONTROLUJTE GRAF V PROHLÍŽEČI:")
        print(f"URL: {chart_info.share_url}")
        print("Zkontrolujte, že jsou aktivní:")
        print("  ✅ Image download options (PNG, PDF, SVG)")
        print("  ✅ Embed link")
        print("  ✅ Social media share buttons")
        print("🔗" * 60)
        
        input("\n👀 Otevřete URL a zkontrolujte Layout options...")
        
        # Výsledek
        if validation_after['valid']:
            logger.info("\n🎉 TEST ÚSPĚŠNÝ!")
            logger.info("Graf je správně nakonfigurován pro export")
            return True
        else:
            logger.error("\n💥 TEST SELHAL!")
            logger.error("Graf stále není správně nakonfigurován")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_fixed_metadata_configuration()
    
    if success:
        print("\n✅ Test opravené konfigurace úspěšný!")
    else:
        print("\n❌ Test opravené konfigurace selhal!")
    
    sys.exit(0 if success else 1)
