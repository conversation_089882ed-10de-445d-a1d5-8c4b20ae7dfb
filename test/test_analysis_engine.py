#!/usr/bin/env python3
"""
Test Analysis Engine

Testuje nový Analysis Engine modul integrovaný z nedokončeného projektu.
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_metadata_loader():
    """Test načítání metadata z nedokončeného projektu"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST METADATA LOADER")
        logger.info("=" * 60)
        
        from analysis_engine import MetadataLoader
        
        # Inicializace s cestou k nedokončenému projektu
        metadata_dir = Path(__file__).parent.parent / "tmp" / "limesurvey-structure-metadata"
        loader = MetadataLoader(metadata_dir)
        
        # Načtení všech metadata
        success = loader.load_all_metadata()
        
        if success:
            logger.info("✅ Metadata úspěšně načtena")
            
            # Kontrola načtených dat
            logger.info(f"📊 Typy otázek: {len(loader.question_types)}")
            logger.info(f"📈 Typy analýz: {len(loader.analysis_types)}")
            logger.info(f"📉 Typy vizualizací: {len(loader.visualization_types)}")
            
            # Ukázka některých typů
            logger.info("\n🔍 Ukázka typů otázek:")
            for i, (qid, qtype) in enumerate(list(loader.question_types.items())[:5], 1):
                logger.info(f"   {i}. {qid}: {qtype.name}")
            
            logger.info("\n📊 Ukázka analýz:")
            for i, (aid, atype) in enumerate(list(loader.analysis_types.items())[:5], 1):
                logger.info(f"   {i}. {aid}: {atype.name} (priorita: {atype.priority})")
            
            # Test mapování
            if 'L' in loader.question_types:
                analyses = loader.get_analyses_for_question('L')
                logger.info(f"\n🔗 Analýzy pro typ L: {len(analyses)}")
                for analysis in analyses[:3]:
                    logger.info(f"   - {analysis.name}")
            
            return True
        else:
            logger.error("❌ Načítání metadata selhalo")
            return False
            
    except Exception as e:
        logger.error(f"❌ Chyba při testu metadata loader: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_question_analyzer():
    """Test analyzéru otázek"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST QUESTION ANALYZER")
        logger.info("=" * 60)
        
        from analysis_engine import MetadataLoader, QuestionAnalyzer
        from analysis_engine.question_analyzer import LimeSurveyQuestion
        
        # Načtení metadata
        metadata_dir = Path(__file__).parent.parent / "tmp" / "limesurvey-structure-metadata"
        loader = MetadataLoader(metadata_dir)
        loader.load_all_metadata()
        
        # Inicializace analyzéru
        analyzer = QuestionAnalyzer(loader)
        
        # Testovací otázky
        test_questions = [
            LimeSurveyQuestion(
                qid="Q1",
                question_code="gender",
                question_text="Jaké je vaše pohlaví?",
                question_type="L",
                answer_options=[{"code": "M", "text": "Muž"}, {"code": "F", "text": "Žena"}]
            ),
            LimeSurveyQuestion(
                qid="Q2", 
                question_code="satisfaction",
                question_text="Jak jste spokojeni s naší službou?",
                question_type="5"
            ),
            LimeSurveyQuestion(
                qid="Q3",
                question_code="comments",
                question_text="Máte nějaké komentáře nebo návrhy?",
                question_type="T"
            ),
            LimeSurveyQuestion(
                qid="Q4",
                question_code="unknown_type",
                question_text="Neznámý typ otázky",
                question_type="UNKNOWN"
            )
        ]
        
        # Analýza otázek
        analyzed_questions = analyzer.analyze_survey_questions(test_questions)
        
        logger.info(f"📊 Analyzováno {len(analyzed_questions)} otázek")
        
        # Detailní výsledky
        for i, analyzed in enumerate(analyzed_questions, 1):
            logger.info(f"\n🔍 Otázka {i}: {analyzed.original_question.question_code}")
            logger.info(f"   Typ: {analyzed.matched_type.name if analyzed.matched_type else 'Nerozpoznáno'}")
            logger.info(f"   Spolehlivost: {analyzed.confidence:.1%}")
            logger.info(f"   Doporučené analýzy: {len(analyzed.recommended_analyses)}")
            logger.info(f"   Poznámky: {analyzed.analysis_notes}")
        
        # Souhrn analýzy
        summary = analyzer.get_analysis_summary(analyzed_questions)
        logger.info(f"\n📈 SOUHRN ANALÝZY:")
        logger.info(f"   Celkem otázek: {summary['total_questions']}")
        logger.info(f"   Rozpoznáno: {summary['recognized_questions']}")
        logger.info(f"   Úspěšnost: {summary['recognition_rate']:.1%}")
        logger.info(f"   Doporučené analýzy: {len(summary['recommended_analyses'])}")
        
        # Úspěch pokud rozpoznáme alespoň 75% otázek
        success = summary['recognition_rate'] >= 0.75
        
        if success:
            logger.info("✅ Question Analyzer funguje správně!")
        else:
            logger.error("❌ Question Analyzer má nízkou úspěšnost rozpoznávání")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu question analyzer: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_analysis_recommender():
    """Test doporučovače analýz"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST ANALYSIS RECOMMENDER")
        logger.info("=" * 60)
        
        from analysis_engine import MetadataLoader, QuestionAnalyzer, AnalysisRecommender
        from analysis_engine.question_analyzer import LimeSurveyQuestion
        
        # Příprava dat
        metadata_dir = Path(__file__).parent.parent / "tmp" / "limesurvey-structure-metadata"
        loader = MetadataLoader(metadata_dir)
        loader.load_all_metadata()
        
        analyzer = QuestionAnalyzer(loader)
        recommender = AnalysisRecommender(loader)
        
        # Více testovacích otázek pro lepší doporučení
        test_questions = [
            LimeSurveyQuestion(qid="Q1", question_code="gender", question_text="Pohlaví", question_type="L"),
            LimeSurveyQuestion(qid="Q2", question_code="age", question_text="Věk", question_type="N"),
            LimeSurveyQuestion(qid="Q3", question_code="satisfaction1", question_text="Spokojenost 1", question_type="5"),
            LimeSurveyQuestion(qid="Q4", question_code="satisfaction2", question_text="Spokojenost 2", question_type="5"),
            LimeSurveyQuestion(qid="Q5", question_code="comments", question_text="Komentáře", question_type="T"),
            LimeSurveyQuestion(qid="Q6", question_code="feedback", question_text="Zpětná vazba", question_type="T"),
        ]
        
        # Analýza otázek
        analyzed_questions = analyzer.analyze_survey_questions(test_questions)
        
        # Doporučení analýz
        analysis_plan = recommender.recommend_analyses(analyzed_questions)
        
        logger.info(f"📊 PLÁN ANALÝZ:")
        logger.info(f"   Analýzy otázek: {len(analysis_plan.question_level_analyses)}")
        logger.info(f"   Analýzy sekcí: {len(analysis_plan.section_level_analyses)}")
        logger.info(f"   Cross-question analýzy: {len(analysis_plan.cross_question_analyses)}")
        logger.info(f"   Odhadovaný čas: {analysis_plan.total_estimated_time} minut")
        logger.info(f"   Složitost: {analysis_plan.complexity_score:.1f}/10")
        
        # Detail doporučení
        logger.info(f"\n🔍 TOP 3 DOPORUČENÉ ANALÝZY:")
        all_analyses = (analysis_plan.question_level_analyses + 
                       analysis_plan.section_level_analyses + 
                       analysis_plan.cross_question_analyses)
        
        top_analyses = sorted(all_analyses, key=lambda a: a.priority_score, reverse=True)[:3]
        
        for i, analysis in enumerate(top_analyses, 1):
            logger.info(f"   {i}. {analysis.analysis_type.name}")
            logger.info(f"      Priorita: {analysis.priority_score:.2f}")
            logger.info(f"      Otázky: {len(analysis.applicable_questions)}")
            logger.info(f"      Složitost: {analysis.estimated_complexity}/5")
            logger.info(f"      Zdůvodnění: {analysis.reasoning}")
        
        # Úspěch pokud máme alespoň nějaká doporučení
        success = len(all_analyses) > 0
        
        if success:
            logger.info("✅ Analysis Recommender funguje správně!")
        else:
            logger.error("❌ Analysis Recommender nevygeneroval žádná doporučení")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu analysis recommender: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_visualization_mapper():
    """Test mapperu vizualizací"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST VISUALIZATION MAPPER")
        logger.info("=" * 60)
        
        from analysis_engine import MetadataLoader, QuestionAnalyzer, AnalysisRecommender, VisualizationMapper
        from analysis_engine.question_analyzer import LimeSurveyQuestion
        
        # Příprava dat
        metadata_dir = Path(__file__).parent.parent / "tmp" / "limesurvey-structure-metadata"
        loader = MetadataLoader(metadata_dir)
        loader.load_all_metadata()
        
        analyzer = QuestionAnalyzer(loader)
        recommender = AnalysisRecommender(loader)
        mapper = VisualizationMapper(loader)
        
        # Testovací otázky
        test_questions = [
            LimeSurveyQuestion(qid="Q1", question_code="satisfaction", question_text="Spokojenost", question_type="5"),
            LimeSurveyQuestion(qid="Q2", question_code="comments", question_text="Komentáře", question_type="T"),
        ]
        
        # Celý pipeline
        analyzed_questions = analyzer.analyze_survey_questions(test_questions)
        analysis_plan = recommender.recommend_analyses(analyzed_questions)
        
        # Mapování na vizualizace
        all_analyses = (analysis_plan.question_level_analyses + 
                       analysis_plan.section_level_analyses + 
                       analysis_plan.cross_question_analyses)
        
        chart_plan = mapper.map_analyses_to_visualizations(all_analyses)
        
        logger.info(f"📊 PLÁN GRAFŮ:")
        logger.info(f"   Primární vizualizace: {len(chart_plan.primary_visualizations)}")
        logger.info(f"   Alternativní vizualizace: {len(chart_plan.alternative_visualizations)}")
        logger.info(f"   Nepodporované analýzy: {len(chart_plan.unsupported_analyses)}")
        logger.info(f"   Odhad grafů: {chart_plan.total_charts_estimate}")
        logger.info(f"   Datawrapper kompatibilita: {chart_plan.datawrapper_compatibility:.1%}")
        
        # Detail vizualizací
        logger.info(f"\n🎨 TOP VIZUALIZACE:")
        for i, viz in enumerate(chart_plan.primary_visualizations[:3], 1):
            logger.info(f"   {i}. {viz.visualization_type.name}")
            logger.info(f"      Typ: {viz.datawrapper_type or 'Externí generátor'}")
            logger.info(f"      Priorita: {viz.priority_score:.2f}")
            logger.info(f"      Požadavky: {', '.join(viz.data_requirements[:2])}")
            
            # Test Datawrapper konfigurace
            if viz.datawrapper_type:
                config = mapper.get_datawrapper_chart_config(viz)
                logger.info(f"      Datawrapper config: {config['type'] if config else 'N/A'}")
            else:
                external_info = mapper.get_external_generator_info(viz)
                logger.info(f"      Externí generátor: {external_info['generator'] if external_info else 'N/A'}")
        
        # Úspěch pokud máme alespoň nějaké vizualizace
        success = len(chart_plan.primary_visualizations) > 0
        
        if success:
            logger.info("✅ Visualization Mapper funguje správně!")
        else:
            logger.error("❌ Visualization Mapper nevygeneroval žádné vizualizace")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu visualization mapper: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_integration_with_existing_app():
    """Test integrace s existující aplikací"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST INTEGRACE S EXISTUJÍCÍ APLIKACÍ")
        logger.info("=" * 60)
        
        # Test importu existujících modulů
        try:
            from datawrapper_export import DatawrapperExportClient
            logger.info("✅ Import DatawrapperExportClient úspěšný")
        except ImportError as e:
            logger.error(f"❌ Import DatawrapperExportClient selhal: {e}")
            return False
        
        # Test importu nových modulů
        try:
            from analysis_engine import MetadataLoader, QuestionAnalyzer
            logger.info("✅ Import Analysis Engine úspěšný")
        except ImportError as e:
            logger.error(f"❌ Import Analysis Engine selhal: {e}")
            return False
        
        # Test kompatibility
        logger.info("🔗 Testování kompatibility...")
        
        # Simulace integrace
        metadata_dir = Path(__file__).parent.parent / "tmp" / "limesurvey-structure-metadata"
        if metadata_dir.exists():
            loader = MetadataLoader(metadata_dir)
            success = loader.load_all_metadata()
            
            if success:
                logger.info("✅ Analysis Engine je připraven k integraci")
                logger.info("💡 Doporučení: Přidat Analysis Engine do Menu 13")
                return True
            else:
                logger.error("❌ Analysis Engine není funkční")
                return False
        else:
            logger.warning("⚠️ Metadata adresář neexistuje - Analysis Engine nebude funkční")
            logger.info("💡 Doporučení: Zkopírovat metadata z nedokončeného projektu")
            return False
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu integrace: {str(e)}")
        return False


def main():
    """Spuštění všech testů Analysis Engine"""
    
    print("🚀 SPOUŠTÍM TESTY ANALYSIS ENGINE")
    
    tests = [
        ("Metadata Loader", test_metadata_loader),
        ("Question Analyzer", test_question_analyzer),
        ("Analysis Recommender", test_analysis_recommender),
        ("Visualization Mapper", test_visualization_mapper),
        ("Integrace s existující aplikací", test_integration_with_existing_app)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Spouštím test: {test_name}")
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ Test '{test_name}' úspěšný")
            else:
                logger.error(f"❌ Test '{test_name}' selhal")
                
        except Exception as e:
            logger.error(f"💥 Test '{test_name}' vyvolal výjimku: {str(e)}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SHRNUTÍ TESTŮ ANALYSIS ENGINE")
    logger.info("=" * 60)
    
    successful = 0
    for test_name, success in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        logger.info(f"{status}: {test_name}")
        if success:
            successful += 1
    
    logger.info(f"\nÚspěšnost: {successful}/{len(results)} testů")
    
    if successful == len(results):
        print("\n🎉 VŠECHNY TESTY ANALYSIS ENGINE ÚSPĚŠNÉ!")
        print("✅ Metadata loader funguje")
        print("✅ Question analyzer rozpoznává typy otázek")
        print("✅ Analysis recommender doporučuje analýzy")
        print("✅ Visualization mapper mapuje na grafy")
        print("✅ Integrace s existující aplikací je připravena")
        print("🚀 Analysis Engine je připraven k produkčnímu použití!")
    else:
        print(f"\n⚠️ {len(results) - successful} testů selhalo")
        print("💡 Zkontrolujte logy výše pro detaily")
    
    return successful == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
