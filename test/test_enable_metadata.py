#!/usr/bin/env python3
"""
Test zapnutí správných metadata

Vezme existující graf a zapne správná metadata pro Layout options.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from datawrapper_export import DatawrapperExportClient

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_enable_metadata():
    """Test zapnutí správných metadata"""
    
    # Zadejte ID grafu, který chcete upravit
    chart_id = input("Zadejte Chart ID (nebo Enter pro vytvoření nového): ").strip()
    
    if not chart_id:
        # Vytvoříme nový testovací graf
        chart_id = create_test_chart()
        if not chart_id:
            return False
    
    try:
        logger.info("=" * 60)
        logger.info(f"TEST ZAPNUTÍ METADATA PRO GRAF: {chart_id}")
        logger.info("=" * 60)
        
        # Inicializace klienta
        client = DatawrapperExportClient()
        
        # Krok 1: Kontrola aktuálního stavu
        logger.info("\n🔍 KROK 1: Kontrola aktuálního stavu")
        
        response = client._make_request('GET', f'charts/{chart_id}')
        if response.status_code != 200:
            logger.error(f"❌ Graf {chart_id} nenalezen")
            return False
        
        full_data = response.json()
        metadata = full_data.get('metadata', {})
        
        # Zobrazení aktuálních nastavení
        publish_meta = metadata.get('publish', {})
        blocks = publish_meta.get('blocks', {})
        
        logger.info("📋 Aktuální BLOCKS nastavení:")
        logger.info(f"  embed: {blocks.get('embed', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-image: {blocks.get('download-image', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-pdf: {blocks.get('download-pdf', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-svg: {blocks.get('download-svg', 'NENÍ NASTAVENO')}")
        logger.info(f"  get-the-data: {blocks.get('get-the-data', 'NENÍ NASTAVENO')}")
        
        visualize_meta = metadata.get('visualize', {})
        sharing = visualize_meta.get('sharing', {})
        
        logger.info("📋 Aktuální SHARING nastavení:")
        logger.info(f"  enabled: {sharing.get('enabled', 'NENÍ NASTAVENO')}")
        logger.info(f"  auto: {sharing.get('auto', 'NENÍ NASTAVENO')}")
        
        # Krok 2: Příprava nových metadata
        logger.info("\n⚙️ KROK 2: Příprava nových metadata")
        
        # Správná metadata pro Layout options
        new_metadata = {
            'publish': {
                'locale': 'cs-CZ',
                # BLOCKS - Layout options v UI
                'blocks': {
                    'embed': True,                   # Embed link
                    'download-image': True,          # Image download options
                    'download-pdf': True,            # PDF download
                    'download-svg': True,            # SVG download
                    'get-the-data': True,            # Data download
                    'logo': {'enabled': False}       # Logo (obvykle vypnuto)
                },
                # Chart footer (pro kompatibilitu)
                'chart-footer': {
                    'data-download': True,
                    'image-download': {
                        'png': True,
                        'pdf': True,
                        'svg': True
                    },
                    'embed-link': True,
                    'social-sharing': True
                }
            },
            # Social sharing v visualize sekci
            'visualize': {
                'sharing': {
                    'enabled': True,                 # Zapnout social sharing
                    'auto': False                    # Použít site URL místo chart URL
                }
            }
        }
        
        logger.info("📝 Nová metadata připravena:")
        logger.info("  ✅ blocks.embed = True")
        logger.info("  ✅ blocks.download-image = True")
        logger.info("  ✅ blocks.download-pdf = True")
        logger.info("  ✅ blocks.download-svg = True")
        logger.info("  ✅ visualize.sharing.enabled = True")
        logger.info("  ✅ visualize.sharing.auto = False (site URL)")
        
        # Krok 3: Unpublish grafu
        logger.info("\n📤 KROK 3: Unpublish grafu")
        
        unpublish_success = client.unpublish_chart(chart_id)
        if unpublish_success:
            logger.info("✅ Graf unpublished")
        else:
            logger.warning("⚠️ Unpublish selhal, pokračujem")
        
        time.sleep(2)
        
        # Krok 4: Aktualizace metadata
        logger.info("\n⚙️ KROK 4: Aktualizace metadata")
        
        # Sloučení s existujícími metadaty
        def deep_merge(target, source):
            for key, value in source.items():
                if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                    deep_merge(target[key], value)
                else:
                    target[key] = value
        
        # Vytvoříme kopii existujících metadata
        updated_metadata = metadata.copy()
        
        # Sloučíme s novými nastaveními
        deep_merge(updated_metadata, new_metadata)
        
        # Aktualizace metadata
        metadata_success = client.update_chart_metadata(chart_id, updated_metadata)
        
        if not metadata_success:
            logger.error("❌ Nepodařilo se aktualizovat metadata")
            return False
        
        logger.info("✅ Metadata aktualizována")
        time.sleep(3)
        
        # Krok 5: Republish grafu
        logger.info("\n📤 KROK 5: Republish grafu")
        
        share_url = client.publish_chart(chart_id)
        
        if not share_url:
            logger.error("❌ Nepodařilo se publikovat graf")
            return False
        
        logger.info(f"✅ Graf publikován: {share_url}")
        
        # Krok 6: Ověření výsledku
        logger.info("\n🔍 KROK 6: Ověření výsledku")
        
        time.sleep(5)  # Počkáme na zpracování
        
        # Znovu načteme metadata
        response = client._make_request('GET', f'charts/{chart_id}')
        updated_full_data = response.json()
        updated_metadata = updated_full_data.get('metadata', {})
        
        # Kontrola nových nastavení
        updated_publish = updated_metadata.get('publish', {})
        updated_blocks = updated_publish.get('blocks', {})
        
        logger.info("📋 NOVÁ BLOCKS nastavení:")
        logger.info(f"  embed: {updated_blocks.get('embed', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-image: {updated_blocks.get('download-image', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-pdf: {updated_blocks.get('download-pdf', 'NENÍ NASTAVENO')}")
        logger.info(f"  download-svg: {updated_blocks.get('download-svg', 'NENÍ NASTAVENO')}")
        logger.info(f"  get-the-data: {updated_blocks.get('get-the-data', 'NENÍ NASTAVENO')}")
        
        updated_visualize = updated_metadata.get('visualize', {})
        updated_sharing = updated_visualize.get('sharing', {})
        
        logger.info("📋 NOVÁ SHARING nastavení:")
        logger.info(f"  enabled: {updated_sharing.get('enabled', 'NENÍ NASTAVENO')}")
        logger.info(f"  auto: {updated_sharing.get('auto', 'NENÍ NASTAVENO')}")
        
        # Výsledek
        print("\n" + "🔗" * 60)
        print("GRAF S AKTUALIZOVANÝMI METADATA:")
        print(f"Chart ID: {chart_id}")
        print(f"URL: {share_url}")
        print("🔗" * 60)
        
        print("\n📋 PROSÍM ZKONTROLUJTE:")
        print("1. Otevřete graf v Datawrapper editoru")
        print("2. Přejděte na Layout tab")
        print("3. Zkontrolujte sekci 'Share buttons'")
        print("4. Otevřete graf v prohlížeči a zkontrolujte funkce")
        
        user_report = input("\n👀 Co vidíte v Layout → Share buttons? ")
        logger.info(f"USER REPORT: {user_report}")
        
        user_browser = input("\n👀 Co vidíte na grafu v prohlížeči? ")
        logger.info(f"USER BROWSER REPORT: {user_browser}")
        
        logger.info("\n🎉 TEST DOKONČEN!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def create_test_chart():
    """Vytvoří nový testovací graf"""
    try:
        from datawrapper_client import DatawrapperClient
        
        logger.info("📊 Vytvářím nový testovací graf...")
        
        client = DatawrapperClient()
        
        # Vytvoření grafu
        chart_data = client.create_chart(
            title="Test Graf - Metadata Configuration",
            chart_type="d3-bars",
            description="Test grafu pro metadata konfiguraci",
            data_source="LimWrapp Test",
            byline="Test Data"
        )
        
        if not chart_data or 'id' not in chart_data:
            logger.error("❌ Nepodařilo se vytvořit graf")
            return None
        
        chart_id = chart_data['id']
        logger.info(f"✅ Graf vytvořen s ID: {chart_id}")
        
        # Přidání dat
        csv_data = """Odpověď,Počet respondentů
Velmi spokojen,42
Spokojen,35
Neutrální,15
Nespokojen,6
Velmi nespokojen,2"""
        
        client.update_chart_data(chart_id, csv_data)
        logger.info("✅ Data přidána")
        
        # Základní metadata
        basic_metadata = {
            "data": {
                "column-format": {
                    "Odpověď": {"type": "text", "input-format": "auto"},
                    "Počet respondentů": {"type": "number", "input-format": "auto"}
                }
            }
        }
        
        client.update_chart_metadata(chart_id, basic_metadata)
        time.sleep(2)
        
        # Publikování
        share_url = client.publish_chart(chart_id)
        logger.info(f"✅ Graf publikován: {share_url}")
        
        return chart_id
        
    except Exception as e:
        logger.error(f"❌ Chyba při vytváření grafu: {str(e)}")
        return None


if __name__ == "__main__":
    success = test_enable_metadata()
    
    if success:
        print("\n✅ Test zapnutí metadata úspěšný!")
    else:
        print("\n❌ Test zapnutí metadata selhal!")
