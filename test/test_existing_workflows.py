#!/usr/bin/env python3
"""
Test existujících workflow pro Datawrapper

Testuje Menu 9 (PNG download) a Menu 12 (HTML export).
"""

import os
import sys
import time
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_png_download_workflow():
    """Test Menu 9 - PNG download workflow"""
    logger.info("=" * 60)
    logger.info("TEST MENU 9 - PNG DOWNLOAD WORKFLOW")
    logger.info("=" * 60)
    
    try:
        # Import funkcí z main.py
        from main import download_png_charts
        from config_loader import load_config
        
        # Test survey ID
        test_survey_id = "827822"
        
        logger.info(f"Testuji PNG download pro survey: {test_survey_id}")
        
        # Kontrola konfigurace
        config = load_config()
        charts_root = config.get('charts_root', 'charts')
        
        logger.info(f"Charts root directory: {charts_root}")
        logger.info(f"Target directory: {charts_root}/{test_survey_id}")
        
        # Kontrola Datawrapper konfigurace
        team_id = config.get('DATAWRAPPER_TEAM_ID')
        parent_folder_id = config.get('DATAWRAPPER_LIMESURVEY_FOLDER_ID')
        
        logger.info(f"Datawrapper Team ID: {team_id}")
        logger.info(f"Parent Folder ID: {parent_folder_id}")
        
        if not team_id or not parent_folder_id:
            logger.error("❌ Chybí Datawrapper konfigurace v .env")
            return False
        
        print("\n📋 SIMULACE MENU 9:")
        print("Funkce download_png_charts() je připravena")
        print(f"Survey ID: {test_survey_id}")
        print(f"Cílový adresář: {charts_root}/{test_survey_id}")
        
        # Simulace uživatelského vstupu
        print("\n⚠️ POZNÁMKA: Tato funkce vyžaduje interaktivní vstup")
        print("Pro skutečný test spusťte Menu 9 v hlavní aplikaci")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu PNG download: {str(e)}")
        return False


def test_html_export_workflow():
    """Test Menu 12 - HTML export workflow"""
    logger.info("\n" + "=" * 60)
    logger.info("TEST MENU 12 - HTML EXPORT WORKFLOW")
    logger.info("=" * 60)
    
    try:
        # Import Datawrapper Export modulů
        from datawrapper_export import (
            ExportManager,
            DatawrapperExportClient,
            ChartCollector
        )
        
        # Test survey ID
        test_survey_id = "827822"
        
        logger.info(f"Testuji HTML export pro survey: {test_survey_id}")
        
        # Krok 1: Inicializace klientů
        logger.info("\n🔧 KROK 1: Inicializace klientů")
        
        export_client = DatawrapperExportClient()
        logger.info("✅ DatawrapperExportClient inicializován")
        
        chart_collector = ChartCollector(export_client)
        logger.info("✅ ChartCollector inicializován")
        
        export_manager = ExportManager()
        logger.info("✅ ExportManager inicializován")
        
        # Krok 2: Zjištění složky
        logger.info("\n🔍 KROK 2: Zjištění Datawrapper složky")
        
        folder_id = export_client.find_survey_folder(test_survey_id)
        
        if folder_id:
            logger.info(f"✅ Nalezena složka: {folder_id}")
        else:
            logger.warning(f"⚠️ Složka pro survey {test_survey_id} nebyla nalezena")
            logger.info("💡 Zkuste jiné survey ID nebo vytvořte složku v Datawrapper")
            return False
        
        # Krok 3: Sběr grafů
        logger.info("\n📊 KROK 3: Sběr grafů ze složky")
        
        charts = chart_collector.collect_charts_for_survey(test_survey_id)
        
        if charts:
            logger.info(f"✅ Nalezeno {len(charts)} grafů")
            
            # Zobrazení prvních 3 grafů
            for i, chart in enumerate(charts[:3]):
                logger.info(f"  {i+1}. {chart.title} (ID: {chart.id}, Status: {chart.status})")
            
            if len(charts) > 3:
                logger.info(f"  ... a dalších {len(charts) - 3} grafů")
        else:
            logger.warning("⚠️ Ve složce nebyly nalezeny žádné grafy")
            return False
        
        # Krok 4: Test export workflow (bez skutečného exportu)
        logger.info("\n🎨 KROK 4: Test export workflow")
        
        output_dir = "test_output"
        os.makedirs(output_dir, exist_ok=True)
        
        logger.info(f"Výstupní adresář: {output_dir}")
        logger.info("Survey název: Test Survey")
        logger.info("Jazykový filtr: None (všechny jazyky)")
        logger.info("Force reconfigure: False")
        
        print("\n📋 SIMULACE MENU 12:")
        print("ExportManager.export_survey_charts() je připraven")
        print(f"Survey ID: {test_survey_id}")
        print(f"Nalezeno grafů: {len(charts)}")
        print(f"Výstupní adresář: {output_dir}")
        
        # Simulace exportu (bez skutečného spuštění)
        print("\n⚠️ POZNÁMKA: Pro skutečný export spusťte Menu 12 v hlavní aplikaci")
        print("Workflow zahrnuje:")
        print("  1. ✅ Sběr grafů ze složky")
        print("  2. ⚙️ Konfigurace metadata")
        print("  3. 🔄 Republish grafů")
        print("  4. 🎨 Generování HTML")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu HTML export: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_folder_discovery():
    """Test zjištění složky podle survey ID"""
    logger.info("\n" + "=" * 60)
    logger.info("TEST ZJIŠTĚNÍ SLOŽKY PODLE SURVEY ID")
    logger.info("=" * 60)
    
    try:
        from datawrapper_export import DatawrapperExportClient
        
        client = DatawrapperExportClient()
        
        # Test různých survey ID
        test_surveys = ["827822", "123456", "nonexistent"]
        
        for survey_id in test_surveys:
            logger.info(f"\n🔍 Hledám složku pro survey: {survey_id}")
            
            folder_id = client.find_survey_folder(survey_id)
            
            if folder_id:
                logger.info(f"✅ Nalezena složka: {folder_id}")
                
                # Zkusíme načíst grafy
                charts = client.get_charts_in_folder(folder_id)
                logger.info(f"📊 Grafů ve složce: {len(charts)}")
                
            else:
                logger.info(f"❌ Složka pro survey {survey_id} nebyla nalezena")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu zjištění složky: {str(e)}")
        return False


def main():
    """Spuštění všech testů"""
    logger.info("🚀 SPOUŠTÍM TESTY EXISTUJÍCÍCH WORKFLOW")
    
    tests = [
        ("Zjištění složky podle Survey ID", test_folder_discovery),
        ("PNG Download Workflow (Menu 9)", test_png_download_workflow),
        ("HTML Export Workflow (Menu 12)", test_html_export_workflow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Spouštím test: {test_name}")
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ Test '{test_name}' úspěšný")
            else:
                logger.error(f"❌ Test '{test_name}' selhal")
                
        except Exception as e:
            logger.error(f"💥 Test '{test_name}' vyvolal výjimku: {str(e)}")
            results.append((test_name, False))
    
    # Shrnutí výsledků
    logger.info("\n" + "=" * 60)
    logger.info("SHRNUTÍ TESTŮ")
    logger.info("=" * 60)
    
    successful = 0
    for test_name, success in results:
        status = "✅ ÚSPĚCH" if success else "❌ SELHÁNÍ"
        logger.info(f"{status}: {test_name}")
        if success:
            successful += 1
    
    logger.info(f"\nÚspěšnost: {successful}/{len(results)} testů")
    
    if successful == len(results):
        print("\n🎉 VŠECHNY TESTY ÚSPĚŠNÉ!")
        print("📋 Existující workflow jsou připraveny k použití:")
        print("  • Menu 9: PNG download z Datawrapper složky")
        print("  • Menu 12: HTML export s metadata konfigurací")
    else:
        print(f"\n⚠️ {len(results) - successful} testů selhalo")
        print("💡 Zkontrolujte konfiguraci a závislosti")


if __name__ == "__main__":
    main()
