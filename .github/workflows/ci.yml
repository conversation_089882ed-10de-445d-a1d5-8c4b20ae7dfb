name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install black flake8 safety
    - name: Run tests
      run: |
        python -m pytest
    - name: Lint code
      run: |
        flake8 src/ tests/
    - name: Format code
      run: |
        black --check src/ tests/
    - name: Check dependencies
      run: |
        safety check -r requirements.txt
    - name: Build package
      run: |
        python setup.py sdist bdist_wheel
