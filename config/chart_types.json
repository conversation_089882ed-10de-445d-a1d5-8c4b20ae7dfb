{"questions": {"Q1": {"question_text": "Test otázka pro persistent settings", "data_type": "text", "hidden": false, "charts": [{"chart_type": "wordcloud", "generator": "internal_wordcloud", "parameters": {"max_words": 200, "color_scheme": "default"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "table", "generator": "internal_table", "parameters": {"max_categories": 10}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "wordcloud", "generator": "internal_wordcloud", "parameters": {"max_words": 150, "color_scheme": "blue", "width": 1000, "height": 500, "background_color": "white", "use_ai": true, "prompt_template": "general_keywords"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "table", "generator": "internal_table", "parameters": {"max_categories": 15, "include_percentages": true, "sort_by": "frequency", "output_format": "html"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}]}, "Q2": {"question_text": "Test otázka Q2", "data_type": "choice", "hidden": false, "charts": [{"chart_type": "column", "generator": "datawrapper", "parameters": {"chart_type": "d3-bars"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "pie", "generator": "datawrapper", "parameters": {"chart_type": "d3-pies"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "table", "generator": "internal_table", "parameters": {"max_categories": 15, "include_percentages": true, "sort_by": "frequency", "output_format": "html"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}]}, "Q4": {"question_text": "Test otázka Q4", "data_type": "choice", "hidden": false, "charts": [{"chart_type": "column", "generator": "datawrapper", "parameters": {"chart_type": "d3-bars"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "pie", "generator": "datawrapper", "parameters": {"chart_type": "d3-pies"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "table", "generator": "internal_table", "parameters": {"max_categories": 15, "include_percentages": true, "sort_by": "frequency", "output_format": "html"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}]}, "Q3": {"question_text": "Test otázka Q3", "data_type": "choice", "hidden": false, "charts": [{"chart_type": "column", "generator": "datawrapper", "parameters": {"chart_type": "d3-bars"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "pie", "generator": "datawrapper", "parameters": {"chart_type": "d3-pies"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "donut", "generator": "datawrapper", "parameters": {"chart_type": "d3-donut", "show_legend": true, "inner_radius": 0.5, "colors": "default"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}]}, "Q5": {"question_text": "Test otázka Q5", "data_type": "choice", "hidden": false, "charts": [{"chart_type": "column", "generator": "datawrapper", "parameters": {"chart_type": "d3-bars"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "pie", "generator": "datawrapper", "parameters": {"chart_type": "d3-pies"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "donut", "generator": "datawrapper", "parameters": {"chart_type": "d3-donut", "show_legend": true, "inner_radius": 0.5, "colors": "default"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}]}, "Q6": {"question_text": "Test otázka Q6", "data_type": "choice", "hidden": false, "charts": [{"chart_type": "column", "generator": "datawrapper", "parameters": {"chart_type": "d3-bars"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "pie", "generator": "datawrapper", "parameters": {"chart_type": "d3-pies"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}, {"chart_type": "donut", "generator": "datawrapper", "parameters": {"chart_type": "d3-donut", "show_legend": true, "inner_radius": 0.5, "colors": "default"}, "ai_analysis": false, "prompt_template": null, "custom_prompt": null}]}}}