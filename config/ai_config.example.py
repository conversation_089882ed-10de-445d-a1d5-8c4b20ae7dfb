"""
Example AI Configuration for LimWrapp
Ukázkový konfigurační soubor pro AI komponenty
"""

import os
from pathlib import Path

# OpenAI API Configuration
OPENAI_CONFIG = {
    'api_key': os.getenv('OPENAI_API_KEY'),
    'organization_id': os.getenv('OPENAI_ORG_ID'),
    'default_model': 'gpt-4o-mini',
    'enable_cache': True,
    'cache_ttl': 3600,  # 1 hour
    'max_retries': 3,
    'timeout': 60.0
}

# Redis Configuration for Caching
REDIS_CONFIG = {
    'url': os.getenv('REDIS_URL', 'redis://localhost:6379/0'),
    'enabled': True
}

# Cost Control Settings
COST_CONTROL = {
    'daily_limit': 50.0,  # USD
    'monthly_limit': 500.0,  # USD
    'alert_threshold': 0.8,  # Alert at 80% of limit
    'track_usage': True
}

# WordCloud Default Settings
WORDCLOUD_DEFAULTS = {
    'max_words': 200,
    'min_word_length': 3,
    'width': 800,
    'height': 400,
    'background_color': 'white',
    'color_scheme': 'default',
    'output_format': 'png',
    'dpi': 300,
    'use_ai': True,
    'language': 'cs'
}

# Parameter Management
PARAMETER_CONFIG = {
    'config_dir': 'config/parameters',
    'auto_save': True,
    'history_size': 50
}

# Prompt Management
PROMPT_CONFIG = {
    'prompts_dir': 'src/ai/prompts',
    'auto_reload': True,
    'validate_on_load': True
}

# Scenario Generation
SCENARIO_CONFIG = {
    'max_scenarios': 3,
    'default_complexity': 'auto',
    'include_templates': True,
    'use_ai_enhancement': True
}

# Logging Configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'logs/ai.log',
    'max_bytes': 10485760,  # 10MB
    'backup_count': 5
}

# Model Configurations
MODEL_CONFIGS = {
    'gpt-4o-mini': {
        'max_tokens': 16384,
        'context_window': 128000,
        'input_cost_per_1k': 0.150,
        'output_cost_per_1k': 0.600,
        'cached_cost_per_1k': 0.075,
        'rpm_limit': 500,
        'tpm_limit': 200000
    },
    'gpt-4o': {
        'max_tokens': 4096,
        'context_window': 128000,
        'input_cost_per_1k': 2.50,
        'output_cost_per_1k': 10.00,
        'cached_cost_per_1k': 1.25,
        'rpm_limit': 500,
        'tpm_limit': 30000
    }
}

# Feature Flags
FEATURES = {
    'enable_ai_wordcloud': True,
    'enable_scenario_generation': True,
    'enable_parameter_suggestions': True,
    'enable_text_analysis': True,
    'enable_translation': False,  # Not implemented yet
    'enable_cost_tracking': True,
    'enable_usage_analytics': True
}

# Integration Settings
INTEGRATION = {
    'datawrapper_api_key': os.getenv('DATAWRAPPER_API_KEY'),
    'enable_datawrapper_integration': False,
    'export_formats': ['png', 'svg', 'pdf'],
    'auto_export': False
}

def get_ai_config():
    """Get complete AI configuration"""
    return {
        'openai': OPENAI_CONFIG,
        'redis': REDIS_CONFIG,
        'cost_control': COST_CONTROL,
        'wordcloud': WORDCLOUD_DEFAULTS,
        'parameters': PARAMETER_CONFIG,
        'prompts': PROMPT_CONFIG,
        'scenarios': SCENARIO_CONFIG,
        'logging': LOGGING_CONFIG,
        'models': MODEL_CONFIGS,
        'features': FEATURES,
        'integration': INTEGRATION
    }

def validate_config():
    """Validate AI configuration"""
    errors = []
    
    # Check required API keys
    if not OPENAI_CONFIG['api_key']:
        errors.append("OPENAI_API_KEY is required")
    
    # Check directories exist
    for dir_path in [PARAMETER_CONFIG['config_dir'], PROMPT_CONFIG['prompts_dir']]:
        if not Path(dir_path).exists():
            try:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
            except Exception as e:
                errors.append(f"Cannot create directory {dir_path}: {e}")
    
    # Check cost limits
    if COST_CONTROL['daily_limit'] <= 0:
        errors.append("Daily cost limit must be positive")
    
    return errors

def setup_logging():
    """Setup logging for AI components"""
    import logging
    import logging.handlers
    
    # Create logs directory
    log_dir = Path(LOGGING_CONFIG['file']).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Setup logger
    logger = logging.getLogger('limwrapp.ai')
    logger.setLevel(getattr(logging, LOGGING_CONFIG['level']))
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        LOGGING_CONFIG['file'],
        maxBytes=LOGGING_CONFIG['max_bytes'],
        backupCount=LOGGING_CONFIG['backup_count']
    )
    file_handler.setFormatter(logging.Formatter(LOGGING_CONFIG['format']))
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(LOGGING_CONFIG['format']))
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# Environment-specific overrides
if os.getenv('ENVIRONMENT') == 'development':
    OPENAI_CONFIG['default_model'] = 'gpt-4o-mini'
    COST_CONTROL['daily_limit'] = 10.0
    LOGGING_CONFIG['level'] = 'DEBUG'
    FEATURES['enable_cost_tracking'] = True

elif os.getenv('ENVIRONMENT') == 'production':
    OPENAI_CONFIG['default_model'] = 'gpt-4o'
    COST_CONTROL['daily_limit'] = 100.0
    LOGGING_CONFIG['level'] = 'INFO'
    FEATURES['enable_usage_analytics'] = True

# Export main configuration
AI_CONFIG = get_ai_config()
