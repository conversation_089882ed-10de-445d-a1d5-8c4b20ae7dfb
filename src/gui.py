from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QListWidget, QTextEdit, QComboBox, QPushButton,
    QLabel, QLineEdit, QMenuBar, QMenu, QAction,
    QTreeWidget, QTreeWidgetItem, QTableWidget, QTableWidgetItem,
    QSpinBox, QTabWidget, QApplication
)
from PyQt5.QtCore import Qt
import json
import os
from logger import ProcessLogger
from table_manager import TableManager
from structure_manager import StructureManager
from question_manager import QuestionManager
from survey_manager import SurveyManager
from chart_manager import ChartManager
from chart_metadata_dialog import ChartMetadataDialog

def get_file_separator(file_path):
    """Ur<PERSON><PERSON> oddělovač podle přípony souboru"""
    ext = os.path.splitext(file_path)[1].lower()
    if ext == '.csv':
        return ';'  # <PERSON><PERSON><PERSON><PERSON><PERSON> oddělov<PERSON>č pro CSV
    elif ext == '.tsv':
        return '\t'  # Tabulátor pro TSV
    return ';'  # <PERSON>ý<PERSON><PERSON><PERSON> odd<PERSON>lov<PERSON>č pro ostatní soubory

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # Managers
        self.survey_manager = SurveyManager()
        self.chart_manager = ChartManager()
        self.logger = ProcessLogger("gui")
        
        # Main widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)
        
        # Left column
        left_column = QWidget()
        left_layout = QVBoxLayout(left_column)
        
        # Surveys panel
        surveys_panel = QWidget()
        surveys_layout = QVBoxLayout(surveys_panel)
        surveys_layout.addWidget(QLabel("Průzkumy"))
        self.survey_list = QListWidget()
        self.survey_list.itemClicked.connect(self.on_survey_selected)
        surveys_layout.addWidget(self.survey_list)
        
        # Questions panel
        questions_panel = QWidget()
        questions_layout = QVBoxLayout(questions_panel)
        questions_layout.addWidget(QLabel("Otázky"))
        self.question_tree = QTreeWidget()
        self.question_tree.setHeaderHidden(True)
        self.question_tree.itemClicked.connect(self.on_question_selected)
        questions_layout.addWidget(self.question_tree)
        
        left_layout.addWidget(surveys_panel, 1)
        left_layout.addWidget(questions_panel, 1)
        
        # Initialize managers
        self.question_manager = QuestionManager(self.question_tree)
        
        # Right column
        right_column = QWidget()
        right_layout = QVBoxLayout(right_column)
        
        # Tabs
        self.tab_widget = QTabWidget()
        
        # Tab 1 - Questions
        questions_tab = QWidget()
        questions_layout = QVBoxLayout(questions_tab)
        self.question_detail = QTextEdit()
        self.question_detail.setReadOnly(True)
        questions_layout.addWidget(self.question_detail)
        
        # Chart type
        questions_layout.addWidget(QLabel("Typ grafu:"))
        self.chart_type = QComboBox()
        self.chart_type.addItems(self.chart_manager.get_chart_types())
        questions_layout.addWidget(self.chart_type)
        
        # Chart buttons
        chart_buttons = QHBoxLayout()
        
        # Create chart button
        self.create_chart_btn = QPushButton("Vytvořit graf")
        self.create_chart_btn.clicked.connect(self.create_chart)
        chart_buttons.addWidget(self.create_chart_btn)
        
        # Chart settings button
        self.chart_settings_btn = QPushButton("Nastavení grafu")
        self.chart_settings_btn.clicked.connect(self.show_chart_settings)
        chart_buttons.addWidget(self.chart_settings_btn)
        
        questions_layout.addLayout(chart_buttons)
        
        self.tab_widget.addTab(questions_tab, "Otázky")
        
        # Tab 2 - CSV Data
        csv_tab = QWidget()
        csv_layout = QVBoxLayout(csv_tab)
        
        # CSV toolbar
        csv_toolbar = QWidget()
        csv_toolbar_layout = QHBoxLayout(csv_toolbar)
        
        self.csv_search = QLineEdit()
        self.csv_search.setPlaceholderText("Hledat...")
        self.csv_search.textChanged.connect(self.filter_csv_table)
        csv_toolbar_layout.addWidget(self.csv_search)
        
        self.csv_page_size = QSpinBox()
        self.csv_page_size.setRange(10, 100)
        self.csv_page_size.setValue(20)
        self.csv_page_size.valueChanged.connect(self.update_csv_table)
        csv_toolbar_layout.addWidget(QLabel("Počet řádků:"))
        csv_toolbar_layout.addWidget(self.csv_page_size)
        
        csv_layout.addWidget(csv_toolbar)
        
        # CSV table
        self.csv_table = QTableWidget()
        self.csv_table.setSortingEnabled(True)
        self.csv_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.csv_table.horizontalHeader().sectionClicked.connect(self.sort_csv_table)
        self.csv_table.page_size = self.csv_page_size.value()
        csv_layout.addWidget(self.csv_table)
        
        # CSV navigation
        csv_nav = QWidget()
        csv_nav_layout = QHBoxLayout(csv_nav)
        
        self.csv_prev_btn = QPushButton("←")
        self.csv_prev_btn.clicked.connect(self.prev_csv_page)
        csv_nav_layout.addWidget(self.csv_prev_btn)
        
        self.csv_page_label = QLabel("1/1")
        self.csv_table.page_label = self.csv_page_label
        csv_nav_layout.addWidget(self.csv_page_label)
        
        self.csv_next_btn = QPushButton("→")
        self.csv_next_btn.clicked.connect(self.next_csv_page)
        csv_nav_layout.addWidget(self.csv_next_btn)
        
        csv_layout.addWidget(csv_nav)
        
        self.tab_widget.addTab(csv_tab, "CSV Data")
        
        # Tab 3 - Structure
        structure_tab = QWidget()
        structure_layout = QVBoxLayout(structure_tab)
        
        self.structure_tree = QTreeWidget()
        self.structure_tree.setHeaderLabels(["Název", "Typ", "ID"])
        self.structure_tree.setSortingEnabled(True)
        structure_layout.addWidget(self.structure_tree)
        
        self.tab_widget.addTab(structure_tab, "Struktura")
        
        # Tab 4 - Mapping
        mapping_tab = QWidget()
        mapping_layout = QVBoxLayout(mapping_tab)
        
        # Mapping toolbar
        mapping_toolbar = QWidget()
        mapping_toolbar_layout = QHBoxLayout(mapping_toolbar)
        
        self.mapping_search = QLineEdit()
        self.mapping_search.setPlaceholderText("Hledat...")
        self.mapping_search.textChanged.connect(self.filter_mapping_table)
        mapping_toolbar_layout.addWidget(self.mapping_search)
        
        self.mapping_page_size = QSpinBox()
        self.mapping_page_size.setRange(10, 100)
        self.mapping_page_size.setValue(20)
        self.mapping_page_size.valueChanged.connect(self.update_mapping_table)
        mapping_toolbar_layout.addWidget(QLabel("Počet řádků:"))
        mapping_toolbar_layout.addWidget(self.mapping_page_size)
        
        mapping_layout.addWidget(mapping_toolbar)
        
        # Mapping table
        self.mapping_table = QTableWidget()
        self.mapping_table.setSortingEnabled(True)
        self.mapping_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.mapping_table.horizontalHeader().sectionClicked.connect(self.sort_mapping_table)
        self.mapping_table.page_size = self.mapping_page_size.value()
        mapping_layout.addWidget(self.mapping_table)
        
        # Mapping navigation
        mapping_nav = QWidget()
        mapping_nav_layout = QHBoxLayout(mapping_nav)
        
        self.mapping_prev_btn = QPushButton("←")
        self.mapping_prev_btn.clicked.connect(self.prev_mapping_page)
        mapping_nav_layout.addWidget(self.mapping_prev_btn)
        
        self.mapping_page_label = QLabel("1/1")
        self.mapping_table.page_label = self.mapping_page_label
        mapping_nav_layout.addWidget(self.mapping_page_label)
        
        self.mapping_next_btn = QPushButton("→")
        self.mapping_next_btn.clicked.connect(self.next_mapping_page)
        mapping_nav_layout.addWidget(self.mapping_next_btn)
        
        mapping_layout.addWidget(mapping_nav)
        
        self.tab_widget.addTab(mapping_tab, "Transformace")
        
        # Initialize table managers
        self.csv_table_manager = TableManager(self.csv_table)
        self.mapping_table_manager = TableManager(self.mapping_table)
        self.structure_manager = StructureManager(self.structure_tree)
        
        # Top panel
        top_panel = QWidget()
        top_layout = QHBoxLayout(top_panel)
        
        # Question detail
        question_detail_panel = QWidget()
        question_detail_layout = QVBoxLayout(question_detail_panel)
        question_detail_layout.addWidget(self.tab_widget)
        
        # Survey detail
        survey_detail_panel = QWidget()
        survey_detail_layout = QVBoxLayout(survey_detail_panel)
        survey_detail_layout.addWidget(QLabel("Detail průzkumu"))
        
        # Chart prefix
        survey_detail_layout.addWidget(QLabel("Prefix názvu grafů:"))
        self.chart_prefix = QLineEdit()
        survey_detail_layout.addWidget(self.chart_prefix)
        
        # Default chart type
        survey_detail_layout.addWidget(QLabel("Výchozí typ grafu:"))
        self.default_chart_type = QComboBox()
        self.default_chart_type.addItems(self.chart_manager.get_chart_types())
        survey_detail_layout.addWidget(self.default_chart_type)
        
        # Save settings button
        self.save_settings_btn = QPushButton("Uložit nastavení")
        self.save_settings_btn.clicked.connect(self.save_survey_settings)
        survey_detail_layout.addWidget(self.save_settings_btn)
        
        top_layout.addWidget(question_detail_panel, 1)
        top_layout.addWidget(survey_detail_panel, 1)
        
        # Log
        log_panel = QWidget()
        log_layout = QVBoxLayout(log_panel)
        log_layout.addWidget(QLabel("Log"))
        self.log_window = QTextEdit()
        self.log_window.setReadOnly(True)
        log_layout.addWidget(self.log_window)
        
        # CLI
        cli_panel = QWidget()
        cli_layout = QVBoxLayout(cli_panel)
        cli_layout.addWidget(QLabel("CLI"))
        self.cli_input = QLineEdit()
        self.cli_input.returnPressed.connect(self.execute_cli_command)
        cli_layout.addWidget(self.cli_input)
        
        right_layout.addWidget(top_panel, 7)
        right_layout.addWidget(log_panel, 2)
        right_layout.addWidget(cli_panel, 1)
        
        main_layout.addWidget(left_column, 1)
        main_layout.addWidget(right_column, 1)
        
        # Menu
        menubar = self.menuBar()
        file_menu = menubar.addMenu("Soubor")
        
        load_surveys_action = QAction("Načíst průzkumy", self)
        load_surveys_action.triggered.connect(self.load_surveys)
        file_menu.addAction(load_surveys_action)
        
        save_settings_action = QAction("Uložit nastavení", self)
        file_menu.addAction(save_settings_action)
        
        exit_action = QAction("Konec", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        self.setWindowTitle("LimeWrapper - Průzkumy")
        self.showMaximized()

    def load_surveys(self):
        surveys = self.survey_manager.load_surveys()
        self.survey_list.clear()
        for survey in surveys:
            self.survey_list.addItem(f"{survey['sid']}: {survey['surveyls_title']}")

    def on_survey_selected(self, item):
        survey_id = item.text().split(":")[0]
        responses, structure = self.survey_manager.load_survey_data(survey_id)
        if structure:
            self.question_manager.load_questions(survey_id, structure)
            
            # Load survey settings
            settings = self.survey_manager.load_survey_settings(survey_id)
            self.chart_prefix.setText(settings.get('chart_prefix', ''))
            self.default_chart_type.setCurrentText(settings.get('default_chart_type', 'Sloupcový'))
            
            # Load data into tables
            files = self.survey_manager.get_survey_files(survey_id)
            
            # Načtení CSV dat s detekcí oddělovače
            responses_file = files['responses']
            responses_sep = get_file_separator(responses_file)
            self.csv_table_manager.load_data(responses_file, separator=responses_sep)
            
            # Načtení struktury
            self.structure_manager.load_structure(files['structure'])
            
            # Načtení mapování s detekcí oddělovače
            mapping_file = files['mapping']
            mapping_sep = get_file_separator(mapping_file)
            self.mapping_table_manager.load_data(mapping_file, separator=mapping_sep)
            
            self.log_window.append(f"Načten průzkum {survey_id}")
            self.current_survey = survey_id

    def on_question_selected(self, item):
        question_id = item.data(0, Qt.UserRole)
        question = self.question_manager.get_question_details(question_id)
        if question:
            self.question_detail.setText(self.question_manager.get_question_text(question))
            self.current_question = question_id
            
            # Suggest chart type
            question_type = self.question_manager.get_question_type(question_id)
            answers = self.question_manager.get_available_answers(question_id)
            suggested_type = self.chart_manager.suggest_chart_type(question_type, len(answers))
            self.chart_type.setCurrentText(suggested_type)

    def show_chart_settings(self):
        """Zobrazí dialog pro nastavení metadat grafu"""
        if hasattr(self, 'current_question'):
            dialog = ChartMetadataDialog(
                self.chart_manager,
                self.chart_type.currentText(),
                self
            )
            if dialog.exec_():
                self.chart_metadata = dialog.get_metadata()
                self.log_window.append("Nastavení grafu uloženo")

    def create_chart(self):
        if hasattr(self, 'current_question'):
            chart_type = self.chart_type.currentText()
            if chart_type:
                try:
                    metadata = getattr(self, 'chart_metadata', None)
                    chart_id, image_path = self.chart_manager.create_chart(
                        self.current_question,
                        chart_type,
                        self.chart_prefix.text(),
                        metadata
                    )
                    self.log_window.append(f"Vytvořen graf pro otázku {self.current_question}")
                except Exception as e:
                    self.log_window.append(f"Chyba při vytváření grafu: {str(e)}")

    def save_survey_settings(self):
        if hasattr(self, 'current_survey'):
            try:
                settings = {
                    'survey_id': self.current_survey,
                    'chart_prefix': self.chart_prefix.text(),
                    'default_chart_type': self.default_chart_type.currentText()
                }
                self.survey_manager.save_survey_settings(self.current_survey, settings)
                self.log_window.append(f"Uloženo nastavení pro průzkum {self.current_survey}")
            except Exception as e:
                self.log_window.append(f"Chyba při ukládání nastavení: {str(e)}")

    # CSV table methods
    def filter_csv_table(self, text):
        self.csv_table_manager.filter_data(text)
        
    def update_csv_table(self):
        self.csv_table.page_size = self.csv_page_size.value()
        self.csv_table_manager.update_table()
        
    def next_csv_page(self):
        self.csv_table_manager.next_page()
        
    def prev_csv_page(self):
        self.csv_table_manager.prev_page()
        
    def sort_csv_table(self, column):
        self.csv_table_manager.sort_by_column(column)

    # Mapping table methods
    def filter_mapping_table(self, text):
        self.mapping_table_manager.filter_data(text)
        
    def update_mapping_table(self):
        self.mapping_table.page_size = self.mapping_page_size.value()
        self.mapping_table_manager.update_table()
        
    def next_mapping_page(self):
        self.mapping_table_manager.next_page()
        
    def prev_mapping_page(self):
        self.mapping_table_manager.prev_page()
        
    def sort_mapping_table(self, column):
        self.mapping_table_manager.sort_by_column(column)

    def execute_cli_command(self):
        command = self.cli_input.text()
        self.cli_input.clear()
        self.log_window.append(f"> {command}")
        # TODO: Implement CLI command execution

if __name__ == "__main__":
    import sys
    
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
