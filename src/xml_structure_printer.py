import xml.etree.ElementTree as ET
import os
import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class XMLStructurePrinter:
    def __init__(self):
        logger.info("XMLStructurePrinter initialized.")

    def generate_html_printout(self, xml_file_path: str, output_html_path: str) -> bool:
        try:
            logger.info(f"Generating HTML printout from XML: {xml_file_path}")
            logger.info(f"Output HTML path: {output_html_path}")

            tree = ET.parse(xml_file_path)
            root = tree.getroot()

            html_content = self._generate_html_header()
            html_content += self._generate_survey_structure_html(root)
            html_content += self._generate_html_footer()

            os.makedirs(os.path.dirname(output_html_path), exist_ok=True)
            with open(output_html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            logger.info(f"Successfully generated HTML printout: {output_html_path}")
            return True
        except Exception as e:
            logger.error(f"Error generating HTML printout: {e}", exc_info=True)
            return False

    def _generate_html_header(self) -> str:
        return """
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tisková verze průzkumu</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; background-color: #f4f4f4; color: #333; }
        .container { max-width: 900px; margin: auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        h1, h2, h3, h4 { color: #0056b3; }
        .group { border: 1px solid #ddd; border-radius: 5px; margin-bottom: 20px; padding: 15px; background-color: #e9f5ff; }
        .group-header { font-size: 1.5em; margin-bottom: 10px; font-weight: bold; }
        .group-description { font-style: italic; margin-bottom: 15px; color: #555; }
        .question { border: 1px solid #ccc; border-radius: 5px; margin-bottom: 15px; padding: 10px; background-color: #fff; }
        .question-header { font-size: 1.2em; font-weight: bold; margin-bottom: 5px; }
        .question-meta { font-size: 0.9em; color: #666; margin-bottom: 5px; }
        .question-text { margin-bottom: 10px; }
        .subquestions, .answer-options, .relevance { margin-left: 20px; margin-top: 10px; }
        .subquestions ul, .answer-options ul { list-style-type: none; padding: 0; }
        .subquestions li, .answer-options li { background-color: #f0f0f0; padding: 8px; margin-bottom: 5px; border-radius: 3px; }
        .relevance p { font-family: monospace; background-color: #f8f8f8; padding: 5px; border-radius: 3px; border: 1px dashed #ccc; }
        .code { font-weight: bold; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Struktura průzkumu</h1>
"""

    def _generate_html_footer(self) -> str:
        return """
    </div>
</body>
</html>
"""

    def _generate_survey_structure_html(self, root: ET.Element) -> str:
        html = ""
        
        groups = []
        for group_elem in root.findall(".//group"):
            group_order = int(group_elem.get('group_order', '0')) 
            groups.append((group_order, group_elem))
        
        groups.sort(key=lambda x: x[0]) 

        for order, group_elem in groups:
            gid = group_elem.get('gid', 'N/A')
            group_name = group_elem.findtext('group_name', 'N/A')
            group_description = group_elem.findtext('description', '')

            html += f'<div class="group">'
            html += f'<div class="group-header">Skupina {gid}: {group_name}</div>'
            if group_description:
                html += f'<div class="group-description">{group_description}</div>'

            questions = []
            for question_elem in group_elem.findall('.//question'):
                question_order = int(question_elem.get('question_order', '0')) 
                questions.append((question_order, question_elem))
            
            questions.sort(key=lambda x: x[0]) 

            for order, question_elem in questions:
                html += self._generate_question_html(question_elem)
            
            html += '</div>' 
        return html

    def _generate_question_html(self, question_elem: ET.Element) -> str:
        qid = question_elem.get('qid', 'N/A')
        title = question_elem.get('title', 'N/A')
        q_type = question_elem.get('type', 'N/A')
        question_text = question_elem.findtext('question', 'N/A')
        
        relevance = question_elem.get('relevance', '1') 
        if relevance == '1': 
            relevance_elem = question_elem.find('relevance')
            if relevance_elem is not None:
                relevance = relevance_elem.text or '1'

        html = f'<div class="question">'
        html += f'<div class="question-header"><span class="code">QID: {qid}</span> | <span class="code">Kód: {title}</span> | Typ: <span class="code">{q_type}</span></div>'
        html += f'<div class="question-text">{question_text}</div>'

        subquestions_html = ""
        subquestion_count = 0
        for subq_elem in question_elem.findall('.//subquestion'):
            subquestion_count += 1
            subq_title = subq_elem.get('title', 'N/A')
            subq_text = subq_elem.findtext('question', 'N/A') 
            subquestions_html += f'<li><span class="code">{subq_title}</span>: {subq_text}</li>'
        
        if subquestions_html:
            html += f'<div class="subquestions"><h4>Subotázky ({subquestion_count}):</h4><ul>{subquestions_html}</ul></div>'

        answer_options_html = ""
        answer_option_count = 0
        for ans_elem in question_elem.findall('.//answer'):
            answer_option_count += 1
            ans_code = ans_elem.get('code', 'N/A')
            ans_text = ans_elem.findtext('.', 'N/A') 
            answer_options_html += f'<li><span class="code">{ans_code}</span>: {ans_text}</li>'
        
        if answer_options_html:
            html += f'<div class="answer-options"><h4>Možnosti odpovědí ({answer_option_count}):</h4><ul>{answer_options_html}</ul></div>'

        if relevance and relevance.strip() != '1':
            html += f'<div class="relevance"><h4>Podmínka zobrazení:</h4><p>{relevance}</p></div>'

        html += '</div>' 
        return html
