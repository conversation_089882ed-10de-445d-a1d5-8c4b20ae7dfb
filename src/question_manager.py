from PyQt5.QtWidgets import QTreeWidgetItem
from PyQt5.QtCore import Qt

class QuestionManager:
    def __init__(self, tree_widget):
        self.questions = {}
        self.question_tree = tree_widget
        
    def load_questions(self, survey_id, structure):
        """Načte otázky ze struktury průzkumu a zobrazí je ve stromovém widgetu"""
        self.questions.clear()
        self.question_tree.clear()
        
        for group in structure.get('groups', []):
            # Uložení všech otázek do slovníku
            for question in group.get('questions', []):
                self.questions[question.get('title', '')] = question
                
            # Vytvoření položky skupiny
            group_item = QTreeWidgetItem([group.get('group_name', '')])
            
            # Filtrování otázek druhé úrovně (bez nadřazené otázky)
            second_level = [q for q in group.get('questions', []) 
                          if q['properties'].get('parent_qid') == 0]
            # <PERSON><PERSON>azen<PERSON> podle pořadí
            second_level.sort(key=lambda q: q.get('question_order', 0))
            
            # Přidán<PERSON> ot<PERSON>k do stromu
            for question in second_level:
                question_item = QTreeWidgetItem([question.get('question', '')])
                question_item.setData(0, Qt.UserRole, question.get('title', ''))
                group_item.addChild(question_item)
                
                # Přidání dostupných odpovědí jako podpoložky
                available_answers = question['properties'].get('available_answers', {})
                if isinstance(available_answers, dict):
                    for answer_key, answer_text in available_answers.items():
                        answer_item = QTreeWidgetItem([answer_text])
                        answer_item.setData(0, Qt.UserRole, answer_key)
                        question_item.addChild(answer_item)
            
            self.question_tree.addTopLevelItem(group_item)
        
    def get_question_details(self, question_id):
        """Vrátí detaily o otázce podle jejího ID"""
        return self.questions.get(question_id)
        
    def get_question_text(self, question):
        """Vytvoří formátovaný text s detaily otázky"""
        if not question:
            return ""
            
        return f"""
        Název: {question.get('title', '')}
        Otázka: {question.get('question', '')}
        Typ: {question.get('type', '')}
        Skupina: {question.get('group', '')}
        """
        
    def get_question_type(self, question_id):
        """Vrátí typ otázky podle jejího ID"""
        question = self.get_question_details(question_id)
        if question:
            return question.get('type', '')
        return None
        
    def get_available_answers(self, question_id):
        """Vrátí seznam dostupných odpovědí pro otázku"""
        question = self.get_question_details(question_id)
        if question:
            answers = question.get('properties', {}).get('available_answers', {})
            if isinstance(answers, dict):
                return [(key, text) for key, text in answers.items()]
        return []
        
    def get_subquestions(self, question_id):
        """Vrátí seznam podotázek pro danou otázku"""
        question = self.get_question_details(question_id)
        if question:
            return question.get('subquestions', [])
        return []
        
    def get_group_name(self, question_id):
        """Vrátí název skupiny, do které otázka patří"""
        question = self.get_question_details(question_id)
        if question:
            return question.get('group', '')
        return None
