import json
from limesurvey_client import LimeSurveyClient

class SurveyManager:
    def __init__(self):
        self.client = LimeSurveyClient()
        
    def load_surveys(self):
        """Načte seznam dostupných průzkumů"""
        return self.client.list_surveys()
        
    def load_survey_data(self, survey_id):
        """Načte data a strukturu průzkumu"""
        responses = self.client.get_responses(survey_id)
        structure = self.client.get_survey_structure(survey_id)
        return responses, structure
        
    def load_survey_settings(self, survey_id):
        """Načte nastavení průzkumu ze souboru"""
        try:
            with open(f"data/survey_{survey_id}_settings.json", "r") as f:
                return json.load(f)
        except FileNotFoundError:
            return {
                'chart_prefix': '',
                'default_chart_type': 'Sloupcový'
            }
            
    def save_survey_settings(self, survey_id, settings):
        """Uloží nastavení průzkumu do souboru"""
        with open(f"data/survey_{survey_id}_settings.json", "w") as f:
            json.dump(settings, f, indent=2)
            
    def get_survey_files(self, survey_id):
        """Vrátí cesty k souborům průzkumu"""
        return {
            'responses': f"data/{survey_id}/responses.csv",
            'structure': f"data/{survey_id}/structure.lss",
            'settings': f"data/survey_{survey_id}_settings.json",
            'mapping': f"data/question_mapping.csv"
        }
        
    def validate_survey_files(self, survey_id):
        """Zkontroluje existenci potřebných souborů průzkumu"""
        files = self.get_survey_files(survey_id)
        missing = []
        
        for name, path in files.items():
            if not os.path.exists(path):
                missing.append(name)
                
        return len(missing) == 0, missing
        
    def get_survey_title(self, survey_id):
        """Vrátí název průzkumu"""
        surveys = self.load_surveys()
        for survey in surveys:
            if survey['sid'] == survey_id:
                return survey['surveyls_title']
        return None
        
    def get_survey_info(self, survey_id):
        """Vrátí základní informace o průzkumu"""
        title = self.get_survey_title(survey_id)
        settings = self.load_survey_settings(survey_id)
        valid, missing = self.validate_survey_files(survey_id)
        
        return {
            'id': survey_id,
            'title': title,
            'settings': settings,
            'valid': valid,
            'missing_files': missing
        }
