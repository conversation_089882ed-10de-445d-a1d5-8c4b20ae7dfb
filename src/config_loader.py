import os
try:
    from dotenv import load_dotenv
except ImportError:
    def load_dotenv():
        pass

def load_config():
    load_dotenv()

    # Načtení všech serverů
    servers = {}
    i = 1
    while True:
        server_name = os.getenv(f'LIMESURVEY_SERVER{i}_NAME')
        if not server_name:
            break

        servers[f'server{i}'] = {
            'name': server_name,
            'API_URL': os.getenv(f'LIMESURVEY_SERVER{i}_URL'),
            'USERNAME': os.getenv(f'LIMESURVEY_SERVER{i}_USERNAME'),
            'PASSWORD': os.getenv(f'LIMESURVEY_SERVER{i}_PASSWORD')
        }
        i += 1

    return {
        'DEFAULT_CHART_TYPE': os.getenv('DEFAULT_CHART_TYPE', 'bar_chart'),
        'GRAPH_FOOTER': os.getenv('GRAPH_FOOTER', 'Data z LimeSurvey'),
        'EXPORT_PNG': os.getenv('EXPORT_PNG', 'true').lower() == 'true',
        'charts_root': os.getenv('CHARTS_ROOT', 'charts'),
        'data_root': os.getenv('DATA_ROOT', 'data'),
        'DATAWRAPPER_TEAM_ID': os.getenv('DATAWRAPPER_TEAM_ID'),
        'DATAWRAPPER_LIMESURVEY_FOLDER_ID': os.getenv('DATAWRAPPER_LIMESURVEY_FOLDER_ID'),
        'LIMESURVEY': {
            'API_URL': os.getenv('LIMESURVEY_API_URL'),
            'USERNAME': os.getenv('LIMESURVEY_USERNAME'),
            'PASSWORD': os.getenv('LIMESURVEY_PASSWORD')
        },
        'LIMESURVEY_SERVERS': servers
    }

def get_available_servers():
    """Vrátí seznam dostupných LimeSurvey serverů"""
    config = load_config()
    return config.get('LIMESURVEY_SERVERS', {})
