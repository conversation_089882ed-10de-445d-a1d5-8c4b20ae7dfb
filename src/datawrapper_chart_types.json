{"chart_types": [{"name": "Bar Chart", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M16 4H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h13a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1Zm11 8H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h24a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Zm-3 8H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h21a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"orientation": ["horizontal", "vertical"], "bar_width": "number", "bar_spacing": "number"}}, {"name": "Stacked Bars", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 5a1 1 0 0 1 1-1h6v6H3a1 1 0 0 1-1-1V5Zm0 8a1 1 0 0 1 1-1h11v6H3a1 1 0 0 1-1-1v-4Zm0 8a1 1 0 0 1 1-1h14v6H3a1 1 0 0 1-1-1v-4Z\" fill=\"#1D81A2\"></path><path d=\"M18 20h9a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-9v-6Zm-3-8h5a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-5v-6Zm-5-8h12a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H10V4Z\" fill=\"#92CADC\"></path></svg>", "specific_metadata": {"stack_order": ["ascending", "descending", "custom"], "stack_labels": "boolean"}}, {"name": "Grouped Bars", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3Zm0 15a1 1 0 0 1 1-1h18a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-3Z\" fill=\"#1D81A2\"></path><path d=\"M2 9a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V9Zm0 15a1 1 0 0 1 1-1h24a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-3Z\" fill=\"#92CADC\"></path></svg>", "specific_metadata": {"group_spacing": "number", "group_labels": "boolean"}}, {"name": "Split Bars", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M7 4H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1Zm5 8H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h9a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Zm2 8H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Z\" fill=\"#1D81A2\"></path><path d=\"M23 4h-4a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1Zm4 8h-8a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Zm-5 8h-3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Z\" fill=\"#92CADC\"></path></svg>", "specific_metadata": {"split_direction": ["left-right", "top-bottom"], "split_labels": "boolean"}}, {"name": "Bullet Bars", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 7a1 1 0 0 1 1-1h24a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7Zm0 11a1 1 0 0 1 1-1h9a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-5Z\" fill=\"#92CADC\"></path><path d=\"M2 19h16a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H2v-3ZM2 8h9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H2V8Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"target_value": "number", "comparison_value": "number"}}, {"name": "Column Chart", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M4 15v12a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V15a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1Zm8-11v23a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1Zm8 4v19a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"column_width": "number", "column_spacing": "number"}}, {"name": "Stacked Columns", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M21 28a1 1 0 0 1-1-1v-8h6v8a1 1 0 0 1-1 1h-4Zm-8 0a1 1 0 0 1-1-1V16h6v11a1 1 0 0 1-1 1h-4Zm-8 0a1 1 0 0 1-1-1v-5h6v5a1 1 0 0 1-1 1H5Z\" fill=\"#1D81A2\"></path><path d=\"M20 18v-8a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v8h-6Zm-8-3V6a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v9h-6Zm-8 6v-8a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v8H4Z\" fill=\"#92CADC\"></path></svg>", "specific_metadata": {"stack_order": ["ascending", "descending", "custom"], "stack_labels": "boolean"}}, {"name": "Grouped Columns", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M3 28a1 1 0 0 1-1-1V16a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v11a1 1 0 0 1-1 1H3Zm15 0a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1h-3Z\" fill=\"#1D81A2\"></path><path d=\"M9 28a1 1 0 0 1-1-1v-9a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1H9Zm15 0a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v22a1 1 0 0 1-1 1h-3Z\" fill=\"#92CADC\"></path></svg>", "specific_metadata": {"group_spacing": "number", "group_labels": "boolean"}}, {"name": "Lines", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 27a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z\" fill-opacity=\".2\" fill=\"#556367\"></path><path d=\"M4.5 14c0 .085-.007.168-.02.249l6.602 8.253c.094.005.185.019.273.04l6.157-4.233a1.5 1.5 0 0 1 2.771-.586l5.802 1.088a1.5 1.5 0 1 1-.369 1.966l-5.801-1.088a1.493 1.493 0 0 1-1.27.269l-6.157 4.233a1.5 1.5 0 1 1-2.967-.44l-6.603-8.253A1.5 1.5 0 1 1 4.5 14Z\" fill=\"#92CADC\"></path><path d=\"M27.118 4.495a1.5 1.5 0 1 0-1.591-1.211l-6.645 8.72c-.18.014-.35.06-.506.132l-5.95-2.604a1.5 1.5 0 0 0-2.911.68L2.955 18a1.5 1.5 0 1 0 1.53 1.288l6.56-7.79c.206-.006.402-.053.579-.135l5.95 2.604a1.5 1.5 0 0 0 2.9-.752l6.644-8.72Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"line_style": ["solid", "dashed", "dotted"], "marker_style": ["circle", "square", "none"]}}, {"name": "Multiple Lines", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 27a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm0-14a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm14 14a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H17a1 1 0 0 1-1-1Zm0-14a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H17a1 1 0 0 1-1-1Z\" fill-opacity=\".2\" fill=\"#556367\"></path><path d=\"M27.257 18.967a1 1 0 1 0-1.248-.832l-2.266 3.398a.997.997 0 0 0-.203.079l-2.716-.68a.999.999 0 0 0-1.751.193l-1.817.908a1 1 0 1 0 .671 1.342l1.817-.908a1 1 0 0 0 .716-.079l2.716.68a.999.999 0 0 0 1.815-.703l2.266-3.398ZM26.47 4.151l-1.694-.282a1 1 0 0 0-1.775.68l-3.109 3.885-1.904-1.587A1 1 0 1 0 17.028 8l1.984 1.653a1 1 0 0 0 1.987-.201l3.173-3.967c.13-.022.25-.07.357-.136l1.695.282a1 1 0 1 0 .247-1.48ZM6.998 19.437l3.065 3.065a.998.998 0 0 1 .194.031l1.816-.908a1 1 0 1 1 .67 1.342l-1.816.908a1 1 0 0 1-1.925-.312l-3.065-3.065a1 1 0 0 1-.194-.031l-1.816.908a1 1 0 1 1-.67-1.342l1.816-.908a1 1 0 0 1 1.925.312Zm6.259-15.47a1 1 0 1 0-1.248-.833l-2.266 3.4a.993.993 0 0 0-.203.078l-2.716-.68A.999.999 0 0 0 5 6.5v.014L2.861 9.01A1 1 0 1 0 4 10v-.014L6.139 7.49a.993.993 0 0 0 .321-.102l2.716.68a.999.999 0 0 0 1.815-.702l2.266-3.4Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"line_style": ["solid", "dashed", "dotted"], "marker_style": ["circle", "square", "none"], "line_labels": "boolean"}}, {"name": "Area Chart", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 27a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z\" fill-opacity=\".2\" fill=\"#556367\"></path><path d=\"M27.118 4.495a1.5 1.5 0 1 0-1.591-1.211l-6.645 8.72c-.18.014-.35.06-.506.132l-5.95-2.604a1.5 1.5 0 0 0-2.911.68L2.955 18a1.5 1.5 0 1 0 1.53 1.288l6.56-7.79c.206-.006.402-.053.579-.135l5.95 2.604a1.5 1.5 0 0 0 2.9-.752l6.644-8.72Z\" fill=\"#1D81A2\"></path><path d=\"M4.5 14c0 .085-.007.168-.02.249l6.602 8.253c.094.005.185.019.273.04l6.157-4.233a1.5 1.5 0 0 1 2.771-.586l5.802 1.088a1.5 1.5 0 1 1-.369 1.966l-5.801-1.088a1.493 1.493 0 0 1-1.27.269l-6.157 4.233a1.5 1.5 0 1 1-2.967-.44l-6.603-8.253A1.5 1.5 0 1 1 4.5 14Z\" fill=\"#92CADC\"></path></svg>", "specific_metadata": {"area_opacity": "number", "area_labels": "boolean"}, "czech_name": "Plošný graf"}, {"name": "Scatter Plot", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 27a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z\" fill-opacity=\".2\" fill=\"#556367\"></path><path d=\"M7.5 14a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm15-5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Zm-10 10a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"point_size": "number", "point_shape": ["circle", "square", "triangle"]}, "czech_name": "Bodov<PERSON> graf"}, {"name": "Dot Plot", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 27a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z\" fill-opacity=\".2\" fill=\"#556367\"></path><path d=\"M7.5 14a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm15-5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Zm-10 10a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"dot_size": "number", "dot_spacing": "number"}, "czech_name": "Tečkový graf"}, {"name": "Range Plot", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 27a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z\" fill-opacity=\".2\" fill=\"#556367\"></path><path d=\"M7.5 14a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm15-5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Zm-10 10a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"range_style": ["bar", "line"], "range_labels": "boolean"}, "czech_name": "Rozsahov<PERSON> graf"}, {"name": "Arrow Plot", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 27a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z\" fill-opacity=\".2\" fill=\"#556367\"></path><path d=\"M7.5 14a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm15-5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Zm-10 10a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"arrow_direction": ["left", "right", "up", "down"], "arrow_size": "number"}, "czech_name": "Šipkov<PERSON> graf"}, {"name": "Election Donut", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 27a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z\" fill-opacity=\".2\" fill=\"#556367\"></path><path d=\"M7.5 14a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm15-5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Zm-10 10a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"donut_thickness": "number", "donut_labels": "boolean"}, "czech_name": "Volební prstencový graf"}, {"name": "Pie Chart", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 27a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z\" fill-opacity=\".2\" fill=\"#556367\"></path><path d=\"M7.5 14a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm15-5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Zm-10 10a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"pie_labels": ["inside", "outside", "none"], "pie_rotation": "number"}, "czech_name": "Koláčový graf"}, {"name": "Multiple Pies", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 27a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z\" fill-opacity=\".2\" fill=\"#556367\"></path><path d=\"M7.5 14a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm15-5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Zm-10 10a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"pie_spacing": "number", "pie_labels": ["inside", "outside", "none"]}, "czech_name": "<PERSON><PERSON><PERSON> koláčových grafů"}, {"name": "Donut Chart", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 27a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z\" fill-opacity=\".2\" fill=\"#556367\"></path><path d=\"M7.5 14a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm15-5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Zm-10 10a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"donut_thickness": "number", "donut_labels": ["inside", "outside", "none"]}, "czech_name": "Prstencov<PERSON> graf"}, {"name": "Multiple Donuts", "icon": "<svg viewBox=\"0 0 30 30\" height=\"30\" width=\"30\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2 27a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z\" fill-opacity=\".2\" fill=\"#556367\"></path><path d=\"M7.5 14a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm15-5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Zm-10 10a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\" fill=\"#1D81A2\"></path></svg>", "specific_metadata": {"donut_spacing": "number", "donut_labels": ["inside", "outside", "none"]}, "czech_name": "<PERSON><PERSON><PERSON> gra<PERSON>"}, {"name": "Table", "specific_metadata": {"table_style": ["striped", "bordered", "plain"], "table_sorting": ["ascending", "descending", "none"]}}], "common_metadata": {"title": {"text": "string", "font_size": "number", "font_family": "string", "czech_name": "Název grafu"}, "legend": {"position": ["top", "bottom", "left", "right", "none"], "font_size": "number", "czech_name": "<PERSON>a"}, "colors": {"palette": "string", "custom_colors": "array", "czech_name": "Barvy"}, "axes": {"x_axis": {"label": "string", "font_size": "number", "czech_name": "Osa X"}, "y_axis": {"label": "string", "font_size": "number", "czech_name": "<PERSON><PERSON>"}}, "annotations": {"enabled": "boolean", "font_size": "number", "czech_name": "Anotace"}, "locale": {"language": ["cs-CZ", "en-US"], "decimal_separator": "string", "thousand_separator": "string", "czech_name": "Lokalizace"}, "layout": {"theme": ["Datawrapper", "Custom"], "dark_mode": "boolean", "dark_mode_colors": "boolean", "czech_name": "Rozložení"}, "footer": {"data_download": "boolean", "image_download": ["PNG", "PDF", "SVG"], "embed_link": "boolean", "attribution": "boolean", "czech_name": "Zápatí"}, "sharing": {"social_buttons": "boolean", "site_url": "string", "chart_url": "string", "czech_name": "Sdílení"}, "metadata": {"description": "string", "data_source": "string", "source_url": "string", "byline": "string", "alt_text": "string", "czech_name": "<PERSON><PERSON><PERSON>"}, "text_annotations": {"highlight_range": "boolean", "czech_name": "Textové anotace"}, "export": {"image_format": ["PNG", "JPG", "SVG", "PDF"], "width": "number", "height": "number", "border_size": "number", "scale_factor": "number", "include": ["full", "chart_only"], "background": ["white", "transparent"], "czech_name": "Export obrázku"}}}