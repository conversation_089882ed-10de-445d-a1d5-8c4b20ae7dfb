"""
LimeSurvey Question Type Analyzer
Analyzuje typy otázek z LSS struktury a doporučuje vhodné typy grafů
"""

import json
from typing import Dict, Optional, List, Any
from logger import get_logger

logger = get_logger(__name__)

class LSSQuestionAnalyzer:
    """Analyzuje typy otázek z LSS struktury"""
    
    # Mapování typů LimeSurvey otázek na typy grafů a dat
    QUESTION_TYPE_MAPPING = {
        # Jednoduché výběry
        'L': {
            'chart_type': 'column-chart', 
            'data_type': 'categorical',
            'datawrapper_type': 'd3-bars',
            'description': 'List (radio buttons)'
        },
        '!': {
            'chart_type': 'column-chart', 
            'data_type': 'categorical',
            'datawrapper_type': 'd3-bars',
            'description': 'List (dropdown)'
        },
        'Y': {
            'chart_type': 'pie-chart', 
            'data_type': 'binary',
            'datawrapper_type': 'd3-pies',
            'description': 'Yes/No'
        },
        'G': {
            'chart_type': 'pie-chart', 
            'data_type': 'categorical',
            'datawrapper_type': 'd3-pies',
            'description': 'Gender'
        },
        '5': {
            'chart_type': 'column-chart', 
            'data_type': 'scale',
            'datawrapper_type': 'd3-bars',
            'description': '5-point choice'
        },
        
        # Více možností
        'M': {
            'chart_type': 'grouped-column-chart', 
            'data_type': 'multiple_choice',
            'datawrapper_type': 'd3-bars-stacked',
            'description': 'Multiple choice'
        },
        'P': {
            'chart_type': 'grouped-column-chart', 
            'data_type': 'multiple_choice',
            'datawrapper_type': 'd3-bars-stacked',
            'description': 'Multiple choice with comments'
        },
        
        # Pole otázek (Arrays)
        'F': {
            'chart_type': 'stacked-column-chart', 
            'data_type': 'array',
            'datawrapper_type': 'd3-bars-stacked',
            'description': 'Array'
        },
        'A': {
            'chart_type': 'stacked-column-chart', 
            'data_type': 'array_scale',
            'datawrapper_type': 'd3-bars-stacked',
            'description': 'Array (5-point choice)'
        },
        'B': {
            'chart_type': 'stacked-column-chart', 
            'data_type': 'array_scale',
            'datawrapper_type': 'd3-bars-stacked',
            'description': 'Array (10-point choice)'
        },
        'H': {
            'chart_type': 'diverging-bar-chart', 
            'data_type': 'array_scale',
            'datawrapper_type': 'd3-bars-split',
            'description': 'Array (increase/same/decrease)'
        },
        'E': {
            'chart_type': 'stacked-column-chart', 
            'data_type': 'array_choice',
            'datawrapper_type': 'd3-bars-stacked',
            'description': 'Array (yes/no/uncertain)'
        },
        'C': {
            'chart_type': 'stacked-column-chart', 
            'data_type': 'array_binary',
            'datawrapper_type': 'd3-bars-stacked',
            'description': 'Array (yes/no)'
        },
        '1': {
            'chart_type': 'grouped-column-chart', 
            'data_type': 'array_dual',
            'datawrapper_type': 'd3-bars-grouped',
            'description': 'Array dual scale'
        },
        
        # Číselné
        'N': {
            'chart_type': 'histogram', 
            'data_type': 'numerical',
            'datawrapper_type': 'd3-bars',
            'description': 'Numerical input'
        },
        'K': {
            'chart_type': 'scatter-plot', 
            'data_type': 'multiple_numerical',
            'datawrapper_type': 'd3-scatter-plot',
            'description': 'Multiple numerical input'
        },
        ':': {
            'chart_type': 'line-chart', 
            'data_type': 'array_numerical',
            'datawrapper_type': 'd3-lines',
            'description': 'Array (numbers)'
        },
        
        # Texty
        'S': {
            'chart_type': 'word-cloud', 
            'data_type': 'text',
            'datawrapper_type': 'word-cloud',
            'description': 'Short free text'
        },
        'U': {
            'chart_type': 'word-cloud', 
            'data_type': 'long_text',
            'datawrapper_type': 'word-cloud',
            'description': 'Huge free text'
        },
        'Q': {
            'chart_type': 'table', 
            'data_type': 'multiple_text',
            'datawrapper_type': 'table',
            'description': 'Multiple short text'
        },
        ';': {
            'chart_type': 'table', 
            'data_type': 'array_text',
            'datawrapper_type': 'table',
            'description': 'Array (texts)'
        },
        
        # Speciální
        'R': {
            'chart_type': 'ranking-chart', 
            'data_type': 'ranking',
            'datawrapper_type': 'd3-bars',
            'description': 'Ranking'
        },
        'D': {
            'chart_type': 'timeline', 
            'data_type': 'date',
            'datawrapper_type': 'd3-lines',
            'description': 'Date'
        },
        'O': {
            'chart_type': 'column-chart', 
            'data_type': 'categorical_comment',
            'datawrapper_type': 'd3-bars',
            'description': 'List with comment'
        },
        
        # Bez grafu
        'X': {
            'chart_type': None, 
            'data_type': 'info',
            'datawrapper_type': None,
            'description': 'Boilerplate question'
        },
        'I': {
            'chart_type': None, 
            'data_type': 'control',
            'datawrapper_type': None,
            'description': 'Language switch'
        },
        'T': {
            'chart_type': None, 
            'data_type': 'text_input',
            'datawrapper_type': None,
            'description': 'Text display'
        }
    }
    
    def __init__(self):
        """Inicializace analyzeru"""
        self.logger = logger
    
    def analyze_question(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyzuje otázku a vrátí doporučený typ grafu
        
        Args:
            question_data: Data otázky z LSS struktury
            
        Returns:
            Dict s analýzou otázky
        """
        try:
            question_type = question_data.get('type', '')
            mapping = self.QUESTION_TYPE_MAPPING.get(question_type, {})
            
            analysis = {
                'limesurvey_type': question_type,
                'chart_type': mapping.get('chart_type'),
                'data_type': mapping.get('data_type'),
                'datawrapper_type': mapping.get('datawrapper_type'),
                'description': mapping.get('description', f'Unknown type: {question_type}'),
                'requires_special_processing': self._requires_special_processing(question_type),
                'has_subquestions': self._has_subquestions(question_data),
                'scale_info': self._get_scale_info(question_data),
                'answer_options': self._get_answer_options(question_data),
                'subquestions': self._get_subquestions(question_data),
                'is_array': self._is_array_question(question_type),
                'supports_chart': mapping.get('chart_type') is not None
            }
            
            self.logger.debug(f"Analyzována otázka typu '{question_type}': {analysis['description']}")
            return analysis
            
        except Exception as e:
            self.logger.error(f"Chyba při analýze otázky: {str(e)}")
            return self._get_default_analysis(question_data)
    
    def analyze_lss_structure(self, lss_path: str) -> Dict[str, Any]:
        """
        Analyzuje celou LSS strukturu
        
        Args:
            lss_path: Cesta k LSS souboru
            
        Returns:
            Dict s analýzou celé struktury
        """
        try:
            with open(lss_path, 'r', encoding='utf-8') as f:
                structure = json.load(f)
            
            analysis = {
                'survey_id': structure.get('survey_id'),
                'total_questions': 0,
                'chartable_questions': 0,
                'question_types': {},
                'questions': []
            }
            
            for group in structure.get('groups', []):
                for question in group.get('questions', []):
                    question_analysis = self.analyze_question(question)
                    
                    # Přidání informací o skupině
                    question_analysis['group_name'] = group.get('group_name', '')
                    question_analysis['group_id'] = group.get('gid', '')
                    question_analysis['question_id'] = question.get('qid', '')
                    question_analysis['question_title'] = question.get('title', '')
                    question_analysis['question_text'] = question.get('question', '')
                    
                    analysis['questions'].append(question_analysis)
                    analysis['total_questions'] += 1
                    
                    if question_analysis['supports_chart']:
                        analysis['chartable_questions'] += 1
                    
                    # Počítání typů otázek
                    q_type = question_analysis['limesurvey_type']
                    if q_type in analysis['question_types']:
                        analysis['question_types'][q_type] += 1
                    else:
                        analysis['question_types'][q_type] = 1
            
            self.logger.info(f"Analyzována LSS struktura: {analysis['total_questions']} otázek, "
                           f"{analysis['chartable_questions']} vhodných pro grafy")
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Chyba při analýze LSS struktury: {str(e)}")
            return {'error': str(e)}
    
    def get_recommended_charts(self, lss_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Vrátí seznam doporučených grafů pro analyzovanou strukturu
        
        Args:
            lss_analysis: Výsledek analýzy LSS struktury
            
        Returns:
            Seznam doporučených grafů
        """
        recommended = []
        
        for question in lss_analysis.get('questions', []):
            if question['supports_chart']:
                recommended.append({
                    'question_id': question['question_id'],
                    'question_title': question['question_title'],
                    'question_text': question['question_text'],
                    'group_name': question['group_name'],
                    'chart_type': question['chart_type'],
                    'datawrapper_type': question['datawrapper_type'],
                    'data_type': question['data_type'],
                    'priority': self._get_chart_priority(question)
                })
        
        # Seřazení podle priority
        recommended.sort(key=lambda x: x['priority'], reverse=True)
        
        return recommended
    
    def _requires_special_processing(self, question_type: str) -> bool:
        """Určuje, zda typ otázky vyžaduje speciální zpracování"""
        special_types = ['F', 'A', 'B', 'H', 'E', 'C', '1', 'M', 'P', 'R', 'N', 'K', ':', 'S', 'U']
        return question_type in special_types
    
    def _has_subquestions(self, question_data: Dict[str, Any]) -> bool:
        """Kontroluje, zda otázka má podotázky"""
        properties = question_data.get('properties', {})
        subquestions = properties.get('subquestions', '')
        return subquestions != 'No available answers' and subquestions != ''
    
    def _get_subquestions(self, question_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Získává seznam podotázek s jejich texty"""
        properties = question_data.get('properties', {})
        subquestions = properties.get('subquestions', {})
        
        if isinstance(subquestions, dict) and subquestions != 'No available answers':
            return [{'code': k, 'text': v} for k, v in subquestions.items()]
        
        return []
    
    def _get_scale_info(self, question_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Získává informace o škále otázky"""
        question_type = question_data.get('type', '')
        
        if question_type == '5':
            return {'min': 1, 'max': 5, 'type': '5-point'}
        elif question_type == 'A':
            return {'min': 1, 'max': 5, 'type': '5-point-array'}
        elif question_type == 'B':
            return {'min': 1, 'max': 10, 'type': '10-point-array'}
        elif question_type == 'H':
            return {'values': ['increase', 'same', 'decrease'], 'type': 'increase-same-decrease'}
        elif question_type == 'E':
            return {'values': ['yes', 'no', 'uncertain'], 'type': 'yes-no-uncertain'}
        elif question_type == 'C':
            return {'values': ['yes', 'no'], 'type': 'yes-no'}
        
        return None
    
    def _get_answer_options(self, question_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Získává možnosti odpovědí"""
        properties = question_data.get('properties', {})
        available_answers = properties.get('available_answers', {})
        
        if isinstance(available_answers, dict) and available_answers != 'No available answers':
            return [{'code': k, 'text': v} for k, v in available_answers.items()]
        
        return []
    
    def _is_array_question(self, question_type: str) -> bool:
        """Určuje, zda se jedná o pole otázek"""
        array_types = ['F', 'A', 'B', 'H', 'E', 'C', '1', ':', ';']
        return question_type in array_types
    
    def _get_chart_priority(self, question_analysis: Dict[str, Any]) -> int:
        """Určuje prioritu grafu (vyšší číslo = vyšší priorita)"""
        data_type = question_analysis['data_type']
        
        # Priorita podle typu dat
        priority_map = {
            'categorical': 10,
            'binary': 9,
            'scale': 8,
            'multiple_choice': 7,
            'array_scale': 6,
            'array': 5,
            'numerical': 4,
            'ranking': 3,
            'text': 2,
            'date': 1
        }
        
        return priority_map.get(data_type, 0)
    
    def _get_default_analysis(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
        """Vrátí výchozí analýzu pro neznámý typ otázky"""
        return {
            'limesurvey_type': question_data.get('type', 'unknown'),
            'chart_type': 'column-chart',
            'data_type': 'categorical',
            'datawrapper_type': 'd3-bars',
            'description': 'Unknown question type - using default',
            'requires_special_processing': False,
            'has_subquestions': False,
            'scale_info': None,
            'answer_options': [],
            'is_array': False,
            'supports_chart': True
        }