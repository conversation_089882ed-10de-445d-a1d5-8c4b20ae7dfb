#!/usr/bin/env python3
"""
Translation Manager - <PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> a úprav názvů pro grafy
"""

import json
import os
from typing import Dict, Any, List
from logger import get_logger

logger = get_logger(__name__)

class TranslationManager:
    """<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> překladů a úprav názvů"""
    
    def __init__(self, survey_id: str, language: str = None):
        self.survey_id = survey_id
        # Použití PathManageru pro správné cesty
        from path_manager import path_manager
        self.survey_dir = path_manager.get_data_path(survey_id)
        self.current_language = language or self._get_default_language()
        self.translation_file = self._get_translation_file_path(self.current_language)
        self.translations = self._load_translations()
    
    def _get_default_language(self) -> str:
        """Získá výchozí jazyk z existujících překladů nebo vrátí cs-CZ"""
        try:
            # Zkusíme najít existující translations.json (starý formát)
            old_file = os.path.join(self.survey_dir, "translations.json")
            if os.path.exists(old_file):
                with open(old_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('language_settings', {}).get('chart_language', 'cs-CZ')
        except:
            pass
        return 'cs-CZ'

    def _get_translation_file_path(self, language: str) -> str:
        """Vrátí cestu k souboru překladů pro daný jazyk"""
        return os.path.join(self.survey_dir, f"translations_{language}.json")

    def get_available_translation_files(self) -> Dict[str, str]:
        """Vrátí seznam existujících souborů překladů"""
        files = {}
        if not os.path.exists(self.survey_dir):
            return files

        for filename in os.listdir(self.survey_dir):
            if filename.startswith('translations_') and filename.endswith('.json'):
                # Extrahuj jazyk z názvu souboru
                language = filename.replace('translations_', '').replace('.json', '')
                files[language] = os.path.join(self.survey_dir, filename)

        # Zkontroluj také starý formát
        old_file = os.path.join(self.survey_dir, "translations.json")
        if os.path.exists(old_file):
            files['legacy'] = old_file

        return files

    def _load_translations(self) -> Dict[str, Any]:
        """Načte existující překlady nebo vytvoří prázdný slovník"""
        if os.path.exists(self.translation_file):
            try:
                with open(self.translation_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Chyba při načítání překladů: {e}")
        
        return {
            "metadata": {
                "survey_id": self.survey_id,
                "language": self.current_language,
                "created": "auto-generated",
                "version": "3.0",
                "structure": "categorized_for_llm_safety"
            },
            "language_settings": {
                "chart_language": self.current_language,
                "available_languages": {
                    "cs-CZ": "Čeština",
                    "en-US": "English (US)",
                    "en-GB": "English (UK)",
                    "de-DE": "Deutsch",
                    "fr-FR": "Français",
                    "es-ES": "Español"
                }
            },
            "question_names": {},           # Názvy otázek
            "subquestions": {},             # Podotázky
            "scale_responses": {},          # Škálové odpovědi (rozhodně ano, spíše ano, atd.)
            "choice_responses": {},         # Jednoduché volby (Ano/Ne)
            "open_text_responses": {},      # Volné textové odpovědi (bez rozdělení safe/personal)
            "chart_titles": {}              # Vlastní názvy grafů
        }
    
    def save_translations(self) -> bool:
        """Uloží překlady do souboru"""
        try:
            os.makedirs(self.survey_dir, exist_ok=True)
            with open(self.translation_file, 'w', encoding='utf-8') as f:
                json.dump(self.translations, f, ensure_ascii=False, indent=2)
            logger.info(f"Překlady uloženy do {self.translation_file}")
            return True
        except Exception as e:
            logger.error(f"Chyba při ukládání překladů: {e}")
            return False
    
    def extract_translatable_strings(self, chart_data_path: str) -> Dict[str, List[str]]:
        """Extrahuje všechny přeložitelné řetězce z chart_data.json"""
        try:
            with open(chart_data_path, 'r', encoding='utf-8') as f:
                chart_data = json.load(f)
            
            # Načteme question_mapping.csv pro správné mapování subotázek
            question_mapping = self._load_question_mapping()
            
            strings = {
                "question_names": set(),
                "subquestions": set(),
                "scale_responses": set(),
                "choice_responses": set(),
                "free_text_responses": {}  # Bude obsahovat {question_code: {"safe": [], "personal": []}}
            }
            
            for item in chart_data:
                question_code = item.get('code', 'unknown')

                # Názvy otázek
                if 'name' in item and item['name'] is not None:
                    # Kontrola na NaN hodnoty
                    name_value = item['name']
                    if isinstance(name_value, str):
                        strings["question_names"].add(name_value)
                    elif hasattr(name_value, '__str__') and str(name_value) != 'nan':
                        strings["question_names"].add(str(name_value))

                # Data podle typu
                if item.get('type') == 'array' and 'data' in item:
                    main_question_code = item.get('code', '')
                    for i, data_item in enumerate(item['data']):
                        # Podotázky - mapujeme na kódy z question_mapping
                        if 'subquestion' in data_item and data_item['subquestion'] is not None:
                            subq_value = data_item['subquestion']
                            if isinstance(subq_value, str):
                                # Najdeme odpovídající kód subotázky
                                subq_code = self._find_subquestion_code(main_question_code, subq_value, i, question_mapping)
                                if subq_code:
                                    strings["subquestions"].add(subq_code)
                            elif hasattr(subq_value, '__str__') and str(subq_value) not in ['nan', 'None', '']:
                                subq_code = self._find_subquestion_code(main_question_code, str(subq_value), i, question_mapping)
                                if subq_code:
                                    strings["subquestions"].add(subq_code)

                        # Odpovědi z array otázek - kategorizace
                        if 'responses' in data_item:
                            for response_label in data_item['responses'].keys():
                                self._categorize_response(response_label, strings, question_code)

                elif 'data' in item:
                    # Jiné typy otázek
                    for data_item in item['data']:
                        # Labels pro single/multiple choice
                        if 'label' in data_item and data_item['label'] is not None:
                            self._categorize_response(data_item['label'], strings, question_code)

                        # Value labels (pro škálové otázky)
                        if 'value' in data_item and data_item['value'] is not None:
                            # Zpracujeme pouze řetězcové hodnoty nebo převeditelné na řetězec
                            value = data_item['value']
                            if isinstance(value, str):
                                self._categorize_response(value, strings, question_code)
                            elif hasattr(value, '__str__') and str(value) not in ['nan', 'None', '']:
                                self._categorize_response(str(value), strings, question_code)

                        # Responses (pokud jsou na této úrovni)
                        if 'responses' in data_item:
                            for response_label in data_item['responses'].keys():
                                self._categorize_response(response_label, strings, question_code)
            
            # Převod na seznamy (kromě free_text_responses)
            result = {}
            for category in ['question_names', 'subquestions', 'scale_responses', 'choice_responses']:
                result[category] = sorted(list(strings[category]))
            result['free_text_responses'] = strings['free_text_responses']

            logger.info(f"Extrahováno: {len(result['question_names'])} názvů otázek, "
                       f"{len(result['subquestions'])} podotázek, "
                       f"{len(result['scale_responses'])} škálových odpovědí, "
                       f"{len(result['choice_responses'])} jednoduchých odpovědí, "
                       f"{len(result['free_text_responses'])} otázek s volným textem")

            return result
            
        except Exception as e:
            logger.error(f"Chyba při extrakci řetězců: {e}")
            return {"question_names": [], "subquestions": [], "scale_responses": [],
                   "choice_responses": [], "free_text_responses": {}}

    def _load_question_mapping(self) -> Dict[str, str]:
        """Načte question_mapping.csv pro mapování subotázek"""
        try:
            import pandas as pd
            mapping_path = os.path.join(self.survey_dir, "question_mapping.csv")
            if os.path.exists(mapping_path):
                df = pd.read_csv(mapping_path)
                # Vytvoříme mapování question_name -> question_code
                mapping = {}
                for _, row in df.iterrows():
                    if pd.notna(row['question_name']) and pd.notna(row['question_code']):
                        mapping[row['question_name']] = row['question_code']
                return mapping
        except Exception as e:
            logger.warning(f"Nepodařilo se načíst question_mapping.csv: {e}")
        return {}
    
    def _load_question_mapping_reverse(self) -> Dict[str, str]:
        """Načte question_mapping.csv pro mapování kódů na názvy"""
        try:
            import pandas as pd
            mapping_path = os.path.join(self.survey_dir, "question_mapping.csv")
            if os.path.exists(mapping_path):
                df = pd.read_csv(mapping_path)
                # Vytvoříme mapování question_code -> question_name
                mapping = {}
                for _, row in df.iterrows():
                    if pd.notna(row['question_name']) and pd.notna(row['question_code']):
                        # Kontrola na NaN hodnoty a převod na string
                        question_name = str(row['question_name'])
                        question_code = str(row['question_code'])
                        if question_name not in ['nan', 'None', ''] and question_code not in ['nan', 'None', '']:
                            mapping[question_code] = question_name
                return mapping
        except Exception as e:
            logger.warning(f"Nepodařilo se načíst question_mapping.csv: {e}")
        return {}
    
    def _find_subquestion_code(self, main_question_code: str, subq_text: str, index: int, question_mapping: Dict[str, str]) -> str:
        """Najde kód subotázky podle textu a pozice"""
        # Nejprve zkusíme najít přesnou shodu v mapování
        if subq_text in question_mapping:
            return question_mapping[subq_text]
        
        # Pokud nenajdeme přesnou shodu, zkusíme najít podle pozice
        # Generujeme pravděpodobný kód subotázky
        sq_code = f"SQ{index+1:03d}"  # SQ001, SQ002, atd.
        expected_code = f"{main_question_code}[{sq_code}]"
        
        # Zkontrolujeme, zda tento kód existuje v mapování
        for name, code in question_mapping.items():
            if code == expected_code:
                return code
        
        # Fallback - vrátíme text jako je
        logger.warning(f"Nepodařilo se najít kód pro subotázku: {subq_text} (main: {main_question_code}, index: {index})")
        return subq_text

    def _categorize_response(self, response_label: str, strings: dict, question_code: str):
        """Kategorizuje odpověď podle typu pro bezpečný překlad"""
        import re
        
        # Kontrola na neřetězcové hodnoty (NaN, None, atd.)
        if response_label is None:
            return
        if not isinstance(response_label, str):
            # Pokud je to číslo nebo jiný typ, převedeme na řetězec
            if hasattr(response_label, '__str__'):
                str_value = str(response_label)
                if str_value in ['nan', 'None', '']:
                    return
                response_label = str_value
            else:
                return

        # Škálové odpovědi - bezpečné pro LLM
        scale_patterns = [
            r'^(rozhodně|určitě)\s+(ano|ne)$',
            r'^spíše\s+(ano|ne)$',
            r'^(neumím|nevím)\s+(to\s+)?(posoudit|říci)$',
            r'^(velmi\s+)?(spokojen|nespokojen|důležité|nedůležité)$',
            r'^(často|někdy|nikdy|vždy)$',
            r'^(vysoká|střední|nízká)\s+(priorita|důležitost)$'
        ]

        # Jednoduché volby - bezpečné pro LLM
        simple_choices = {
            'ano', 'ne', 'yes', 'no', 'nevím', "don't know", 'jiné', 'other',
            'muž', 'žena', 'male', 'female', 'veřejný sektor', 'soukromý sektor'
        }

        response_lower = response_label.lower().strip()

        # Kontrola škálových odpovědí
        for pattern in scale_patterns:
            if re.match(pattern, response_lower, re.IGNORECASE):
                strings["scale_responses"].add(response_label)
                return

        # Kontrola jednoduchých voleb
        if response_lower in simple_choices:
            strings["choice_responses"].add(response_label)
            return

        # Detekce osobních údajů a citlivých informací
        personal_data_patterns = [
            r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # Email
            r'\+?\d{3,}\s*\d{3,}\s*\d{3,}',                      # Telefon
            r'www\.|http[s]?://',                                # URL
            r'\d{5,}',                                           # Dlouhá čísla (IČO, atd.)
        ]

        is_personal = False
        for pattern in personal_data_patterns:
            if re.search(pattern, response_label, re.IGNORECASE):
                is_personal = True
                break

        # Detekce dlouhých textů (pravděpodobně specifické popisy organizací)
        is_long_text = len(response_label) > 50

        # Přidání do free_text_responses podle otázky
        if question_code not in strings["free_text_responses"]:
            strings["free_text_responses"][question_code] = {
                "safe_to_translate": [],
                "personal_data": []
            }

        if is_personal or is_long_text:
            strings["free_text_responses"][question_code]["personal_data"].append(response_label)
        else:
            # Krátké, obecné texty - pravděpodobně bezpečné
            strings["free_text_responses"][question_code]["safe_to_translate"].append(response_label)

    def _translate_response(self, response_label: str) -> str:
        """Přeloží odpověď podle nové kategorizované struktury"""
        # Zkusíme najít v škálových odpovědích
        if response_label in self.translations.get('scale_responses', {}):
            return self.translations['scale_responses'][response_label]

        # Zkusíme najít v jednoduchých volbách
        if response_label in self.translations.get('choice_responses', {}):
            return self.translations['choice_responses'][response_label]

        # Zkusíme najít v open text responses (všechny otázky)
        open_text_data = self.translations.get('open_text_responses', {})
        for question_code, question_data in open_text_data.items():
            if response_label in question_data:
                return question_data[response_label]

        # Pokud nenalezeno, vrátíme původní
        return response_label
    
    def generate_translation_template(self, chart_data_path: str) -> bool:
        """Vygeneruje šablonu pro překlad z chart_data.json"""
        try:
            strings = self.extract_translatable_strings(chart_data_path)
            
            # Načteme question_mapping pro získání skutečných názvů
            question_mapping_reverse = self._load_question_mapping_reverse()
            
            # Přidáme nové řetězce do překladů (pokud ještě nejsou)
            for category in ['question_names', 'subquestions', 'scale_responses', 'choice_responses']:
                if category not in self.translations:
                    self.translations[category] = {}

                if category in strings:
                    for item in strings[category]:
                        # Pro subotázky použijeme skutečné názvy z question_mapping
                        if category == 'subquestions' and item in question_mapping_reverse:
                            self.translations[category][item] = question_mapping_reverse[item]
                        elif item not in self.translations[category]:
                            self.translations[category][item] = item  # Výchozí = stejný text

            # Speciální zpracování open_text_responses (bez safe/personal rozdělení)
            if 'free_text_responses' in strings:
                if 'open_text_responses' not in self.translations:
                    self.translations['open_text_responses'] = {}

                for question_code, question_data in strings['free_text_responses'].items():
                    if question_code not in self.translations['open_text_responses']:
                        self.translations['open_text_responses'][question_code] = {}

                    # Všechny texty přidáme k překladu (bez rozdělení na safe/personal)
                    all_texts = []
                    all_texts.extend(question_data.get('safe_to_translate', []))
                    all_texts.extend(question_data.get('personal_data', []))

                    for text in all_texts:
                        if text not in self.translations['open_text_responses'][question_code]:
                            self.translations['open_text_responses'][question_code][text] = text
            
            return self.save_translations()
            
        except Exception as e:
            logger.error(f"Chyba při generování šablony: {e}")
            return False
    
    def apply_translations(self, chart_data_path: str, output_path: str = None) -> bool:
        """Aplikuje překlady na chart_data.json a vytvoří jazykovou verzi"""
        try:
            if output_path is None:
                # Nová logika: vytvoř chart_data_{jazyk}.json místo _translated.json
                language = self.translations.get('metadata', {}).get('language', 'cs-CZ')
                base_path = chart_data_path.replace('.json', '')
                output_path = f"{base_path}_{language}.json"
            
            with open(chart_data_path, 'r', encoding='utf-8') as f:
                chart_data = json.load(f)
            
            # Aplikace překladů
            translations_applied = 0

            for item in chart_data:
                # Názvy otázek
                if 'name' in item:
                    original_name = item['name']
                    if original_name in self.translations.get('question_names', {}):
                        new_name = self.translations['question_names'][original_name]
                        if new_name != original_name:  # Pouze pokud se liší
                            item['name'] = new_name
                            translations_applied += 1
                            logger.info(f"Přeložen název: '{original_name[:50]}...' → '{new_name[:50]}...'")
                        else:
                            logger.debug(f"Název zůstává stejný: '{original_name[:50]}...'")
                    else:
                        logger.debug(f"Název nenalezen v překladech: '{original_name[:50]}...'")
                        # Zkusíme najít podobný klíč
                        for key in self.translations.get('question_names', {}):
                            if key.strip() == original_name.strip():
                                new_name = self.translations['question_names'][key]
                                if new_name != key:
                                    item['name'] = new_name
                                    translations_applied += 1
                                    logger.info(f"Přeložen název (trim): '{original_name[:50]}...' → '{new_name[:50]}...'")
                                break


                
                # Data podle typu
                if item.get('type') == 'array' and 'data' in item:
                    for data_item in item['data']:
                        # Podotázky
                        if 'subquestion' in data_item:
                            original = data_item['subquestion']
                            if original in self.translations.get('subquestions', {}):
                                data_item['subquestion'] = self.translations['subquestions'][original]
                        
                        # Odpovědi - nová struktura
                        if 'responses' in data_item:
                            new_responses = {}
                            for response_label, count in data_item['responses'].items():
                                translated_label = self._translate_response(response_label)
                                if translated_label != response_label:
                                    translations_applied += 1
                                    logger.info(f"Přeložena odpověď: '{response_label}' → '{translated_label}'")
                                new_responses[translated_label] = count
                            data_item['responses'] = new_responses
                
                elif 'data' in item:
                    for data_item in item['data']:
                        # Labels pro jiné typy otázek
                        if 'label' in data_item:
                            original_label = data_item['label']
                            translated_label = self._translate_response(original_label)
                            if translated_label != original_label:
                                data_item['label'] = translated_label
                                translations_applied += 1
                                logger.info(f"Přeložen label: '{original_label}' → '{translated_label}'")
            
            # Uložení přeloženého souboru
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(chart_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Přeložená data uložena do {output_path}")
            logger.info(f"Celkem aplikováno {translations_applied} překladů")

            if translations_applied == 0:
                logger.warning("Žádné překlady nebyly aplikovány - zkontrolujte, zda jsou klíče v translations.json správné")

            return True
            
        except Exception as e:
            logger.error(f"Chyba při aplikaci překladů: {e}")
            return False
    
    def get_translation_stats(self) -> Dict[str, int]:
        """Vrátí statistiky překladů"""
        stats = {}

        # Základní kategorie
        for category in ['question_names', 'subquestions', 'scale_responses', 'choice_responses']:
            category_data = self.translations.get(category, {})
            total = len(category_data)
            translated = sum(1 for k, v in category_data.items() if k != v)
            stats[category] = {"total": total, "translated": translated}

        # Open text responses - počítáme všechny texty
        open_text_data = self.translations.get('open_text_responses', {})
        open_count = 0
        open_translated = 0

        for question_code, question_data in open_text_data.items():
            open_count += len(question_data)
            open_translated += sum(1 for k, v in question_data.items() if k != v)

        if open_count > 0:
            stats['open_text_responses'] = {"total": open_count, "translated": open_translated}

        return stats

    def get_chart_language(self) -> str:
        """Vrátí aktuální jazyk pro grafy"""
        return self.translations.get('language_settings', {}).get('chart_language', 'cs-CZ')

    def set_chart_language(self, language_code: str) -> bool:
        """Nastaví jazyk pro grafy"""
        try:
            if 'language_settings' not in self.translations:
                self.translations['language_settings'] = self._load_translations()['language_settings']

            available_languages = self.translations['language_settings'].get('available_languages', {})
            if language_code not in available_languages:
                logger.error(f"Nepodporovaný jazyk: {language_code}")
                return False

            self.translations['language_settings']['chart_language'] = language_code
            logger.info(f"Jazyk grafů nastaven na: {available_languages[language_code]} ({language_code})")
            return self.save_translations()

        except Exception as e:
            logger.error(f"Chyba při nastavování jazyka: {e}")
            return False

    def get_available_languages(self) -> Dict[str, str]:
        """Vrátí dostupné jazyky"""
        return self.translations.get('language_settings', {}).get('available_languages', {
            'cs-CZ': 'Čeština',
            'en-US': 'English (US)'
        })

    def switch_language(self, language: str) -> bool:
        """Přepne na jiný jazyk a načte jeho překlady"""
        try:
            if language not in self.get_available_languages():
                logger.error(f"Nepodporovaný jazyk: {language}")
                return False

            self.current_language = language
            self.translation_file = self._get_translation_file_path(language)
            self.translations = self._load_translations()

            logger.info(f"Přepnuto na jazyk: {language}")
            return True

        except Exception as e:
            logger.error(f"Chyba při přepínání jazyka: {e}")
            return False

    def create_translation_template_for_language(self, chart_data_path: str, language: str,
                                               overwrite: bool = False) -> bool:
        """Vytvoří šablonu pro konkrétní jazyk"""
        try:
            target_file = self._get_translation_file_path(language)

            # Kontrola existence souboru
            if os.path.exists(target_file) and not overwrite:
                logger.warning(f"Soubor {target_file} už existuje")
                return False

            # Dočasně přepneme na cílový jazyk
            original_language = self.current_language
            original_file = self.translation_file
            original_translations = self.translations

            self.current_language = language
            self.translation_file = target_file
            # Vytvoříme novou strukturu s metadaty pro cílový jazyk
            self.translations = self._load_translations()

            # Vygenerujeme šablonu
            result = self.generate_translation_template(chart_data_path)

            # Vrátíme původní nastavení
            self.current_language = original_language
            self.translation_file = original_file
            self.translations = original_translations

            return result

        except Exception as e:
            logger.error(f"Chyba při vytváření šablony pro jazyk {language}: {e}")
            return False

    def get_available_chart_languages(self, chart_data_base_path: str) -> Dict[str, str]:
        """Vrátí dostupné jazykové verze chart_data souborů"""
        available = {}

        # Původní verze (bez jazykového kódu)
        if os.path.exists(chart_data_base_path):
            available['original'] = 'Původní (bez překladů)'

        # Jazykové verze
        base_path = chart_data_base_path.replace('.json', '')

        for filename in os.listdir(os.path.dirname(chart_data_base_path)):
            if filename.startswith(os.path.basename(base_path) + '_') and filename.endswith('.json'):
                # Extrakce jazykového kódu z názvu souboru
                lang_code = filename.replace(os.path.basename(base_path) + '_', '').replace('.json', '')

                # Mapování jazykových kódů na názvy
                lang_names = {
                    'cs-CZ': 'Čeština',
                    'en-US': 'English (US)',
                    'en-GB': 'English (UK)',
                    'de-DE': 'Deutsch',
                    'fr-FR': 'Français',
                    'es-ES': 'Español'
                }

                lang_name = lang_names.get(lang_code, lang_code)
                available[lang_code] = lang_name

        return available

    def apply_translations_from_language(self, chart_data_path: str, language: str,
                                       output_path: str = None) -> bool:
        """Aplikuje překlady z konkrétního jazykového souboru"""
        try:
            translation_file = self._get_translation_file_path(language)

            if not os.path.exists(translation_file):
                logger.error(f"Soubor překladů pro jazyk {language} neexistuje: {translation_file}")
                return False

            # Dočasně načteme překlady z cílového souboru
            with open(translation_file, 'r', encoding='utf-8') as f:
                temp_translations = json.load(f)

            # Dočasně přepneme překlady
            original_translations = self.translations
            self.translations = temp_translations

            # Aplikujeme překlady
            result = self.apply_translations(chart_data_path, output_path)

            # Vrátíme původní překlady
            self.translations = original_translations

            return result

        except Exception as e:
            logger.error(f"Chyba při aplikaci překladů z jazyka {language}: {e}")
            return False
