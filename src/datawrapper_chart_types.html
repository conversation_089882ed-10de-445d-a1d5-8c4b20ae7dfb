<div class="block"><div class="grid svelte-68k4w1"><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M16 4H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h13a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1Zm11 8H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h24a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Zm-3 8H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h21a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Z" fill="#1D81A2"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Bar Chart</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 5a1 1 0 0 1 1-1h6v6H3a1 1 0 0 1-1-1V5Zm0 8a1 1 0 0 1 1-1h11v6H3a1 1 0 0 1-1-1v-4Zm0 8a1 1 0 0 1 1-1h14v6H3a1 1 0 0 1-1-1v-4Z" fill="#1D81A2"></path>
    <path d="M18 20h9a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-9v-6Zm-3-8h5a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-5v-6Zm-5-8h12a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H10V4Z" fill="#92CADC"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Stacked Bars</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3Zm0 15a1 1 0 0 1 1-1h18a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-3Z" fill="#1D81A2"></path>
    <path d="M2 9a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V9Zm0 15a1 1 0 0 1 1-1h24a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-3Z" fill="#92CADC"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Grouped Bars</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M7 4H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1Zm5 8H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h9a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Zm2 8H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Z" fill="#1D81A2"></path>
    <path d="M23 4h-4a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1Zm4 8h-8a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Zm-5 8h-3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Z" fill="#92CADC"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Split Bars</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 7a1 1 0 0 1 1-1h24a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7Zm0 11a1 1 0 0 1 1-1h9a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-5Z" fill="#92CADC"></path>
    <path d="M2 19h16a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H2v-3ZM2 8h9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H2V8Z" fill="#1D81A2"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Bullet Bars</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M4 15v12a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V15a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1Zm8-11v23a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1Zm8 4v19a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1Z" fill="#1D81A2"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Column Chart</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M21 28a1 1 0 0 1-1-1v-8h6v8a1 1 0 0 1-1 1h-4Zm-8 0a1 1 0 0 1-1-1V16h6v11a1 1 0 0 1-1 1h-4Zm-8 0a1 1 0 0 1-1-1v-5h6v5a1 1 0 0 1-1 1H5Z" fill="#1D81A2"></path>
    <path d="M20 18v-8a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v8h-6Zm-8-3V6a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v9h-6Zm-8 6v-8a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v8H4Z" fill="#92CADC"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Stacked Columns</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M3 28a1 1 0 0 1-1-1V16a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v11a1 1 0 0 1-1 1H3Zm15 0a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1h-3Z" fill="#1D81A2"></path>
    <path d="M9 28a1 1 0 0 1-1-1v-9a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1H9Zm15 0a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v22a1 1 0 0 1-1 1h-3Z" fill="#92CADC"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Grouped Columns</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 27a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z" fill-opacity=".2" fill="#556367"></path>
    <path d="M4.5 14c0 .085-.007.168-.02.249l6.602 8.253c.094.005.185.019.273.04l6.157-4.233a1.5 1.5 0 0 1 2.771-.586l5.802 1.088a1.5 1.5 0 1 1-.369 1.966l-5.801-1.088a1.493 1.493 0 0 1-1.27.269l-6.157 4.233a1.5 1.5 0 1 1-2.967-.44l-6.603-8.253A1.5 1.5 0 1 1 4.5 14Z" fill="#92CADC"></path>
    <path d="M27.118 4.495a1.5 1.5 0 1 0-1.591-1.211l-6.645 8.72c-.18.014-.35.06-.506.132l-5.95-2.604a1.5 1.5 0 0 0-2.911.68L2.955 18a1.5 1.5 0 1 0 1.53 1.288l6.56-7.79c.206-.006.402-.053.579-.135l5.95 2.604a1.5 1.5 0 0 0 2.9-.752l6.644-8.72Z" fill="#1D81A2"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Lines</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 27a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm0-14a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm14 14a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H17a1 1 0 0 1-1-1Zm0-14a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H17a1 1 0 0 1-1-1Z" fill-opacity=".2" fill="#556367"></path>
    <path d="M27.257 18.967a1 1 0 1 0-1.248-.832l-2.266 3.398a.997.997 0 0 0-.203.079l-2.716-.68a.999.999 0 0 0-1.751.193l-1.817.908a1 1 0 1 0 .671 1.342l1.817-.908a1 1 0 0 0 .716-.079l2.716.68a.999.999 0 0 0 1.815-.703l2.266-3.398ZM26.47 4.151l-1.694-.282a1 1 0 0 0-1.775.68l-3.109 3.885-1.904-1.587A1 1 0 1 0 17.028 8l1.984 1.653a1 1 0 0 0 1.987-.201l3.173-3.967c.13-.022.25-.07.357-.136l1.695.282a1 1 0 1 0 .247-1.48ZM6.998 19.437l3.065 3.065a.998.998 0 0 1 .194.031l1.816-.908a1 1 0 1 1 .67 1.342l-1.816.908a1 1 0 0 1-1.925-.312l-3.065-3.065a1 1 0 0 1-.194-.031l-1.816.908a1 1 0 1 1-.67-1.342l1.816-.908a1 1 0 0 1 1.925.312Zm6.259-15.47a1 1 0 1 0-1.248-.833l-2.266 3.4a.993.993 0 0 0-.203.078l-2.716-.68A.999.999 0 0 0 5 6.5v.014L2.861 9.01A1 1 0 1 0 4 10v-.014L6.139 7.49a.993.993 0 0 0 .321-.102l2.716.68a.999.999 0 0 0 1.815-.702l2.266-3.4Z" fill="#1D81A2"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Multiple Lines</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M10.537 9.95 19.5 12 28 4v12.5l-8.5 3-8.963-4L2 20.5V17l8.537-7.05Z" fill="#92CADC"></path>
    <path d="M2 22v3a1 1 0 0 0 1 1h24a1 1 0 0 0 1-1v-7l-8.5 3-9-4L2 22Z" fill="#1D81A2"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Area Chart</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M19 19a6 6 0 1 1-12 0 6 6 0 0 1 12 0Zm7-6a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm-3-8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" fill="#92CADC"></path>
    <path d="M9 10.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm-4 6a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm4 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm16-7a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm-9-10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm12-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z" fill="#1D81A2"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Scatter Plot</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 6a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm0 9a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm0 9a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z" fill-opacity=".2" fill="#556367"></path>
    <path d="M12 15a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Zm13-9a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Zm-7 18a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Z" fill="#1D81A2"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Dot Plot</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 6a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm0 9a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm0 9a1 1 0 0 1 1-1h24a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Z" fill-opacity=".2" fill="#556367"></path>
    <path d="M28 6a2.5 2.5 0 0 1-4.792 1H15.5a1 1 0 1 1 0-2h7.708A2.5 2.5 0 0 1 28 6Zm-15.5 8a1 1 0 1 0 0 2h4.658a2.5 2.5 0 1 0 0-2H12.5Zm-8 10a1 1 0 0 1 1-1h9.708a2.5 2.5 0 1 1 0 2H5.5a1 1 0 0 1-1-1Z" fill="#1D81A2"></path>
    <path d="M14 15a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Zm3-9a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0ZM7 24a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Z" fill="#92CADC"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Range Plot</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 6a1 1 0 0 1 1-1h24a1 1 0 0 1 0 2H3a1 1 0 0 1-1-1Zm0 9a1 1 0 0 1 1-1h24a1 1 0 0 1 0 2H3a1 1 0 0 1-1-1Zm0 9a1 1 0 0 1 1-1h24a1 1 0 0 1 0 2H3a1 1 0 0 1-1-1Z" fill-opacity=".2" fill="#556367"></path>
    <path d="M21.207 1.793 25.414 6l-4.207 4.207-1.414-1.414L21.586 7H11V5h10.586l-1.793-1.793 1.414-1.414Zm-12.414 9L4.586 15l4.207 4.207 1.414-1.414L8.414 16H14v-2H8.414l1.793-1.793-1.414-1.414Zm5.414 9-1.414 1.414L14.586 23H9v2h5.586l-1.793 1.793 1.414 1.414L18.414 24l-4.207-4.207Z" fill="#1D81A2"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Arrow Plot</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1 active"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M14.25 13.046a5.995 5.995 0 0 0-4.032 2.33l-6.954-4.012A13.99 13.99 0 0 1 14.25 5.02v8.026Zm13.237-.384-6.954 4.012A5.98 5.98 0 0 1 21 19c0 .762-.142 1.492-.402 2.163l7.07 3.806A13.946 13.946 0 0 0 29 19a13.94 13.94 0 0 0-1.513-6.338ZM2.333 24.97l7.069-3.807A5.972 5.972 0 0 1 9 19c0-.825.166-1.61.467-2.326l-6.954-4.012A13.94 13.94 0 0 0 1 19c0 2.135.478 4.159 1.333 5.97Z" fill="#1D81A2"></path>
    <path d="M15.75 5.02v8.026a5.995 5.995 0 0 1 4.032 2.33l6.954-4.012A13.99 13.99 0 0 0 15.75 5.02Z" fill="#92CADC"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Election Donut</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M25.32 21.128A11.944 11.944 0 0 0 27 15c0-6.376-4.972-11.59-11.25-11.977v11.58l9.57 6.525Z" fill="#92CADC"></path>
    <path d="M14.25 3.023C7.972 3.41 3 8.624 3 15c0 6.627 5.373 12 12 12a11.98 11.98 0 0 0 9.473-4.633l-10.223-6.97V3.022Z" fill="#1D81A2"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Pie Chart</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.5 28a5.5 5.5 0 0 0 4.227-9.02L7 23.707v-6.685A5.5 5.5 0 0 0 7.5 28Zm10.369-2.532L22 22.256v-5.233a5.5 5.5 0 0 0-4.131 8.445Zm-6.352-14.211A5.5 5.5 0 1 1 7 2.022v5.723l4.517 3.512ZM22.5 12.978A5.5 5.5 0 0 0 27.98 7L22 8.632V2a5.5 5.5 0 0 0 .5 10.978Z" fill="#1D81A2"></path>
    <path d="M8 17.022v4.27l3.02-3.019A5.475 5.475 0 0 0 8 17.023ZM22.5 28a5.5 5.5 0 0 0 .5-10.977v5.722l-4.517 3.512A5.485 5.485 0 0 0 22.5 28ZM12.131 10.468A5.5 5.5 0 0 0 8 2.022v5.233l4.131 3.213ZM23 2v5.323l4.803-1.31A5.504 5.504 0 0 0 23 2Z" fill="#92CADC"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Multiple Pies</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M27 15c0 2.239-.613 4.334-1.68 6.128l-5.812-3.962a5.001 5.001 0 0 0-3.758-7.11V3.023C22.028 3.41 27 8.624 27 15Z" fill="#92CADC"></path>
    <path d="M3 15C3 8.624 7.972 3.41 14.25 3.023v7.033a5.001 5.001 0 1 0 4.412 8.348l5.811 3.963A11.98 11.98 0 0 1 15 27C8.373 27 3 21.627 3 15Z" fill="#1D81A2"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Donut Chart</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.5 13c1.585 0 3.013-.67 4.017-1.743L9.127 9.4A2.5 2.5 0 1 1 7 5.05V2.022A5.5 5.5 0 0 0 7.5 13Zm5.5 9.5a5.5 5.5 0 1 1-6-5.478v3.028a2.501 2.501 0 1 0 2.586 1.071l2.14-2.14A5.477 5.477 0 0 1 13 22.5Zm7.26 1.11-2.391 1.858A5.5 5.5 0 0 1 22 17.023v3.028a2.501 2.501 0 0 0-1.74 3.56ZM28 7.478A5.5 5.5 0 1 1 22 2v3.05a2.5 2.5 0 1 0 2.98 2.77l3-.819c.013.157.02.316.02.477Z" fill="#1D81A2"></path>
    <path d="M13 7.5a5.474 5.474 0 0 1-.869 2.968L9.741 8.61A2.5 2.5 0 0 0 8 5.05V2.022A5.5 5.5 0 0 1 12.999 7.5ZM8 20.05v-3.028a5.475 5.475 0 0 1 3.02 1.251l-2.141 2.141c-.263-.174-.56-.3-.879-.364Zm20 2.45a5.5 5.5 0 0 1-9.517 3.757l2.39-1.858A2.5 2.5 0 1 0 23 20.05v-3.027a5.5 5.5 0 0 1 5 5.477ZM23 5.05V2a5.504 5.504 0 0 1 4.803 4.013l-2.901.791A2.504 2.504 0 0 0 23 5.05Z" fill="#92CADC"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Multiple Donuts</div> </button><button data-uid="vis-type-button" class="vis-thumb svelte-68k4w1"> <svg viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
    <path clip-rule="evenodd" d="M3 3a1 1 0 0 0-1 1v22a1 1 0 0 0 1 1h24a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H3Zm4 4H4v3h3V7Zm2 0v3h3V7H9Zm5 0v3h12V7H14Zm12 5H14v3h12v-3Zm0 5H14v3h12v-3Zm0 5H14v3h12v-3Zm-14 3v-3H9v3h3Zm-5 0v-3H4v3h3Zm-3-5h3v-3H4v3Zm0-5h3v-3H4v3Zm5 0v-3h3v3H9Zm0 2h3v3H9v-3Z" fill-rule="evenodd" fill="#60ACC6"></path>
    <path d="M15 23h4v1h-4v-1Zm0-5h10v1H15v-1Zm0-10h8v1h-8V8Zm0 5h6v1h-6v-1ZM2 4a1 1 0 0 1 1-1h24a1 1 0 0 1 1 1v3H2V4Z" fill="#1D81A2"></path>
  </svg>
   <div class="vis-title svelte-68k4w1">Table</div> </button></div> <p class="mt-5"><b>Hint:</b> In case the visualization doesn't look like you expected, you should try to <button class="button is-ghost is-inline">transpose the data</button></p></div>