"""
AI Settings Dialog - Dialog pro nastavení AI služeb
Umožňuje konfiguraci API klíčů a parametrů AI generování
"""

from typing import Dict, Any

try:
    from PyQt6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
        QTabWidget, QWidget, QFormLayout, QSpinBox, QDoubleSpinBox,
        QComboBox, QCheckBox, QTextEdit, QGroupBox, QMessageBox
    )
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False


class AISettingsDialog(QDialog):
    """Dialog pro nastavení AI služeb"""
    
    def __init__(self, ai_integration, parent=None):
        super().__init__(parent)
        
        if not PYQT_AVAILABLE:
            raise ImportError("PyQt6 není dostupné")
            
        self.ai_integration = ai_integration
        
        self.setWindowTitle("Nastavení AI")
        self.setModal(True)
        self.resize(500, 400)
        
        self.init_ui()
        self.load_current_settings()
        
    def init_ui(self):
        """Inicializace uživatelského rozhraní"""
        layout = QVBoxLayout(self)
        
        # Tab widget
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # Záložka API
        self.create_api_tab(tab_widget)
        
        # Záložka Modely
        self.create_models_tab(tab_widget)
        
        # Záložka Pokročilé
        self.create_advanced_tab(tab_widget)
        
        # Tlačítka
        self.create_buttons(layout)
        
    def create_api_tab(self, tab_widget):
        """Vytvoření záložky API"""
        api_widget = QWidget()
        layout = QVBoxLayout(api_widget)
        
        # API Key skupina
        api_group = QGroupBox("OpenAI API")
        api_layout = QFormLayout(api_group)
        
        # API Key
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.api_key_edit.setPlaceholderText("sk-...")
        api_layout.addRow("API Key:", self.api_key_edit)
        
        # Tlačítko pro zobrazení/skrytí
        show_key_btn = QPushButton("👁️")
        show_key_btn.setMaximumWidth(30)
        show_key_btn.clicked.connect(self.toggle_api_key_visibility)
        
        key_layout = QHBoxLayout()
        key_layout.addWidget(self.api_key_edit)
        key_layout.addWidget(show_key_btn)
        api_layout.addRow("API Key:", key_layout)
        
        # Test připojení
        test_layout = QHBoxLayout()
        self.test_btn = QPushButton("Test připojení")
        self.test_btn.clicked.connect(self.test_connection)
        test_layout.addWidget(self.test_btn)
        test_layout.addStretch()
        
        self.status_label = QLabel("Nepřipojeno")
        self.status_label.setStyleSheet("color: gray;")
        test_layout.addWidget(self.status_label)
        
        api_layout.addRow(test_layout)
        
        layout.addWidget(api_group)
        
        # Informace
        info_group = QGroupBox("Informace")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QLabel("""
<b>Jak získat API klíč:</b><br>
1. Jděte na <a href="https://platform.openai.com/api-keys">platform.openai.com/api-keys</a><br>
2. Přihlaste se nebo vytvořte účet<br>
3. Klikněte na "Create new secret key"<br>
4. Zkopírujte klíč a vložte ho výše<br><br>
<b>Poznámka:</b> API klíč je uložen pouze lokálně ve vaší aplikaci.
        """)
        info_text.setWordWrap(True)
        info_text.setOpenExternalLinks(True)
        info_layout.addWidget(info_text)
        
        layout.addWidget(info_group)
        layout.addStretch()
        
        tab_widget.addTab(api_widget, "API")
        
    def create_models_tab(self, tab_widget):
        """Vytvoření záložky Modely"""
        models_widget = QWidget()
        layout = QVBoxLayout(models_widget)
        
        # Model skupina
        model_group = QGroupBox("Nastavení modelu")
        model_layout = QFormLayout(model_group)
        
        # Model
        self.model_combo = QComboBox()
        self.model_combo.addItems([
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
            "gpt-4",
            "gpt-4-turbo-preview"
        ])
        model_layout.addRow("Model:", self.model_combo)
        
        # Max tokens
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(100, 4000)
        self.max_tokens_spin.setValue(1000)
        model_layout.addRow("Max tokens:", self.max_tokens_spin)
        
        # Temperature
        self.temperature_spin = QDoubleSpinBox()
        self.temperature_spin.setRange(0.0, 2.0)
        self.temperature_spin.setSingleStep(0.1)
        self.temperature_spin.setValue(0.3)
        model_layout.addRow("Temperature:", self.temperature_spin)
        
        layout.addWidget(model_group)
        
        # Výkon skupina
        performance_group = QGroupBox("Výkon")
        performance_layout = QFormLayout(performance_group)
        
        # Timeout
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(10, 300)
        self.timeout_spin.setValue(60)
        self.timeout_spin.setSuffix(" s")
        performance_layout.addRow("Timeout:", self.timeout_spin)
        
        # Max requests per minute
        self.rate_limit_spin = QSpinBox()
        self.rate_limit_spin.setRange(1, 100)
        self.rate_limit_spin.setValue(20)
        performance_layout.addRow("Max požadavků/min:", self.rate_limit_spin)
        
        layout.addWidget(performance_group)
        layout.addStretch()
        
        tab_widget.addTab(models_widget, "Modely")
        
    def create_advanced_tab(self, tab_widget):
        """Vytvoření záložky Pokročilé"""
        advanced_widget = QWidget()
        layout = QVBoxLayout(advanced_widget)
        
        # Systémové prompty
        prompts_group = QGroupBox("Vlastní systémové prompty")
        prompts_layout = QVBoxLayout(prompts_group)
        
        self.custom_prompts_edit = QTextEdit()
        self.custom_prompts_edit.setPlaceholderText("""
Zadejte vlastní systémové prompty ve formátu:
Název: Popis promptu

Například:
Marketingový analytik: Jste marketingový analytik zaměřený na consumer insights...
        """)
        self.custom_prompts_edit.setMaximumHeight(150)
        prompts_layout.addWidget(self.custom_prompts_edit)
        
        layout.addWidget(prompts_group)
        
        # Pokročilé nastavení
        advanced_group = QGroupBox("Pokročilé nastavení")
        advanced_layout = QFormLayout(advanced_group)
        
        # Retry attempts
        self.retry_spin = QSpinBox()
        self.retry_spin.setRange(1, 5)
        self.retry_spin.setValue(3)
        advanced_layout.addRow("Počet opakování:", self.retry_spin)
        
        # Cache responses
        self.cache_checkbox = QCheckBox("Cachovat odpovědi")
        self.cache_checkbox.setChecked(True)
        advanced_layout.addRow(self.cache_checkbox)
        
        # Debug mode
        self.debug_checkbox = QCheckBox("Debug režim")
        advanced_layout.addRow(self.debug_checkbox)
        
        layout.addWidget(advanced_group)
        layout.addStretch()
        
        tab_widget.addTab(advanced_widget, "Pokročilé")
        
    def create_buttons(self, layout):
        """Vytvoření tlačítek"""
        button_layout = QHBoxLayout()
        
        # Reset na výchozí
        reset_btn = QPushButton("Reset na výchozí")
        reset_btn.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        
        # Zrušit
        cancel_btn = QPushButton("Zrušit")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # Uložit
        save_btn = QPushButton("Uložit")
        save_btn.clicked.connect(self.save_settings)
        save_btn.setDefault(True)
        button_layout.addWidget(save_btn)
        
        layout.addLayout(button_layout)
        
    def toggle_api_key_visibility(self):
        """Přepnutí viditelnosti API klíče"""
        if self.api_key_edit.echoMode() == QLineEdit.EchoMode.Password:
            self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Normal)
        else:
            self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
            
    def test_connection(self):
        """Test připojení k API"""
        self.test_btn.setEnabled(False)
        self.test_btn.setText("Testuji...")
        self.status_label.setText("Testuji připojení...")
        self.status_label.setStyleSheet("color: blue;")
        
        # Test připojení pomocí existujícího AI systému
        result = self.ai_integration.test_connection()
        
        # Zobrazit výsledek
        if result['success']:
            self.status_label.setText("✅ Připojení úspěšné")
            self.status_label.setStyleSheet("color: green;")
        else:
            self.status_label.setText(f"❌ {result['error']}")
            self.status_label.setStyleSheet("color: red;")
            
        self.test_btn.setEnabled(True)
        self.test_btn.setText("Test připojení")
        
    def load_current_settings(self):
        """Načtení současných nastavení"""
        # API klíč - načteme z .env
        import os
        from dotenv import load_dotenv
        load_dotenv()
        api_key = os.getenv('OPENAI_API_KEY', '')
        if api_key:
            # Zobrazíme jen část klíče z bezpečnostních důvodů
            masked_key = api_key[:8] + "..." + api_key[-4:] if len(api_key) > 12 else api_key
            self.api_key_edit.setText(masked_key)
            
        # Status
        if self.ai_integration.is_available():
            self.status_label.setText("✅ Připojeno")
            self.status_label.setStyleSheet("color: green;")
        else:
            self.status_label.setText(f"❌ {self.ai_integration.get_status_message()}")
            self.status_label.setStyleSheet("color: red;")
            
    def reset_to_defaults(self):
        """Reset na výchozí nastavení"""
        reply = QMessageBox.question(
            self,
            "Potvrzení",
            "Opravdu chcete obnovit výchozí nastavení?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # API
            self.api_key_edit.clear()
            
            # Modely
            self.model_combo.setCurrentText("gpt-3.5-turbo")
            self.max_tokens_spin.setValue(1000)
            self.temperature_spin.setValue(0.3)
            
            # Výkon
            self.timeout_spin.setValue(60)
            self.rate_limit_spin.setValue(20)
            
            # Pokročilé
            self.custom_prompts_edit.clear()
            self.retry_spin.setValue(3)
            self.cache_checkbox.setChecked(True)
            self.debug_checkbox.setChecked(False)
            
    def save_settings(self):
        """Uložení nastavení"""
        QMessageBox.information(
            self, 
            "Informace", 
            "AI nastavení se spravují centrálně přes .env soubor.\n\n"
            "Pro změnu API klíče upravte OPENAI_API_KEY v .env souboru "
            "a restartujte aplikaci."
        )
        self.accept()