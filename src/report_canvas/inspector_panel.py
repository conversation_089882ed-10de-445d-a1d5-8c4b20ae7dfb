"""
Inspector Panel - Pravý panel pro detailní editaci vybraného uzlu
Poskytuje rozšířen<PERSON> mož<PERSON> editace a nastavení
"""

from typing import Dict, List, Any, Optional

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTextEdit,
        QPushButton, QComboBox, QListWidget, QListWidgetItem, QFrame,
        QScrollArea, QGroupBox, QSpinBox, QCheckBox, QTabWidget,
        QSplitter, QMessageBox
    )
    from PyQt6.QtCore import Qt, pyqtSignal
    from PyQt6.QtGui import QFont
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False


class InspectorPanel(QWidget):
    """Pravý panel - Inspector pro detailní editaci uzlu"""
    
    # Signály
    node_updated = pyqtSignal(str, dict)  # node_id, node_data
    generate_requested = pyqtSignal(str)  # node_id
    
    def __init__(self):
        super().__init__()
        
        if not PYQT_AVAILABLE:
            raise ImportError("PyQt6 není dostupné")
            
        self.current_node_id: Optional[str] = None
        self.current_node_data: Dict[str, Any] = {}
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """Inicializace uživatelského rozhraní"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Hlavička
        self.create_header(layout)
        
        # Scroll area pro obsah
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # Widget pro obsah
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setSpacing(10)
        
        # Vytvoření sekcí
        self.create_basic_info_section()
        self.create_content_section()
        self.create_data_sources_section()
        self.create_ai_settings_section()
        self.create_metadata_section()
        
        # Přidání stretch na konec
        self.content_layout.addStretch()
        
        scroll_area.setWidget(self.content_widget)
        layout.addWidget(scroll_area)
        
        # Zobrazení prázdného stavu
        self.show_empty_state()
        
    def create_header(self, layout):
        """Vytvoření hlavičky"""
        header_frame = QFrame()
        header_frame.setStyleSheet("QFrame { border-bottom: 1px solid #ccc; background: #f8f8f8; }")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 8, 10, 8)
        
        self.title_label = QLabel("Inspector")
        font = QFont()
        font.setBold(True)
        font.setPointSize(12)
        self.title_label.setFont(font)
        header_layout.addWidget(self.title_label)
        
        header_layout.addStretch()
        
        # Tlačítko aplikace změn
        self.apply_btn = QPushButton("✓ Aplikovat")
        self.apply_btn.clicked.connect(self.apply_changes)
        self.apply_btn.setEnabled(False)
        header_layout.addWidget(self.apply_btn)
        
        layout.addWidget(header_frame)
        
    def create_basic_info_section(self):
        """Sekce základních informací"""
        group = QGroupBox("Základní informace")
        layout = QVBoxLayout(group)
        
        # ID uzlu
        id_layout = QHBoxLayout()
        id_layout.addWidget(QLabel("ID uzlu:"))
        self.node_id_label = QLabel("—")
        self.node_id_label.setStyleSheet("font-family: monospace; color: #666;")
        id_layout.addWidget(self.node_id_label)
        id_layout.addStretch()
        layout.addLayout(id_layout)
        
        # Nadpis
        layout.addWidget(QLabel("Nadpis kapitoly:"))
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("Zadejte nadpis kapitoly...")
        layout.addWidget(self.title_edit)
        
        # Status
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("Status:"))
        self.status_label = QLabel("—")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        layout.addLayout(status_layout)
        
        self.content_layout.addWidget(group)
        
    def create_content_section(self):
        """Sekce obsahu"""
        group = QGroupBox("Obsah kapitoly")
        layout = QVBoxLayout(group)
        
        # Obsah
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("Zde bude obsah kapitoly...")
        self.content_edit.setMaximumHeight(150)
        layout.addWidget(self.content_edit)
        
        # Tlačítka pro obsah
        content_buttons = QHBoxLayout()
        
        self.clear_content_btn = QPushButton("🗑 Vymazat")
        self.clear_content_btn.clicked.connect(self.clear_content)
        content_buttons.addWidget(self.clear_content_btn)
        
        content_buttons.addStretch()
        
        self.generate_content_btn = QPushButton("🤖 Generovat")
        self.generate_content_btn.clicked.connect(self.request_generation)
        content_buttons.addWidget(self.generate_content_btn)
        
        layout.addLayout(content_buttons)
        
        self.content_layout.addWidget(group)
        
    def create_data_sources_section(self):
        """Sekce datových zdrojů"""
        group = QGroupBox("Datové zdroje")
        layout = QVBoxLayout(group)
        
        # Seznam datových zdrojů
        self.data_sources_list = QListWidget()
        self.data_sources_list.setMaximumHeight(100)
        layout.addWidget(self.data_sources_list)
        
        # Tlačítka pro správu dat
        data_buttons = QHBoxLayout()
        
        self.remove_data_btn = QPushButton("➖ Odstranit")
        self.remove_data_btn.clicked.connect(self.remove_selected_data_source)
        self.remove_data_btn.setEnabled(False)
        data_buttons.addWidget(self.remove_data_btn)
        
        data_buttons.addStretch()
        
        self.clear_data_btn = QPushButton("🗑 Vymazat vše")
        self.clear_data_btn.clicked.connect(self.clear_all_data_sources)
        data_buttons.addWidget(self.clear_data_btn)
        
        layout.addLayout(data_buttons)
        
        # Info label
        self.data_info_label = QLabel("Přetáhněte data z Data Explorer")
        self.data_info_label.setStyleSheet("color: #888; font-size: 11px;")
        layout.addWidget(self.data_info_label)
        
        self.content_layout.addWidget(group)
        
    def create_ai_settings_section(self):
        """Sekce nastavení AI"""
        group = QGroupBox("Nastavení AI")
        layout = QVBoxLayout(group)
        
        # Prompt
        layout.addWidget(QLabel("Instrukce pro AI:"))
        self.prompt_edit = QTextEdit()
        self.prompt_edit.setPlaceholderText("Napište instrukce pro AI generování...")
        self.prompt_edit.setMaximumHeight(100)
        layout.addWidget(self.prompt_edit)
        
        # Systémový prompt
        layout.addWidget(QLabel("Systémový prompt:"))
        self.system_prompt_combo = QComboBox()
        self.system_prompt_combo.addItems([
            "Analytik - obecný",
            "Analytik - technický", 
            "Manažerský souhrn",
            "Akademický styl",
            "Novinářský styl",
            "Vlastní..."
        ])
        layout.addWidget(self.system_prompt_combo)
        
        # Pokročilé nastavení
        advanced_layout = QHBoxLayout()
        
        # Délka odpovědi
        advanced_layout.addWidget(QLabel("Max. délka:"))
        self.max_length_spin = QSpinBox()
        self.max_length_spin.setRange(100, 5000)
        self.max_length_spin.setValue(1000)
        self.max_length_spin.setSuffix(" slov")
        advanced_layout.addWidget(self.max_length_spin)
        
        advanced_layout.addStretch()
        
        layout.addLayout(advanced_layout)
        
        # Checkboxy
        self.include_data_checkbox = QCheckBox("Zahrnout data do promptu")
        self.include_data_checkbox.setChecked(True)
        layout.addWidget(self.include_data_checkbox)
        
        self.creative_mode_checkbox = QCheckBox("Kreativní režim")
        layout.addWidget(self.creative_mode_checkbox)
        
        self.content_layout.addWidget(group)
        
    def create_metadata_section(self):
        """Sekce metadata"""
        group = QGroupBox("Metadata")
        layout = QVBoxLayout(group)
        
        # Čas vytvoření
        creation_layout = QHBoxLayout()
        creation_layout.addWidget(QLabel("Vytvořeno:"))
        self.creation_time_label = QLabel("—")
        self.creation_time_label.setStyleSheet("font-size: 11px; color: #666;")
        creation_layout.addWidget(self.creation_time_label)
        creation_layout.addStretch()
        layout.addLayout(creation_layout)
        
        # Poslední úprava
        modified_layout = QHBoxLayout()
        modified_layout.addWidget(QLabel("Upraveno:"))
        self.modified_time_label = QLabel("—")
        self.modified_time_label.setStyleSheet("font-size: 11px; color: #666;")
        modified_layout.addWidget(self.modified_time_label)
        modified_layout.addStretch()
        layout.addLayout(modified_layout)
        
        # Počet slov
        words_layout = QHBoxLayout()
        words_layout.addWidget(QLabel("Počet slov:"))
        self.word_count_label = QLabel("0")
        self.word_count_label.setStyleSheet("font-size: 11px; color: #666;")
        words_layout.addWidget(self.word_count_label)
        words_layout.addStretch()
        layout.addLayout(words_layout)
        
        self.content_layout.addWidget(group)
        
    def setup_connections(self):
        """Nastavení propojení signálů"""
        # Změny v polích
        self.title_edit.textChanged.connect(self.on_field_changed)
        self.content_edit.textChanged.connect(self.on_content_changed)
        self.prompt_edit.textChanged.connect(self.on_field_changed)
        self.system_prompt_combo.currentTextChanged.connect(self.on_field_changed)
        self.max_length_spin.valueChanged.connect(self.on_field_changed)
        self.include_data_checkbox.toggled.connect(self.on_field_changed)
        self.creative_mode_checkbox.toggled.connect(self.on_field_changed)
        
        # Seznam datových zdrojů
        self.data_sources_list.itemSelectionChanged.connect(self.on_data_selection_changed)
        
    def on_field_changed(self):
        """Obsluha změny pole"""
        self.apply_btn.setEnabled(True)
        
    def on_content_changed(self):
        """Obsluha změny obsahu"""
        self.update_word_count()
        self.on_field_changed()
        
    def on_data_selection_changed(self):
        """Obsluha změny výběru datových zdrojů"""
        has_selection = bool(self.data_sources_list.selectedItems())
        self.remove_data_btn.setEnabled(has_selection)
        
    def update_word_count(self):
        """Aktualizace počtu slov"""
        content = self.content_edit.toPlainText()
        word_count = len(content.split()) if content.strip() else 0
        self.word_count_label.setText(str(word_count))
        
    def set_selected_node(self, node_id: str):
        """Nastavení vybraného uzlu"""
        if node_id:
            self.current_node_id = node_id
            self.show_node_editor()
        else:
            self.current_node_id = None
            self.show_empty_state()
            
    def show_empty_state(self):
        """Zobrazení prázdného stavu"""
        self.title_label.setText("Inspector")
        self.content_widget.setEnabled(False)
        self.apply_btn.setEnabled(False)
        
        # Vymazání polí
        self.node_id_label.setText("—")
        self.title_edit.clear()
        self.content_edit.clear()
        self.prompt_edit.clear()
        self.status_label.setText("—")
        self.creation_time_label.setText("—")
        self.modified_time_label.setText("—")
        self.word_count_label.setText("0")
        self.data_sources_list.clear()
        
    def show_node_editor(self):
        """Zobrazení editoru uzlu"""
        self.title_label.setText(f"Inspector - {self.current_node_id}")
        self.content_widget.setEnabled(True)
        self.apply_btn.setEnabled(False)
        
    def load_node_data(self, node_data: Dict[str, Any]):
        """Načtení dat uzlu"""
        self.current_node_data = node_data.copy()
        
        # Základní informace
        self.node_id_label.setText(node_data.get('node_id', '—'))
        self.title_edit.setText(node_data.get('title', ''))
        
        # Status
        if node_data.get('is_manually_edited', False):
            self.status_label.setText("✏️ Manuálně upraveno")
            self.status_label.setStyleSheet("color: #ff6600;")
        elif node_data.get('content', '').strip():
            self.status_label.setText("🤖 Vygenerováno AI")
            self.status_label.setStyleSheet("color: #00aa00;")
        else:
            self.status_label.setText("Žádný obsah")
            self.status_label.setStyleSheet("color: #888;")
            
        # Obsah
        self.content_edit.setPlainText(node_data.get('content', ''))
        
        # AI nastavení
        self.prompt_edit.setPlainText(node_data.get('prompt', ''))
        
        system_prompt = node_data.get('system_prompt', 'Analytik - obecný')
        index = self.system_prompt_combo.findText(system_prompt)
        if index >= 0:
            self.system_prompt_combo.setCurrentIndex(index)
            
        # Pokročilé nastavení
        self.max_length_spin.setValue(node_data.get('max_length', 1000))
        self.include_data_checkbox.setChecked(node_data.get('include_data', True))
        self.creative_mode_checkbox.setChecked(node_data.get('creative_mode', False))
        
        # Datové zdroje
        self.load_data_sources(node_data.get('data_sources', []))
        
        # Metadata
        creation_time = node_data.get('creation_time', '')
        if creation_time:
            try:
                from datetime import datetime
                dt = datetime.fromisoformat(creation_time.replace('Z', '+00:00'))
                self.creation_time_label.setText(dt.strftime('%d.%m.%Y %H:%M'))
            except:
                self.creation_time_label.setText("—")
        else:
            self.creation_time_label.setText("—")
            
        # Aktualizace počtu slov
        self.update_word_count()
        
        # Reset apply button
        self.apply_btn.setEnabled(False)
        
    def load_data_sources(self, data_sources: List[Dict[str, Any]]):
        """Načtení datových zdrojů"""
        self.data_sources_list.clear()
        
        for data in data_sources:
            data_type = data.get('type', 'unknown')
            if data_type == 'csv_column':
                text = f"📊 CSV: {data.get('column_name', 'N/A')}"
            elif data_type == 'csv_file':
                text = f"📄 CSV soubor: {data.get('path', 'N/A').split('/')[-1]}"
            elif data_type == 'excel_file':
                text = f"📈 Excel: {data.get('path', 'N/A').split('/')[-1]}"
            elif data_type == 'user_file':
                text = f"📎 Soubor: {data.get('path', 'N/A').split('/')[-1]}"
            elif data_type == 'report_result':
                text = f"📋 Výsledek: {data.get('result_id', 'N/A')}"
            else:
                text = f"❓ Data: {data_type}"
                
            item = QListWidgetItem(text)
            item.setData(Qt.ItemDataRole.UserRole, data)
            self.data_sources_list.addItem(item)
            
        # Aktualizace info labelu
        count = len(data_sources)
        if count == 0:
            self.data_info_label.setText("Přetáhněte data z Data Explorer")
        else:
            self.data_info_label.setText(f"Připojeno {count} datových zdrojů")
            
    def apply_changes(self):
        """Aplikace změn"""
        if not self.current_node_id:
            return
            
        # Sestavení aktualizovaných dat
        updated_data = self.current_node_data.copy()
        updated_data.update({
            'title': self.title_edit.text(),
            'content': self.content_edit.toPlainText(),
            'prompt': self.prompt_edit.toPlainText(),
            'system_prompt': self.system_prompt_combo.currentText(),
            'max_length': self.max_length_spin.value(),
            'include_data': self.include_data_checkbox.isChecked(),
            'creative_mode': self.creative_mode_checkbox.isChecked(),
            'is_manually_edited': True  # Označit jako manuálně upravené
        })
        
        # Emitování signálu
        self.node_updated.emit(self.current_node_id, updated_data)
        
        # Reset apply button
        self.apply_btn.setEnabled(False)
        
        # Aktualizace current_node_data
        self.current_node_data = updated_data
        
    def clear_content(self):
        """Vymazání obsahu"""
        reply = QMessageBox.question(
            self,
            "Potvrzení",
            "Opravdu chcete vymazat obsah kapitoly?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.content_edit.clear()
            self.on_field_changed()
            
    def request_generation(self):
        """Požadavek na generování obsahu"""
        if not self.current_node_id:
            return
            
        # Kontrola, zda je obsah
        if self.content_edit.toPlainText().strip():
            reply = QMessageBox.question(
                self,
                "Potvrzení",
                "Stávající obsah bude přepsán. Pokračovat?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply != QMessageBox.StandardButton.Yes:
                return
                
        self.generate_requested.emit(self.current_node_id)
        
    def remove_selected_data_source(self):
        """Odstranění vybraného datového zdroje"""
        selected_items = self.data_sources_list.selectedItems()
        if not selected_items:
            return
            
        for item in selected_items:
            row = self.data_sources_list.row(item)
            self.data_sources_list.takeItem(row)
            
        self.on_field_changed()
        
    def clear_all_data_sources(self):
        """Vymazání všech datových zdrojů"""
        if self.data_sources_list.count() == 0:
            return
            
        reply = QMessageBox.question(
            self,
            "Potvrzení",
            "Opravdu chcete odstranit všechny datové zdroje?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.data_sources_list.clear()
            self.data_info_label.setText("Přetáhněte data z Data Explorer")
            self.on_field_changed()
            
    def clear_selection(self):
        """Vymazání výběru"""
        self.current_node_id = None
        self.current_node_data = {}
        self.show_empty_state()