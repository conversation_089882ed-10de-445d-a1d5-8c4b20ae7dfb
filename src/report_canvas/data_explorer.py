"""
Data Explorer - Levý panel pro správu datových zdro<PERSON><PERSON>
Zobrazuje stromovou strukturu dostupných dat pro použití v reportu
"""

import os
import json
import csv
from pathlib import Path
from typing import Dict, List, Any, Optional

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget, QTreeWidgetItem,
        QPushButton, QLabel, QFileDialog, QMessageBox, QMenu, QHeaderView
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QMimeData
    from PyQt6.QtGui import QDragEnterEvent, QDropEvent, QIcon
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False


class DataExplorer(QWidget):
    """Levý panel - Data Explorer pro správu datových zdrojů"""
    
    # Signály
    data_dropped = pyqtSignal(str, dict)  # node_id, data_info
    data_selected = pyqtSignal(dict)  # data_info
    
    def __init__(self):
        super().__init__()
        
        if not PYQT_AVAILABLE:
            raise ImportError("PyQt6 není dostupné")
            
        self.data_sources = {
            'csv_data': {},
            'excel_data': {},
            'user_files': {},
            'report_results': {}
        }
        
        self.init_ui()
        self.setup_drag_drop()
        
    def init_ui(self):
        """Inicializace uživatelského rozhraní"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Hlavička
        header_layout = QHBoxLayout()
        title_label = QLabel("Data Explorer")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Tlačítko pro přidání dat
        add_data_btn = QPushButton("+ Přidat data")
        add_data_btn.clicked.connect(self.add_data_source)
        header_layout.addWidget(add_data_btn)
        
        layout.addLayout(header_layout)
        
        # Stromový widget
        self.tree_widget = QTreeWidget()
        self.tree_widget.setHeaderLabel("Datové zdroje")
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setDragDropMode(QTreeWidget.DragDropMode.DragOnly)
        self.tree_widget.itemSelectionChanged.connect(self.on_selection_changed)
        self.tree_widget.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addWidget(self.tree_widget)
        
        # Informační panel
        info_layout = QVBoxLayout()
        self.info_label = QLabel("Vyberte datový zdroj pro zobrazení detailů")
        self.info_label.setWordWrap(True)
        self.info_label.setStyleSheet("color: gray; font-size: 11px;")
        info_layout.addWidget(self.info_label)
        
        layout.addLayout(info_layout)
        
        # Inicializace stromu
        self.init_tree_structure()
        
    def setup_drag_drop(self):
        """Nastavení drag & drop"""
        self.setAcceptDrops(True)
        
    def init_tree_structure(self):
        """Inicializace základní struktury stromu"""
        self.tree_widget.clear()
        
        # Kořenové uzly
        self.csv_root = QTreeWidgetItem(self.tree_widget, ["Surová Data (CSV)"])
        self.csv_root.setExpanded(True)
        
        self.excel_root = QTreeWidgetItem(self.tree_widget, ["Statistiky (XLS)"])
        self.excel_root.setExpanded(True)
        
        self.files_root = QTreeWidgetItem(self.tree_widget, ["Uživatelské Soubory"])
        self.files_root.setExpanded(True)
        
        self.results_root = QTreeWidgetItem(self.tree_widget, ["Výsledky z Reportu"])
        self.results_root.setExpanded(True)
        
        # Placeholder položky
        self.add_placeholder_item(self.csv_root, "Žádná CSV data nenačtena")
        self.add_placeholder_item(self.excel_root, "Žádné Excel soubory nenačteny")
        self.add_placeholder_item(self.files_root, "Žádné soubory nepřidány")
        self.add_placeholder_item(self.results_root, "Žádné výsledky zatím nevygenerovány")
        
    def add_placeholder_item(self, parent: QTreeWidgetItem, text: str):
        """Přidání placeholder položky"""
        item = QTreeWidgetItem(parent, [text])
        item.setData(0, Qt.ItemDataRole.UserRole, {'type': 'placeholder'})
        item.setFlags(Qt.ItemFlag.ItemIsEnabled)  # Není možné vybrat ani přetáhnout
        
    def add_data_source(self):
        """Přidání nového datového zdroje"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "Vyberte datové soubory",
            "",
            "Všechny podporované (*.csv *.xlsx *.xls *.txt *.docx *.png *.jpg *.jpeg);;CSV soubory (*.csv);;Excel soubory (*.xlsx *.xls);;Textové soubory (*.txt);;Word dokumenty (*.docx);;Obrázky (*.png *.jpg *.jpeg)"
        )
        
        for file_path in file_paths:
            self.load_data_file(file_path)
            
    def load_data_file(self, file_path: str):
        """Načtení datového souboru"""
        try:
            file_path = Path(file_path)
            extension = file_path.suffix.lower()
            
            if extension == '.csv':
                self.load_csv_file(file_path)
            elif extension in ['.xlsx', '.xls']:
                self.load_excel_file(file_path)
            elif extension in ['.txt', '.docx', '.png', '.jpg', '.jpeg']:
                self.load_user_file(file_path)
            else:
                QMessageBox.warning(self, "Nepodporovaný formát", f"Formát {extension} není podporován")
                
        except Exception as e:
            QMessageBox.critical(self, "Chyba", f"Nepodařilo se načíst soubor:\n{str(e)}")
            
    def load_csv_file(self, file_path: Path):
        """Načtení CSV souboru"""
        try:
            # Analýza CSV struktury
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                headers = next(reader)
                row_count = sum(1 for _ in reader)
                
            # Odstranění placeholder
            self.remove_placeholder_items(self.csv_root)
            
            # Přidání CSV souboru do stromu
            file_item = QTreeWidgetItem(self.csv_root, [file_path.name])
            file_item.setData(0, Qt.ItemDataRole.UserRole, {
                'type': 'csv_file',
                'path': str(file_path),
                'headers': headers,
                'row_count': row_count
            })
            
            # Přidání sloupců jako podpoložky
            for header in headers[:10]:  # Zobrazit jen prvních 10 sloupců
                column_item = QTreeWidgetItem(file_item, [header])
                column_item.setData(0, Qt.ItemDataRole.UserRole, {
                    'type': 'csv_column',
                    'file_path': str(file_path),
                    'column_name': header
                })
                
            if len(headers) > 10:
                more_item = QTreeWidgetItem(file_item, [f"... a dalších {len(headers) - 10} sloupců"])
                more_item.setFlags(Qt.ItemFlag.ItemIsEnabled)
                
            file_item.setExpanded(True)
            self.data_sources['csv_data'][str(file_path)] = {
                'headers': headers,
                'row_count': row_count
            }
            
        except Exception as e:
            raise Exception(f"Chyba při načítání CSV: {str(e)}")
            
    def load_excel_file(self, file_path: Path):
        """Načtení Excel souboru"""
        try:
            # Pro MVP - základní informace o souboru
            file_size = file_path.stat().st_size
            
            # Odstranění placeholder
            self.remove_placeholder_items(self.excel_root)
            
            # Přidání Excel souboru do stromu
            file_item = QTreeWidgetItem(self.excel_root, [file_path.name])
            file_item.setData(0, Qt.ItemDataRole.UserRole, {
                'type': 'excel_file',
                'path': str(file_path),
                'size': file_size
            })
            
            # Placeholder pro listy (v plné verzi by se načetly skutečné listy)
            sheet_item = QTreeWidgetItem(file_item, ["List1 (příklad)"])
            sheet_item.setData(0, Qt.ItemDataRole.UserRole, {
                'type': 'excel_sheet',
                'file_path': str(file_path),
                'sheet_name': 'List1'
            })
            
            file_item.setExpanded(True)
            self.data_sources['excel_data'][str(file_path)] = {
                'size': file_size
            }
            
        except Exception as e:
            raise Exception(f"Chyba při načítání Excel: {str(e)}")
            
    def load_user_file(self, file_path: Path):
        """Načtení uživatelského souboru"""
        try:
            file_size = file_path.stat().st_size
            
            # Odstranění placeholder
            self.remove_placeholder_items(self.files_root)
            
            # Přidání souboru do stromu
            file_item = QTreeWidgetItem(self.files_root, [file_path.name])
            file_item.setData(0, Qt.ItemDataRole.UserRole, {
                'type': 'user_file',
                'path': str(file_path),
                'size': file_size,
                'extension': file_path.suffix.lower()
            })
            
            self.data_sources['user_files'][str(file_path)] = {
                'size': file_size,
                'extension': file_path.suffix.lower()
            }
            
        except Exception as e:
            raise Exception(f"Chyba při načítání souboru: {str(e)}")
            
    def remove_placeholder_items(self, parent: QTreeWidgetItem):
        """Odstranění placeholder položek"""
        items_to_remove = []
        for i in range(parent.childCount()):
            child = parent.child(i)
            data = child.data(0, Qt.ItemDataRole.UserRole)
            if data and data.get('type') == 'placeholder':
                items_to_remove.append(child)
                
        for item in items_to_remove:
            parent.removeChild(item)
            
    def add_report_result(self, result_id: str, result_data: Dict[str, Any]):
        """Přidání výsledku z reportu"""
        # Odstranění placeholder
        self.remove_placeholder_items(self.results_root)
        
        # Přidání výsledku do stromu
        result_item = QTreeWidgetItem(self.results_root, [result_id])
        result_item.setData(0, Qt.ItemDataRole.UserRole, {
            'type': 'report_result',
            'result_id': result_id,
            'data': result_data
        })
        
        self.data_sources['report_results'][result_id] = result_data
        
    def on_selection_changed(self):
        """Obsluha změny výběru"""
        selected_items = self.tree_widget.selectedItems()
        if not selected_items:
            self.info_label.setText("Vyberte datový zdroj pro zobrazení detailů")
            return
            
        item = selected_items[0]
        data = item.data(0, Qt.ItemDataRole.UserRole)
        
        if not data or data.get('type') == 'placeholder':
            self.info_label.setText("Vyberte datový zdroj pro zobrazení detailů")
            return
            
        # Zobrazení informací o vybraném zdroji
        info_text = self.get_data_info_text(data)
        self.info_label.setText(info_text)
        
        # Emitování signálu
        self.data_selected.emit(data)
        
    def get_data_info_text(self, data: Dict[str, Any]) -> str:
        """Získání informačního textu o datovém zdroji"""
        data_type = data.get('type', 'unknown')
        
        if data_type == 'csv_file':
            return f"CSV soubor\\nSloupců: {len(data.get('headers', []))}\\nŘádků: {data.get('row_count', 0)}"
        elif data_type == 'csv_column':
            return f"CSV sloupec: {data.get('column_name', 'N/A')}\\nSoubor: {Path(data.get('file_path', '')).name}"
        elif data_type == 'excel_file':
            size_mb = data.get('size', 0) / (1024 * 1024)
            return f"Excel soubor\\nVelikost: {size_mb:.1f} MB"
        elif data_type == 'excel_sheet':
            return f"Excel list: {data.get('sheet_name', 'N/A')}\\nSoubor: {Path(data.get('file_path', '')).name}"
        elif data_type == 'user_file':
            size_kb = data.get('size', 0) / 1024
            ext = data.get('extension', 'N/A')
            return f"Uživatelský soubor ({ext})\\nVelikost: {size_kb:.1f} KB"
        elif data_type == 'report_result':
            return f"Výsledek z reportu\\nID: {data.get('result_id', 'N/A')}"
        else:
            return "Neznámý typ dat"
            
    def show_context_menu(self, position):
        """Zobrazení kontextového menu"""
        item = self.tree_widget.itemAt(position)
        if not item:
            return
            
        data = item.data(0, Qt.ItemDataRole.UserRole)
        if not data or data.get('type') == 'placeholder':
            return
            
        menu = QMenu(self)
        
        # Akce podle typu dat
        if data.get('type') in ['csv_file', 'excel_file', 'user_file']:
            remove_action = menu.addAction("Odstranit ze seznamu")
            remove_action.triggered.connect(lambda: self.remove_data_source(item))
            
            menu.addSeparator()
            
            info_action = menu.addAction("Zobrazit detaily")
            info_action.triggered.connect(lambda: self.show_data_details(data))
            
        menu.exec(self.tree_widget.mapToGlobal(position))
        
    def remove_data_source(self, item: QTreeWidgetItem):
        """Odstranění datového zdroje"""
        data = item.data(0, Qt.ItemDataRole.UserRole)
        if not data:
            return
            
        # Odstranění z dat
        data_type = data.get('type')
        if data_type == 'csv_file':
            path = data.get('path')
            if path in self.data_sources['csv_data']:
                del self.data_sources['csv_data'][path]
        elif data_type == 'excel_file':
            path = data.get('path')
            if path in self.data_sources['excel_data']:
                del self.data_sources['excel_data'][path]
        elif data_type == 'user_file':
            path = data.get('path')
            if path in self.data_sources['user_files']:
                del self.data_sources['user_files'][path]
                
        # Odstranění ze stromu
        parent = item.parent()
        parent.removeChild(item)
        
        # Přidání placeholder pokud je sekce prázdná
        if parent.childCount() == 0:
            if parent == self.csv_root:
                self.add_placeholder_item(parent, "Žádná CSV data nenačtena")
            elif parent == self.excel_root:
                self.add_placeholder_item(parent, "Žádné Excel soubory nenačteny")
            elif parent == self.files_root:
                self.add_placeholder_item(parent, "Žádné soubory nepřidány")
                
    def show_data_details(self, data: Dict[str, Any]):
        """Zobrazení detailů o datovém zdroji"""
        details = json.dumps(data, indent=2, ensure_ascii=False)
        QMessageBox.information(self, "Detaily datového zdroje", f"<pre>{details}</pre>")
        
    def clear_data(self):
        """Vymazání všech dat"""
        self.data_sources = {
            'csv_data': {},
            'excel_data': {},
            'user_files': {},
            'report_results': {}
        }
        self.init_tree_structure()
        
    def get_save_data(self) -> Dict[str, Any]:
        """Získání dat pro uložení"""
        return {
            'data_sources': self.data_sources,
            'version': '1.0'
        }
        
    def load_from_data(self, data: Dict[str, Any]):
        """Načtení dat ze souboru"""
        if 'data_sources' in data:
            self.data_sources = data['data_sources']
            self.rebuild_tree_from_data()
            
    def rebuild_tree_from_data(self):
        """Přestavění stromu z načtených dat"""
        self.init_tree_structure()
        
        # Načtení CSV souborů
        for path, info in self.data_sources.get('csv_data', {}).items():
            if os.path.exists(path):
                try:
                    self.load_csv_file(Path(path))
                except:
                    pass  # Soubor možná už neexistuje
                    
        # Načtení Excel souborů
        for path, info in self.data_sources.get('excel_data', {}).items():
            if os.path.exists(path):
                try:
                    self.load_excel_file(Path(path))
                except:
                    pass
                    
        # Načtení uživatelských souborů
        for path, info in self.data_sources.get('user_files', {}).items():
            if os.path.exists(path):
                try:
                    self.load_user_file(Path(path))
                except:
                    pass
                    
        # Načtení výsledků reportu
        for result_id, result_data in self.data_sources.get('report_results', {}).items():
            self.add_report_result(result_id, result_data)
            
    def dragEnterEvent(self, event: QDragEnterEvent):
        """Obsluha drag enter"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            
    def dropEvent(self, event: QDropEvent):
        """Obsluha drop"""
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            if file_path:
                self.load_data_file(file_path)