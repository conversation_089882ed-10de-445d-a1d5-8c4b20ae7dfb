# Analytický Report Canvas

MVP implementace desktopové aplikace pro vizuální tvorbu analytických zpráv.

## Přehled

Report Canvas je vizuální editor, k<PERSON><PERSON> revolučním způsobem mění proces tvorby datových zpráv. Uživatel nepracuje s abstraktními procesy, ale přímo **sestavuje finální report jako strom vizuálních uzlů**. <PERSON><PERSON><PERSON><PERSON> uzel reprezentuje kapitolu nebo sekci zprávy.

## Funkce

### 🎨 Vizuální Canvas Editor
- Drag & drop rozhraní pro tvorbu reportů
- Uzly kapitol s přepínateln<PERSON><PERSON> (Obsah/Nastavení)
- Propojování uzlů pro strukturu reportu
- Zoom, pan a další navigační funkce

### 📁 Data Explorer
- Stromová struktura datových zdrojů
- Podpora CSV, Excel, textových souborů a obrázků
- Drag & drop připojení dat k uzlům
- Automatické rozpoznání struktury dat

### 🔍 Inspector Panel
- Detailní editace vybraného uzlu
- Rozšířené nastavení AI generování
- Správa datových zdrojů
- Metadata a statistiky

### 🤖 AI Integrace
- Automatické generování textového obsahu
- Konfigurovatelné systémové prompty
- Podpora OpenAI API
- Hromadné generování celého reportu

### 💾 Persistence
- Ukládání projektů do JSON formátu
- Automatické ukládání změn
- Undo/Redo funkcionalita
- Export do HTML

## Instalace

### Požadavky
- Python 3.8+
- PyQt6 (GUI framework)
- OpenAI (volitelné, pro AI funkce)

### Instalace závislostí
```bash
pip install PyQt6
pip install openai  # volitelné pro AI funkce
```

### Konfigurace AI
1. Získejte OpenAI API klíč na [platform.openai.com](https://platform.openai.com/api-keys)
2. Nastavte proměnnou prostředí:
   ```bash
   export OPENAI_API_KEY="your-api-key-here"
   ```
3. Nebo vytvořte soubor `src/config/ai_config.py`:
   ```python
   OPENAI_API_KEY = "your-api-key-here"
   ```

## Spuštění

### Z hlavního CLI
```bash
python src/main.py
# Vyberte Menu 15
```

### Přímo
```bash
python -m src.report_canvas.main_window
```

## Použití

### 1. Vytvoření nového projektu
1. Spusťte aplikaci
2. Klikněte pravým tlačítkem na canvas
3. Vyberte "Přidat kapitolu"

### 2. Přidání dat
1. V Data Explorer klikněte "Přidat data"
2. Vyberte CSV, Excel nebo jiné soubory
3. Přetáhněte data na uzel kapitoly

### 3. Generování obsahu
1. Vyberte uzel kapitoly
2. V Inspector panelu zadejte instrukce pro AI
3. Klikněte "Generovat"

### 4. Export reportu
1. Menu → Export → HTML
2. Vyberte umístění souboru

## Architektura

```
src/report_canvas/
├── __init__.py              # Modul init
├── main_window.py           # Hlavní okno aplikace
├── canvas_editor.py         # Canvas editor (střední panel)
├── chapter_node.py          # Uzel kapitoly
├── data_explorer.py         # Data explorer (levý panel)
├── inspector_panel.py       # Inspector (pravý panel)
├── ai_integration.py        # AI integrace
├── ai_settings_dialog.py    # Dialog nastavení AI
├── cli_launcher.py          # CLI launcher
└── README.md               # Tato dokumentace
```

### Klíčové komponenty

#### MainWindow
- Hlavní okno s menu a toolbar
- Správa projektů (nový, otevřít, uložit)
- Koordinace mezi panely

#### CanvasEditor
- Vizuální editor s uzly kapitol
- Graphics View/Scene architektura
- Undo/Redo funkcionalita

#### ChapterNode
- Uzel kapitoly s přepínatelnými záložkami
- Editace obsahu a nastavení
- Drag & drop podpora

#### DataExplorer
- Stromový widget pro datové zdroje
- Automatická analýza CSV/Excel souborů
- Drag & drop export dat

#### InspectorPanel
- Detailní editace vybraného uzlu
- Formuláře pro všechna nastavení
- Metadata a statistiky

#### AIIntegration
- Worker threads pro AI generování
- OpenAI API integrace
- Konfigurovatelné prompty

## User Stories

### Základní workflow
1. **Prázdný list**: Analytik vytvoří první kapitolu a začne psát
2. **Vložení dat**: Přetáhne tabulku z Data Explorer na uzel
3. **AI syntéza**: Připojí data, napíše prompt a nechá AI vygenerovat obsah
4. **Manuální úprava**: Upraví vygenerovaný text podle potřeby
5. **Struktura**: Vytvoří další kapitoly a propojí je
6. **Export**: Exportuje hotový report do HTML

### Pokročilé funkce
- **Řetězení AI operací**: Výstup jedné kapitoly jako vstup pro další
- **Hromadné generování**: Generování obsahu pro všechny kapitoly najednou
- **Verzování**: Historie změn a možnost návratu (budoucí)

## Konfigurace

### AI Prompty
Aplikace podporuje několik předdefinovaných systémových promptů:
- **Analytik - obecný**: Standardní datová analýza
- **Analytik - technický**: Technické analýzy s detaily
- **Manažerský souhrn**: Business-orientované výstupy
- **Akademický styl**: Vědecké analýzy
- **Novinářský styl**: Poutavé datové příběhy

### Formát projektů
Projekty se ukládají jako JSON soubory s příponou `.rcproj`:
```json
{
  "version": "1.0",
  "canvas": {
    "nodes": {...},
    "connections": [...]
  },
  "data_sources": {...},
  "metadata": {...}
}
```

## Omezení MVP

- Pouze lokální spuštění (bez webového rozhraní)
- Základní export (HTML, PDF plánováno)
- Jednoduchá AI integrace (pouze OpenAI)
- Bez pokročilého verzování
- Bez kolaborativních funkcí

## Budoucí rozšíření

- **Webové rozhraní**: Browser-based verze
- **Pokročilé vizualizace**: Integrované grafy a diagramy
- **Kolaborace**: Sdílení a společná editace
- **Více AI poskytovatelů**: Anthropic, Google, lokální modely
- **Pokročilý export**: PDF, Word, PowerPoint
- **Šablony**: Předpřipravené struktury reportů

## Podpora

Pro podporu a hlášení chyb kontaktujte vývojový tým LimWrapp.

## Licence

Součást LimWrapp projektu - interní použití.