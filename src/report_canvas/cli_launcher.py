"""
CLI Launcher - Spuštění Report Canvas z hlavního CLI menu
Menu 15 - Analytický Report Canvas
"""

import sys
import os
from pathlib import Path

def check_dependencies():
    """Kontrola závislostí pro Report Canvas"""
    missing_deps = []
    
    # PyQt6
    try:
        import PyQt6
    except ImportError:
        missing_deps.append("PyQt6")
        
    # OpenAI (volitelné)
    try:
        import openai
    except ImportError:
        missing_deps.append("openai (volitelné pro AI funkce)")
        
    return missing_deps

def install_dependencies():
    """Nabídka instalace závislostí"""
    print("\n" + "=" * 60)
    print("INSTALACE ZÁVISLOSTÍ PRO REPORT CANVAS")
    print("=" * 60)
    print("Pro spuštění Report Canvas jsou potřeba tyto knihovny:")
    print("1. PyQt6 - GUI framework")
    print("2. openai - AI integrace (volitelné)")
    print("\nChcete je nainstalovat? (y/n): ", end="")
    
    try:
        choice = input().lower()
        if choice in ['y', 'yes', 'ano']:
            print("\nInstalace závislostí...")
            print("Spusťte následující příkazy:")
            print("  pip install PyQt6")
            print("  pip install openai  # volitelné")
            print("\nPo instalaci spusťte Menu 15 znovu.")
            return True
    except EOFError:
        pass
        
    return False

def launch_report_canvas():
    """Spuštění Report Canvas aplikace"""
    print("\n" + "=" * 60)
    print("=== 📊 ANALYTICKÝ REPORT CANVAS ===")
    print("MVP verze 1.0 - Vizuální editor analytických zpráv")
    print("=" * 60)
    
    # Kontrola závislostí
    missing_deps = check_dependencies()
    if missing_deps:
        print("❌ Chybí závislosti:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n💡 Tip: Nainstalujte závislosti pomocí:")
        print("   pip install PyQt6 openai")
        
        if install_dependencies():
            return
        else:
            print("\nSpuštění zrušeno.")
            return
    
    # Import a spuštění
    try:
        from .main_window import main as run_report_canvas
        
        print("🚀 Spouštím Report Canvas...")
        print("💡 Tip: Pro ukončení zavřete okno aplikace nebo použijte Ctrl+C")
        print("=" * 60)
        
        # Detekce prostředí a spuštění
        import os
        if os.environ.get('DISPLAY') is None:
            # Žádný displej - pokus o virtuální displej
            print("🖥️  Detekováno prostředí bez displeje, používám virtuální displej...")
            try:
                import subprocess
                import sys
                
                # Spuštění s xvfb-run
                cmd = ['xvfb-run', '-a', sys.executable, '-c', 
                       'import sys; sys.path.insert(0, "src"); from report_canvas.main_window import main; main()']
                
                result = subprocess.run(cmd, cwd=os.getcwd(), capture_output=False)
                
                if result.returncode == 0:
                    print("\n✅ Report Canvas byl úspěšně ukončen")
                else:
                    print(f"\n❌ Report Canvas skončil s chybou (kód: {result.returncode})")
                    
            except FileNotFoundError:
                print("❌ Chyba: xvfb není nainstalován")
                print("💡 Instalujte: sudo apt-get install xvfb")
                print("💡 Nebo spusťte v prostředí s grafickým rozhraním")
            except Exception as e:
                print(f"❌ Chyba při spuštění s virtuálním displejem: {str(e)}")
                print("💡 Zkuste spustit v prostředí s grafickým rozhraním")
        else:
            # Normální spuštění s displejem
            success = run_report_canvas()
            
            if success:
                print("\n✅ Report Canvas byl úspěšně ukončen")
            else:
                print("\n❌ Report Canvas skončil s chybou")
            
    except ImportError as e:
        print(f"❌ Chyba při importu: {str(e)}")
        print("💡 Zkontrolujte instalaci PyQt6: pip install PyQt6")
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {str(e)}")
        import traceback
        traceback.print_exc()

def show_report_canvas_info():
    """Zobrazení informací o Report Canvas"""
    print("\n" + "=" * 60)
    print("=== 📊 ANALYTICKÝ REPORT CANVAS - INFORMACE ===")
    print("=" * 60)
    print("🎯 Účel:")
    print("   Vizuální editor pro tvorbu analytických zpráv")
    print("   Drag & drop rozhraní s AI generováním obsahu")
    print()
    print("🔧 Funkce:")
    print("   • Vizuální canvas s uzly kapitol")
    print("   • Data Explorer pro správu datových zdrojů") 
    print("   • AI generování textového obsahu")
    print("   • Export do HTML/PDF")
    print("   • Ukládání projektů do JSON")
    print()
    print("💻 Technologie:")
    print("   • PyQt6 - GUI framework")
    print("   • OpenAI API - AI generování")
    print("   • JSON - persistence dat")
    print()
    print("📋 Požadavky:")
    print("   • Python 3.8+")
    print("   • PyQt6")
    print("   • OpenAI API klíč (volitelné)")
    print()
    print("🚀 Spuštění:")
    print("   Menu 15 z hlavního CLI")
    print("=" * 60)

def main():
    """Hlavní funkce pro CLI launcher"""
    if len(sys.argv) > 1 and sys.argv[1] == "--info":
        show_report_canvas_info()
    else:
        launch_report_canvas()

if __name__ == "__main__":
    main()