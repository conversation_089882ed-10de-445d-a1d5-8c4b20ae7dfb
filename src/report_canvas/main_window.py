"""
Hlavn<PERSON> okno Analytického Report Canvas
MVP implementace desktopové aplikace pro vizuální tvorbu analytických zpráv
"""

import sys
import os
import json
from pathlib import Path
from typing import Optional, Dict, Any

try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
        QSplitter, QMenuBar, QMenu, QStatusBar, QMessageBox, QFileDialog,
        QToolBar, QLabel
    )
    from PyQt6.QtCore import Qt, QTimer, pyqtSignal
    from PyQt6.QtGui import QAction, QIcon, QKeySequence
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    print("PyQt6 není dostupné. Instalujte: pip install PyQt6")

from .data_explorer import DataExplorer
from .canvas_editor import CanvasEditor
from .inspector_panel import <PERSON><PERSON><PERSON><PERSON>
from .ai_integration import AIIntegration


class ReportCanvasMainWindow(QMainWindow):
    """Hlavní okno Report Canvas aplikace"""
    
    # Signály
    project_saved = pyqtSignal(str)  # Cesta k uloženému projektu
    project_loaded = pyqtSignal(str)  # Cesta k načtenému projektu
    
    def __init__(self):
        super().__init__()
        
        if not PYQT_AVAILABLE:
            raise ImportError("PyQt6 není dostupné")
            
        self.current_project_path: Optional[str] = None
        self.is_modified = False
        
        # Inicializace AI integrace
        self.ai_integration = AIIntegration()
        
        self.init_ui()
        self.setup_connections()
        self.setup_auto_save()
        
    def init_ui(self):
        """Inicializace uživatelského rozhraní"""
        self.setWindowTitle("Analytický Report Canvas - MVP")
        self.setGeometry(100, 100, 1400, 900)
        
        # Vytvoření menu
        self.create_menu_bar()
        
        # Vytvoření toolbar
        self.create_toolbar()
        
        # Vytvoření hlavního widget
        self.create_main_widget()
        
        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Připraven")
        
    def create_menu_bar(self):
        """Vytvoření menu baru"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('&Soubor')
        
        # Nový projekt
        new_action = QAction('&Nový projekt', self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)
        
        # Otevřít projekt
        open_action = QAction('&Otevřít projekt...', self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # Uložit projekt
        save_action = QAction('&Uložit projekt', self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.triggered.connect(self.save_project)
        file_menu.addAction(save_action)
        
        # Uložit jako
        save_as_action = QAction('Uložit &jako...', self)
        save_as_action.setShortcut(QKeySequence.StandardKey.SaveAs)
        save_as_action.triggered.connect(self.save_project_as)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        # Export
        export_menu = file_menu.addMenu('&Export')
        
        export_html_action = QAction('Export do &HTML', self)
        export_html_action.triggered.connect(self.export_to_html)
        export_menu.addAction(export_html_action)
        
        export_pdf_action = QAction('Export do &PDF', self)
        export_pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(export_pdf_action)
        
        file_menu.addSeparator()
        
        # Ukončit
        exit_action = QAction('&Ukončit', self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Edit menu
        edit_menu = menubar.addMenu('&Úpravy')
        
        undo_action = QAction('&Zpět', self)
        undo_action.setShortcut(QKeySequence.StandardKey.Undo)
        undo_action.triggered.connect(self.undo)
        edit_menu.addAction(undo_action)
        
        redo_action = QAction('&Znovu', self)
        redo_action.setShortcut(QKeySequence.StandardKey.Redo)
        redo_action.triggered.connect(self.redo)
        edit_menu.addAction(redo_action)
        
        # View menu
        view_menu = menubar.addMenu('&Zobrazení')
        
        zoom_in_action = QAction('&Přiblížit', self)
        zoom_in_action.setShortcut(QKeySequence.StandardKey.ZoomIn)
        zoom_in_action.triggered.connect(self.zoom_in)
        view_menu.addAction(zoom_in_action)
        
        zoom_out_action = QAction('&Oddálit', self)
        zoom_out_action.setShortcut(QKeySequence.StandardKey.ZoomOut)
        zoom_out_action.triggered.connect(self.zoom_out)
        view_menu.addAction(zoom_out_action)
        
        zoom_fit_action = QAction('&Přizpůsobit', self)
        zoom_fit_action.triggered.connect(self.zoom_fit)
        view_menu.addAction(zoom_fit_action)
        
        # AI menu
        ai_menu = menubar.addMenu('&AI')
        
        generate_all_action = QAction('&Generovat celý report', self)
        generate_all_action.triggered.connect(self.generate_all_chapters)
        ai_menu.addAction(generate_all_action)
        
        ai_settings_action = QAction('&Nastavení AI', self)
        ai_settings_action.triggered.connect(self.show_ai_settings)
        ai_menu.addAction(ai_settings_action)
        
        # Help menu
        help_menu = menubar.addMenu('&Nápověda')
        
        about_action = QAction('&O aplikaci', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_toolbar(self):
        """Vytvoření toolbar"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # Základní akce
        toolbar.addAction("Nový", self.new_project)
        toolbar.addAction("Otevřít", self.open_project)
        toolbar.addAction("Uložit", self.save_project)
        toolbar.addSeparator()
        
        # Canvas akce
        toolbar.addAction("Přidat kapitolu", self.add_chapter_node)
        toolbar.addAction("Smazat vybrané", self.delete_selected)
        toolbar.addSeparator()
        
        # AI akce
        toolbar.addAction("Generovat vše", self.generate_all_chapters)
        
    def create_main_widget(self):
        """Vytvoření hlavního widget s třemi panely"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Hlavní layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # Splitter pro tři panely
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Levý panel - Data Explorer
        self.data_explorer = DataExplorer()
        splitter.addWidget(self.data_explorer)
        
        # Střední panel - Canvas Editor
        self.canvas_editor = CanvasEditor()
        splitter.addWidget(self.canvas_editor)
        
        # Pravý panel - Inspector
        self.inspector_panel = InspectorPanel()
        splitter.addWidget(self.inspector_panel)
        
        # Nastavení poměrů panelů (20%, 60%, 20%)
        splitter.setSizes([280, 840, 280])
        
    def setup_connections(self):
        """Nastavení propojení mezi komponenty"""
        # Canvas -> Inspector
        self.canvas_editor.node_selected.connect(self.inspector_panel.set_selected_node)
        self.canvas_editor.canvas_modified.connect(self.mark_as_modified)
        
        # Inspector -> Canvas
        self.inspector_panel.node_updated.connect(self.canvas_editor.update_node)
        self.inspector_panel.generate_requested.connect(self.generate_chapter)
        
        # Data Explorer -> Canvas
        self.data_explorer.data_dropped.connect(self.canvas_editor.add_data_to_selected_node)
        
        # AI Integration
        self.ai_integration.generation_completed.connect(self.on_generation_completed)
        self.ai_integration.generation_failed.connect(self.on_generation_failed)
        
    def setup_auto_save(self):
        """Nastavení automatického ukládání"""
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save)
        self.auto_save_timer.start(60000)  # Auto-save každou minutu
        
    def mark_as_modified(self):
        """Označí projekt jako změněný"""
        if not self.is_modified:
            self.is_modified = True
            title = self.windowTitle()
            if not title.endswith(" *"):
                self.setWindowTitle(title + " *")
                
    def mark_as_saved(self):
        """Označí projekt jako uložený"""
        self.is_modified = False
        title = self.windowTitle()
        if title.endswith(" *"):
            self.setWindowTitle(title[:-2])
            
    # Akce menu
    def new_project(self):
        """Vytvoření nového projektu"""
        if self.check_unsaved_changes():
            self.canvas_editor.clear_canvas()
            self.data_explorer.clear_data()
            self.inspector_panel.clear_selection()
            self.current_project_path = None
            self.mark_as_saved()
            self.status_bar.showMessage("Nový projekt vytvořen")
            
    def open_project(self):
        """Otevření existujícího projektu"""
        if not self.check_unsaved_changes():
            return
            
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "Otevřít projekt", 
            "", 
            "Report Canvas projekty (*.rcproj);;JSON soubory (*.json)"
        )
        
        if file_path:
            self.load_project(file_path)
            
    def save_project(self):
        """Uložení aktuálního projektu"""
        if self.current_project_path:
            self.save_project_to_path(self.current_project_path)
        else:
            self.save_project_as()
            
    def save_project_as(self):
        """Uložení projektu pod novým názvem"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Uložit projekt jako",
            "",
            "Report Canvas projekty (*.rcproj);;JSON soubory (*.json)"
        )
        
        if file_path:
            self.save_project_to_path(file_path)
            
    def load_project(self, file_path: str):
        """Načtení projektu ze souboru"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
                
            # Načtení dat do komponent
            self.canvas_editor.load_from_data(project_data.get('canvas', {}))
            self.data_explorer.load_from_data(project_data.get('data_sources', {}))
            
            self.current_project_path = file_path
            self.mark_as_saved()
            self.status_bar.showMessage(f"Projekt načten: {Path(file_path).name}")
            self.project_loaded.emit(file_path)
            
        except Exception as e:
            QMessageBox.critical(self, "Chyba", f"Nepodařilo se načíst projekt:\n{str(e)}")
            
    def save_project_to_path(self, file_path: str):
        """Uložení projektu do souboru"""
        try:
            project_data = {
                'version': '1.0',
                'canvas': self.canvas_editor.get_save_data(),
                'data_sources': self.data_explorer.get_save_data(),
                'metadata': {
                    'created': self.canvas_editor.creation_time,
                    'modified': self.canvas_editor.get_current_time()
                }
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)
                
            self.current_project_path = file_path
            self.mark_as_saved()
            self.status_bar.showMessage(f"Projekt uložen: {Path(file_path).name}")
            self.project_saved.emit(file_path)
            
        except Exception as e:
            QMessageBox.critical(self, "Chyba", f"Nepodařilo se uložit projekt:\n{str(e)}")
            
    def auto_save(self):
        """Automatické uložení"""
        if self.is_modified and self.current_project_path:
            self.save_project_to_path(self.current_project_path)
            
    def check_unsaved_changes(self) -> bool:
        """Kontrola neuložených změn"""
        if not self.is_modified:
            return True
            
        reply = QMessageBox.question(
            self,
            "Neuložené změny",
            "Projekt obsahuje neuložené změny. Chcete je uložit?",
            QMessageBox.StandardButton.Save | 
            QMessageBox.StandardButton.Discard | 
            QMessageBox.StandardButton.Cancel
        )
        
        if reply == QMessageBox.StandardButton.Save:
            self.save_project()
            return not self.is_modified
        elif reply == QMessageBox.StandardButton.Discard:
            return True
        else:
            return False
            
    # Canvas akce
    def add_chapter_node(self):
        """Přidání nového uzlu kapitoly"""
        self.canvas_editor.add_chapter_node()
        
    def delete_selected(self):
        """Smazání vybraných uzlů"""
        self.canvas_editor.delete_selected_nodes()
        
    # AI akce
    def generate_chapter(self, node_id: str):
        """Generování obsahu pro konkrétní kapitolu"""
        node_data = self.canvas_editor.get_node_data(node_id)
        if node_data:
            self.ai_integration.generate_chapter_content(node_id, node_data)
            self.status_bar.showMessage(f"Generuji obsah pro kapitolu {node_id}...")
            
    def generate_all_chapters(self):
        """Generování obsahu pro všechny kapitoly"""
        nodes = self.canvas_editor.get_all_nodes()
        if not nodes:
            QMessageBox.information(self, "Info", "Žádné kapitoly k vygenerování")
            return
            
        reply = QMessageBox.question(
            self,
            "Generování celého reportu",
            f"Chcete vygenerovat obsah pro všech {len(nodes)} kapitol?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.ai_integration.generate_all_chapters(nodes)
            self.status_bar.showMessage(f"Generuji obsah pro {len(nodes)} kapitol...")
            
    def on_generation_completed(self, node_id: str, content: str):
        """Callback po dokončení generování"""
        self.canvas_editor.update_node_content(node_id, content)
        self.status_bar.showMessage(f"Obsah vygenerován pro kapitolu {node_id}")
        self.mark_as_modified()
        
    def on_generation_failed(self, node_id: str, error: str):
        """Callback při chybě generování"""
        QMessageBox.warning(self, "Chyba generování", f"Nepodařilo se vygenerovat obsah pro kapitolu {node_id}:\n{error}")
        self.status_bar.showMessage("Generování selhalo")
        
    # View akce
    def zoom_in(self):
        """Přiblížení canvas"""
        self.canvas_editor.zoom_in()
        
    def zoom_out(self):
        """Oddálení canvas"""
        self.canvas_editor.zoom_out()
        
    def zoom_fit(self):
        """Přizpůsobení zobrazení"""
        self.canvas_editor.zoom_fit()
        
    # Edit akce
    def undo(self):
        """Zpět"""
        self.canvas_editor.undo()
        
    def redo(self):
        """Znovu"""
        self.canvas_editor.redo()
        
    # Export akce
    def export_to_html(self):
        """Export do HTML"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export do HTML",
            "",
            "HTML soubory (*.html)"
        )
        
        if file_path:
            try:
                html_content = self.canvas_editor.export_to_html()
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                self.status_bar.showMessage(f"Export do HTML dokončen: {Path(file_path).name}")
            except Exception as e:
                QMessageBox.critical(self, "Chyba", f"Export do HTML selhal:\n{str(e)}")
                
    def export_to_pdf(self):
        """Export do PDF"""
        QMessageBox.information(self, "Info", "Export do PDF bude implementován v budoucí verzi")
        
    # Nastavení a nápověda
    def show_ai_settings(self):
        """Zobrazení nastavení AI"""
        from .ai_settings_dialog import AISettingsDialog
        dialog = AISettingsDialog(self.ai_integration, self)
        dialog.exec()
        
    def show_about(self):
        """Zobrazení informací o aplikaci"""
        QMessageBox.about(
            self,
            "O aplikaci",
            """<h3>Analytický Report Canvas</h3>
            <p>MVP verze 1.0</p>
            <p>Vizuální editor pro tvorbu analytických zpráv</p>
            <p>© 2024 LimWrapp Team</p>"""
        )
        
    def closeEvent(self, event):
        """Obsluha zavření aplikace"""
        if self.check_unsaved_changes():
            event.accept()
        else:
            event.ignore()


def main():
    """Hlavní funkce pro spuštění aplikace"""
    if not PYQT_AVAILABLE:
        print("Chyba: PyQt6 není dostupné")
        print("Instalujte: pip install PyQt6")
        return False
        
    app = QApplication(sys.argv)
    app.setApplicationName("Report Canvas")
    app.setApplicationVersion("1.0.0")
    
    window = ReportCanvasMainWindow()
    window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())