"""
AI Integration - Integrace s existujícím AI systémem LimWrapp
Používá centrální AI manager místo vlastní implementace OpenAI
"""

import os
import json
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

# Přidání src do path pro import AI systému
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from PyQt6.QtCore import QObject, pyqtSignal, QThread, QTimer
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    # Fallback třídy pro testování bez PyQt6
    class QObject:
        def __init__(self):
            pass
    class QThread:
        def __init__(self):
            pass
        def start(self):
            pass
        def terminate(self):
            pass
        def wait(self):
            pass
    def pyqtSignal(*args):
        return None

# Import existujícího AI systému
try:
    from ai.integration import get_ai_integration, is_ai_available
    from ai.ai_manager import AIManager
    AI_SYSTEM_AVAILABLE = True
except ImportError:
    AI_SYSTEM_AVAILABLE = False


class AIGenerationWorker(QThread):
    """Worker thread pro AI generování pomocí existujícího AI systému"""
    
    generation_completed = pyqtSignal(str, str)  # node_id, content
    generation_failed = pyqtSignal(str, str)  # node_id, error
    
    def __init__(self, node_id: str, node_data: Dict[str, Any], ai_integration):
        super().__init__()
        self.node_id = node_id
        self.node_data = node_data
        self.ai_integration = ai_integration
        
    def run(self):
        """Spuštění generování v separátním vlákně"""
        try:
            content = self.generate_content()
            self.generation_completed.emit(self.node_id, content)
        except Exception as e:
            self.generation_failed.emit(self.node_id, str(e))
            
    def generate_content(self) -> str:
        """Generování obsahu pomocí existujícího AI systému"""
        if not self.ai_integration or not self.ai_integration.is_available():
            raise Exception("AI systém není dostupný")
            
        # Sestavení promptu
        prompt = self.build_prompt()
        system_prompt = self.get_system_prompt()
        
        # Použití existujícího AI manageru
        ai_manager = self.ai_integration.ai_manager
        
        # Vytvoření zpráv pro chat completion
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        # Volání přes enhanced OpenAI client
        response = ai_manager.openai_client.chat_completion(
            messages=messages,
            model="gpt-4o-mini",  # Použití dostupného modelu
            temperature=0.7 if self.node_data.get('creative_mode', False) else 0.3,
            max_tokens=self.node_data.get('max_length', 1000)
        )
        
        return response.content.strip()
        
    def build_prompt(self) -> str:
        """Sestavení promptu pro AI"""
        parts = []
        
        # Základní instrukce
        title = self.node_data.get('title', 'Kapitola')
        parts.append(f"Napište obsah pro kapitolu '{title}'.")
        
        # Uživatelské instrukce
        user_prompt = self.node_data.get('prompt', '').strip()
        if user_prompt:
            parts.append(f"Specifické instrukce: {user_prompt}")
            
        # Datové zdroje
        if self.node_data.get('include_data', True):
            data_sources = self.node_data.get('data_sources', [])
            if data_sources:
                parts.append("Dostupná data:")
                for i, data in enumerate(data_sources, 1):
                    data_desc = self.describe_data_source(data)
                    parts.append(f"{i}. {data_desc}")
                    
        # Požadavky na formát
        parts.append("Požadavky:")
        parts.append("- Použijte český jazyk")
        parts.append("- Strukturujte text do odstavců")
        parts.append("- Buďte konkrétní a faktický")
        parts.append("- Nepoužívejte markdown formátování")
        
        return "\n\n".join(parts)
        
    def describe_data_source(self, data: Dict[str, Any]) -> str:
        """Popis datového zdroje pro prompt"""
        data_type = data.get('type', 'unknown')
        
        if data_type == 'csv_column':
            return f"CSV sloupec '{data.get('column_name', 'N/A')}' ze souboru {data.get('file_path', 'N/A').split('/')[-1]}"
        elif data_type == 'csv_file':
            headers = data.get('headers', [])
            row_count = data.get('row_count', 0)
            return f"CSV soubor s {len(headers)} sloupci a {row_count} řádky dat"
        elif data_type == 'excel_file':
            return f"Excel soubor {data.get('path', 'N/A').split('/')[-1]}"
        elif data_type == 'user_file':
            ext = data.get('extension', '')
            return f"Uživatelský soubor ({ext}): {data.get('path', 'N/A').split('/')[-1]}"
        elif data_type == 'report_result':
            return f"Výsledek z jiné kapitoly: {data.get('result_id', 'N/A')}"
        else:
            return f"Datový zdroj typu {data_type}"
            
    def get_system_prompt(self) -> str:
        """Získání systémového promptu"""
        system_prompt_type = self.node_data.get('system_prompt', 'Analytik - obecný')
        
        prompts = {
            'Analytik - obecný': """Jste zkušený datový analytik. Vaším úkolem je vytvářet jasné, srozumitelné a faktické analýzy dat. Zaměřujte se na klíčová zjištění, trendy a doporučení. Používejte profesionální, ale přístupný jazyk.""",
            
            'Analytik - technický': """Jste technický datový analytik. Vytvářejte detailní technické analýzy s důrazem na metodologii, statistické ukazatele a technické aspekty. Používejte odbornou terminologii a buďte precizní v číslech a výpočtech.""",
            
            'Manažerský souhrn': """Jste business analytik připravující materiály pro management. Zaměřujte se na strategické poznatky, obchodní dopady a konkrétní doporučení pro rozhodování. Používejte jasný, výstižný jazyk orientovaný na výsledky.""",
            
            'Akademický styl': """Jste akademický výzkumník. Vytvářejte strukturované, vědecky podložené analýzy s důrazem na metodologii, objektivitu a kritické zhodnocení. Používejte formální akademický jazyk.""",
            
            'Novinářský styl': """Jste datový novinář. Vytvářejte poutavé, ale faktické příběhy založené na datech. Zaměřujte se na to, co je pro čtenáře zajímavé a relevantní. Používejte živý, ale profesionální jazyk."""
        }
        
        return prompts.get(system_prompt_type, prompts['Analytik - obecný'])


class AIIntegration(QObject):
    """Hlavní třída pro AI integraci s existujícím AI systémem"""
    
    # Signály
    generation_completed = pyqtSignal(str, str)  # node_id, content
    generation_failed = pyqtSignal(str, str)  # node_id, error
    generation_progress = pyqtSignal(str, int)  # node_id, progress_percent
    
    def __init__(self):
        super().__init__()
        
        if not PYQT_AVAILABLE:
            raise ImportError("PyQt6 není dostupné")
            
        self.ai_integration = self.get_ai_integration()
        self.active_workers: Dict[str, AIGenerationWorker] = {}
        
    def get_ai_integration(self):
        """Získání existující AI integrace"""
        try:
            if AI_SYSTEM_AVAILABLE:
                # Pokusíme se získat existující AI integraci
                ai_integration = get_ai_integration()
                if ai_integration is None:
                    # Pokud neexistuje, zkusíme inicializovat s dummy config managerem
                    from ai.integration import AIIntegration as CoreAIIntegration
                    
                    # Vytvoříme dummy config manager
                    class DummyConfigManager:
                        def get_config(self, key, default=None):
                            return default
                    
                    dummy_config = DummyConfigManager()
                    ai_integration = CoreAIIntegration(dummy_config)
                    if ai_integration.initialize():
                        return ai_integration
                return ai_integration
            return None
        except Exception as e:
            print(f"Chyba při získávání AI integrace: {e}")
            return None
        
    def is_available(self) -> bool:
        """Kontrola dostupnosti AI služeb"""
        return (AI_SYSTEM_AVAILABLE and 
                self.ai_integration is not None and 
                self.ai_integration.is_available())
        
    def get_status_message(self) -> str:
        """Získání statusové zprávy"""
        if not AI_SYSTEM_AVAILABLE:
            return "AI systém není dostupný"
        elif not self.ai_integration:
            return "AI integrace není inicializována"
        elif not self.ai_integration.is_available():
            return "AI služby nejsou dostupné"
        else:
            return "AI služby jsou dostupné"
            
    def generate_chapter_content(self, node_id: str, node_data: Dict[str, Any]):
        """Generování obsahu pro kapitolu"""
        if not self.is_available():
            error_msg = self.get_status_message()
            self.generation_failed.emit(node_id, error_msg)
            return
            
        # Kontrola, zda už pro tento uzel neběží generování
        if node_id in self.active_workers:
            self.generation_failed.emit(node_id, "Generování pro tento uzel již běží")
            return
            
        # Vytvoření a spuštění worker threadu
        worker = AIGenerationWorker(node_id, node_data, self.ai_integration)
        worker.generation_completed.connect(self.on_worker_completed)
        worker.generation_failed.connect(self.on_worker_failed)
        worker.finished.connect(lambda: self.cleanup_worker(node_id))
        
        self.active_workers[node_id] = worker
        worker.start()
        
    def generate_all_chapters(self, nodes_data: List[Dict[str, Any]]):
        """Generování obsahu pro všechny kapitoly"""
        if not self.is_available():
            error_msg = self.get_status_message()
            # Emitovat chybu pro první uzel jako reprezentativní
            if nodes_data:
                self.generation_failed.emit(nodes_data[0].get('node_id', 'unknown'), error_msg)
            return
            
        # Spuštění generování pro každý uzel postupně
        for node_data in nodes_data:
            node_id = node_data.get('node_id')
            if node_id and node_id not in self.active_workers:
                # Malé zpoždění mezi požadavky
                QTimer.singleShot(len(self.active_workers) * 1000, 
                                lambda nd=node_data: self.generate_chapter_content(nd.get('node_id'), nd))
                                
    def on_worker_completed(self, node_id: str, content: str):
        """Obsluha dokončení generování"""
        self.generation_completed.emit(node_id, content)
        
    def on_worker_failed(self, node_id: str, error: str):
        """Obsluha chyby generování"""
        self.generation_failed.emit(node_id, error)
        
    def cleanup_worker(self, node_id: str):
        """Úklid worker threadu"""
        if node_id in self.active_workers:
            del self.active_workers[node_id]
            
    def cancel_generation(self, node_id: str):
        """Zrušení generování pro uzel"""
        if node_id in self.active_workers:
            worker = self.active_workers[node_id]
            worker.terminate()
            worker.wait()
            del self.active_workers[node_id]
            
    def cancel_all_generations(self):
        """Zrušení všech běžících generování"""
        for node_id in list(self.active_workers.keys()):
            self.cancel_generation(node_id)
            
    def get_active_generations(self) -> List[str]:
        """Získání seznamu aktivních generování"""
        return list(self.active_workers.keys())
        
    def get_usage_statistics(self) -> Dict[str, Any]:
        """Získání statistik použití AI"""
        if not self.is_available():
            return {
                'success': False,
                'error': self.get_status_message()
            }
            
        try:
            return self.ai_integration.get_usage_statistics()
        except Exception as e:
            return {
                'success': False,
                'error': f'Chyba při získávání statistik: {str(e)}'
            }
        
    def test_connection(self) -> Dict[str, Any]:
        """Test připojení k AI službě"""
        if not self.is_available():
            return {
                'success': False,
                'error': self.get_status_message()
            }
            
        try:
            # Test pomocí existujícího AI systému
            ai_manager = self.ai_integration.ai_manager
            
            # Jednoduchý test požadavek
            response = ai_manager.openai_client.chat_completion(
                messages=[
                    {"role": "user", "content": "Odpověz pouze 'OK' pro test připojení."}
                ],
                model="gpt-4o-mini",
                max_tokens=10
            )
            
            if response.content.strip().upper() == 'OK':
                return {'success': True}
            else:
                return {
                    'success': False,
                    'error': 'Neočekávaná odpověď z API'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'Chyba připojení: {str(e)}'
            }


# Utility funkce pro konfiguraci
def create_ai_config_template():
    """Vytvoření šablony konfiguračního souboru"""
    config_dir = os.path.join(os.path.dirname(__file__), '..', 'config')
    os.makedirs(config_dir, exist_ok=True)
    
    config_path = os.path.join(config_dir, 'ai_config.py')
    
    if not os.path.exists(config_path):
        template = '''"""
AI Configuration for Report Canvas
Konfigurace AI služeb pro Report Canvas
"""

# OpenAI API Key
# Získejte na: https://platform.openai.com/api-keys
OPENAI_API_KEY = "your-openai-api-key-here"

# Model settings
DEFAULT_MODEL = "gpt-3.5-turbo"
MAX_TOKENS = 1000
TEMPERATURE = 0.3

# Rate limiting
MAX_REQUESTS_PER_MINUTE = 20
'''
        
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(template)
            
        return config_path
    
    return None