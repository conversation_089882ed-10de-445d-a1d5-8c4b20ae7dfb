"""
Chapter Node - Uzel kapitoly na canvas
Reprezentuje jednu kapitolu reportu s přepínatelnými z<PERSON>lož<PERSON>
"""

from typing import Dict, List, Any, Optional
from datetime import datetime

try:
    from PyQt6.QtWidgets import (
        QGraphicsWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QTextEdit,
        QLineEdit, QLabel, QPushButton, QFrame, QComboBox, QListWidget,
        QListWidgetItem, QWidget, QGraphicsProxyWidget, QSizePolicy
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QRectF, QSizeF
    from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QFont
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False


class ChapterNode(QGraphicsWidget):
    """<PERSON><PERSON> kapitoly s přepínatelnými z<PERSON>ž<PERSON>"""
    
    # Signály
    content_changed = pyqtSignal(str)  # content
    node_modified = pyqtSignal()
    selection_changed = pyqtSignal(str, bool)  # node_id, selected
    generation_requested = pyqtSignal(str)  # node_id
    
    def __init__(self, node_id: str, title: str = "Nová kapitola"):
        super().__init__()
        
        if not PYQT_AVAILABLE:
            raise ImportError("PyQt6 není dostupné")
            
        self.node_id = node_id
        self.title = title
        self.content = ""
        self.prompt = ""
        self.data_sources: List[Dict[str, Any]] = []
        self.is_manually_edited = False
        self.creation_time = datetime.now().isoformat()
        
        # Nastavení uzlu
        self.setFlag(QGraphicsWidget.GraphicsItemFlag.ItemIsMovable, True)
        self.setFlag(QGraphicsWidget.GraphicsItemFlag.ItemIsSelectable, True)
        self.setFlag(QGraphicsWidget.GraphicsItemFlag.ItemSendsGeometryChanges, True)
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """Inicializace uživatelského rozhraní"""
        # Hlavní widget
        self.main_widget = QWidget()
        self.main_widget.setFixedSize(300, 400)
        self.main_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 2px solid #ccc;
                border-radius: 8px;
            }
            QWidget:hover {
                border-color: #007acc;
            }
        """)
        
        # Layout
        layout = QVBoxLayout(self.main_widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(5)
        
        # Hlavička
        self.create_header(layout)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Záložka "Obsah"
        self.create_content_tab()
        
        # Záložka "Nastavení & Prompt"
        self.create_settings_tab()
        
        # Tlačítka
        self.create_buttons(layout)
        
        # Proxy widget pro graphics scene
        self.proxy = QGraphicsProxyWidget(self)
        self.proxy.setWidget(self.main_widget)
        
        # Nastavení velikosti
        self.setGeometry(QRectF(0, 0, 300, 400))
        
    def create_header(self, layout):
        """Vytvoření hlavičky uzlu"""
        header_frame = QFrame()
        header_frame.setStyleSheet("QFrame { border: none; background-color: #f0f0f0; border-radius: 4px; }")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(5, 5, 5, 5)
        
        # ID uzlu
        id_label = QLabel(f"#{self.node_id}")
        id_label.setStyleSheet("font-weight: bold; color: #666; font-size: 10px;")
        header_layout.addWidget(id_label)
        
        header_layout.addStretch()
        
        # Editovatelný nadpis
        self.title_edit = QLineEdit(self.title)
        self.title_edit.setStyleSheet("QLineEdit { border: none; background: transparent; font-weight: bold; }")
        self.title_edit.textChanged.connect(self.on_title_changed)
        header_layout.addWidget(self.title_edit)
        
        layout.addWidget(header_frame)
        
    def create_content_tab(self):
        """Vytvoření záložky 'Obsah'"""
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Status label
        self.status_label = QLabel("Žádný obsah")
        self.status_label.setStyleSheet("color: #888; font-size: 11px;")
        layout.addWidget(self.status_label)
        
        # Text editor
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("Zde bude vygenerovaný obsah kapitoly...")
        self.content_edit.textChanged.connect(self.on_content_changed)
        layout.addWidget(self.content_edit)
        
        self.tab_widget.addTab(content_widget, "Obsah")
        
    def create_settings_tab(self):
        """Vytvoření záložky 'Nastavení & Prompt'"""
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)
        
        # Datové vstupy
        data_label = QLabel("Datové vstupy:")
        data_label.setStyleSheet("font-weight: bold; font-size: 11px;")
        layout.addWidget(data_label)
        
        self.data_list = QListWidget()
        self.data_list.setMaximumHeight(80)
        self.data_list.setStyleSheet("QListWidget { font-size: 10px; }")
        layout.addWidget(self.data_list)
        
        # Prompt
        prompt_label = QLabel("Instrukce pro AI:")
        prompt_label.setStyleSheet("font-weight: bold; font-size: 11px;")
        layout.addWidget(prompt_label)
        
        self.prompt_edit = QTextEdit()
        self.prompt_edit.setPlaceholderText("Napište instrukce pro AI generování...")
        self.prompt_edit.setMaximumHeight(100)
        self.prompt_edit.textChanged.connect(self.on_prompt_changed)
        layout.addWidget(self.prompt_edit)
        
        # Systémový prompt
        system_label = QLabel("Systémový prompt:")
        system_label.setStyleSheet("font-weight: bold; font-size: 11px;")
        layout.addWidget(system_label)
        
        self.system_combo = QComboBox()
        self.system_combo.addItems([
            "Analytik - obecný",
            "Analytik - technický",
            "Manažerský souhrn",
            "Akademický styl",
            "Vlastní..."
        ])
        layout.addWidget(self.system_combo)
        
        layout.addStretch()
        
        self.tab_widget.addTab(settings_widget, "Nastavení")
        
    def create_buttons(self, layout):
        """Vytvoření tlačítek"""
        button_layout = QHBoxLayout()
        
        # Tlačítko generování
        self.generate_btn = QPushButton("🔄 Generovat")
        self.generate_btn.clicked.connect(self.request_generation)
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #005999;
            }
        """)
        button_layout.addWidget(self.generate_btn)
        
        # Tlačítko přepnutí pohledu
        self.toggle_btn = QPushButton("👁️")
        self.toggle_btn.clicked.connect(self.toggle_view)
        self.toggle_btn.setToolTip("Přepnout pohled")
        button_layout.addWidget(self.toggle_btn)
        
        layout.addLayout(button_layout)
        
    def setup_connections(self):
        """Nastavení propojení signálů"""
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
    def on_title_changed(self):
        """Obsluha změny nadpisu"""
        self.title = self.title_edit.text()
        self.node_modified.emit()
        
    def on_content_changed(self):
        """Obsluha změny obsahu"""
        new_content = self.content_edit.toPlainText()
        if new_content != self.content:
            self.content = new_content
            self.is_manually_edited = True
            self.update_status_label()
            self.content_changed.emit(self.content)
            self.node_modified.emit()
            
    def on_prompt_changed(self):
        """Obsluha změny promptu"""
        self.prompt = self.prompt_edit.toPlainText()
        self.node_modified.emit()
        
    def on_tab_changed(self, index):
        """Obsluha změny záložky"""
        # Aktualizace podle aktivní záložky
        pass
        
    def toggle_view(self):
        """Přepnutí mezi záložkami"""
        current_index = self.tab_widget.currentIndex()
        new_index = 1 - current_index  # Přepnutí mezi 0 a 1
        self.tab_widget.setCurrentIndex(new_index)
        
    def request_generation(self):
        """Požadavek na generování obsahu"""
        if self.is_manually_edited and self.content.strip():
            # Varování o přepsání
            from PyQt6.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                None,
                "Potvrzení",
                "Stávající obsah bude přepsán. Pokračovat?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply != QMessageBox.StandardButton.Yes:
                return
                
        self.generation_requested.emit(self.node_id)
        
    def add_data_source(self, data_info: Dict[str, Any]):
        """Přidání datového zdroje"""
        self.data_sources.append(data_info)
        self.update_data_list()
        self.node_modified.emit()
        
    def update_data_list(self):
        """Aktualizace seznamu datových zdrojů"""
        self.data_list.clear()
        for data in self.data_sources:
            data_type = data.get('type', 'unknown')
            if data_type == 'csv_column':
                text = f"CSV: {data.get('column_name', 'N/A')}"
            elif data_type == 'csv_file':
                text = f"CSV soubor: {data.get('path', 'N/A').split('/')[-1]}"
            elif data_type == 'excel_file':
                text = f"Excel: {data.get('path', 'N/A').split('/')[-1]}"
            elif data_type == 'user_file':
                text = f"Soubor: {data.get('path', 'N/A').split('/')[-1]}"
            elif data_type == 'report_result':
                text = f"Výsledek: {data.get('result_id', 'N/A')}"
            else:
                text = f"Data: {data_type}"
                
            item = QListWidgetItem(text)
            item.setData(Qt.ItemDataRole.UserRole, data)
            self.data_list.addItem(item)
            
    def update_status_label(self):
        """Aktualizace status labelu"""
        if self.is_manually_edited:
            self.status_label.setText("✏️ Manuálně upraveno")
            self.status_label.setStyleSheet("color: #ff6600; font-size: 11px;")
        elif self.content.strip():
            self.status_label.setText("🤖 Vygenerováno AI")
            self.status_label.setStyleSheet("color: #00aa00; font-size: 11px;")
        else:
            self.status_label.setText("Žádný obsah")
            self.status_label.setStyleSheet("color: #888; font-size: 11px;")
            
    def set_generated_content(self, content: str):
        """Nastavení vygenerovaného obsahu"""
        self.content = content
        self.content_edit.setPlainText(content)
        self.is_manually_edited = False
        self.update_status_label()
        
    def get_node_data(self) -> Dict[str, Any]:
        """Získání dat uzlu"""
        return {
            'node_id': self.node_id,
            'title': self.title,
            'content': self.content,
            'prompt': self.prompt,
            'data_sources': self.data_sources,
            'is_manually_edited': self.is_manually_edited,
            'creation_time': self.creation_time,
            'system_prompt': self.system_combo.currentText(),
            'current_tab': self.tab_widget.currentIndex()
        }
        
    def load_node_data(self, data: Dict[str, Any]):
        """Načtení dat uzlu"""
        self.node_id = data.get('node_id', self.node_id)
        self.title = data.get('title', self.title)
        self.content = data.get('content', '')
        self.prompt = data.get('prompt', '')
        self.data_sources = data.get('data_sources', [])
        self.is_manually_edited = data.get('is_manually_edited', False)
        self.creation_time = data.get('creation_time', self.creation_time)
        
        # Aktualizace UI
        self.title_edit.setText(self.title)
        self.content_edit.setPlainText(self.content)
        self.prompt_edit.setPlainText(self.prompt)
        
        # Systémový prompt
        system_prompt = data.get('system_prompt', 'Analytik - obecný')
        index = self.system_combo.findText(system_prompt)
        if index >= 0:
            self.system_combo.setCurrentIndex(index)
            
        # Aktuální záložka
        current_tab = data.get('current_tab', 0)
        self.tab_widget.setCurrentIndex(current_tab)
        
        # Aktualizace seznamů a statusu
        self.update_data_list()
        self.update_status_label()
        
    def itemChange(self, change, value):
        """Obsluha změn položky"""
        if change == QGraphicsWidget.GraphicsItemChange.ItemPositionHasChanged:
            self.node_modified.emit()
        elif change == QGraphicsWidget.GraphicsItemChange.ItemSelectedHasChanged:
            self.selection_changed.emit(self.node_id, self.isSelected())
            
        return super().itemChange(change, value)
        
    def paint(self, painter, option, widget):
        """Vykreslení uzlu"""
        # Základní vykreslení se děje přes proxy widget
        super().paint(painter, option, widget)
        
        # Přidání výběrového rámečku
        if self.isSelected():
            painter.setPen(QPen(QColor(0, 120, 204), 3))
            painter.setBrush(QBrush())
            painter.drawRect(self.boundingRect())
            
    def boundingRect(self):
        """Ohraničující obdélník"""
        return QRectF(0, 0, 300, 400)