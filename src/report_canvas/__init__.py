# Report Canvas Module
# Analyticky Report Canvas - MVP implementace

__version__ = "1.0.0"
__author__ = "LimWrapp Team"

# Podmínené importy - pouze pokud je PyQt6 dostupné
try:
    from .main_window import ReportCanvasMainWindow
    from .data_explorer import DataExplorer
    from .inspector_panel import InspectorPanel
    PYQT_AVAILABLE = True
except (ImportError, NameError):
    PYQT_AVAILABLE = False
    ReportCanvasMainWindow = None
    DataExplorer = None
    InspectorPanel = None

# AI integrace je dostupná i bez PyQt6
from .ai_integration import AIIntegration

__all__ = [
    'ReportCanvasMainWindow',
    'DataExplorer',
    'InspectorPanel',
    'AIIntegration',
    'PYQT_AVAILABLE'
]