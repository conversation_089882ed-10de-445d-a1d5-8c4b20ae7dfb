"""
Canvas Editor - Střední panel pro vizuální editaci reportu
Hlavní pracovní plocha s uzly kapitol a jejich propojením
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QGraphicsView, QGraphicsScene,
        QGraphicsItem, QGraphicsWidget, QGraphicsProxyWidget, QPushButton,
        QLabel, QTextEdit, QTabWidget, QLineEdit, QComboBox, QSpinBox,
        QFrame, QScrollArea, QMessageBox, QMenu
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QRectF, QPointF, QTimer
    from PyQt6.QtGui import (
        QPainter, QPen, QBrush, QColor, QFont, QFontMetrics,
        QContextMenuEvent, QMouseEvent, QWheelEvent
    )
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

from .chapter_node import ChapterNode


class CanvasEditor(QWidget):
    """Střední panel - Canvas Editor pro vizuální editaci reportu"""
    
    # Signály
    node_selected = pyqtSignal(str)  # node_id
    canvas_modified = pyqtSignal()
    node_content_changed = pyqtSignal(str, str)  # node_id, content
    
    def __init__(self):
        super().__init__()
        
        if not PYQT_AVAILABLE:
            raise ImportError("PyQt6 není dostupné")
            
        self.nodes: Dict[str, ChapterNode] = {}
        self.connections: List[Tuple[str, str]] = []  # (parent_id, child_id)
        self.selected_node_id: Optional[str] = None
        self.creation_time = datetime.now().isoformat()
        
        # Undo/Redo stack
        self.undo_stack = []
        self.redo_stack = []
        self.max_undo_steps = 50
        
        self.init_ui()
        self.setup_auto_save_state()
        
    def init_ui(self):
        """Inicializace uživatelského rozhraní"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Hlavička s nástroji
        self.create_toolbar()
        layout.addWidget(self.toolbar)
        
        # Graphics View pro canvas
        self.graphics_view = QGraphicsView()
        self.graphics_scene = QGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)
        
        # Nastavení view
        self.graphics_view.setDragMode(QGraphicsView.DragMode.RubberBandDrag)
        self.graphics_view.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.graphics_view.setViewportUpdateMode(QGraphicsView.ViewportUpdateMode.FullViewportUpdate)
        
        # Nastavení scene
        self.graphics_scene.setSceneRect(-2000, -2000, 4000, 4000)
        self.graphics_scene.setBackgroundBrush(QBrush(QColor(245, 245, 245)))
        
        # Propojení signálů
        self.graphics_scene.selectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(self.graphics_view)
        
        # Kontextové menu
        self.graphics_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.graphics_view.customContextMenuRequested.connect(self.show_context_menu)
        
    def create_toolbar(self):
        """Vytvoření toolbar s nástroji"""
        self.toolbar = QFrame()
        self.toolbar.setFixedHeight(40)
        self.toolbar.setStyleSheet("QFrame { border-bottom: 1px solid #ccc; background: #f8f8f8; }")
        
        layout = QHBoxLayout(self.toolbar)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # Tlačítka
        add_node_btn = QPushButton("+ Kapitola")
        add_node_btn.clicked.connect(lambda: self.add_chapter_node())
        layout.addWidget(add_node_btn)
        
        delete_btn = QPushButton("🗑 Smazat")
        delete_btn.clicked.connect(self.delete_selected_nodes)
        layout.addWidget(delete_btn)
        
        layout.addWidget(QLabel("|"))  # Separator
        
        # Zoom ovládání
        zoom_out_btn = QPushButton("🔍-")
        zoom_out_btn.clicked.connect(self.zoom_out)
        layout.addWidget(zoom_out_btn)
        
        zoom_fit_btn = QPushButton("⊞")
        zoom_fit_btn.clicked.connect(self.zoom_fit)
        layout.addWidget(zoom_fit_btn)
        
        zoom_in_btn = QPushButton("🔍+")
        zoom_in_btn.clicked.connect(self.zoom_in)
        layout.addWidget(zoom_in_btn)
        
        layout.addStretch()
        
        # Informace
        self.info_label = QLabel("Prázdný canvas - klikněte pravým tlačítkem pro přidání kapitoly")
        self.info_label.setStyleSheet("color: gray; font-size: 11px;")
        layout.addWidget(self.info_label)
        
    def setup_auto_save_state(self):
        """Nastavení automatického ukládání stavu pro undo/redo"""
        self.state_timer = QTimer()
        self.state_timer.timeout.connect(self.save_state_for_undo)
        self.state_timer.setSingleShot(True)
        
    def add_chapter_node(self, position: Optional[QPointF] = None):
        """Přidání nového uzlu kapitoly"""
        if position is None:
            # Výchozí pozice ve středu view
            center = self.graphics_view.mapToScene(self.graphics_view.rect().center())
            position = center
            
        # Generování ID
        node_id = f"KAP{len(self.nodes) + 1:02d}"
        
        # Vytvoření uzlu
        node = ChapterNode(node_id, f"Kapitola {len(self.nodes) + 1}")
        node.setPos(position)
        
        # Propojení signálů
        node.content_changed.connect(lambda content: self.node_content_changed.emit(node_id, content))
        node.node_modified.connect(self.canvas_modified.emit)
        node.selection_changed.connect(self.on_node_selection_changed)
        
        # Přidání do scene a registrace
        self.graphics_scene.addItem(node)
        self.nodes[node_id] = node
        
        # Aktualizace UI
        self.update_info_label()
        self.canvas_modified.emit()
        self.save_state_for_undo()
        
        return node_id
        
    def delete_selected_nodes(self):
        """Smazání vybraných uzlů"""
        selected_items = self.graphics_scene.selectedItems()
        if not selected_items:
            return
            
        # Filtrování pouze ChapterNode objektů
        nodes_to_delete = [item for item in selected_items if isinstance(item, ChapterNode)]
        
        if not nodes_to_delete:
            return
            
        # Potvrzení
        if len(nodes_to_delete) == 1:
            message = f"Opravdu chcete smazat kapitolu '{nodes_to_delete[0].title}'?"
        else:
            message = f"Opravdu chcete smazat {len(nodes_to_delete)} kapitol?"
            
        reply = QMessageBox.question(self, "Potvrzení", message)
        if reply != QMessageBox.StandardButton.Yes:
            return
            
        # Uložení stavu pro undo
        self.save_state_for_undo()
        
        # Smazání uzlů
        for node in nodes_to_delete:
            node_id = node.node_id
            
            # Odstranění ze scene
            self.graphics_scene.removeItem(node)
            
            # Odstranění z registrace
            if node_id in self.nodes:
                del self.nodes[node_id]
                
            # Odstranění spojení
            self.connections = [(p, c) for p, c in self.connections if p != node_id and c != node_id]
            
        # Aktualizace UI
        self.update_info_label()
        self.canvas_modified.emit()
        
    def on_selection_changed(self):
        """Obsluha změny výběru ve scene"""
        selected_items = self.graphics_scene.selectedItems()
        
        if selected_items:
            # Najít první ChapterNode
            for item in selected_items:
                if isinstance(item, ChapterNode):
                    self.selected_node_id = item.node_id
                    self.node_selected.emit(item.node_id)
                    return
                    
        # Žádný uzel není vybrán
        self.selected_node_id = None
        self.node_selected.emit("")
        
    def on_node_selection_changed(self, node_id: str, selected: bool):
        """Obsluha změny výběru uzlu"""
        if selected:
            self.selected_node_id = node_id
            self.node_selected.emit(node_id)
        elif self.selected_node_id == node_id:
            self.selected_node_id = None
            self.node_selected.emit("")
            
    def show_context_menu(self, position):
        """Zobrazení kontextového menu"""
        # Převod pozice na scene souřadnice
        scene_pos = self.graphics_view.mapToScene(position)
        
        menu = QMenu(self)
        
        # Přidání kapitoly
        add_action = menu.addAction("➕ Přidat kapitolu")
        add_action.triggered.connect(lambda: self.add_chapter_node(scene_pos))
        
        # Pokud je něco vybráno
        selected_items = self.graphics_scene.selectedItems()
        if selected_items:
            menu.addSeparator()
            
            delete_action = menu.addAction("🗑 Smazat vybrané")
            delete_action.triggered.connect(self.delete_selected_nodes)
            
            # Pokud je vybrán jeden uzel
            chapter_nodes = [item for item in selected_items if isinstance(item, ChapterNode)]
            if len(chapter_nodes) == 1:
                node = chapter_nodes[0]
                
                menu.addSeparator()
                
                duplicate_action = menu.addAction("📋 Duplikovat")
                duplicate_action.triggered.connect(lambda: self.duplicate_node(node.node_id))
                
                generate_action = menu.addAction("🤖 Generovat obsah")
                generate_action.triggered.connect(lambda: self.request_generation(node.node_id))
                
        menu.exec(self.graphics_view.mapToGlobal(position))
        
    def duplicate_node(self, node_id: str):
        """Duplikování uzlu"""
        if node_id not in self.nodes:
            return
            
        original_node = self.nodes[node_id]
        
        # Nová pozice (posunutá)
        new_pos = original_node.pos() + QPointF(50, 50)
        
        # Vytvoření nového uzlu
        new_node_id = self.add_chapter_node(new_pos)
        new_node = self.nodes[new_node_id]
        
        # Kopírování dat
        original_data = original_node.get_node_data()
        new_node.load_node_data({
            **original_data,
            'title': f"{original_data['title']} (kopie)",
            'node_id': new_node_id
        })
        
    def request_generation(self, node_id: str):
        """Požadavek na generování obsahu"""
        if node_id in self.nodes:
            node = self.nodes[node_id]
            node.request_generation()
            
    def update_info_label(self):
        """Aktualizace informačního labelu"""
        node_count = len(self.nodes)
        if node_count == 0:
            self.info_label.setText("Prázdný canvas - klikněte pravým tlačítkem pro přidání kapitoly")
        else:
            self.info_label.setText(f"Kapitol: {node_count} | Vybráno: {self.selected_node_id or 'žádná'}")
            
    def add_data_to_selected_node(self, node_id: str, data_info: Dict[str, Any]):
        """Přidání dat k vybranému uzlu"""
        if self.selected_node_id and self.selected_node_id in self.nodes:
            node = self.nodes[self.selected_node_id]
            node.add_data_source(data_info)
            self.canvas_modified.emit()
            
    def update_node(self, node_id: str, node_data: Dict[str, Any]):
        """Aktualizace uzlu z inspektoru"""
        if node_id in self.nodes:
            node = self.nodes[node_id]
            node.load_node_data(node_data)
            self.canvas_modified.emit()
            
    def update_node_content(self, node_id: str, content: str):
        """Aktualizace obsahu uzlu"""
        if node_id in self.nodes:
            node = self.nodes[node_id]
            node.set_generated_content(content)
            self.canvas_modified.emit()
            
    def get_node_data(self, node_id: str) -> Optional[Dict[str, Any]]:
        """Získání dat uzlu"""
        if node_id in self.nodes:
            return self.nodes[node_id].get_node_data()
        return None
        
    def get_all_nodes(self) -> List[Dict[str, Any]]:
        """Získání všech uzlů"""
        return [node.get_node_data() for node in self.nodes.values()]
        
    # Zoom funkce
    def zoom_in(self):
        """Přiblížení"""
        self.graphics_view.scale(1.2, 1.2)
        
    def zoom_out(self):
        """Oddálení"""
        self.graphics_view.scale(0.8, 0.8)
        
    def zoom_fit(self):
        """Přizpůsobení zobrazení"""
        if self.nodes:
            self.graphics_view.fitInView(self.graphics_scene.itemsBoundingRect(), Qt.AspectRatioMode.KeepAspectRatio)
        else:
            self.graphics_view.resetTransform()
            
    # Undo/Redo
    def save_state_for_undo(self):
        """Uložení stavu pro undo"""
        state = self.get_save_data()
        self.undo_stack.append(state)
        
        # Omezení velikosti stacku
        if len(self.undo_stack) > self.max_undo_steps:
            self.undo_stack.pop(0)
            
        # Vymazání redo stacku
        self.redo_stack.clear()
        
    def undo(self):
        """Zpět"""
        if len(self.undo_stack) < 2:  # Potřebujeme alespoň 2 stavy
            return
            
        # Aktuální stav do redo
        current_state = self.undo_stack.pop()
        self.redo_stack.append(current_state)
        
        # Načtení předchozího stavu
        previous_state = self.undo_stack[-1]
        self.load_from_data(previous_state)
        
    def redo(self):
        """Znovu"""
        if not self.redo_stack:
            return
            
        # Načtení stavu z redo
        state = self.redo_stack.pop()
        self.undo_stack.append(state)
        self.load_from_data(state)
        
    # Persistence
    def get_save_data(self) -> Dict[str, Any]:
        """Získání dat pro uložení"""
        nodes_data = {}
        for node_id, node in self.nodes.items():
            nodes_data[node_id] = {
                **node.get_node_data(),
                'position': {'x': node.pos().x(), 'y': node.pos().y()}
            }
            
        return {
            'nodes': nodes_data,
            'connections': self.connections,
            'creation_time': self.creation_time,
            'version': '1.0'
        }
        
    def load_from_data(self, data: Dict[str, Any]):
        """Načtení dat ze souboru"""
        # Vymazání současného obsahu
        self.clear_canvas()
        
        # Načtení uzlů
        nodes_data = data.get('nodes', {})
        for node_id, node_data in nodes_data.items():
            # Vytvoření uzlu
            position = QPointF(
                node_data.get('position', {}).get('x', 0),
                node_data.get('position', {}).get('y', 0)
            )
            
            created_node_id = self.add_chapter_node(position)
            
            # Načtení dat uzlu
            if created_node_id in self.nodes:
                node = self.nodes[created_node_id]
                node.load_node_data(node_data)
                
                # Aktualizace ID pokud se liší
                if node_id != created_node_id:
                    del self.nodes[created_node_id]
                    node.node_id = node_id
                    self.nodes[node_id] = node
                    
        # Načtení spojení
        self.connections = data.get('connections', [])
        
        # Načtení metadata
        self.creation_time = data.get('creation_time', datetime.now().isoformat())
        
        self.update_info_label()
        
    def clear_canvas(self):
        """Vymazání canvas"""
        # Odstranění všech uzlů ze scene
        for node in self.nodes.values():
            self.graphics_scene.removeItem(node)
            
        # Vymazání registrace
        self.nodes.clear()
        self.connections.clear()
        self.selected_node_id = None
        
        self.update_info_label()
        
    def export_to_html(self) -> str:
        """Export do HTML"""
        html_parts = [
            "<!DOCTYPE html>",
            "<html>",
            "<head>",
            "<meta charset='utf-8'>",
            "<title>Analytický Report</title>",
            "<style>",
            "body { font-family: Arial, sans-serif; margin: 40px; }",
            "h1 { color: #333; border-bottom: 2px solid #333; }",
            "h2 { color: #666; margin-top: 30px; }",
            ".chapter { margin-bottom: 30px; padding: 20px; border-left: 4px solid #007acc; }",
            ".metadata { color: #888; font-size: 12px; margin-bottom: 10px; }",
            "</style>",
            "</head>",
            "<body>",
            "<h1>Analytický Report</h1>",
            f"<div class='metadata'>Vygenerováno: {datetime.now().strftime('%d.%m.%Y %H:%M')}</div>"
        ]
        
        # Seřazení kapitol podle pozice (shora dolů)
        sorted_nodes = sorted(self.nodes.values(), key=lambda n: n.pos().y())
        
        for node in sorted_nodes:
            node_data = node.get_node_data()
            html_parts.extend([
                "<div class='chapter'>",
                f"<h2>{node_data['title']}</h2>",
                f"<div class='metadata'>ID: {node_data['node_id']}</div>",
                f"<div>{node_data.get('content', 'Žádný obsah').replace(chr(10), '<br>')}</div>",
                "</div>"
            ])
            
        html_parts.extend([
            "</body>",
            "</html>"
        ])
        
        return "\n".join(html_parts)
        
    def get_current_time(self) -> str:
        """Získání aktuálního času"""
        return datetime.now().isoformat()