import os
import json
from datawrapper_client import DatawrapperClient

class ChartManager:
    def __init__(self):
        self.client = DatawrapperClient()
        self.chart_types = {}
        self.common_metadata = {}
        self.load_chart_types()
        
    def load_chart_types(self):
        """Načte typy grafů a metadata z JSON souboru"""
        try:
            with open('src/datawrapper_chart_types.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.chart_types = {
                    chart['name']: {
                        'icon': chart.get('icon', ''),  # Použít prázdný string jako fallback
                        'metadata': chart.get('specific_metadata', {})
                    }
                    for chart in data.get('chart_types', [])  # Použít get s prázdným seznamem jako fallback
                }
                self.common_metadata = data.get('common_metadata', {})  # <PERSON>už<PERSON>t get s prázdným slovníkem jako fallback
        except Exception as e:
            print(f"Chyba při načítání typů grafů: {str(e)}")
            # Fallback na základní typy
            self.chart_types = {
                "Bar Chart": {"icon": "", "metadata": {}},
                "Pie Chart": {"icon": "", "metadata": {}},
                "Line Chart": {"icon": "", "metadata": {}}
            }
            self.common_metadata = {
                "title": {"text": "string", "czech_name": "Název"},
                "locale": {"language": ["cs-CZ"], "czech_name": "Lokalizace"}
            }
        
    def create_chart(self, question_id, chart_type, prefix='', metadata=None):
        """Vytvoří nový graf pro otázku"""
        chart_id = self.client.create_chart(question_id, chart_type)
        
        # Vytvoření adresáře pro grafy pokud neexistuje
        os.makedirs('charts', exist_ok=True)
        
        # Vytvoření cesty k souboru
        filename = f"{prefix}{question_id}" if prefix else question_id
        image_path = f"charts/{filename}.png"
        
        # Aplikace metadat
        if metadata:
            self.update_chart_metadata(chart_id, metadata)
        
        # Export grafu
        self.client.export_chart(chart_id, image_path)
        return chart_id, image_path
        
    def update_chart(self, chart_id, data=None, options=None, metadata=None):
        """Aktualizuje existující graf"""
        if data:
            self.client.update_chart_data(chart_id, data)
        if options:
            self.client.update_chart_options(chart_id, options)
        if metadata:
            self.update_chart_metadata(chart_id, metadata)
            
    def update_chart_metadata(self, chart_id, metadata):
        """Aktualizuje metadata grafu"""
        # Validace metadat podle schématu
        validated_metadata = {}
        
        # Zpracování společných metadat
        for key, value in metadata.items():
            if key in self.common_metadata:
                schema = self.common_metadata[key]
                if self.validate_metadata_value(value, schema):
                    validated_metadata[key] = value
                    
        # Zpracování specifických metadat pro typ grafu
        chart_type = metadata.get('type')
        if chart_type in self.chart_types:
            specific_metadata = self.chart_types[chart_type].get('metadata', {})
            for key, value in metadata.items():
                if key in specific_metadata:
                    schema = specific_metadata[key]
                    if self.validate_metadata_value(value, schema):
                        validated_metadata[key] = value
                        
        if validated_metadata:
            self.client.update_chart_metadata(chart_id, validated_metadata)
            
    def validate_metadata_value(self, value, schema):
        """Validuje hodnotu metadat podle schématu"""
        if isinstance(schema, list):
            return value in schema
        elif isinstance(schema, str):
            if schema == "string":
                return isinstance(value, str)
            elif schema == "number":
                return isinstance(value, (int, float))
            elif schema == "boolean":
                return isinstance(value, bool)
            elif schema == "array":
                return isinstance(value, list)
        elif isinstance(schema, dict):
            # Pro vnořené slovníky kontrolujeme pouze existenci czech_name
            return True
        return False
            
    def get_chart_types(self):
        """Vrátí seznam dostupných typů grafů"""
        return list(self.chart_types.keys())
        
    def get_chart_metadata_schema(self, chart_type=None):
        """Vrátí schéma metadat pro daný typ grafu"""
        schema = {
            'common': self.common_metadata
        }
        if chart_type and chart_type in self.chart_types:
            schema['specific'] = self.chart_types[chart_type].get('metadata', {})
        return schema
        
    def get_chart_icon(self, chart_type):
        """Vrátí SVG ikonu pro daný typ grafu"""
        if chart_type in self.chart_types:
            return self.chart_types[chart_type].get('icon', '')
        return ''
        
    def suggest_chart_type(self, question_type, data_length):
        """Navrhne vhodný typ grafu podle typu otázky a množství dat"""
        suggestions = {
            "single_choice": "Bar Chart" if data_length > 8 else "Pie Chart",
            "multiple_choice": "Bar Chart",
            "array": "Bar Chart",
            "numeric": "Line Chart",
            "text": "Table"
        }
        return suggestions.get(question_type, "Bar Chart")
        
    def get_default_metadata(self, chart_type=None):
        """Vrátí výchozí metadata pro daný typ grafu"""
        defaults = {
            'locale': {
                'language': 'cs-CZ',
                'decimal_separator': ',',
                'thousand_separator': ' '
            },
            'layout': {
                'theme': 'Datawrapper',
                'dark_mode': False
            },
            'footer': {
                'data_download': True,
                'image_download': ['PNG'],
                'attribution': True
            }
        }
        
        if chart_type in self.chart_types:
            specific_metadata = self.chart_types[chart_type].get('metadata', {})
            if 'orientation' in specific_metadata:
                defaults['orientation'] = 'vertical'
            if 'stack_order' in specific_metadata:
                defaults['stack_order'] = 'ascending'
                
        return defaults
        
    def export_all_charts(self, survey_id, chart_ids, output_dir=None):
        """Exportuje všechny grafy průzkumu do PNG souborů"""
        if output_dir is None:
            output_dir = f"charts/{survey_id}"
            
        os.makedirs(output_dir, exist_ok=True)
        
        results = []
        for chart_id in chart_ids:
            try:
                image_path = f"{output_dir}/{chart_id}.png"
                self.client.export_chart(chart_id, image_path)
                results.append({
                    'chart_id': chart_id,
                    'path': image_path,
                    'success': True
                })
            except Exception as e:
                results.append({
                    'chart_id': chart_id,
                    'error': str(e),
                    'success': False
                })
                
        return results
