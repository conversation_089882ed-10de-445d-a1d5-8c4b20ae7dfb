"""
WordCloud Chart Generator
Externí generátor pro WordCloud grafy s AI enhancement
BEZPEČNÝ - neovlivňuje stávající Datawrapper workflow
"""

import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
import json

from .base_generator import BaseChartGenerator

logger = logging.getLogger(__name__)


class WordCloudChartGenerator(BaseChartGenerator):
    """
    Externí generátor pro WordCloud grafy
    BEZPEČNÝ - používá existující AI WordCloud komponenty
    """
    
    def __init__(self, output_dir: str = "charts", ai_manager=None):
        """
        Initialize WordCloud chart generator
        
        Args:
            output_dir: Output directory for charts
            ai_manager: AI manager for enhanced processing
        """
        super().__init__("wordcloud", output_dir)
        self.ai_manager = ai_manager
        self.version = "1.0.0"
        
        # Default configuration
        self.default_config = {
            'max_words': 200,
            'min_word_length': 3,
            'width': 800,
            'height': 400,
            'background_color': 'white',
            'color_scheme': 'default',
            'output_format': 'png',
            'dpi': 300,
            'use_ai': True,
            'language': 'cs'
        }
        
        logger.info("WordCloudChartGenerator initialized")
    
    def get_supported_chart_types(self) -> List[str]:
        """Get supported chart types"""
        return ['wordcloud', 'wordcloud_shaped', 'wordcloud_hierarchical']
    
    def create_chart(self, config: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create WordCloud chart
        
        Args:
            config: Chart configuration
            data: Chart data
            
        Returns:
            Chart creation result
        """
        try:
            # Validate inputs
            config_validation = self.validate_config(config)
            if not config_validation['valid']:
                return self.create_error_result(
                    f"Invalid configuration: {config_validation['errors']}", 
                    config
                )
            
            data_validation = self.validate_data(data, config.get('chart_type', 'wordcloud'))
            if not data_validation['valid']:
                return self.create_error_result(
                    f"Invalid data: {data_validation['errors']}", 
                    config
                )
            
            # Prepare WordCloud configuration
            wc_config = self._prepare_wordcloud_config(config)
            
            # Extract text data
            text_data = self._extract_text_data(data)
            if not text_data:
                return self.create_error_result("No text data found", config)
            
            # Generate WordCloud
            if self.ai_manager and wc_config.get('use_ai', True):
                result = self._generate_ai_wordcloud(text_data, wc_config, config)
            else:
                result = self._generate_basic_wordcloud(text_data, wc_config, config)
            
            return result
            
        except Exception as e:
            logger.error(f"WordCloud generation failed: {e}")
            return self.create_error_result(str(e), config)
    
    def _validate_chart_type_data(
        self, 
        data: Dict[str, Any], 
        chart_type: str, 
        validation_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate WordCloud-specific data"""
        
        # Check for text data
        text_fields = ['text', 'texts', 'responses', 'concatenated_text']
        has_text = any(field in data for field in text_fields)
        
        if not has_text:
            validation_result['valid'] = False
            validation_result['errors'].append(
                f"WordCloud requires text data. Expected one of: {text_fields}"
            )
        
        # Check for frequencies (alternative to raw text)
        if 'frequencies' in data:
            frequencies = data['frequencies']
            if not isinstance(frequencies, dict) or len(frequencies) == 0:
                validation_result['warnings'].append(
                    "Frequencies data is empty or invalid"
                )
        
        return validation_result
    
    def _prepare_wordcloud_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare WordCloud configuration"""
        # Start with defaults
        wc_config = self.default_config.copy()
        
        # Update with provided parameters
        parameters = config.get('parameters', {})
        wc_config.update(parameters)
        
        # Map chart type to WordCloud type
        chart_type = config.get('chart_type', 'wordcloud')
        if chart_type == 'wordcloud_shaped':
            wc_config['visualization_type'] = 'WCT'
        elif chart_type == 'wordcloud_hierarchical':
            wc_config['visualization_type'] = 'WCH'
        else:
            wc_config['visualization_type'] = 'WCS'
        
        return wc_config
    
    def _extract_text_data(self, data: Dict[str, Any]) -> Optional[str]:
        """Extract text data from various formats"""
        
        # Try different text field names
        text_fields = ['text', 'concatenated_text', 'texts', 'responses']
        for field in text_fields:
            if field in data:
                text_value = data[field]
                if isinstance(text_value, str):
                    return text_value
                elif isinstance(text_value, list):
                    return ' '.join(str(item) for item in text_value if item)
        
        # Try to extract from frequencies
        if 'frequencies' in data:
            frequencies = data['frequencies']
            if isinstance(frequencies, dict):
                # Reconstruct text from frequencies
                text_parts = []
                for word, freq in frequencies.items():
                    text_parts.extend([word] * freq)
                return ' '.join(text_parts)
        
        # Try to extract from individual_texts
        if 'individual_texts' in data:
            texts = data['individual_texts']
            if isinstance(texts, list):
                return ' '.join(str(text) for text in texts if text)
        
        return None
    
    def _generate_ai_wordcloud(
        self, 
        text_data: str, 
        wc_config: Dict[str, Any], 
        chart_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate WordCloud using AI enhancement"""
        try:
            # Use AI manager to generate WordCloud
            result = self.ai_manager.generate_wordcloud(
                text=text_data,
                use_ai=True,
                **wc_config
            )
            
            if not result.get('success'):
                return self.create_error_result(
                    f"AI WordCloud generation failed: {result.get('error', 'Unknown error')}", 
                    chart_config
                )
            
            # Save the generated image
            output_path = self._save_wordcloud_image(
                result['image_data'], 
                chart_config, 
                wc_config
            )
            
            # Create success result
            success_result = self.create_success_result(output_path, chart_config, {
                'frequencies': result.get('frequencies', {}),
                'metadata': result.get('metadata', {}),
                'ai_analysis': result.get('ai_analysis', {}),
                'wordcloud_config': wc_config
            })
            
            # Save metadata
            self.save_chart_metadata(output_path, chart_config, {
                'text_length': len(text_data),
                'frequencies': result.get('frequencies', {}),
                'ai_analysis': result.get('ai_analysis', {})
            }, success_result)
            
            return success_result
            
        except Exception as e:
            logger.error(f"AI WordCloud generation failed: {e}")
            return self.create_error_result(str(e), chart_config)
    
    def _generate_basic_wordcloud(
        self, 
        text_data: str, 
        wc_config: Dict[str, Any], 
        chart_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate basic WordCloud without AI"""
        try:
            # Import WordCloud library
            from wordcloud import WordCloud
            import matplotlib.pyplot as plt
            from io import BytesIO
            
            # Create WordCloud
            wordcloud = WordCloud(
                width=wc_config.get('width', 800),
                height=wc_config.get('height', 400),
                background_color=wc_config.get('background_color', 'white'),
                max_words=wc_config.get('max_words', 200),
                min_font_size=wc_config.get('min_font_size', 10),
                max_font_size=wc_config.get('max_font_size', 100),
                random_state=42
            ).generate(text_data)
            
            # Convert to image bytes
            img_buffer = BytesIO()
            wordcloud.to_image().save(img_buffer, format='PNG')
            image_data = img_buffer.getvalue()
            
            # Save the generated image
            output_path = self._save_wordcloud_image(
                image_data, 
                chart_config, 
                wc_config
            )
            
            # Create success result
            success_result = self.create_success_result(output_path, chart_config, {
                'frequencies': dict(wordcloud.words_),
                'wordcloud_config': wc_config,
                'ai_enhanced': False
            })
            
            # Save metadata
            self.save_chart_metadata(output_path, chart_config, {
                'text_length': len(text_data),
                'frequencies': dict(wordcloud.words_)
            }, success_result)
            
            return success_result
            
        except ImportError:
            return self.create_error_result(
                "WordCloud library not available. Install with: pip install wordcloud", 
                chart_config
            )
        except Exception as e:
            logger.error(f"Basic WordCloud generation failed: {e}")
            return self.create_error_result(str(e), chart_config)

    def _save_wordcloud_image(
        self,
        image_data: bytes,
        chart_config: Dict[str, Any],
        wc_config: Dict[str, Any]
    ) -> Path:
        """Save WordCloud image to file"""

        # Determine output path
        survey_id = chart_config.get('survey_id', 'unknown')
        question_id = chart_config.get('question_id', 'unknown')
        chart_type = chart_config.get('chart_type', 'wordcloud')
        output_format = wc_config.get('output_format', 'png')

        output_path = self.prepare_output_path(
            survey_id,
            question_id,
            chart_type,
            output_format
        )

        # Save image
        with open(output_path, 'wb') as f:
            f.write(image_data)

        logger.info(f"Saved WordCloud to {output_path}")
        return output_path

    def get_default_config(self) -> Dict[str, Any]:
        """Get default WordCloud configuration"""
        return self.default_config.copy()

    def update_default_config(self, updates: Dict[str, Any]):
        """Update default configuration"""
        self.default_config.update(updates)
        logger.info("Updated default WordCloud configuration")

    def create_config_from_template(self, template_name: str) -> Dict[str, Any]:
        """Create configuration from predefined template"""
        templates = {
            'presentation': {
                'width': 1200,
                'height': 600,
                'max_words': 150,
                'background_color': 'white',
                'color_scheme': 'blue',
                'dpi': 300
            },
            'web': {
                'width': 800,
                'height': 400,
                'max_words': 200,
                'background_color': 'transparent',
                'color_scheme': 'default',
                'output_format': 'svg'
            },
            'print': {
                'width': 2400,
                'height': 1200,
                'max_words': 300,
                'background_color': 'white',
                'color_scheme': 'grayscale',
                'dpi': 600,
                'output_format': 'png'
            },
            'social_media': {
                'width': 1080,
                'height': 1080,
                'max_words': 100,
                'background_color': '#f0f0f0',
                'color_scheme': 'vibrant',
                'dpi': 150
            }
        }

        if template_name not in templates:
            logger.warning(f"Template '{template_name}' not found, using default")
            return self.default_config.copy()

        config = self.default_config.copy()
        config.update(templates[template_name])
        return config


def create_wordcloud_generator(output_dir: str = "charts", ai_manager=None) -> WordCloudChartGenerator:
    """
    Factory function to create WordCloud generator
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    return WordCloudChartGenerator(output_dir, ai_manager)
