"""
Table Chart Generator
Externí generátor pro tabulkové grafy z textových dat
BEZPEČNÝ - neovlivňuje stávající Datawrapper workflow
"""

import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
import json
import pandas as pd
from io import StringIO

from .base_generator import BaseChartGenerator

logger = logging.getLogger(__name__)


class TableChartGenerator(BaseChartGenerator):
    """
    Externí generátor pro tabulkové grafy
    BEZPEČNÝ - používá pandas pro tabulkové zpracování
    """
    
    def __init__(self, output_dir: str = "charts", ai_manager=None):
        """
        Initialize Table chart generator
        
        Args:
            output_dir: Output directory for charts
            ai_manager: AI manager for enhanced processing
        """
        super().__init__("table", output_dir)
        self.ai_manager = ai_manager
        self.version = "1.0.0"
        
        # Default configuration
        self.default_config = {
            'max_categories': 20,
            'min_frequency': 1,
            'sort_by': 'frequency',  # 'frequency', 'alphabetical', 'custom'
            'sort_order': 'desc',    # 'asc', 'desc'
            'include_percentages': True,
            'include_totals': True,
            'output_format': 'html',  # 'html', 'csv', 'json', 'png'
            'table_style': 'default', # 'default', 'minimal', 'bootstrap', 'custom'
            'use_ai_categorization': True,
            'language': 'cs'
        }
        
        logger.info("TableChartGenerator initialized")
    
    def get_supported_chart_types(self) -> List[str]:
        """Get supported chart types"""
        return ['table', 'frequency_table', 'crosstab', 'summary_table']
    
    def create_chart(self, config: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create table chart
        
        Args:
            config: Chart configuration
            data: Chart data
            
        Returns:
            Chart creation result
        """
        try:
            # Validate inputs
            config_validation = self.validate_config(config)
            if not config_validation['valid']:
                return self.create_error_result(
                    f"Invalid configuration: {config_validation['errors']}", 
                    config
                )
            
            data_validation = self.validate_data(data, config.get('chart_type', 'table'))
            if not data_validation['valid']:
                return self.create_error_result(
                    f"Invalid data: {data_validation['errors']}", 
                    config
                )
            
            # Prepare table configuration
            table_config = self._prepare_table_config(config)
            
            # Extract and process data
            processed_data = self._process_table_data(data, table_config)
            if not processed_data:
                return self.create_error_result("No data to process", config)
            
            # Generate table based on chart type
            chart_type = config.get('chart_type', 'table')
            
            if chart_type == 'frequency_table':
                result = self._generate_frequency_table(processed_data, table_config, config)
            elif chart_type == 'crosstab':
                result = self._generate_crosstab_table(processed_data, table_config, config)
            elif chart_type == 'summary_table':
                result = self._generate_summary_table(processed_data, table_config, config)
            else:
                result = self._generate_basic_table(processed_data, table_config, config)
            
            return result
            
        except Exception as e:
            logger.error(f"Table generation failed: {e}")
            return self.create_error_result(str(e), config)
    
    def _validate_chart_type_data(
        self, 
        data: Dict[str, Any], 
        chart_type: str, 
        validation_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate table-specific data"""
        
        # Check for required data fields
        required_fields = ['category_counts', 'categories', 'frequencies']
        has_required = any(field in data for field in required_fields)
        
        if not has_required:
            # Check for raw text data that can be processed
            text_fields = ['text', 'texts', 'responses', 'concatenated_text']
            has_text = any(field in data for field in text_fields)
            
            if not has_text:
                validation_result['valid'] = False
                validation_result['errors'].append(
                    f"Table requires either processed data ({required_fields}) or raw text data ({text_fields})"
                )
        
        return validation_result
    
    def _prepare_table_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare table configuration"""
        # Start with defaults
        table_config = self.default_config.copy()
        
        # Update with provided parameters
        parameters = config.get('parameters', {})
        table_config.update(parameters)
        
        # Chart type specific adjustments
        chart_type = config.get('chart_type', 'table')
        if chart_type == 'frequency_table':
            table_config['include_percentages'] = True
            table_config['sort_by'] = 'frequency'
        elif chart_type == 'summary_table':
            table_config['include_totals'] = True
            table_config['max_categories'] = 10
        
        return table_config
    
    def _process_table_data(self, data: Dict[str, Any], table_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process and prepare data for table generation"""
        
        # Try to extract frequency data
        if 'category_counts' in data:
            categories = list(data['category_counts'].keys())
            frequencies = list(data['category_counts'].values())
        elif 'frequencies' in data:
            categories = list(data['frequencies'].keys())
            frequencies = list(data['frequencies'].values())
        elif 'categories' in data and 'values' in data:
            categories = data['categories']
            frequencies = data['values']
        else:
            # Try to process raw text data
            text_data = self._extract_text_data(data)
            if text_data:
                return self._process_text_to_frequencies(text_data, table_config)
            else:
                return None
        
        # Filter by minimum frequency
        min_freq = table_config.get('min_frequency', 1)
        filtered_data = [
            (cat, freq) for cat, freq in zip(categories, frequencies) 
            if freq >= min_freq
        ]
        
        # Sort data
        sort_by = table_config.get('sort_by', 'frequency')
        sort_order = table_config.get('sort_order', 'desc')
        
        if sort_by == 'frequency':
            filtered_data.sort(key=lambda x: x[1], reverse=(sort_order == 'desc'))
        elif sort_by == 'alphabetical':
            filtered_data.sort(key=lambda x: x[0], reverse=(sort_order == 'desc'))
        
        # Limit to max categories
        max_categories = table_config.get('max_categories', 20)
        if len(filtered_data) > max_categories:
            filtered_data = filtered_data[:max_categories]
        
        # Calculate totals and percentages
        total_count = sum(freq for _, freq in filtered_data)
        
        processed_data = {
            'categories': [cat for cat, _ in filtered_data],
            'frequencies': [freq for _, freq in filtered_data],
            'percentages': [round((freq / total_count) * 100, 1) for _, freq in filtered_data],
            'total_count': total_count,
            'category_count': len(filtered_data)
        }
        
        return processed_data
    
    def _extract_text_data(self, data: Dict[str, Any]) -> Optional[str]:
        """Extract text data from various formats"""
        text_fields = ['text', 'concatenated_text', 'texts', 'responses']
        for field in text_fields:
            if field in data:
                text_value = data[field]
                if isinstance(text_value, str):
                    return text_value
                elif isinstance(text_value, list):
                    return ' '.join(str(item) for item in text_value if item)
        return None
    
    def _process_text_to_frequencies(self, text: str, table_config: Dict[str, Any]) -> Dict[str, Any]:
        """Process raw text into frequency data"""
        # Simple word frequency analysis
        words = text.lower().split()
        
        # Filter by minimum word length
        min_length = table_config.get('min_word_length', 3)
        words = [word.strip('.,!?;:"()[]{}') for word in words if len(word) >= min_length]
        
        # Count frequencies
        from collections import Counter
        word_counts = Counter(words)
        
        # Convert to our format
        categories = list(word_counts.keys())
        frequencies = list(word_counts.values())
        total_count = sum(frequencies)
        
        return {
            'categories': categories,
            'frequencies': frequencies,
            'percentages': [round((freq / total_count) * 100, 1) for freq in frequencies],
            'total_count': total_count,
            'category_count': len(categories)
        }
    
    def _generate_frequency_table(
        self, 
        processed_data: Dict[str, Any], 
        table_config: Dict[str, Any], 
        chart_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate frequency table"""
        try:
            # Create DataFrame
            df_data = {
                'Kategorie': processed_data['categories'],
                'Počet': processed_data['frequencies']
            }
            
            if table_config.get('include_percentages', True):
                df_data['Procenta'] = [f"{p}%" for p in processed_data['percentages']]
            
            df = pd.DataFrame(df_data)
            
            # Add totals row if requested
            if table_config.get('include_totals', True):
                total_row = {'Kategorie': 'CELKEM', 'Počet': processed_data['total_count']}
                if table_config.get('include_percentages', True):
                    total_row['Procenta'] = '100.0%'
                df = pd.concat([df, pd.DataFrame([total_row])], ignore_index=True)
            
            # Generate output
            output_path = self._save_table(df, chart_config, table_config, 'frequency_table')
            
            return self.create_success_result(output_path, chart_config, {
                'table_data': df.to_dict('records'),
                'total_categories': processed_data['category_count'],
                'total_count': processed_data['total_count'],
                'table_config': table_config
            })
            
        except Exception as e:
            logger.error(f"Frequency table generation failed: {e}")
            return self.create_error_result(str(e), chart_config)
    
    def _generate_basic_table(
        self, 
        processed_data: Dict[str, Any], 
        table_config: Dict[str, Any], 
        chart_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate basic table"""
        return self._generate_frequency_table(processed_data, table_config, chart_config)
    
    def _generate_crosstab_table(
        self, 
        processed_data: Dict[str, Any], 
        table_config: Dict[str, Any], 
        chart_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate crosstab table - placeholder for future implementation"""
        logger.warning("Crosstab table generation not yet implemented")
        return self.create_error_result("Crosstab tables not yet implemented", chart_config)
    
    def _generate_summary_table(
        self, 
        processed_data: Dict[str, Any], 
        table_config: Dict[str, Any], 
        chart_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate summary table"""
        try:
            # Create summary statistics
            frequencies = processed_data['frequencies']
            
            summary_stats = {
                'Statistika': [
                    'Celkový počet odpovědí',
                    'Počet kategorií',
                    'Nejčastější kategorie',
                    'Nejvyšší četnost',
                    'Průměrná četnost',
                    'Medián četnosti'
                ],
                'Hodnota': [
                    processed_data['total_count'],
                    processed_data['category_count'],
                    processed_data['categories'][0] if processed_data['categories'] else 'N/A',
                    max(frequencies) if frequencies else 0,
                    round(sum(frequencies) / len(frequencies), 1) if frequencies else 0,
                    sorted(frequencies)[len(frequencies)//2] if frequencies else 0
                ]
            }
            
            df = pd.DataFrame(summary_stats)
            
            # Generate output
            output_path = self._save_table(df, chart_config, table_config, 'summary_table')
            
            return self.create_success_result(output_path, chart_config, {
                'summary_stats': summary_stats,
                'table_data': df.to_dict('records'),
                'table_config': table_config
            })
            
        except Exception as e:
            logger.error(f"Summary table generation failed: {e}")
            return self.create_error_result(str(e), chart_config)
    
    def _save_table(
        self, 
        df: pd.DataFrame, 
        chart_config: Dict[str, Any], 
        table_config: Dict[str, Any],
        table_type: str
    ) -> Path:
        """Save table to file"""
        
        # Determine output path
        survey_id = chart_config.get('survey_id', 'unknown')
        question_id = chart_config.get('question_id', 'unknown')
        output_format = table_config.get('output_format', 'html')
        
        output_path = self.prepare_output_path(
            survey_id, 
            question_id, 
            table_type, 
            output_format
        )
        
        # Save based on format
        if output_format == 'html':
            self._save_html_table(df, output_path, table_config)
        elif output_format == 'csv':
            df.to_csv(output_path, index=False, encoding='utf-8')
        elif output_format == 'json':
            df.to_json(output_path, orient='records', force_ascii=False, indent=2)
        elif output_format == 'png':
            self._save_png_table(df, output_path, table_config)
        else:
            # Default to HTML
            self._save_html_table(df, output_path.with_suffix('.html'), table_config)
        
        logger.info(f"Saved table to {output_path}")
        return output_path
    
    def _save_html_table(self, df: pd.DataFrame, output_path: Path, table_config: Dict[str, Any]):
        """Save table as HTML"""
        table_style = table_config.get('table_style', 'default')
        
        # CSS styles
        styles = {
            'default': """
                <style>
                table { border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                tr:last-child { font-weight: bold; background-color: #e6f3ff; }
                </style>
            """,
            'minimal': """
                <style>
                table { border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; }
                th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
                th { font-weight: bold; }
                </style>
            """,
            'bootstrap': """
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                .table { margin-top: 20px; }
                </style>
            """
        }
        
        css = styles.get(table_style, styles['default'])
        
        # Generate HTML
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Tabulka - {chart_config.get('question_id', 'Unknown')}</title>
            {css}
        </head>
        <body>
            <h2>Tabulka četností</h2>
            {df.to_html(index=False, escape=False, classes='table table-striped' if table_style == 'bootstrap' else '')}
        </body>
        </html>
        """
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def _save_png_table(self, df: pd.DataFrame, output_path: Path, table_config: Dict[str, Any]):
        """Save table as PNG image"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib.patches as patches
            
            fig, ax = plt.subplots(figsize=(10, len(df) * 0.5 + 2))
            ax.axis('tight')
            ax.axis('off')
            
            # Create table
            table = ax.table(
                cellText=df.values,
                colLabels=df.columns,
                cellLoc='center',
                loc='center'
            )
            
            # Style table
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1.2, 1.5)
            
            # Header styling
            for i in range(len(df.columns)):
                table[(0, i)].set_facecolor('#4CAF50')
                table[(0, i)].set_text_props(weight='bold', color='white')
            
            plt.title(f"Tabulka četností - {chart_config.get('question_id', 'Unknown')}", 
                     fontsize=14, fontweight='bold', pad=20)
            
            plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            plt.close()
            
        except ImportError:
            logger.warning("Matplotlib not available for PNG export, falling back to HTML")
            self._save_html_table(df, output_path.with_suffix('.html'), table_config)


def create_table_generator(output_dir: str = "charts", ai_manager=None) -> TableChartGenerator:
    """
    Factory function to create Table generator
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    return TableChartGenerator(output_dir, ai_manager)
