"""
Multi-Chart Generator
Externí generátor pro multi donuts a grouped charts
BEZPEČNÝ - neovlivňuje stávající Datawrapper workflow
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import json
import math

from .base_generator import BaseChartGenerator

logger = logging.getLogger(__name__)


class MultiChartGenerator(BaseChartGenerator):
    """
    Externí generátor pro multi-chart layouts
    BEZPEČNÝ - vytváří kompozitní grafy z více otázek
    """
    
    def __init__(self, output_dir: str = "charts", datawrapper_client=None):
        """
        Initialize Multi-Chart generator
        
        Args:
            output_dir: Output directory for charts
            datawrapper_client: Datawrapper client for individual chart creation
        """
        super().__init__("multi_chart", output_dir)
        self.datawrapper_client = datawrapper_client
        self.version = "1.0.0"
        
        # Default configuration
        self.default_config = {
            'layout': 'grid',        # 'grid', 'horizontal', 'vertical'
            'grid_columns': 2,       # For grid layout
            'grid_rows': 3,          # For grid layout
            'chart_width': 400,      # Individual chart width
            'chart_height': 300,     # Individual chart height
            'spacing': 20,           # Space between charts
            'title_height': 50,      # Space for titles
            'output_format': 'png',  # 'png', 'svg', 'html'
            'background_color': 'white',
            'show_individual_titles': True,
            'show_main_title': True,
            'main_title': 'Multi-Chart Analysis'
        }
        
        logger.info("MultiChartGenerator initialized")
    
    def get_supported_chart_types(self) -> List[str]:
        """Get supported chart types"""
        return ['multi_donut', 'multi_column', 'multi_pie', 'dashboard', 'comparison_grid']
    
    def create_chart(self, config: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create multi-chart layout
        
        Args:
            config: Chart configuration
            data: Chart data (multiple questions/datasets)
            
        Returns:
            Chart creation result
        """
        try:
            # Validate inputs
            config_validation = self.validate_config(config)
            if not config_validation['valid']:
                return self.create_error_result(
                    f"Invalid configuration: {config_validation['errors']}", 
                    config
                )
            
            data_validation = self.validate_data(data, config.get('chart_type', 'multi_donut'))
            if not data_validation['valid']:
                return self.create_error_result(
                    f"Invalid data: {data_validation['errors']}", 
                    config
                )
            
            # Prepare multi-chart configuration
            multi_config = self._prepare_multi_chart_config(config)
            
            # Process multiple datasets
            chart_datasets = self._process_multi_chart_data(data, multi_config)
            if not chart_datasets:
                return self.create_error_result("No valid datasets found", config)
            
            # Generate individual charts
            individual_charts = self._generate_individual_charts(chart_datasets, multi_config, config)
            if not individual_charts:
                return self.create_error_result("Failed to generate individual charts", config)
            
            # Compose multi-chart layout
            result = self._compose_multi_chart(individual_charts, multi_config, config)
            
            return result
            
        except Exception as e:
            logger.error(f"Multi-chart generation failed: {e}")
            return self.create_error_result(str(e), config)
    
    def _validate_chart_type_data(
        self, 
        data: Dict[str, Any], 
        chart_type: str, 
        validation_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate multi-chart specific data"""
        
        # Check for multiple datasets
        if 'datasets' not in data and 'questions' not in data:
            validation_result['valid'] = False
            validation_result['errors'].append(
                "Multi-chart requires 'datasets' or 'questions' with multiple data sources"
            )
            return validation_result
        
        # Check minimum number of datasets
        datasets = data.get('datasets', data.get('questions', {}))
        if len(datasets) < 2:
            validation_result['warnings'].append(
                "Multi-chart works best with 2 or more datasets"
            )
        
        # Check maximum number for layout
        if len(datasets) > 12:
            validation_result['warnings'].append(
                "Large number of datasets may result in small individual charts"
            )
        
        return validation_result
    
    def _prepare_multi_chart_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare multi-chart configuration"""
        # Start with defaults
        multi_config = self.default_config.copy()
        
        # Update with provided parameters
        parameters = config.get('parameters', {})
        multi_config.update(parameters)
        
        # Chart type specific adjustments
        chart_type = config.get('chart_type', 'multi_donut')
        
        if chart_type == 'multi_donut':
            multi_config['individual_chart_type'] = 'donut'
            multi_config['chart_width'] = 300
            multi_config['chart_height'] = 300
        elif chart_type == 'multi_column':
            multi_config['individual_chart_type'] = 'column'
            multi_config['chart_width'] = 400
            multi_config['chart_height'] = 300
        elif chart_type == 'multi_pie':
            multi_config['individual_chart_type'] = 'pie'
            multi_config['chart_width'] = 300
            multi_config['chart_height'] = 300
        elif chart_type == 'dashboard':
            multi_config['layout'] = 'grid'
            multi_config['show_main_title'] = True
            multi_config['main_title'] = 'Dashboard Overview'
        elif chart_type == 'comparison_grid':
            multi_config['layout'] = 'grid'
            multi_config['show_individual_titles'] = True
        
        return multi_config
    
    def _process_multi_chart_data(self, data: Dict[str, Any], multi_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Process multiple datasets for multi-chart"""
        chart_datasets = []
        
        # Extract datasets
        if 'datasets' in data:
            datasets = data['datasets']
        elif 'questions' in data:
            datasets = data['questions']
        else:
            return []
        
        # Process each dataset
        for dataset_id, dataset in datasets.items():
            processed_dataset = self._process_single_dataset(dataset_id, dataset, multi_config)
            if processed_dataset:
                chart_datasets.append(processed_dataset)
        
        # Limit number of charts based on layout
        max_charts = self._calculate_max_charts(multi_config)
        if len(chart_datasets) > max_charts:
            logger.warning(f"Limiting to {max_charts} charts due to layout constraints")
            chart_datasets = chart_datasets[:max_charts]
        
        return chart_datasets
    
    def _process_single_dataset(self, dataset_id: str, dataset: Dict[str, Any], multi_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process single dataset for chart generation"""
        try:
            # Extract basic information
            question_text = dataset.get('question_text', dataset_id)
            
            # Extract data in various formats
            if 'frequencies' in dataset:
                categories = list(dataset['frequencies'].keys())
                values = list(dataset['frequencies'].values())
            elif 'category_counts' in dataset:
                categories = list(dataset['category_counts'].keys())
                values = list(dataset['category_counts'].values())
            elif 'categories' in dataset and 'values' in dataset:
                categories = dataset['categories']
                values = dataset['values']
            else:
                logger.warning(f"No valid data format found for dataset {dataset_id}")
                return None
            
            # Validate data
            if not categories or not values or len(categories) != len(values):
                logger.warning(f"Invalid data structure for dataset {dataset_id}")
                return None
            
            return {
                'dataset_id': dataset_id,
                'question_text': question_text,
                'categories': categories,
                'values': values,
                'total': sum(values),
                'chart_type': multi_config.get('individual_chart_type', 'donut')
            }
            
        except Exception as e:
            logger.error(f"Failed to process dataset {dataset_id}: {e}")
            return None
    
    def _calculate_max_charts(self, multi_config: Dict[str, Any]) -> int:
        """Calculate maximum number of charts based on layout"""
        layout = multi_config.get('layout', 'grid')
        
        if layout == 'grid':
            rows = multi_config.get('grid_rows', 3)
            cols = multi_config.get('grid_columns', 2)
            return rows * cols
        elif layout == 'horizontal':
            return 6  # Reasonable limit for horizontal layout
        elif layout == 'vertical':
            return 8  # Reasonable limit for vertical layout
        else:
            return 6  # Default limit
    
    def _generate_individual_charts(
        self, 
        chart_datasets: List[Dict[str, Any]], 
        multi_config: Dict[str, Any], 
        chart_config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate individual charts for each dataset"""
        individual_charts = []
        
        for dataset in chart_datasets:
            try:
                # Prepare individual chart config
                individual_config = {
                    'chart_type': dataset['chart_type'],
                    'generator': 'datawrapper',  # Use Datawrapper for individual charts
                    'parameters': {
                        'width': multi_config.get('chart_width', 400),
                        'height': multi_config.get('chart_height', 300),
                        'title': dataset['question_text'] if multi_config.get('show_individual_titles', True) else '',
                        'show_legend': True
                    },
                    'survey_id': chart_config.get('survey_id', 'unknown'),
                    'question_id': dataset['dataset_id']
                }
                
                # Prepare individual chart data
                individual_data = {
                    'categories': dataset['categories'],
                    'values': dataset['values']
                }
                
                # Generate chart using Datawrapper (if available) or fallback
                if self.datawrapper_client:
                    chart_result = self._generate_datawrapper_chart(individual_config, individual_data)
                else:
                    chart_result = self._generate_fallback_chart(individual_config, individual_data)
                
                if chart_result.get('success'):
                    individual_charts.append({
                        'dataset_id': dataset['dataset_id'],
                        'question_text': dataset['question_text'],
                        'chart_result': chart_result,
                        'position': len(individual_charts)
                    })
                else:
                    logger.warning(f"Failed to generate chart for {dataset['dataset_id']}")
                
            except Exception as e:
                logger.error(f"Failed to generate individual chart for {dataset['dataset_id']}: {e}")
        
        return individual_charts
    
    def _generate_datawrapper_chart(self, config: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate chart using Datawrapper API"""
        try:
            # This would integrate with actual Datawrapper client
            # For now, return a placeholder result
            return {
                'success': True,
                'chart_id': f"dw_chart_{config['question_id']}",
                'chart_url': f"https://datawrapper.dwcdn.net/placeholder/{config['question_id']}/",
                'embed_code': f'<iframe src="https://datawrapper.dwcdn.net/placeholder/{config["question_id"]}/" width="{config["parameters"]["width"]}" height="{config["parameters"]["height"]}"></iframe>'
            }
        except Exception as e:
            logger.error(f"Datawrapper chart generation failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _generate_fallback_chart(self, config: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate fallback chart representation"""
        try:
            # Create simple text-based chart representation
            chart_text = f"Chart: {config['question_id']}\n"
            chart_text += f"Type: {config['chart_type']}\n"
            chart_text += "Data:\n"
            
            for cat, val in zip(data['categories'], data['values']):
                chart_text += f"  {cat}: {val}\n"
            
            return {
                'success': True,
                'chart_type': 'text',
                'chart_content': chart_text,
                'width': config['parameters']['width'],
                'height': config['parameters']['height']
            }
        except Exception as e:
            logger.error(f"Fallback chart generation failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _compose_multi_chart(
        self, 
        individual_charts: List[Dict[str, Any]], 
        multi_config: Dict[str, Any], 
        chart_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Compose individual charts into multi-chart layout"""
        try:
            layout = multi_config.get('layout', 'grid')
            output_format = multi_config.get('output_format', 'png')
            
            if output_format == 'html':
                result = self._compose_html_layout(individual_charts, multi_config, chart_config)
            elif output_format == 'svg':
                result = self._compose_svg_layout(individual_charts, multi_config, chart_config)
            else:
                result = self._compose_image_layout(individual_charts, multi_config, chart_config)
            
            return result
            
        except Exception as e:
            logger.error(f"Multi-chart composition failed: {e}")
            return self.create_error_result(str(e), chart_config)
    
    def _compose_html_layout(
        self, 
        individual_charts: List[Dict[str, Any]], 
        multi_config: Dict[str, Any], 
        chart_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Compose HTML layout"""
        try:
            # Calculate layout dimensions
            layout_info = self._calculate_layout_dimensions(len(individual_charts), multi_config)
            
            # Generate HTML
            html_content = self._generate_html_template(individual_charts, layout_info, multi_config, chart_config)
            
            # Save HTML file
            output_path = self._save_multi_chart_file(html_content, chart_config, multi_config, 'html')
            
            return self.create_success_result(output_path, chart_config, {
                'layout_info': layout_info,
                'individual_charts_count': len(individual_charts),
                'output_format': 'html',
                'multi_config': multi_config
            })
            
        except Exception as e:
            logger.error(f"HTML layout composition failed: {e}")
            return self.create_error_result(str(e), chart_config)
    
    def _compose_image_layout(
        self, 
        individual_charts: List[Dict[str, Any]], 
        multi_config: Dict[str, Any], 
        chart_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Compose image layout (PNG/JPG)"""
        try:
            # For now, create a placeholder image composition
            # In a full implementation, this would combine actual chart images
            
            layout_info = self._calculate_layout_dimensions(len(individual_charts), multi_config)
            
            # Create placeholder composition info
            composition_info = {
                'total_width': layout_info['total_width'],
                'total_height': layout_info['total_height'],
                'individual_charts': [
                    {
                        'chart_id': chart['dataset_id'],
                        'position': chart['position'],
                        'x': layout_info['positions'][chart['position']]['x'],
                        'y': layout_info['positions'][chart['position']]['y'],
                        'width': multi_config.get('chart_width', 400),
                        'height': multi_config.get('chart_height', 300)
                    }
                    for chart in individual_charts
                ]
            }
            
            # Save composition info as JSON (placeholder for actual image)
            output_path = self._save_multi_chart_file(
                json.dumps(composition_info, indent=2), 
                chart_config, 
                multi_config, 
                'json'
            )
            
            return self.create_success_result(output_path, chart_config, {
                'composition_info': composition_info,
                'individual_charts_count': len(individual_charts),
                'output_format': 'image_placeholder',
                'note': 'Image composition requires additional image processing libraries'
            })
            
        except Exception as e:
            logger.error(f"Image layout composition failed: {e}")
            return self.create_error_result(str(e), chart_config)
    
    def _compose_svg_layout(
        self, 
        individual_charts: List[Dict[str, Any]], 
        multi_config: Dict[str, Any], 
        chart_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Compose SVG layout - placeholder"""
        logger.warning("SVG layout composition not yet implemented")
        return self.create_error_result("SVG layout not yet implemented", chart_config)
    
    def _calculate_layout_dimensions(self, chart_count: int, multi_config: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate layout dimensions and positions"""
        layout = multi_config.get('layout', 'grid')
        chart_width = multi_config.get('chart_width', 400)
        chart_height = multi_config.get('chart_height', 300)
        spacing = multi_config.get('spacing', 20)
        title_height = multi_config.get('title_height', 50)
        
        if layout == 'grid':
            cols = multi_config.get('grid_columns', 2)
            rows = math.ceil(chart_count / cols)
        elif layout == 'horizontal':
            cols = chart_count
            rows = 1
        elif layout == 'vertical':
            cols = 1
            rows = chart_count
        else:
            cols = 2
            rows = math.ceil(chart_count / 2)
        
        total_width = cols * chart_width + (cols - 1) * spacing
        total_height = rows * chart_height + (rows - 1) * spacing + title_height
        
        # Calculate positions
        positions = []
        for i in range(chart_count):
            row = i // cols
            col = i % cols
            x = col * (chart_width + spacing)
            y = title_height + row * (chart_height + spacing)
            positions.append({'x': x, 'y': y})
        
        return {
            'layout': layout,
            'cols': cols,
            'rows': rows,
            'total_width': total_width,
            'total_height': total_height,
            'chart_width': chart_width,
            'chart_height': chart_height,
            'spacing': spacing,
            'positions': positions
        }
    
    def _generate_html_template(
        self, 
        individual_charts: List[Dict[str, Any]], 
        layout_info: Dict[str, Any], 
        multi_config: Dict[str, Any], 
        chart_config: Dict[str, Any]
    ) -> str:
        """Generate HTML template for multi-chart layout"""
        
        main_title = multi_config.get('main_title', 'Multi-Chart Analysis')
        show_main_title = multi_config.get('show_main_title', True)
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>{main_title}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background-color: {multi_config.get('background_color', 'white')}; }}
                .multi-chart-container {{ position: relative; width: {layout_info['total_width']}px; height: {layout_info['total_height']}px; }}
                .main-title {{ text-align: center; font-size: 24px; font-weight: bold; margin-bottom: 20px; }}
                .chart-item {{ position: absolute; border: 1px solid #ddd; padding: 10px; background: white; }}
                .chart-title {{ font-size: 14px; font-weight: bold; margin-bottom: 10px; text-align: center; }}
                .chart-content {{ text-align: center; }}
            </style>
        </head>
        <body>
        """
        
        if show_main_title:
            html_content += f'<h1 class="main-title">{main_title}</h1>\n'
        
        html_content += '<div class="multi-chart-container">\n'
        
        # Add individual charts
        for i, chart in enumerate(individual_charts):
            pos = layout_info['positions'][i]
            chart_result = chart['chart_result']
            
            html_content += f'''
            <div class="chart-item" style="left: {pos['x']}px; top: {pos['y']}px; width: {layout_info['chart_width']}px; height: {layout_info['chart_height']}px;">
                <div class="chart-title">{chart['question_text']}</div>
                <div class="chart-content">
            '''
            
            if 'embed_code' in chart_result:
                html_content += chart_result['embed_code']
            elif 'chart_content' in chart_result:
                html_content += f'<pre>{chart_result["chart_content"]}</pre>'
            else:
                html_content += f'<p>Chart: {chart["dataset_id"]}</p>'
            
            html_content += '''
                </div>
            </div>
            '''
        
        html_content += '''
        </div>
        </body>
        </html>
        '''
        
        return html_content
    
    def _save_multi_chart_file(
        self, 
        content: str, 
        chart_config: Dict[str, Any], 
        multi_config: Dict[str, Any],
        file_extension: str
    ) -> Path:
        """Save multi-chart file"""
        
        survey_id = chart_config.get('survey_id', 'unknown')
        chart_type = chart_config.get('chart_type', 'multi_chart')
        
        output_path = self.prepare_output_path(
            survey_id, 
            'multi_chart', 
            chart_type, 
            file_extension
        )
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"Saved multi-chart to {output_path}")
        return output_path


def create_multi_chart_generator(output_dir: str = "charts", datawrapper_client=None) -> MultiChartGenerator:
    """
    Factory function to create Multi-Chart generator
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    return MultiChartGenerator(output_dir, datawrapper_client)
