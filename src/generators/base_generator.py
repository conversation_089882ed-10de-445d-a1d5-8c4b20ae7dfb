"""
Base Chart Generator
Z<PERSON>lad<PERSON>í třída pro externí generátory grafů
BEZPEČNÉ - neovlivňuje stávající kód
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime

logger = logging.getLogger(__name__)


class BaseChartGenerator(ABC):
    """
    Základní třída pro externí generátory grafů
    BEZPEČNÉ - poskytuje standardní rozhraní
    """
    
    def __init__(self, name: str, output_dir: str = "charts"):
        """
        Initialize base chart generator
        
        Args:
            name: Name of the generator
            output_dir: Output directory for charts
        """
        self.name = name
        self.output_dir = Path(output_dir)
        self.logger = logging.getLogger(f"{__name__}.{name}")
        
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"Initialized {name} chart generator")
    
    @abstractmethod
    def create_chart(self, config: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a chart from configuration and data
        
        Args:
            config: Chart configuration
            data: Chart data
            
        Returns:
            Chart creation result with metadata
        """
        pass
    
    @abstractmethod
    def get_supported_chart_types(self) -> List[str]:
        """
        Get list of supported chart types
        
        Returns:
            List of supported chart type names
        """
        pass
    
    def validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate chart configuration
        
        Args:
            config: Chart configuration to validate
            
        Returns:
            Validation result with errors and warnings
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Check required fields
        required_fields = ['chart_type', 'generator']
        for field in required_fields:
            if field not in config:
                validation_result['valid'] = False
                validation_result['errors'].append(f"Missing required field: {field}")
        
        # Check chart type support
        chart_type = config.get('chart_type')
        if chart_type and chart_type not in self.get_supported_chart_types():
            validation_result['valid'] = False
            validation_result['errors'].append(f"Unsupported chart type: {chart_type}")
        
        # Check generator match
        generator = config.get('generator')
        expected_generator = f"internal_{self.name.lower()}"
        if generator and generator != expected_generator:
            validation_result['warnings'].append(
                f"Generator mismatch: expected {expected_generator}, got {generator}"
            )
        
        return validation_result
    
    def validate_data(self, data: Dict[str, Any], chart_type: str) -> Dict[str, Any]:
        """
        Validate chart data
        
        Args:
            data: Chart data to validate
            chart_type: Type of chart being created
            
        Returns:
            Validation result with errors and warnings
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Basic data validation
        if not isinstance(data, dict):
            validation_result['valid'] = False
            validation_result['errors'].append("Data must be a dictionary")
            return validation_result
        
        if len(data) == 0:
            validation_result['valid'] = False
            validation_result['errors'].append("Data cannot be empty")
            return validation_result
        
        # Chart type specific validation
        validation_result = self._validate_chart_type_data(data, chart_type, validation_result)
        
        return validation_result
    
    def _validate_chart_type_data(
        self, 
        data: Dict[str, Any], 
        chart_type: str, 
        validation_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate data specific to chart type
        Override in subclasses for specific validation
        """
        return validation_result
    
    def prepare_output_path(
        self, 
        survey_id: str, 
        question_id: str, 
        chart_type: str,
        file_extension: str = "png"
    ) -> Path:
        """
        Prepare output path for chart file
        
        Args:
            survey_id: Survey identifier
            question_id: Question identifier  
            chart_type: Type of chart
            file_extension: File extension
            
        Returns:
            Path object for output file
        """
        # Create survey-specific directory
        survey_dir = self.output_dir / survey_id / self.name.lower()
        survey_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate filename
        filename = f"{question_id}_{chart_type}.{file_extension}"
        
        return survey_dir / filename
    
    def save_chart_metadata(
        self, 
        output_path: Path, 
        config: Dict[str, Any], 
        data: Dict[str, Any],
        result: Dict[str, Any]
    ) -> bool:
        """
        Save chart metadata alongside the chart file
        
        Args:
            output_path: Path to chart file
            config: Chart configuration
            data: Chart data
            result: Chart creation result
            
        Returns:
            True if metadata saved successfully
        """
        try:
            metadata_path = output_path.with_suffix('.json')
            
            metadata = {
                'chart_info': {
                    'generator': self.name,
                    'chart_type': config.get('chart_type'),
                    'created_at': datetime.now().isoformat(),
                    'file_path': str(output_path)
                },
                'config': config,
                'data_summary': self._create_data_summary(data),
                'result': result
            }
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Saved chart metadata to {metadata_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save chart metadata: {e}")
            return False
    
    def _create_data_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create summary of chart data for metadata"""
        summary = {
            'data_keys': list(data.keys()),
            'total_items': 0
        }
        
        # Count total items
        for key, value in data.items():
            if isinstance(value, list):
                summary['total_items'] += len(value)
            elif isinstance(value, dict):
                summary['total_items'] += len(value)
        
        return summary
    
    def create_error_result(self, error_message: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create standardized error result
        
        Args:
            error_message: Error message
            config: Chart configuration (optional)
            
        Returns:
            Error result dictionary
        """
        return {
            'success': False,
            'error': error_message,
            'generator': self.name,
            'chart_type': config.get('chart_type') if config else 'unknown',
            'timestamp': datetime.now().isoformat()
        }
    
    def create_success_result(
        self, 
        output_path: Path, 
        config: Dict[str, Any],
        additional_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Create standardized success result
        
        Args:
            output_path: Path to generated chart file
            config: Chart configuration
            additional_data: Additional result data
            
        Returns:
            Success result dictionary
        """
        result = {
            'success': True,
            'generator': self.name,
            'chart_type': config.get('chart_type'),
            'output_path': str(output_path),
            'file_size': output_path.stat().st_size if output_path.exists() else 0,
            'timestamp': datetime.now().isoformat()
        }
        
        if additional_data:
            result.update(additional_data)
        
        return result
    
    def cleanup_temp_files(self, temp_paths: List[Path]):
        """
        Clean up temporary files
        
        Args:
            temp_paths: List of temporary file paths to clean up
        """
        for path in temp_paths:
            try:
                if path.exists():
                    path.unlink()
                    self.logger.debug(f"Cleaned up temp file: {path}")
            except Exception as e:
                self.logger.warning(f"Failed to cleanup temp file {path}: {e}")
    
    def get_generator_info(self) -> Dict[str, Any]:
        """Get information about this generator"""
        return {
            'name': self.name,
            'supported_chart_types': self.get_supported_chart_types(),
            'output_directory': str(self.output_dir),
            'version': getattr(self, 'version', '1.0.0')
        }


class GeneratorRegistry:
    """
    Registry for chart generators
    BEZPEČNÉ - centrální správa generátorů
    """
    
    def __init__(self):
        self.generators: Dict[str, BaseChartGenerator] = {}
        self.logger = logging.getLogger(f"{__name__}.GeneratorRegistry")
    
    def register(self, generator: BaseChartGenerator):
        """Register a chart generator"""
        self.generators[generator.name] = generator
        self.logger.info(f"Registered generator: {generator.name}")
    
    def unregister(self, name: str):
        """Unregister a chart generator"""
        if name in self.generators:
            del self.generators[name]
            self.logger.info(f"Unregistered generator: {name}")
    
    def get(self, name: str) -> Optional[BaseChartGenerator]:
        """Get a generator by name"""
        return self.generators.get(name)
    
    def list_generators(self) -> List[str]:
        """List all registered generators"""
        return list(self.generators.keys())
    
    def get_supported_chart_types(self) -> Dict[str, List[str]]:
        """Get supported chart types for all generators"""
        supported_types = {}
        for name, generator in self.generators.items():
            try:
                supported_types[name] = generator.get_supported_chart_types()
            except Exception as e:
                self.logger.error(f"Failed to get supported types for {name}: {e}")
                supported_types[name] = []
        return supported_types


# Global generator registry
generator_registry = GeneratorRegistry()
