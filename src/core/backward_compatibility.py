"""
Backward Compatibility System
Zajištění zpětné kompatibility se stávají<PERSON><PERSON><PERSON> daty a workflow
BEZPEČNÉ - zachovává funkčnost stávajícího systému
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import json
import shutil
from datetime import datetime

logger = logging.getLogger(__name__)


class BackwardCompatibilityManager:
    """
    Manager pro zpětnou kompatibilitu
    BEZPEČNÝ - zachovává stávající funkcionalita
    """
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize Backward Compatibility Manager
        
        Args:
            data_dir: <PERSON><PERSON><PERSON><PERSON> s daty
        """
        self.data_dir = Path(data_dir)
        self.backup_dir = self.data_dir / "backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Verze schémat
        self.schema_versions = {
            'chart_data': '2.0',
            'chart_config': '1.0',
            'virtual_questions': '1.0',
            'translations': '2.0'
        }
        
        # Kompati<PERSON><PERSON><PERSON> verze
        self.compatible_versions = {
            'chart_data': ['1.0', '1.1', '2.0'],
            'chart_config': ['1.0'],
            'virtual_questions': ['1.0'],
            'translations': ['1.0', '2.0']
        }
        
        logger.info("BackwardCompatibilityManager initialized")
    
    def check_data_compatibility(self, survey_id: str) -> Dict[str, Any]:
        """
        Kontroluje kompatibilitu dat pro survey
        
        Args:
            survey_id: ID průzkumu
            
        Returns:
            Výsledek kontroly kompatibility
        """
        compatibility_result = {
            'compatible': True,
            'issues': [],
            'warnings': [],
            'migrations_needed': [],
            'files_checked': []
        }
        
        survey_dir = self.data_dir / survey_id
        if not survey_dir.exists():
            compatibility_result['compatible'] = False
            compatibility_result['issues'].append(f"Survey directory {survey_id} not found")
            return compatibility_result
        
        # Kontrola chart_data.json
        chart_data_result = self._check_chart_data_compatibility(survey_dir)
        compatibility_result['files_checked'].append('chart_data.json')
        
        if not chart_data_result['compatible']:
            compatibility_result['compatible'] = False
            compatibility_result['issues'].extend(chart_data_result['issues'])
        
        compatibility_result['warnings'].extend(chart_data_result['warnings'])
        compatibility_result['migrations_needed'].extend(chart_data_result['migrations_needed'])
        
        # Kontrola chart_config.json
        config_result = self._check_chart_config_compatibility(survey_dir)
        compatibility_result['files_checked'].append('chart_config.json')
        
        if not config_result['compatible']:
            compatibility_result['warnings'].extend(config_result['issues'])  # Config issues jsou warnings
        
        # Kontrola translations
        translation_result = self._check_translation_compatibility(survey_dir)
        compatibility_result['files_checked'].append('translations')
        
        if not translation_result['compatible']:
            compatibility_result['warnings'].extend(translation_result['issues'])
        
        return compatibility_result
    
    def migrate_survey_data(self, survey_id: str, create_backup: bool = True) -> Dict[str, Any]:
        """
        Migruje data průzkumu na nejnovější verzi
        
        Args:
            survey_id: ID průzkumu
            create_backup: Zda vytvořit backup před migrací
            
        Returns:
            Výsledek migrace
        """
        migration_result = {
            'success': True,
            'migrations_performed': [],
            'errors': [],
            'backup_created': False,
            'backup_path': None
        }
        
        survey_dir = self.data_dir / survey_id
        if not survey_dir.exists():
            migration_result['success'] = False
            migration_result['errors'].append(f"Survey directory {survey_id} not found")
            return migration_result
        
        # Vytvoření backupu
        if create_backup:
            backup_path = self._create_survey_backup(survey_id)
            if backup_path:
                migration_result['backup_created'] = True
                migration_result['backup_path'] = str(backup_path)
            else:
                migration_result['errors'].append("Failed to create backup")
        
        try:
            # Migrace chart_data.json
            chart_data_migration = self._migrate_chart_data(survey_dir)
            if chart_data_migration['migrated']:
                migration_result['migrations_performed'].append('chart_data.json')
            
            # Migrace chart_config.json
            config_migration = self._migrate_chart_config(survey_dir)
            if config_migration['migrated']:
                migration_result['migrations_performed'].append('chart_config.json')
            
            # Migrace translations
            translation_migration = self._migrate_translations(survey_dir)
            if translation_migration['migrated']:
                migration_result['migrations_performed'].append('translations')
            
            logger.info(f"Migration completed for survey {survey_id}")
            
        except Exception as e:
            migration_result['success'] = False
            migration_result['errors'].append(str(e))
            logger.error(f"Migration failed for survey {survey_id}: {e}")
        
        return migration_result
    
    def restore_from_backup(self, survey_id: str, backup_timestamp: str = None) -> Dict[str, Any]:
        """
        Obnoví data z backupu
        
        Args:
            survey_id: ID průzkumu
            backup_timestamp: Timestamp backupu (nejnovější pokud None)
            
        Returns:
            Výsledek obnovy
        """
        restore_result = {
            'success': True,
            'restored_files': [],
            'errors': [],
            'backup_used': None
        }
        
        try:
            # Nalezení backupu
            if backup_timestamp:
                backup_path = self.backup_dir / f"{survey_id}_{backup_timestamp}"
            else:
                # Najít nejnovější backup
                backup_pattern = f"{survey_id}_*"
                backups = list(self.backup_dir.glob(backup_pattern))
                if not backups:
                    restore_result['success'] = False
                    restore_result['errors'].append(f"No backups found for survey {survey_id}")
                    return restore_result
                
                backup_path = max(backups, key=lambda p: p.stat().st_mtime)
            
            if not backup_path.exists():
                restore_result['success'] = False
                restore_result['errors'].append(f"Backup not found: {backup_path}")
                return restore_result
            
            restore_result['backup_used'] = str(backup_path)
            
            # Obnova dat
            survey_dir = self.data_dir / survey_id
            survey_dir.mkdir(parents=True, exist_ok=True)
            
            # Kopírování souborů z backupu
            for backup_file in backup_path.iterdir():
                if backup_file.is_file():
                    target_file = survey_dir / backup_file.name
                    shutil.copy2(backup_file, target_file)
                    restore_result['restored_files'].append(backup_file.name)
            
            logger.info(f"Restored survey {survey_id} from backup {backup_path}")
            
        except Exception as e:
            restore_result['success'] = False
            restore_result['errors'].append(str(e))
            logger.error(f"Restore failed for survey {survey_id}: {e}")
        
        return restore_result
    
    def validate_legacy_workflow(self, survey_id: str) -> Dict[str, Any]:
        """
        Validuje, že legacy workflow stále funguje
        
        Args:
            survey_id: ID průzkumu
            
        Returns:
            Výsledek validace
        """
        validation_result = {
            'legacy_compatible': True,
            'menu_functions_working': [],
            'menu_functions_broken': [],
            'data_accessible': True,
            'charts_generatable': True,
            'issues': []
        }
        
        try:
            # Test 1: Kontrola přístupu k datům
            survey_dir = self.data_dir / survey_id
            chart_data_file = survey_dir / "chart_data.json"
            
            if chart_data_file.exists():
                with open(chart_data_file, 'r', encoding='utf-8') as f:
                    chart_data = json.load(f)
                
                # Kontrola základní struktury
                if 'questions' in chart_data:
                    validation_result['menu_functions_working'].append('Data loading')
                else:
                    validation_result['data_accessible'] = False
                    validation_result['issues'].append('Invalid chart_data.json structure')
            else:
                validation_result['data_accessible'] = False
                validation_result['issues'].append('chart_data.json not found')
            
            # Test 2: Simulace legacy menu funkcí
            legacy_functions = [
                'menu_1_load_data',
                'menu_2_process_data', 
                'menu_3_generate_charts',
                'menu_4_download_charts',
                'menu_5_export_data'
            ]
            
            for func_name in legacy_functions:
                if self._test_legacy_function_compatibility(func_name, survey_id):
                    validation_result['menu_functions_working'].append(func_name)
                else:
                    validation_result['menu_functions_broken'].append(func_name)
            
            # Test 3: Kontrola generování grafů
            if not self._test_chart_generation_compatibility(survey_id):
                validation_result['charts_generatable'] = False
                validation_result['issues'].append('Chart generation compatibility issues')
            
            # Celkové hodnocení
            if (not validation_result['data_accessible'] or 
                validation_result['menu_functions_broken'] or 
                not validation_result['charts_generatable']):
                validation_result['legacy_compatible'] = False
            
        except Exception as e:
            validation_result['legacy_compatible'] = False
            validation_result['issues'].append(str(e))
            logger.error(f"Legacy workflow validation failed: {e}")
        
        return validation_result
    
    def get_compatibility_report(self, survey_id: str = None) -> Dict[str, Any]:
        """
        Vytvoří kompletní report kompatibility
        
        Args:
            survey_id: ID průzkumu (všechny pokud None)
            
        Returns:
            Kompletní report
        """
        report = {
            'generated_at': datetime.now().isoformat(),
            'schema_versions': self.schema_versions,
            'surveys': {},
            'overall_compatibility': True,
            'recommendations': []
        }
        
        if survey_id:
            survey_ids = [survey_id]
        else:
            # Najít všechny surveys
            survey_ids = [d.name for d in self.data_dir.iterdir() 
                         if d.is_dir() and d.name != 'backups']
        
        for sid in survey_ids:
            survey_report = {
                'compatibility_check': self.check_data_compatibility(sid),
                'legacy_validation': self.validate_legacy_workflow(sid)
            }
            
            report['surveys'][sid] = survey_report
            
            # Aktualizace celkové kompatibility
            if (not survey_report['compatibility_check']['compatible'] or 
                not survey_report['legacy_validation']['legacy_compatible']):
                report['overall_compatibility'] = False
        
        # Generování doporučení
        report['recommendations'] = self._generate_compatibility_recommendations(report)
        
        return report
    
    def _check_chart_data_compatibility(self, survey_dir: Path) -> Dict[str, Any]:
        """Kontrola kompatibility chart_data.json"""
        result = {
            'compatible': True,
            'issues': [],
            'warnings': [],
            'migrations_needed': []
        }
        
        chart_data_file = survey_dir / "chart_data.json"
        
        if not chart_data_file.exists():
            result['warnings'].append('chart_data.json not found - will be created on first use')
            return result
        
        try:
            with open(chart_data_file, 'r', encoding='utf-8') as f:
                chart_data = json.load(f)
            
            # Kontrola verze
            version = chart_data.get('version', '1.0')
            
            if version not in self.compatible_versions['chart_data']:
                result['compatible'] = False
                result['issues'].append(f'Unsupported chart_data version: {version}')
            elif version != self.schema_versions['chart_data']:
                result['migrations_needed'].append(f'chart_data migration from {version} to {self.schema_versions["chart_data"]}')
            
            # Kontrola základní struktury
            if 'questions' not in chart_data:
                result['issues'].append('Missing questions section in chart_data.json')
            
        except json.JSONDecodeError:
            result['compatible'] = False
            result['issues'].append('Invalid JSON in chart_data.json')
        except Exception as e:
            result['compatible'] = False
            result['issues'].append(f'Error reading chart_data.json: {e}')
        
        return result
    
    def _check_chart_config_compatibility(self, survey_dir: Path) -> Dict[str, Any]:
        """Kontrola kompatibility chart_config.json"""
        result = {
            'compatible': True,
            'issues': [],
            'warnings': [],
            'migrations_needed': []
        }
        
        config_file = survey_dir / "chart_config.json"
        
        if not config_file.exists():
            result['warnings'].append('chart_config.json not found - will use defaults')
            return result
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # Základní validace struktury
            if not isinstance(config_data, dict):
                result['issues'].append('chart_config.json should be a dictionary')
            
        except json.JSONDecodeError:
            result['issues'].append('Invalid JSON in chart_config.json')
        except Exception as e:
            result['issues'].append(f'Error reading chart_config.json: {e}')
        
        return result
    
    def _check_translation_compatibility(self, survey_dir: Path) -> Dict[str, Any]:
        """Kontrola kompatibility překladů"""
        result = {
            'compatible': True,
            'issues': [],
            'warnings': [],
            'migrations_needed': []
        }
        
        # Kontrola existence translation souborů
        translation_files = list(survey_dir.glob('translations_*.json'))
        
        if not translation_files:
            result['warnings'].append('No translation files found')
            return result
        
        for trans_file in translation_files:
            try:
                with open(trans_file, 'r', encoding='utf-8') as f:
                    trans_data = json.load(f)
                
                # Kontrola struktury
                if 'question_names' not in trans_data:
                    result['issues'].append(f'Missing question_names in {trans_file.name}')
                
            except Exception as e:
                result['issues'].append(f'Error reading {trans_file.name}: {e}')
        
        return result
    
    def _migrate_chart_data(self, survey_dir: Path) -> Dict[str, Any]:
        """Migrace chart_data.json"""
        result = {'migrated': False, 'errors': []}
        
        chart_data_file = survey_dir / "chart_data.json"
        
        if not chart_data_file.exists():
            return result
        
        try:
            with open(chart_data_file, 'r', encoding='utf-8') as f:
                chart_data = json.load(f)
            
            version = chart_data.get('version', '1.0')
            
            if version == '1.0':
                # Migrace z 1.0 na 2.0
                chart_data['version'] = '2.0'
                chart_data['metadata'] = chart_data.get('metadata', {})
                chart_data['metadata']['migrated_from'] = '1.0'
                chart_data['metadata']['migration_date'] = datetime.now().isoformat()
                
                # Přidání podpory pro virtuální otázky
                for question_id, question_data in chart_data.get('questions', {}).items():
                    if 'virtual' not in question_data:
                        question_data['virtual'] = False
                    if 'hidden' not in question_data:
                        question_data['hidden'] = False
                
                # Uložení migrovaných dat
                with open(chart_data_file, 'w', encoding='utf-8') as f:
                    json.dump(chart_data, f, indent=2, ensure_ascii=False)
                
                result['migrated'] = True
                logger.info(f"Migrated chart_data.json from 1.0 to 2.0")
            
        except Exception as e:
            result['errors'].append(str(e))
            logger.error(f"Chart data migration failed: {e}")
        
        return result
    
    def _migrate_chart_config(self, survey_dir: Path) -> Dict[str, Any]:
        """Migrace chart_config.json"""
        result = {'migrated': False, 'errors': []}
        
        # Pro chart_config zatím není potřeba migrace
        # Implementace podle potřeby v budoucnu
        
        return result
    
    def _migrate_translations(self, survey_dir: Path) -> Dict[str, Any]:
        """Migrace překladů"""
        result = {'migrated': False, 'errors': []}
        
        # Migrace translation souborů podle potřeby
        # Implementace podle potřeby v budoucnu
        
        return result
    
    def _create_survey_backup(self, survey_id: str) -> Optional[Path]:
        """Vytvoří backup survey dat"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = self.backup_dir / f"{survey_id}_{timestamp}"
            backup_path.mkdir(parents=True, exist_ok=True)
            
            survey_dir = self.data_dir / survey_id
            
            # Kopírování všech souborů
            for file_path in survey_dir.iterdir():
                if file_path.is_file():
                    shutil.copy2(file_path, backup_path / file_path.name)
            
            logger.info(f"Created backup for survey {survey_id} at {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"Failed to create backup for survey {survey_id}: {e}")
            return None
    
    def _test_legacy_function_compatibility(self, function_name: str, survey_id: str) -> bool:
        """Test kompatibility legacy funkce"""
        # Simulace testů legacy funkcí
        # V reálné implementaci by se testovaly skutečné funkce
        
        compatible_functions = [
            'menu_1_load_data',
            'menu_2_process_data',
            'menu_3_generate_charts',
            'menu_4_download_charts',
            'menu_5_export_data'
        ]
        
        return function_name in compatible_functions
    
    def _test_chart_generation_compatibility(self, survey_id: str) -> bool:
        """Test kompatibility generování grafů"""
        # Simulace testu generování grafů
        # V reálné implementaci by se testovalo skutečné generování
        
        survey_dir = self.data_dir / survey_id
        chart_data_file = survey_dir / "chart_data.json"
        
        return chart_data_file.exists()
    
    def _generate_compatibility_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """Generuje doporučení na základě reportu"""
        recommendations = []
        
        if not report['overall_compatibility']:
            recommendations.append("Spusťte migrace pro nekompatibilní surveys")
        
        # Kontrola jednotlivých surveys
        for survey_id, survey_report in report['surveys'].items():
            if survey_report['compatibility_check']['migrations_needed']:
                recommendations.append(f"Migrujte data pro survey {survey_id}")
            
            if not survey_report['legacy_validation']['legacy_compatible']:
                recommendations.append(f"Zkontrolujte legacy workflow pro survey {survey_id}")
        
        if not recommendations:
            recommendations.append("Všechny surveys jsou kompatibilní")
        
        return recommendations


def create_backward_compatibility_manager(data_dir: str = "data") -> BackwardCompatibilityManager:
    """
    Factory function to create Backward Compatibility Manager
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    return BackwardCompatibilityManager(data_dir)
