"""
Chart Generator Router
Router pro směrování generování grafů mezi různými generátory
BEZPEČNÝ - neovlivňuje stávající Datawrapper workflow
"""

import logging
from typing import Dict, Any, List, Optional, Protocol
from pathlib import Path
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class ChartGenerator(Protocol):
    """Protocol for chart generators"""
    
    def create_chart(self, config: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a chart from configuration and data"""
        ...
    
    def get_supported_chart_types(self) -> List[str]:
        """Get list of supported chart types"""
        ...


class BaseChartGenerator(ABC):
    """Base class for chart generators"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    @abstractmethod
    def create_chart(self, config: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a chart from configuration and data"""
        pass
    
    @abstractmethod
    def get_supported_chart_types(self) -> List[str]:
        """Get list of supported chart types"""
        pass
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate chart configuration"""
        required_fields = ['chart_type', 'generator', 'parameters']
        return all(field in config for field in required_fields)
    
    def validate_data(self, data: Dict[str, Any]) -> bool:
        """Validate chart data"""
        return isinstance(data, dict) and len(data) > 0


class DatawrapperGenerator(BaseChartGenerator):
    """
    Wrapper for existing Datawrapper functionality
    BEZPEČNÝ - používá stávající kód bez změn
    """
    
    def __init__(self):
        super().__init__("datawrapper")
        self._datawrapper_client = None
    
    def _get_datawrapper_client(self):
        """Get Datawrapper client - lazy loading"""
        if self._datawrapper_client is None:
            try:
                # Import existing Datawrapper functionality
                from ..datawrapper.datawrapper_client import DatawrapperClient
                self._datawrapper_client = DatawrapperClient()
            except ImportError as e:
                self.logger.error(f"Failed to import DatawrapperClient: {e}")
                raise
        return self._datawrapper_client
    
    def create_chart(self, config: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """Create chart using existing Datawrapper functionality"""
        try:
            if not self.validate_config(config) or not self.validate_data(data):
                raise ValueError("Invalid configuration or data")
            
            # Use existing Datawrapper workflow
            client = self._get_datawrapper_client()
            
            # Convert our config to Datawrapper format
            dw_config = self._convert_to_datawrapper_config(config)
            
            # Create chart using existing functionality
            result = client.create_chart(dw_config, data)
            
            return {
                'success': True,
                'chart_id': result.get('chart_id'),
                'chart_url': result.get('chart_url'),
                'generator': self.name,
                'chart_type': config['chart_type']
            }
        except Exception as e:
            self.logger.error(f"Datawrapper chart creation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'generator': self.name
            }
    
    def _convert_to_datawrapper_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Convert our config format to Datawrapper format"""
        # Map our chart types to Datawrapper chart types
        chart_type_mapping = {
            'column': 'd3-bars',
            'pie': 'd3-pies',
            'donut': 'd3-donut',
            'line': 'd3-lines',
            'area': 'd3-area'
        }
        
        dw_chart_type = chart_type_mapping.get(
            config['chart_type'], 
            config['parameters'].get('chart_type', 'd3-bars')
        )
        
        return {
            'chart_type': dw_chart_type,
            'parameters': config['parameters']
        }
    
    def get_supported_chart_types(self) -> List[str]:
        """Get supported chart types"""
        return ['column', 'pie', 'donut', 'line', 'area', 'table']


class FallbackGenerator(BaseChartGenerator):
    """
    Fallback generator when specific generator is not available
    BEZPEČNÝ - poskytuje základní funkcionalitu
    """
    
    def __init__(self):
        super().__init__("fallback")
    
    def create_chart(self, config: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a basic fallback chart"""
        try:
            self.logger.warning(f"Using fallback generator for {config.get('chart_type', 'unknown')}")
            
            # Create basic text representation
            chart_text = self._create_text_chart(data, config)
            
            return {
                'success': True,
                'chart_type': config.get('chart_type', 'text'),
                'chart_data': chart_text,
                'generator': self.name,
                'note': 'Fallback generator used - limited functionality'
            }
        except Exception as e:
            self.logger.error(f"Fallback chart creation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'generator': self.name
            }
    
    def _create_text_chart(self, data: Dict[str, Any], config: Dict[str, Any]) -> str:
        """Create a simple text representation of data"""
        chart_text = f"Chart Type: {config.get('chart_type', 'Unknown')}\n"
        chart_text += "Data Summary:\n"
        
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, (int, float)):
                    chart_text += f"  {key}: {value}\n"
                elif isinstance(value, list):
                    chart_text += f"  {key}: {len(value)} items\n"
                else:
                    chart_text += f"  {key}: {str(value)[:50]}...\n"
        
        return chart_text
    
    def get_supported_chart_types(self) -> List[str]:
        """Get supported chart types"""
        return ['text', 'summary']


class ChartGeneratorRouter:
    """
    Router pro směrování generování grafů
    BEZPEČNÝ - neovlivňuje stávající workflow
    """
    
    def __init__(self):
        """Initialize chart generator router"""
        self.generators: Dict[str, ChartGenerator] = {}
        self.fallback_generator = FallbackGenerator()
        
        # Register default generators
        self._register_default_generators()
        
        logger.info(f"ChartGeneratorRouter initialized with {len(self.generators)} generators")
    
    def _register_default_generators(self):
        """Register default generators"""
        try:
            # Register Datawrapper generator (existing functionality)
            datawrapper_gen = DatawrapperGenerator()
            self.register_generator('datawrapper', datawrapper_gen)
        except Exception as e:
            logger.warning(f"Failed to register Datawrapper generator: {e}")
        
        # Register fallback generator
        self.register_generator('fallback', self.fallback_generator)
    
    def register_generator(self, name: str, generator: ChartGenerator):
        """Register a chart generator"""
        self.generators[name] = generator
        logger.info(f"Registered chart generator: {name}")
    
    def unregister_generator(self, name: str):
        """Unregister a chart generator"""
        if name in self.generators:
            del self.generators[name]
            logger.info(f"Unregistered chart generator: {name}")
    
    def get_generator(self, name: str) -> Optional[ChartGenerator]:
        """Get a generator by name"""
        return self.generators.get(name)
    
    def list_generators(self) -> List[str]:
        """List all registered generators"""
        return list(self.generators.keys())
    
    def get_supported_chart_types(self, generator_name: str = None) -> Dict[str, List[str]]:
        """Get supported chart types for all or specific generator"""
        if generator_name:
            generator = self.get_generator(generator_name)
            if generator:
                return {generator_name: generator.get_supported_chart_types()}
            return {}
        
        # Return for all generators
        supported_types = {}
        for name, generator in self.generators.items():
            try:
                supported_types[name] = generator.get_supported_chart_types()
            except Exception as e:
                logger.error(f"Failed to get supported types for {name}: {e}")
                supported_types[name] = []
        
        return supported_types
    
    def create_chart(
        self, 
        config: Dict[str, Any], 
        data: Dict[str, Any],
        use_fallback: bool = True
    ) -> Dict[str, Any]:
        """
        Create a chart using appropriate generator
        
        Args:
            config: Chart configuration
            data: Chart data
            use_fallback: Whether to use fallback generator on failure
            
        Returns:
            Chart creation result
        """
        generator_name = config.get('generator', 'datawrapper')
        
        # Get primary generator
        generator = self.get_generator(generator_name)
        if not generator:
            logger.error(f"Generator '{generator_name}' not found")
            if use_fallback:
                generator = self.fallback_generator
                generator_name = 'fallback'
            else:
                return {
                    'success': False,
                    'error': f"Generator '{generator_name}' not available",
                    'generator': generator_name
                }
        
        # Try to create chart
        try:
            result = generator.create_chart(config, data)
            
            # Add router metadata
            result['router_info'] = {
                'requested_generator': config.get('generator', 'datawrapper'),
                'used_generator': generator_name,
                'fallback_used': generator_name == 'fallback'
            }
            
            return result
        except Exception as e:
            logger.error(f"Chart creation failed with {generator_name}: {e}")
            
            # Try fallback if enabled and not already using fallback
            if use_fallback and generator_name != 'fallback':
                logger.info("Trying fallback generator")
                try:
                    result = self.fallback_generator.create_chart(config, data)
                    result['router_info'] = {
                        'requested_generator': config.get('generator', 'datawrapper'),
                        'used_generator': 'fallback',
                        'fallback_used': True,
                        'primary_error': str(e)
                    }
                    return result
                except Exception as fallback_error:
                    logger.error(f"Fallback generator also failed: {fallback_error}")
            
            return {
                'success': False,
                'error': str(e),
                'generator': generator_name,
                'router_info': {
                    'requested_generator': config.get('generator', 'datawrapper'),
                    'used_generator': generator_name,
                    'fallback_used': False
                }
            }
    
    def create_multiple_charts(
        self, 
        chart_configs: List[Dict[str, Any]], 
        data_sets: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Create multiple charts"""
        if len(chart_configs) != len(data_sets):
            raise ValueError("Number of configs must match number of data sets")
        
        results = []
        for config, data in zip(chart_configs, data_sets):
            result = self.create_chart(config, data)
            results.append(result)
        
        return results
    
    def validate_chart_request(self, config: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a chart creation request"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Validate config
        if not isinstance(config, dict):
            validation_result['valid'] = False
            validation_result['errors'].append("Config must be a dictionary")
            return validation_result
        
        # Check required fields
        required_fields = ['chart_type', 'generator']
        for field in required_fields:
            if field not in config:
                validation_result['valid'] = False
                validation_result['errors'].append(f"Missing required field: {field}")
        
        # Check generator availability
        generator_name = config.get('generator')
        if generator_name and generator_name not in self.generators:
            validation_result['warnings'].append(f"Generator '{generator_name}' not available, will use fallback")
        
        # Validate data
        if not isinstance(data, dict) or len(data) == 0:
            validation_result['valid'] = False
            validation_result['errors'].append("Data must be a non-empty dictionary")
        
        return validation_result


def create_chart_router() -> ChartGeneratorRouter:
    """
    Factory function to create chart router
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    return ChartGeneratorRouter()
