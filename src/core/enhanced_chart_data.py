"""
Enhanced Chart Data Manager
R<PERSON><PERSON><PERSON>ř<PERSON>á struktura chart_data.json s backward compatibility
BEZPEČNÉ - zachovává kompatibilitu se stávajícím formátem
"""

import json
import logging
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class AIAnalysisResult:
    """Výsledek AI analýzy dat"""
    summary: str
    key_insights: List[str]
    sentiment_score: Optional[float] = None
    confidence: float = 0.0
    metadata_text: str = ""
    model_used: str = ""
    cost: float = 0.0
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


@dataclass
class ProcessedData:
    """Zpracovaná data pro konkrétní typ grafu"""
    chart_type: str
    data: Dict[str, Any]
    ai_analysis: Optional[AIAnalysisResult] = None
    processing_metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.processing_metadata is None:
            self.processing_metadata = {}


@dataclass
class VirtualQuestion:
    """Virtuální otázka (sloučená/vypočítaná)"""
    question_id: str
    question_text: str
    source_questions: List[str]
    merge_strategy: str  # 'concatenate', 'aggregate', 'combine'
    hidden: bool = False
    computed_data: Dict[str, Any] = None
    ai_analysis: Optional[AIAnalysisResult] = None
    
    def __post_init__(self):
        if self.computed_data is None:
            self.computed_data = {}


@dataclass
class EnhancedQuestion:
    """Rozšířená struktura pro otázku"""
    question_id: str
    question_text: str
    question_type: str
    data_type: str  # 'text', 'likert', 'choice', 'numeric'
    is_virtual: bool = False
    hidden: bool = False
    
    # Původní data (pro kompatibilitu)
    raw_data: List[Any] = None
    
    # Nová rozšířená data
    processed_data: Dict[str, ProcessedData] = None
    virtual_config: Optional[VirtualQuestion] = None
    
    def __post_init__(self):
        if self.raw_data is None:
            self.raw_data = []
        if self.processed_data is None:
            self.processed_data = {}


class EnhancedChartDataManager:
    """
    Správce rozšířených chart_data.json
    BEZPEČNÝ - zachovává backward compatibility
    """
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize enhanced chart data manager
        
        Args:
            data_dir: Directory containing survey data
        """
        self.data_dir = Path(data_dir)
        self.current_survey_id: Optional[str] = None
        self.chart_data: Dict[str, Any] = {}
        
        logger.info("EnhancedChartDataManager initialized")
    
    def load_chart_data(self, survey_id: str) -> Dict[str, Any]:
        """
        Load chart data for a survey with backward compatibility
        
        Args:
            survey_id: Survey identifier
            
        Returns:
            Chart data dictionary
        """
        self.current_survey_id = survey_id
        chart_data_file = self.data_dir / survey_id / "chart_data.json"
        
        try:
            if chart_data_file.exists():
                with open(chart_data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Migrate old format to new format if needed
                self.chart_data = self._migrate_chart_data(data)
                logger.info(f"Loaded chart data for survey {survey_id}")
            else:
                # Create new structure
                self.chart_data = self._create_empty_chart_data(survey_id)
                logger.info(f"Created new chart data structure for survey {survey_id}")
            
            return self.chart_data
        except Exception as e:
            logger.error(f"Failed to load chart data for survey {survey_id}: {e}")
            return self._create_empty_chart_data(survey_id)
    
    def _migrate_chart_data(self, old_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate old chart_data.json format to new enhanced format
        ZACHOVÁVÁ KOMPATIBILITU
        """
        # Check if already in new format
        if 'version' in old_data and old_data['version'] >= '2.0':
            return old_data
        
        # Migrate from old format
        new_data = {
            'version': '2.0',
            'survey_info': old_data.get('survey_info', {}),
            'questions': {},
            'virtual_questions': {},
            'chart_configs': {},
            'migration_info': {
                'migrated_at': datetime.now().isoformat(),
                'original_version': old_data.get('version', '1.0')
            }
        }
        
        # Migrate questions
        for q_id, q_data in old_data.get('questions', {}).items():
            enhanced_question = EnhancedQuestion(
                question_id=q_id,
                question_text=q_data.get('question_text', ''),
                question_type=q_data.get('question_type', ''),
                data_type=self._detect_data_type(q_data),
                raw_data=q_data.get('data', [])
            )
            new_data['questions'][q_id] = asdict(enhanced_question)
        
        logger.info("Migrated chart data from old format to enhanced format")
        return new_data
    
    def _detect_data_type(self, question_data: Dict[str, Any]) -> str:
        """Detect data type from question data"""
        # Simple heuristics for data type detection
        question_type = question_data.get('question_type', '').lower()
        
        if 'text' in question_type or 'comment' in question_type:
            return 'text'
        elif 'likert' in question_type or 'scale' in question_type:
            return 'likert'
        elif 'choice' in question_type or 'radio' in question_type:
            return 'choice'
        elif 'numeric' in question_type or 'number' in question_type:
            return 'numeric'
        else:
            return 'text'  # Default
    
    def _create_empty_chart_data(self, survey_id: str) -> Dict[str, Any]:
        """Create empty chart data structure"""
        return {
            'version': '2.0',
            'survey_info': {
                'survey_id': survey_id,
                'created_at': datetime.now().isoformat()
            },
            'questions': {},
            'virtual_questions': {},
            'chart_configs': {}
        }
    
    def save_chart_data(self, survey_id: str = None) -> bool:
        """
        Save chart data to file
        
        Args:
            survey_id: Survey identifier (uses current if not provided)
            
        Returns:
            True if saved successfully
        """
        if survey_id is None:
            survey_id = self.current_survey_id
        
        if not survey_id:
            logger.error("No survey ID provided for saving")
            return False
        
        try:
            chart_data_file = self.data_dir / survey_id / "chart_data.json"
            chart_data_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Update timestamp
            self.chart_data['survey_info']['updated_at'] = datetime.now().isoformat()
            
            with open(chart_data_file, 'w', encoding='utf-8') as f:
                json.dump(self.chart_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved chart data for survey {survey_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to save chart data: {e}")
            return False
    
    def add_question(self, question: EnhancedQuestion) -> bool:
        """Add or update a question"""
        try:
            self.chart_data['questions'][question.question_id] = asdict(question)
            logger.info(f"Added/updated question {question.question_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to add question: {e}")
            return False
    
    def get_question(self, question_id: str) -> Optional[EnhancedQuestion]:
        """Get a question by ID"""
        question_data = self.chart_data.get('questions', {}).get(question_id)
        if question_data:
            return EnhancedQuestion(**question_data)
        return None
    
    def add_virtual_question(self, virtual_question: VirtualQuestion) -> bool:
        """Add a virtual question"""
        try:
            self.chart_data['virtual_questions'][virtual_question.question_id] = asdict(virtual_question)
            logger.info(f"Added virtual question {virtual_question.question_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to add virtual question: {e}")
            return False
    
    def get_virtual_question(self, question_id: str) -> Optional[VirtualQuestion]:
        """Get a virtual question by ID"""
        vq_data = self.chart_data.get('virtual_questions', {}).get(question_id)
        if vq_data:
            return VirtualQuestion(**vq_data)
        return None
    
    def add_processed_data(
        self, 
        question_id: str, 
        chart_type: str, 
        data: Dict[str, Any],
        ai_analysis: Optional[AIAnalysisResult] = None
    ) -> bool:
        """Add processed data for a question and chart type"""
        try:
            question = self.get_question(question_id)
            if not question:
                logger.error(f"Question {question_id} not found")
                return False
            
            processed_data = ProcessedData(
                chart_type=chart_type,
                data=data,
                ai_analysis=ai_analysis,
                processing_metadata={
                    'processed_at': datetime.now().isoformat()
                }
            )
            
            question.processed_data[chart_type] = asdict(processed_data)
            self.add_question(question)
            
            logger.info(f"Added processed data for {question_id}:{chart_type}")
            return True
        except Exception as e:
            logger.error(f"Failed to add processed data: {e}")
            return False
    
    def get_processed_data(self, question_id: str, chart_type: str) -> Optional[ProcessedData]:
        """Get processed data for a question and chart type"""
        question = self.get_question(question_id)
        if question and chart_type in question.processed_data:
            return ProcessedData(**question.processed_data[chart_type])
        return None
    
    def get_all_questions(self, include_hidden: bool = False) -> Dict[str, EnhancedQuestion]:
        """Get all questions"""
        questions = {}
        for q_id, q_data in self.chart_data.get('questions', {}).items():
            question = EnhancedQuestion(**q_data)
            if include_hidden or not question.hidden:
                questions[q_id] = question
        return questions
    
    def get_all_virtual_questions(self, include_hidden: bool = False) -> Dict[str, VirtualQuestion]:
        """Get all virtual questions"""
        virtual_questions = {}
        for vq_id, vq_data in self.chart_data.get('virtual_questions', {}).items():
            virtual_question = VirtualQuestion(**vq_data)
            if include_hidden or not virtual_question.hidden:
                virtual_questions[vq_id] = virtual_question
        return virtual_questions
    
    def toggle_question_visibility(self, question_id: str) -> bool:
        """Toggle visibility of a question"""
        question = self.get_question(question_id)
        if question:
            question.hidden = not question.hidden
            return self.add_question(question)
        
        virtual_question = self.get_virtual_question(question_id)
        if virtual_question:
            virtual_question.hidden = not virtual_question.hidden
            return self.add_virtual_question(virtual_question)
        
        return False
    
    def export_for_legacy_system(self) -> Dict[str, Any]:
        """
        Export data in legacy format for backward compatibility
        ZACHOVÁVÁ KOMPATIBILITU se stávajícím systémem
        """
        legacy_data = {
            'survey_info': self.chart_data.get('survey_info', {}),
            'questions': {}
        }
        
        # Convert enhanced questions back to legacy format
        for q_id, q_data in self.chart_data.get('questions', {}).items():
            question = EnhancedQuestion(**q_data)
            if not question.hidden:  # Only export visible questions
                legacy_data['questions'][q_id] = {
                    'question_text': question.question_text,
                    'question_type': question.question_type,
                    'data': question.raw_data
                }
        
        return legacy_data


def create_enhanced_chart_data_manager(data_dir: str = "data") -> EnhancedChartDataManager:
    """
    Factory function to create enhanced chart data manager
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    return EnhancedChartDataManager(data_dir)
