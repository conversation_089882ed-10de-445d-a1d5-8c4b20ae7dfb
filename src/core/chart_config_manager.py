"""
Chart Type Configuration Manager
Bezpečný modul pro správu konfigurací typů grafů - NOVÝ, neovlivňuje stávaj<PERSON><PERSON><PERSON> kód
"""

import json
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


@dataclass
class ChartConfig:
    """Konfigurace pro jeden graf"""
    chart_type: str  # 'wordcloud', 'table', 'column', 'pie'
    generator: str   # 'datawrapper', 'internal_wordcloud', 'internal_table'
    parameters: Dict[str, Any]
    ai_analysis: bool = False
    prompt_template: Optional[str] = None
    custom_prompt: Optional[str] = None


@dataclass
class QuestionChartConfig:
    """Konfigurace grafů pro jednu otázku"""
    question_id: str
    question_text: str
    data_type: str  # 'text', 'likert', 'choice', 'numeric'
    charts: List[ChartConfig]
    hidden: bool = False


class ChartTypeConfigManager:
    """
    Správce konfigurací typů grafů
    BEZPEČNÝ - neovlivňuje stávající workflow
    """
    
    # Defaultní přiřazení typů grafů podle typu dat
    DEFAULT_ASSIGNMENTS = {
        'text': [
            ChartConfig('wordcloud', 'internal_wordcloud', {'max_words': 200, 'color_scheme': 'default'}),
            ChartConfig('table', 'internal_table', {'max_categories': 10})
        ],
        'likert': [
            ChartConfig('column', 'datawrapper', {'chart_type': 'd3-bars-split'}),
            ChartConfig('donut', 'datawrapper', {'chart_type': 'd3-donut'})
        ],
        'choice': [
            ChartConfig('column', 'datawrapper', {'chart_type': 'd3-bars'}),
            ChartConfig('pie', 'datawrapper', {'chart_type': 'd3-pies'})
        ],
        'numeric': [
            ChartConfig('histogram', 'datawrapper', {'chart_type': 'd3-bars'}),
            ChartConfig('box', 'datawrapper', {'chart_type': 'd3-box-plot'})
        ]
    }
    
    def __init__(self, config_dir: str = "config"):
        """
        Initialize chart config manager
        
        Args:
            config_dir: Directory for storing configurations
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.config_file = self.config_dir / "chart_types.json"
        self.configurations: Dict[str, QuestionChartConfig] = {}
        
        # Load existing configurations
        self._load_configurations()
        
        logger.info(f"ChartTypeConfigManager initialized with {len(self.configurations)} configurations")
    
    def _load_configurations(self):
        """Load configurations from file"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Convert JSON back to dataclasses
                for q_id, q_config in data.get('questions', {}).items():
                    charts = []
                    for chart_data in q_config.get('charts', []):
                        chart = ChartConfig(**chart_data)
                        charts.append(chart)
                    
                    question_config = QuestionChartConfig(
                        question_id=q_id,
                        question_text=q_config.get('question_text', ''),
                        data_type=q_config.get('data_type', 'text'),
                        charts=charts,
                        hidden=q_config.get('hidden', False)
                    )
                    self.configurations[q_id] = question_config
                
                logger.info(f"Loaded {len(self.configurations)} chart configurations")
        except Exception as e:
            logger.error(f"Failed to load chart configurations: {e}")
            self.configurations = {}
    
    def _save_configurations(self):
        """Save configurations to file"""
        try:
            # Convert dataclasses to JSON-serializable format
            data = {
                'questions': {}
            }
            
            for q_id, q_config in self.configurations.items():
                data['questions'][q_id] = {
                    'question_text': q_config.question_text,
                    'data_type': q_config.data_type,
                    'hidden': q_config.hidden,
                    'charts': [asdict(chart) for chart in q_config.charts]
                }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved {len(self.configurations)} chart configurations")
        except Exception as e:
            logger.error(f"Failed to save chart configurations: {e}")
    
    def get_question_config(self, question_id: str) -> Optional[QuestionChartConfig]:
        """Get configuration for a question"""
        return self.configurations.get(question_id)
    
    def set_question_config(self, question_config: QuestionChartConfig):
        """Set configuration for a question"""
        self.configurations[question_config.question_id] = question_config
        self._save_configurations()
    
    def suggest_chart_types(self, data_type: str) -> List[ChartConfig]:
        """Suggest chart types based on data type"""
        return self.DEFAULT_ASSIGNMENTS.get(data_type, [
            ChartConfig('column', 'datawrapper', {'chart_type': 'd3-bars'})
        ])
    
    def configure_question_charts(
        self, 
        question_id: str, 
        question_text: str, 
        data_type: str,
        auto_configure: bool = True
    ) -> QuestionChartConfig:
        """
        Configure charts for a question
        
        Args:
            question_id: Question identifier
            question_text: Question text
            data_type: Type of data (text, likert, choice, numeric)
            auto_configure: Whether to use default suggestions
            
        Returns:
            QuestionChartConfig object
        """
        # Check if already configured
        existing = self.get_question_config(question_id)
        if existing and not auto_configure:
            return existing
        
        # Get suggested charts
        suggested_charts = self.suggest_chart_types(data_type)
        
        # Create configuration
        question_config = QuestionChartConfig(
            question_id=question_id,
            question_text=question_text,
            data_type=data_type,
            charts=suggested_charts.copy(),
            hidden=False
        )
        
        # Save configuration
        self.set_question_config(question_config)
        
        return question_config
    
    def add_chart_to_question(
        self, 
        question_id: str, 
        chart_config: ChartConfig
    ) -> bool:
        """Add a chart configuration to a question"""
        question_config = self.get_question_config(question_id)
        if not question_config:
            logger.error(f"Question {question_id} not found")
            return False
        
        question_config.charts.append(chart_config)
        self.set_question_config(question_config)
        return True
    
    def remove_chart_from_question(
        self, 
        question_id: str, 
        chart_type: str
    ) -> bool:
        """Remove a chart configuration from a question"""
        question_config = self.get_question_config(question_id)
        if not question_config:
            return False
        
        question_config.charts = [
            chart for chart in question_config.charts 
            if chart.chart_type != chart_type
        ]
        self.set_question_config(question_config)
        return True
    
    def toggle_question_visibility(self, question_id: str) -> bool:
        """Toggle hidden status of a question"""
        question_config = self.get_question_config(question_id)
        if not question_config:
            return False
        
        question_config.hidden = not question_config.hidden
        self.set_question_config(question_config)
        
        logger.info(f"Question {question_id} visibility: {'hidden' if question_config.hidden else 'visible'}")
        return True
    
    def get_all_configurations(self) -> Dict[str, QuestionChartConfig]:
        """Get all question configurations"""
        return self.configurations.copy()
    
    def get_visible_configurations(self) -> Dict[str, QuestionChartConfig]:
        """Get only visible question configurations"""
        return {
            q_id: config for q_id, config in self.configurations.items()
            if not config.hidden
        }
    
    def reset_to_defaults(self, question_ids: List[str] = None):
        """Reset configurations to defaults"""
        if question_ids is None:
            question_ids = list(self.configurations.keys())
        
        for question_id in question_ids:
            config = self.get_question_config(question_id)
            if config:
                # Reset charts to defaults
                config.charts = self.suggest_chart_types(config.data_type)
                self.set_question_config(config)
        
        logger.info(f"Reset {len(question_ids)} question configurations to defaults")
    
    def export_configuration(self, output_file: str):
        """Export configuration to a file"""
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create export data
            export_data = {
                'version': '1.0',
                'exported_at': str(Path().cwd()),
                'configurations': {}
            }
            
            for q_id, config in self.configurations.items():
                export_data['configurations'][q_id] = asdict(config)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Configuration exported to {output_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to export configuration: {e}")
            return False
    
    def import_configuration(self, input_file: str) -> bool:
        """Import configuration from a file"""
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Import configurations
            for q_id, config_data in data.get('configurations', {}).items():
                # Convert back to dataclasses
                charts = []
                for chart_data in config_data.get('charts', []):
                    chart = ChartConfig(**chart_data)
                    charts.append(chart)
                
                question_config = QuestionChartConfig(
                    question_id=q_id,
                    question_text=config_data.get('question_text', ''),
                    data_type=config_data.get('data_type', 'text'),
                    charts=charts,
                    hidden=config_data.get('hidden', False)
                )
                self.configurations[q_id] = question_config
            
            self._save_configurations()
            logger.info(f"Configuration imported from {input_file}")
            return True
        except Exception as e:
            logger.error(f"Failed to import configuration: {e}")
            return False


def create_chart_config_manager(config_dir: str = "config") -> ChartTypeConfigManager:
    """
    Factory function to create chart config manager
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    return ChartTypeConfigManager(config_dir)
