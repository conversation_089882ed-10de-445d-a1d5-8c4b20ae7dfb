"""
Virtual Question Manager
S<PERSON><PERSON><PERSON><PERSON> virtu<PERSON><PERSON><PERSON><PERSON> (sloučené/vypočítané) jako Excel hidden columns
BEZPEČNÝ - neovlivňuje stávající otázky a workflow
"""

import logging
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import json

logger = logging.getLogger(__name__)


@dataclass
class MergeStrategy:
    """Strategie pro sloučení dat"""
    name: str
    description: str
    data_types: List[str]  # Podporované typy dat
    parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}


class VirtualQuestionManager:
    """
    Správce virtuálních otázek
    BEZPEČNÝ - funguje jako Excel hidden columns
    """
    
    # Předefinované strategie sloučení
    MERGE_STRATEGIES = {
        'concatenate': MergeStrategy(
            name='concatenate',
            description='Spojí textové odpovědi do jednoho textu',
            data_types=['text'],
            parameters={'separator': ' ', 'remove_duplicates': True}
        ),
        'aggregate_numeric': MergeStrategy(
            name='aggregate_numeric',
            description='Agreguje číselné hodnoty (suma, průměr, atd.)',
            data_types=['numeric', 'likert'],
            parameters={'operation': 'mean', 'ignore_null': True}
        ),
        'combine_categories': MergeStrategy(
            name='combine_categories',
            description='Kombinuje kategoriální odpovědi',
            data_types=['choice', 'likert'],
            parameters={'method': 'union', 'preserve_order': True}
        ),
        'cross_tabulate': MergeStrategy(
            name='cross_tabulate',
            description='Vytvoří křížovou tabulku z více otázek',
            data_types=['choice', 'likert'],
            parameters={'normalize': False}
        )
    }
    
    def __init__(self, chart_data_manager=None):
        """
        Initialize virtual question manager
        
        Args:
            chart_data_manager: Enhanced chart data manager instance
        """
        self.chart_data_manager = chart_data_manager
        self.virtual_questions: Dict[str, Dict[str, Any]] = {}
        
        logger.info("VirtualQuestionManager initialized")
    
    def create_virtual_question(
        self,
        question_id: str,
        question_text: str,
        source_questions: List[str],
        merge_strategy: str,
        parameters: Dict[str, Any] = None,
        hidden: bool = False
    ) -> Dict[str, Any]:
        """
        Vytvoří virtuální otázku
        
        Args:
            question_id: ID virtuální otázky (např. "VIRTUAL_satisfaction")
            question_text: Text virtuální otázky
            source_questions: Seznam ID zdrojových otázek
            merge_strategy: Strategie sloučení
            parameters: Parametry pro sloučení
            hidden: Zda je otázka skrytá (jako Excel hidden column)
            
        Returns:
            Virtuální otázka dictionary
        """
        try:
            # Validate merge strategy
            if merge_strategy not in self.MERGE_STRATEGIES:
                raise ValueError(f"Unknown merge strategy: {merge_strategy}")
            
            # Validate source questions exist
            if self.chart_data_manager:
                for sq_id in source_questions:
                    if not self.chart_data_manager.get_question(sq_id):
                        logger.warning(f"Source question {sq_id} not found")
            
            # Create virtual question
            virtual_question = {
                'question_id': question_id,
                'question_text': question_text,
                'type': 'virtual',
                'source_questions': source_questions,
                'merge_strategy': merge_strategy,
                'merge_parameters': parameters or {},
                'hidden': hidden,
                'created_at': datetime.now().isoformat(),
                'computed_data': {},
                'data_type': self._infer_data_type(source_questions, merge_strategy)
            }
            
            # Store virtual question
            self.virtual_questions[question_id] = virtual_question
            
            # Add to chart data manager if available
            if self.chart_data_manager:
                from .enhanced_chart_data import VirtualQuestion
                vq = VirtualQuestion(
                    question_id=question_id,
                    question_text=question_text,
                    source_questions=source_questions,
                    merge_strategy=merge_strategy,
                    hidden=hidden
                )
                self.chart_data_manager.add_virtual_question(vq)
            
            logger.info(f"Created virtual question: {question_id}")
            return virtual_question
        except Exception as e:
            logger.error(f"Failed to create virtual question: {e}")
            raise
    
    def _infer_data_type(self, source_questions: List[str], merge_strategy: str) -> str:
        """Infer data type for virtual question"""
        strategy = self.MERGE_STRATEGIES.get(merge_strategy)
        if not strategy:
            return 'text'
        
        # Return first supported data type as default
        return strategy.data_types[0] if strategy.data_types else 'text'
    
    def compute_virtual_data(self, question_id: str) -> Dict[str, Any]:
        """
        Vypočítá data pro virtuální otázku
        
        Args:
            question_id: ID virtuální otázky
            
        Returns:
            Vypočítaná data
        """
        virtual_question = self.virtual_questions.get(question_id)
        if not virtual_question:
            raise ValueError(f"Virtual question {question_id} not found")
        
        try:
            merge_strategy = virtual_question['merge_strategy']
            source_questions = virtual_question['source_questions']
            parameters = virtual_question['merge_parameters']
            
            # Get source data
            source_data = self._get_source_data(source_questions)
            
            # Apply merge strategy
            if merge_strategy == 'concatenate':
                computed_data = self._concatenate_text_data(source_data, parameters)
            elif merge_strategy == 'aggregate_numeric':
                computed_data = self._aggregate_numeric_data(source_data, parameters)
            elif merge_strategy == 'combine_categories':
                computed_data = self._combine_categorical_data(source_data, parameters)
            elif merge_strategy == 'cross_tabulate':
                computed_data = self._cross_tabulate_data(source_data, parameters)
            else:
                raise ValueError(f"Unsupported merge strategy: {merge_strategy}")
            
            # Store computed data
            virtual_question['computed_data'] = computed_data
            virtual_question['last_computed'] = datetime.now().isoformat()
            
            logger.info(f"Computed data for virtual question: {question_id}")
            return computed_data
        except Exception as e:
            logger.error(f"Failed to compute virtual data for {question_id}: {e}")
            raise
    
    def _get_source_data(self, source_questions: List[str]) -> Dict[str, List[Any]]:
        """Get data from source questions"""
        source_data = {}
        
        if self.chart_data_manager:
            for sq_id in source_questions:
                question = self.chart_data_manager.get_question(sq_id)
                if question:
                    source_data[sq_id] = question.raw_data
                else:
                    logger.warning(f"Source question {sq_id} not found")
                    source_data[sq_id] = []
        else:
            # Fallback - empty data
            for sq_id in source_questions:
                source_data[sq_id] = []
        
        return source_data
    
    def _concatenate_text_data(self, source_data: Dict[str, List[Any]], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Concatenate text data from multiple questions"""
        separator = parameters.get('separator', ' ')
        remove_duplicates = parameters.get('remove_duplicates', True)
        
        all_texts = []
        for question_data in source_data.values():
            for response in question_data:
                if response and isinstance(response, str):
                    text = response.strip()
                    if text:
                        if not remove_duplicates or text not in all_texts:
                            all_texts.append(text)
        
        concatenated_text = separator.join(all_texts)
        
        return {
            'concatenated_text': concatenated_text,
            'individual_texts': all_texts,
            'total_responses': len(all_texts),
            'source_breakdown': {
                q_id: len([r for r in data if r and isinstance(r, str) and r.strip()])
                for q_id, data in source_data.items()
            }
        }
    
    def _aggregate_numeric_data(self, source_data: Dict[str, List[Any]], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Aggregate numeric data from multiple questions"""
        operation = parameters.get('operation', 'mean')
        ignore_null = parameters.get('ignore_null', True)
        
        all_values = []
        for question_data in source_data.values():
            for response in question_data:
                try:
                    value = float(response) if response is not None else None
                    if value is not None or not ignore_null:
                        all_values.append(value)
                except (ValueError, TypeError):
                    if not ignore_null:
                        all_values.append(None)
        
        # Filter out None values if ignore_null is True
        if ignore_null:
            numeric_values = [v for v in all_values if v is not None]
        else:
            numeric_values = all_values
        
        if not numeric_values:
            return {'error': 'No numeric values found'}
        
        # Perform aggregation
        if operation == 'mean':
            result = sum(numeric_values) / len(numeric_values)
        elif operation == 'sum':
            result = sum(numeric_values)
        elif operation == 'min':
            result = min(numeric_values)
        elif operation == 'max':
            result = max(numeric_values)
        elif operation == 'count':
            result = len(numeric_values)
        else:
            result = sum(numeric_values) / len(numeric_values)  # Default to mean
        
        return {
            'aggregated_value': result,
            'operation': operation,
            'count': len(numeric_values),
            'all_values': all_values,
            'source_breakdown': {
                q_id: len([r for r in data if r is not None])
                for q_id, data in source_data.items()
            }
        }
    
    def _combine_categorical_data(self, source_data: Dict[str, List[Any]], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Combine categorical data from multiple questions"""
        method = parameters.get('method', 'union')
        preserve_order = parameters.get('preserve_order', True)
        
        all_categories = []
        category_counts = {}
        
        for question_data in source_data.values():
            for response in question_data:
                if response:
                    category = str(response).strip()
                    if category:
                        if preserve_order and category not in all_categories:
                            all_categories.append(category)
                        elif not preserve_order:
                            all_categories.append(category)
                        
                        category_counts[category] = category_counts.get(category, 0) + 1
        
        if method == 'union':
            unique_categories = list(set(all_categories)) if not preserve_order else list(dict.fromkeys(all_categories))
        else:
            unique_categories = all_categories
        
        return {
            'categories': unique_categories,
            'category_counts': category_counts,
            'total_responses': len(all_categories),
            'unique_categories': len(set(all_categories)),
            'method': method
        }
    
    def _cross_tabulate_data(self, source_data: Dict[str, List[Any]], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Create cross-tabulation from multiple questions"""
        normalize = parameters.get('normalize', False)
        
        # Simple cross-tabulation for two questions
        question_ids = list(source_data.keys())
        if len(question_ids) < 2:
            return {'error': 'Cross-tabulation requires at least 2 questions'}
        
        q1_data = source_data[question_ids[0]]
        q2_data = source_data[question_ids[1]]
        
        # Ensure same length
        min_length = min(len(q1_data), len(q2_data))
        
        cross_tab = {}
        for i in range(min_length):
            val1 = str(q1_data[i]) if q1_data[i] else 'N/A'
            val2 = str(q2_data[i]) if q2_data[i] else 'N/A'
            
            if val1 not in cross_tab:
                cross_tab[val1] = {}
            cross_tab[val1][val2] = cross_tab[val1].get(val2, 0) + 1
        
        return {
            'cross_tabulation': cross_tab,
            'question_1': question_ids[0],
            'question_2': question_ids[1],
            'total_pairs': min_length,
            'normalized': normalize
        }
    
    def toggle_visibility(self, question_id: str) -> bool:
        """
        Toggle visibility of virtual question (jako Excel hidden columns)
        
        Args:
            question_id: ID virtuální otázky
            
        Returns:
            True if successful
        """
        virtual_question = self.virtual_questions.get(question_id)
        if not virtual_question:
            return False
        
        virtual_question['hidden'] = not virtual_question['hidden']
        
        # Update in chart data manager
        if self.chart_data_manager:
            self.chart_data_manager.toggle_question_visibility(question_id)
        
        logger.info(f"Toggled visibility for {question_id}: {'hidden' if virtual_question['hidden'] else 'visible'}")
        return True
    
    def get_virtual_question(self, question_id: str) -> Optional[Dict[str, Any]]:
        """Get virtual question by ID"""
        return self.virtual_questions.get(question_id)
    
    def get_all_virtual_questions(self, include_hidden: bool = False) -> Dict[str, Dict[str, Any]]:
        """Get all virtual questions"""
        if include_hidden:
            return self.virtual_questions.copy()
        
        return {
            q_id: vq for q_id, vq in self.virtual_questions.items()
            if not vq.get('hidden', False)
        }
    
    def delete_virtual_question(self, question_id: str) -> bool:
        """Delete a virtual question"""
        if question_id in self.virtual_questions:
            del self.virtual_questions[question_id]
            logger.info(f"Deleted virtual question: {question_id}")
            return True
        return False
    
    def get_available_merge_strategies(self, data_types: List[str] = None) -> Dict[str, MergeStrategy]:
        """Get available merge strategies, optionally filtered by data types"""
        if data_types is None:
            return self.MERGE_STRATEGIES.copy()
        
        compatible_strategies = {}
        for name, strategy in self.MERGE_STRATEGIES.items():
            if any(dt in strategy.data_types for dt in data_types):
                compatible_strategies[name] = strategy
        
        return compatible_strategies


def create_virtual_question_manager(chart_data_manager=None) -> VirtualQuestionManager:
    """
    Factory function to create virtual question manager
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    return VirtualQuestionManager(chart_data_manager)
