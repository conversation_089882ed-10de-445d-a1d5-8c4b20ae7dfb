"""
Extended Translation System
Rozšíření překladového systému o support pro virtuální otázky
BEZPEČNÉ - rozšiřuje stávající translation systém
"""

import logging
from typing import Dict, Any, List, Optional, Set
from pathlib import Path
import json
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


@dataclass
class VirtualQuestionTranslation:
    """Překlad virtuální otázky"""
    virtual_question_id: str
    source_questions: List[str]
    translations: Dict[str, str]  # language_code -> translated_text
    merge_strategy: str
    auto_generated: bool = False
    last_updated: str = ""


@dataclass
class TranslationContext:
    """Kontext pro překlad"""
    question_type: str
    data_type: str
    source_language: str
    target_language: str
    merge_strategy: Optional[str] = None
    source_questions: List[str] = None


class ExtendedTranslationSystem:
    """
    Rozšířený překladový systém s podporou virtuálních otázek
    BEZPEČNÝ - zachovává kompatibilitu se stávajícím systémem
    """
    
    def __init__(self, translations_dir: str = "data/translations"):
        """
        Initialize Extended Translation System
        
        Args:
            translations_dir: Adresář s překladovými soubory
        """
        self.translations_dir = Path(translations_dir)
        self.translations_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache pro překlady
        self.translation_cache: Dict[str, Dict[str, Any]] = {}
        self.virtual_translations: Dict[str, VirtualQuestionTranslation] = {}
        
        # Podporované jazyky
        self.supported_languages = {'cs', 'en', 'de', 'sk'}
        self.default_language = 'cs'
        
        logger.info("ExtendedTranslationSystem initialized")
    
    def load_translations(self, language_code: str) -> Dict[str, Any]:
        """
        Načte překlady pro daný jazyk
        
        Args:
            language_code: Kód jazyka (cs, en, de, sk)
            
        Returns:
            Dictionary s překlady
        """
        if language_code in self.translation_cache:
            return self.translation_cache[language_code]
        
        try:
            translation_file = self.translations_dir / f"{language_code}.json"
            
            if translation_file.exists():
                with open(translation_file, 'r', encoding='utf-8') as f:
                    translations = json.load(f)
            else:
                # Vytvoření základní struktury
                translations = self._create_default_translation_structure()
                self.save_translations(language_code, translations)
            
            self.translation_cache[language_code] = translations
            return translations
            
        except Exception as e:
            logger.error(f"Failed to load translations for {language_code}: {e}")
            return self._create_default_translation_structure()
    
    def save_translations(self, language_code: str, translations: Dict[str, Any]) -> bool:
        """
        Uloží překlady pro daný jazyk
        
        Args:
            language_code: Kód jazyka
            translations: Dictionary s překlady
            
        Returns:
            True při úspěchu
        """
        try:
            translation_file = self.translations_dir / f"{language_code}.json"
            
            with open(translation_file, 'w', encoding='utf-8') as f:
                json.dump(translations, f, indent=2, ensure_ascii=False)
            
            # Aktualizace cache
            self.translation_cache[language_code] = translations
            
            logger.info(f"Saved translations for {language_code}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save translations for {language_code}: {e}")
            return False
    
    def translate_virtual_question(
        self,
        virtual_question_id: str,
        source_questions: List[str],
        target_language: str,
        merge_strategy: str = "concatenate",
        custom_translation: str = None
    ) -> Optional[str]:
        """
        Přeloží virtuální otázku
        
        Args:
            virtual_question_id: ID virtuální otázky
            source_questions: Seznam zdrojových otázek
            target_language: Cílový jazyk
            merge_strategy: Strategie sloučení
            custom_translation: Vlastní překlad (volitelný)
            
        Returns:
            Přeložený text nebo None
        """
        try:
            # Pokud je zadán vlastní překlad, použij ho
            if custom_translation:
                self._store_virtual_translation(
                    virtual_question_id, source_questions, target_language, 
                    custom_translation, merge_strategy, auto_generated=False
                )
                return custom_translation
            
            # Kontrola cache
            cached_translation = self._get_cached_virtual_translation(
                virtual_question_id, target_language
            )
            if cached_translation:
                return cached_translation
            
            # Načtení překladů zdrojových otázek
            source_translations = []
            translations = self.load_translations(target_language)
            
            for source_q_id in source_questions:
                source_translation = self._get_question_translation(
                    source_q_id, target_language, translations
                )
                if source_translation:
                    source_translations.append(source_translation)
            
            if not source_translations:
                logger.warning(f"No source translations found for virtual question {virtual_question_id}")
                return None
            
            # Generování překladu podle merge strategy
            translated_text = self._generate_virtual_translation(
                source_translations, merge_strategy, target_language
            )
            
            # Uložení do cache
            if translated_text:
                self._store_virtual_translation(
                    virtual_question_id, source_questions, target_language,
                    translated_text, merge_strategy, auto_generated=True
                )
            
            return translated_text
            
        except Exception as e:
            logger.error(f"Failed to translate virtual question {virtual_question_id}: {e}")
            return None
    
    def update_virtual_question_translation(
        self,
        virtual_question_id: str,
        language_code: str,
        new_translation: str
    ) -> bool:
        """
        Aktualizuje překlad virtuální otázky
        
        Args:
            virtual_question_id: ID virtuální otázky
            language_code: Kód jazyka
            new_translation: Nový překlad
            
        Returns:
            True při úspěchu
        """
        try:
            # Načtení existujícího překladu
            vq_translation = self.virtual_translations.get(virtual_question_id)
            
            if vq_translation:
                # Aktualizace existujícího
                vq_translation.translations[language_code] = new_translation
                vq_translation.auto_generated = False
                vq_translation.last_updated = datetime.now().isoformat()
            else:
                # Vytvoření nového
                vq_translation = VirtualQuestionTranslation(
                    virtual_question_id=virtual_question_id,
                    source_questions=[],  # Bude doplněno později
                    translations={language_code: new_translation},
                    merge_strategy="unknown",
                    auto_generated=False,
                    last_updated=datetime.now().isoformat()
                )
                self.virtual_translations[virtual_question_id] = vq_translation
            
            # Uložení do souboru
            self._save_virtual_translations()
            
            logger.info(f"Updated virtual question translation {virtual_question_id} for {language_code}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update virtual question translation: {e}")
            return False
    
    def get_all_virtual_translations(self, language_code: str = None) -> Dict[str, Any]:
        """
        Získá všechny překlady virtuálních otázek
        
        Args:
            language_code: Kód jazyka (volitelný)
            
        Returns:
            Dictionary s překlady
        """
        result = {}
        
        for vq_id, vq_translation in self.virtual_translations.items():
            if language_code:
                if language_code in vq_translation.translations:
                    result[vq_id] = vq_translation.translations[language_code]
            else:
                result[vq_id] = vq_translation.translations
        
        return result
    
    def validate_virtual_translations(self, virtual_question_id: str) -> Dict[str, Any]:
        """
        Validuje překlady virtuální otázky
        
        Args:
            virtual_question_id: ID virtuální otázky
            
        Returns:
            Výsledek validace
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'missing_languages': [],
            'auto_generated_count': 0
        }
        
        vq_translation = self.virtual_translations.get(virtual_question_id)
        
        if not vq_translation:
            validation_result['valid'] = False
            validation_result['errors'].append(f"No translations found for {virtual_question_id}")
            return validation_result
        
        # Kontrola podporovaných jazyků
        for lang in self.supported_languages:
            if lang not in vq_translation.translations:
                validation_result['missing_languages'].append(lang)
        
        # Počítání auto-generovaných překladů
        if vq_translation.auto_generated:
            validation_result['auto_generated_count'] = len(vq_translation.translations)
        
        # Varování pro chybějící jazyky
        if validation_result['missing_languages']:
            validation_result['warnings'].append(
                f"Missing translations for: {', '.join(validation_result['missing_languages'])}"
            )
        
        return validation_result
    
    def generate_missing_translations(
        self,
        virtual_question_id: str,
        source_language: str = None
    ) -> Dict[str, str]:
        """
        Generuje chybějící překlady pro virtuální otázku
        
        Args:
            virtual_question_id: ID virtuální otázky
            source_language: Zdrojový jazyk (default: cs)
            
        Returns:
            Dictionary s novými překlady
        """
        if source_language is None:
            source_language = self.default_language
        
        generated_translations = {}
        
        vq_translation = self.virtual_translations.get(virtual_question_id)
        if not vq_translation:
            return generated_translations
        
        # Zdrojový text
        source_text = vq_translation.translations.get(source_language)
        if not source_text:
            logger.warning(f"No source text in {source_language} for {virtual_question_id}")
            return generated_translations
        
        # Generování překladů pro chybějící jazyky
        for target_lang in self.supported_languages:
            if target_lang != source_language and target_lang not in vq_translation.translations:
                
                # Jednoduchý překlad (v reálné implementaci by se použil translation service)
                translated_text = self._simple_translate(source_text, source_language, target_lang)
                
                if translated_text:
                    generated_translations[target_lang] = translated_text
                    vq_translation.translations[target_lang] = translated_text
        
        # Uložení aktualizací
        if generated_translations:
            vq_translation.auto_generated = True
            vq_translation.last_updated = datetime.now().isoformat()
            self._save_virtual_translations()
        
        return generated_translations
    
    def export_virtual_translations(self, output_file: str) -> bool:
        """
        Exportuje všechny překlady virtuálních otázek
        
        Args:
            output_file: Cesta k výstupnímu souboru
            
        Returns:
            True při úspěchu
        """
        try:
            export_data = {
                'virtual_translations': {
                    vq_id: asdict(vq_translation)
                    for vq_id, vq_translation in self.virtual_translations.items()
                },
                'supported_languages': list(self.supported_languages),
                'default_language': self.default_language,
                'exported_at': datetime.now().isoformat()
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Exported virtual translations to {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export virtual translations: {e}")
            return False
    
    def import_virtual_translations(self, input_file: str) -> bool:
        """
        Importuje překlady virtuálních otázek
        
        Args:
            input_file: Cesta k vstupnímu souboru
            
        Returns:
            True při úspěchu
        """
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            virtual_translations_data = import_data.get('virtual_translations', {})
            
            for vq_id, vq_data in virtual_translations_data.items():
                vq_translation = VirtualQuestionTranslation(**vq_data)
                self.virtual_translations[vq_id] = vq_translation
            
            # Uložení importovaných dat
            self._save_virtual_translations()
            
            logger.info(f"Imported {len(virtual_translations_data)} virtual translations")
            return True
            
        except Exception as e:
            logger.error(f"Failed to import virtual translations: {e}")
            return False
    
    def _create_default_translation_structure(self) -> Dict[str, Any]:
        """Vytvoří základní strukturu překladů"""
        return {
            'question_names': {},
            'subquestions': {},
            'scale_responses': {},
            'choice_responses': {},
            'virtual_questions': {},
            'metadata': {
                'version': '2.0',
                'supports_virtual': True,
                'last_updated': datetime.now().isoformat()
            }
        }
    
    def _get_question_translation(
        self, 
        question_id: str, 
        language_code: str, 
        translations: Dict[str, Any]
    ) -> Optional[str]:
        """Získá překlad otázky"""
        question_names = translations.get('question_names', {})
        return question_names.get(question_id)
    
    def _generate_virtual_translation(
        self,
        source_translations: List[str],
        merge_strategy: str,
        target_language: str
    ) -> str:
        """Generuje překlad virtuální otázky podle merge strategy"""
        
        if merge_strategy == "concatenate":
            # Spojení překladů
            if target_language == 'en':
                return f"Combined feedback: {' and '.join(source_translations)}"
            else:
                return f"Kombinovaná zpětná vazba: {' a '.join(source_translations)}"
        
        elif merge_strategy == "aggregate_numeric":
            if target_language == 'en':
                return f"Aggregated values from: {', '.join(source_translations)}"
            else:
                return f"Agregované hodnoty z: {', '.join(source_translations)}"
        
        elif merge_strategy == "combine_categories":
            if target_language == 'en':
                return f"Combined categories: {', '.join(source_translations)}"
            else:
                return f"Kombinované kategorie: {', '.join(source_translations)}"
        
        else:
            # Default
            if target_language == 'en':
                return f"Virtual question: {', '.join(source_translations)}"
            else:
                return f"Virtuální otázka: {', '.join(source_translations)}"
    
    def _simple_translate(self, text: str, source_lang: str, target_lang: str) -> str:
        """Jednoduchý překlad (placeholder pro skutečný translation service)"""
        
        # Základní slovník pro demo účely
        translation_dict = {
            'cs_to_en': {
                'Kombinovaná zpětná vazba': 'Combined feedback',
                'Celková spokojenost': 'Overall satisfaction',
                'Virtuální otázka': 'Virtual question',
                'Agregované hodnoty': 'Aggregated values',
                'Kombinované kategorie': 'Combined categories'
            },
            'en_to_cs': {
                'Combined feedback': 'Kombinovaná zpětná vazba',
                'Overall satisfaction': 'Celková spokojenost',
                'Virtual question': 'Virtuální otázka',
                'Aggregated values': 'Agregované hodnoty',
                'Combined categories': 'Kombinované kategorie'
            }
        }
        
        dict_key = f"{source_lang}_to_{target_lang}"
        if dict_key in translation_dict:
            return translation_dict[dict_key].get(text, f"[{target_lang.upper()}] {text}")
        
        return f"[{target_lang.upper()}] {text}"
    
    def _get_cached_virtual_translation(self, virtual_question_id: str, language_code: str) -> Optional[str]:
        """Získá překlad z cache"""
        vq_translation = self.virtual_translations.get(virtual_question_id)
        if vq_translation:
            return vq_translation.translations.get(language_code)
        return None
    
    def _store_virtual_translation(
        self,
        virtual_question_id: str,
        source_questions: List[str],
        language_code: str,
        translation: str,
        merge_strategy: str,
        auto_generated: bool
    ):
        """Uloží překlad virtuální otázky"""
        from datetime import datetime
        
        if virtual_question_id in self.virtual_translations:
            vq_translation = self.virtual_translations[virtual_question_id]
            vq_translation.translations[language_code] = translation
        else:
            vq_translation = VirtualQuestionTranslation(
                virtual_question_id=virtual_question_id,
                source_questions=source_questions,
                translations={language_code: translation},
                merge_strategy=merge_strategy,
                auto_generated=auto_generated,
                last_updated=datetime.now().isoformat()
            )
            self.virtual_translations[virtual_question_id] = vq_translation
    
    def _save_virtual_translations(self):
        """Uloží překlady virtuálních otázek do souboru"""
        try:
            vt_file = self.translations_dir / "virtual_translations.json"
            
            export_data = {
                vq_id: asdict(vq_translation)
                for vq_id, vq_translation in self.virtual_translations.items()
            }
            
            with open(vt_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Failed to save virtual translations: {e}")


def create_extended_translation_system(translations_dir: str = "data/translations") -> ExtendedTranslationSystem:
    """
    Factory function to create Extended Translation System
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    return ExtendedTranslationSystem(translations_dir)
