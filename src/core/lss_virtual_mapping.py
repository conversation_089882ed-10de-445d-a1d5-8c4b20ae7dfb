"""
LSS Virtual Question Mapping
Řešení mapování virtuálních otázek zpět na LSS strukturu
BEZPEČNÉ - neovlivňuje stávající LSS processing
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import xml.etree.ElementTree as ET
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class LSSQuestionMapping:
    """Mapování mezi virtuální otázkou a LSS strukturou"""
    virtual_question_id: str
    source_question_ids: List[str]
    lss_question_data: Dict[str, Any]
    merge_strategy: str
    virtual_question_text: str
    language_mappings: Dict[str, str]


class LSSVirtualMapper:
    """
    Mapper pro virtuální otázky v LSS struktuře
    BEZPEČNÝ - zachovává původní LSS strukturu
    """
    
    def __init__(self):
        """Initialize LSS Virtual Mapper"""
        self.virtual_mappings: Dict[str, LSSQuestionMapping] = {}
        self.lss_cache: Dict[str, ET.Element] = {}
        
        logger.info("LSSVirtualMapper initialized")
    
    def create_virtual_lss_mapping(
        self,
        virtual_question_id: str,
        source_question_ids: List[str],
        lss_file_path: str,
        merge_strategy: str = "concatenate"
    ) -> Optional[LSSQuestionMapping]:
        """
        Vytvoří mapování virtuální otázky na LSS strukturu
        
        Args:
            virtual_question_id: ID virtuální otázky
            source_question_ids: Seznam zdrojových otázek
            lss_file_path: Cesta k LSS souboru
            merge_strategy: Strategie sloučení
            
        Returns:
            LSSQuestionMapping nebo None při chybě
        """
        try:
            # Načtení LSS souboru
            lss_root = self._load_lss_file(lss_file_path)
            if lss_root is None:
                return None
            
            # Extrakce dat zdrojových otázek
            source_data = {}
            for q_id in source_question_ids:
                question_data = self._extract_question_from_lss(lss_root, q_id)
                if question_data:
                    source_data[q_id] = question_data
            
            if not source_data:
                logger.error(f"No source questions found for virtual question {virtual_question_id}")
                return None
            
            # Vytvoření virtuální LSS struktury
            virtual_lss_data = self._create_virtual_lss_structure(
                virtual_question_id,
                source_data,
                merge_strategy
            )
            
            # Extrakce jazykových variant
            language_mappings = self._extract_language_mappings(source_data)
            
            # Vytvoření mapování
            mapping = LSSQuestionMapping(
                virtual_question_id=virtual_question_id,
                source_question_ids=source_question_ids,
                lss_question_data=virtual_lss_data,
                merge_strategy=merge_strategy,
                virtual_question_text=virtual_lss_data.get('question_text', ''),
                language_mappings=language_mappings
            )
            
            # Uložení mapování
            self.virtual_mappings[virtual_question_id] = mapping
            
            logger.info(f"Created LSS mapping for virtual question {virtual_question_id}")
            return mapping
            
        except Exception as e:
            logger.error(f"Failed to create LSS mapping for {virtual_question_id}: {e}")
            return None
    
    def get_virtual_lss_structure(self, virtual_question_id: str) -> Optional[Dict[str, Any]]:
        """
        Získá LSS strukturu pro virtuální otázku
        
        Args:
            virtual_question_id: ID virtuální otázky
            
        Returns:
            LSS struktura nebo None
        """
        mapping = self.virtual_mappings.get(virtual_question_id)
        if mapping:
            return mapping.lss_question_data
        return None
    
    def generate_virtual_lss_xml(self, virtual_question_id: str) -> Optional[str]:
        """
        Generuje XML reprezentaci virtuální otázky v LSS formátu
        
        Args:
            virtual_question_id: ID virtuální otázky
            
        Returns:
            XML string nebo None
        """
        mapping = self.virtual_mappings.get(virtual_question_id)
        if not mapping:
            return None
        
        try:
            # Vytvoření XML elementu pro virtuální otázku
            question_elem = ET.Element("question")
            question_elem.set("qid", virtual_question_id)
            question_elem.set("virtual", "Y")
            question_elem.set("merge_strategy", mapping.merge_strategy)
            
            # Přidání zdrojových otázek
            sources_elem = ET.SubElement(question_elem, "source_questions")
            for source_id in mapping.source_question_ids:
                source_elem = ET.SubElement(sources_elem, "source")
                source_elem.text = source_id
            
            # Přidání textu otázky
            question_text_elem = ET.SubElement(question_elem, "question_text")
            question_text_elem.text = mapping.virtual_question_text
            
            # Přidání jazykových variant
            if mapping.language_mappings:
                languages_elem = ET.SubElement(question_elem, "languages")
                for lang, text in mapping.language_mappings.items():
                    lang_elem = ET.SubElement(languages_elem, "language")
                    lang_elem.set("code", lang)
                    lang_elem.text = text
            
            # Přidání LSS dat
            lss_data_elem = ET.SubElement(question_elem, "lss_data")
            self._dict_to_xml(mapping.lss_question_data, lss_data_elem)
            
            # Konverze na string
            return ET.tostring(question_elem, encoding='unicode')
            
        except Exception as e:
            logger.error(f"Failed to generate XML for {virtual_question_id}: {e}")
            return None
    
    def integrate_virtual_questions_to_lss(
        self,
        original_lss_path: str,
        output_lss_path: str,
        virtual_question_ids: List[str] = None
    ) -> bool:
        """
        Integruje virtuální otázky do LSS souboru
        
        Args:
            original_lss_path: Cesta k původnímu LSS souboru
            output_lss_path: Cesta k výstupnímu LSS souboru
            virtual_question_ids: Seznam virtuálních otázek k integraci
            
        Returns:
            True při úspěchu
        """
        try:
            # Načtení původního LSS
            lss_root = self._load_lss_file(original_lss_path)
            if lss_root is None:
                return False
            
            # Nalezení questions sekce
            questions_elem = lss_root.find(".//questions")
            if questions_elem is None:
                logger.error("Questions section not found in LSS file")
                return False
            
            # Přidání virtuálních otázek
            if virtual_question_ids is None:
                virtual_question_ids = list(self.virtual_mappings.keys())
            
            for vq_id in virtual_question_ids:
                if vq_id in self.virtual_mappings:
                    virtual_xml = self.generate_virtual_lss_xml(vq_id)
                    if virtual_xml:
                        virtual_elem = ET.fromstring(virtual_xml)
                        questions_elem.append(virtual_elem)
                        logger.info(f"Added virtual question {vq_id} to LSS")
            
            # Uložení upraveného LSS
            tree = ET.ElementTree(lss_root)
            tree.write(output_lss_path, encoding='utf-8', xml_declaration=True)
            
            logger.info(f"Integrated {len(virtual_question_ids)} virtual questions to {output_lss_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to integrate virtual questions to LSS: {e}")
            return False
    
    def extract_virtual_questions_from_lss(self, lss_file_path: str) -> List[str]:
        """
        Extrahuje virtuální otázky z LSS souboru
        
        Args:
            lss_file_path: Cesta k LSS souboru
            
        Returns:
            Seznam ID virtuálních otázek
        """
        try:
            lss_root = self._load_lss_file(lss_file_path)
            if lss_root is None:
                return []
            
            virtual_questions = []
            
            # Hledání virtuálních otázek
            for question in lss_root.findall(".//question[@virtual='Y']"):
                qid = question.get("qid")
                if qid:
                    virtual_questions.append(qid)
                    
                    # Rekonstrukce mapování z LSS
                    self._reconstruct_mapping_from_lss(question)
            
            logger.info(f"Found {len(virtual_questions)} virtual questions in LSS")
            return virtual_questions
            
        except Exception as e:
            logger.error(f"Failed to extract virtual questions from LSS: {e}")
            return []
    
    def validate_virtual_lss_mapping(self, virtual_question_id: str) -> Dict[str, Any]:
        """
        Validuje mapování virtuální otázky
        
        Args:
            virtual_question_id: ID virtuální otázky
            
        Returns:
            Výsledek validace
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        mapping = self.virtual_mappings.get(virtual_question_id)
        if not mapping:
            validation_result['valid'] = False
            validation_result['errors'].append(f"Mapping for {virtual_question_id} not found")
            return validation_result
        
        # Validace zdrojových otázek
        if not mapping.source_question_ids:
            validation_result['valid'] = False
            validation_result['errors'].append("No source questions defined")
        
        # Validace merge strategy
        valid_strategies = ['concatenate', 'aggregate_numeric', 'combine_categories', 'cross_tabulate']
        if mapping.merge_strategy not in valid_strategies:
            validation_result['warnings'].append(f"Unknown merge strategy: {mapping.merge_strategy}")
        
        # Validace LSS dat
        if not mapping.lss_question_data:
            validation_result['warnings'].append("No LSS data available")
        
        # Validace jazykových mapování
        if not mapping.language_mappings:
            validation_result['warnings'].append("No language mappings available")
        
        return validation_result
    
    def _load_lss_file(self, lss_file_path: str) -> Optional[ET.Element]:
        """Načte LSS soubor"""
        try:
            if lss_file_path in self.lss_cache:
                return self.lss_cache[lss_file_path]
            
            tree = ET.parse(lss_file_path)
            root = tree.getroot()
            
            self.lss_cache[lss_file_path] = root
            return root
            
        except Exception as e:
            logger.error(f"Failed to load LSS file {lss_file_path}: {e}")
            return None
    
    def _extract_question_from_lss(self, lss_root: ET.Element, question_id: str) -> Optional[Dict[str, Any]]:
        """Extrahuje data otázky z LSS"""
        try:
            # Hledání otázky podle ID
            question_elem = lss_root.find(f".//question[@qid='{question_id}']")
            if question_elem is None:
                return None
            
            question_data = {
                'qid': question_id,
                'type': question_elem.get('type', ''),
                'question_text': '',
                'subquestions': [],
                'answer_options': [],
                'attributes': {}
            }
            
            # Extrakce textu otázky
            question_text_elem = question_elem.find('question_text')
            if question_text_elem is not None:
                question_data['question_text'] = question_text_elem.text or ''
            
            # Extrakce podotázek
            for subq in question_elem.findall('.//subquestion'):
                subq_data = {
                    'code': subq.get('code', ''),
                    'text': subq.text or ''
                }
                question_data['subquestions'].append(subq_data)
            
            # Extrakce odpovědí
            for answer in question_elem.findall('.//answer'):
                answer_data = {
                    'code': answer.get('code', ''),
                    'text': answer.text or '',
                    'order': answer.get('order', '0')
                }
                question_data['answer_options'].append(answer_data)
            
            # Extrakce atributů
            for attr in question_elem.findall('.//attribute'):
                attr_name = attr.get('attribute', '')
                attr_value = attr.text or ''
                question_data['attributes'][attr_name] = attr_value
            
            return question_data
            
        except Exception as e:
            logger.error(f"Failed to extract question {question_id} from LSS: {e}")
            return None
    
    def _create_virtual_lss_structure(
        self,
        virtual_question_id: str,
        source_data: Dict[str, Dict[str, Any]],
        merge_strategy: str
    ) -> Dict[str, Any]:
        """Vytvoří LSS strukturu pro virtuální otázku"""
        
        virtual_structure = {
            'qid': virtual_question_id,
            'type': 'virtual',
            'virtual': True,
            'merge_strategy': merge_strategy,
            'source_questions': list(source_data.keys()),
            'question_text': f'Virtual question combining: {", ".join(source_data.keys())}',
            'subquestions': [],
            'answer_options': [],
            'attributes': {
                'virtual': 'Y',
                'merge_strategy': merge_strategy,
                'source_count': str(len(source_data))
            }
        }
        
        # Merge strategy specific processing
        if merge_strategy == 'concatenate':
            # Pro concatenate zachováváme text format
            virtual_structure['type'] = 'T'  # Text question
            
        elif merge_strategy == 'aggregate_numeric':
            # Pro agregaci číselných hodnot
            virtual_structure['type'] = 'N'  # Numeric question
            
        elif merge_strategy == 'combine_categories':
            # Kombinace kategorií ze zdrojových otázek
            all_answers = []
            for source_q_data in source_data.values():
                all_answers.extend(source_q_data.get('answer_options', []))
            
            # Deduplikace odpovědí
            unique_answers = {}
            for answer in all_answers:
                code = answer.get('code', '')
                if code not in unique_answers:
                    unique_answers[code] = answer
            
            virtual_structure['answer_options'] = list(unique_answers.values())
            virtual_structure['type'] = 'L'  # List question
        
        return virtual_structure
    
    def _extract_language_mappings(self, source_data: Dict[str, Dict[str, Any]]) -> Dict[str, str]:
        """Extrahuje jazykové mapování ze zdrojových dat"""
        language_mappings = {}
        
        # Pro jednoduchost použijeme první zdrojovou otázku
        if source_data:
            first_question = next(iter(source_data.values()))
            question_text = first_question.get('question_text', '')
            
            # Základní mapování (rozšířit podle potřeby)
            language_mappings['cs'] = question_text
            language_mappings['en'] = f"Combined question: {question_text}"
        
        return language_mappings
    
    def _dict_to_xml(self, data: Dict[str, Any], parent_elem: ET.Element):
        """Konvertuje dictionary na XML elementy"""
        for key, value in data.items():
            elem = ET.SubElement(parent_elem, key)
            
            if isinstance(value, dict):
                self._dict_to_xml(value, elem)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        item_elem = ET.SubElement(elem, 'item')
                        self._dict_to_xml(item, item_elem)
                    else:
                        item_elem = ET.SubElement(elem, 'item')
                        item_elem.text = str(item)
            else:
                elem.text = str(value)
    
    def _reconstruct_mapping_from_lss(self, question_elem: ET.Element):
        """Rekonstruuje mapování z LSS elementu"""
        try:
            qid = question_elem.get("qid")
            merge_strategy = question_elem.get("merge_strategy", "concatenate")
            
            # Extrakce zdrojových otázek
            source_questions = []
            sources_elem = question_elem.find("source_questions")
            if sources_elem is not None:
                for source in sources_elem.findall("source"):
                    if source.text:
                        source_questions.append(source.text)
            
            # Extrakce textu otázky
            question_text_elem = question_elem.find("question_text")
            question_text = question_text_elem.text if question_text_elem is not None else ""
            
            # Extrakce jazykových variant
            language_mappings = {}
            languages_elem = question_elem.find("languages")
            if languages_elem is not None:
                for lang in languages_elem.findall("language"):
                    lang_code = lang.get("code")
                    if lang_code and lang.text:
                        language_mappings[lang_code] = lang.text
            
            # Rekonstrukce LSS dat
            lss_data_elem = question_elem.find("lss_data")
            lss_data = {}
            if lss_data_elem is not None:
                lss_data = self._xml_to_dict(lss_data_elem)
            
            # Vytvoření mapování
            mapping = LSSQuestionMapping(
                virtual_question_id=qid,
                source_question_ids=source_questions,
                lss_question_data=lss_data,
                merge_strategy=merge_strategy,
                virtual_question_text=question_text,
                language_mappings=language_mappings
            )
            
            self.virtual_mappings[qid] = mapping
            
        except Exception as e:
            logger.error(f"Failed to reconstruct mapping from LSS: {e}")
    
    def _xml_to_dict(self, elem: ET.Element) -> Dict[str, Any]:
        """Konvertuje XML element na dictionary"""
        result = {}
        
        for child in elem:
            if len(child) == 0:
                # Leaf element
                result[child.tag] = child.text
            else:
                # Has children
                if child.tag not in result:
                    result[child.tag] = []
                result[child.tag].append(self._xml_to_dict(child))
        
        return result


def create_lss_virtual_mapper() -> LSSVirtualMapper:
    """
    Factory function to create LSS Virtual Mapper
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    return LSSVirtualMapper()
