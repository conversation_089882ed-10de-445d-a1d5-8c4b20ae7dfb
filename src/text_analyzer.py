import pandas as pd
from collections import Counter
from typing import List, Dict, Tuple
from .logger import get_logger

logger = get_logger(__name__)

class TextAnalyzer:
    def __init__(self):
        """Inicializace textového analyzátoru"""
        logger.info("Textový analyzátor inicializován")
        
    def extract_keywords(self, texts: List[str], min_freq: int = 2) -> List[Tuple[str, int]]:
        """
        Základní extrakce klíčových slov (zatím bez NLP)
        
        Args:
            texts: Seznam textových odpovědí
            min_freq: Minimální frekvence pro zahrnutí slova
            
        Returns:
            Seznam tuple (slovo, frekvence) seřazený podle frekvence
        """
        logger.info(f"Analyzuji {len(texts)} textových odpovědí")
        
        # Základní tokenizace
        words = []
        for text in texts:
            words.extend(text.lower().split())
        
        # Počítání frekvence
        counts = Counter(words)
        
        # Filtrování podle minimální frekvence
        filtered = [(word, count) for word, count in counts.items() 
                   if count >= min_freq]
        
        # Seřazení podle frekvence
        sorted_keywords = sorted(filtered, key=lambda x: x[1], reverse=True)
        
        logger.info(f"Nalezeno {len(sorted_keywords)} klíčových slov")
        return sorted_keywords

    def create_word_cloud_data(self, texts: List[str]) -> pd.DataFrame:
        """
        Vytvoří data pro word cloud vizualizaci
        
        Args:
            texts: Seznam textových odpovědí
            
        Returns:
            DataFrame s frekvencemi slov pro vizualizaci
        """
        keywords = self.extract_keywords(texts)
        df = pd.DataFrame(keywords, columns=["slovo", "frekvence"])
        return df
