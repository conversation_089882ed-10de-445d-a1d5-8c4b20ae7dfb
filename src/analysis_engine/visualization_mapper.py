"""
Visualization Mapper

Mapuje analýzy na vhodné vizualizace a integruje s Datawrapper.
"""

import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from .metadata_loader import MetadataLoader, VisualizationType
from .analysis_recommender import AnalysisRecommendation

logger = logging.getLogger(__name__)


@dataclass
class VisualizationRecommendation:
    """Doporučení vizualizace"""
    visualization_type: VisualizationType
    analysis_id: str
    datawrapper_type: Optional[str]
    priority_score: float
    data_requirements: List[str]
    configuration_hints: Dict[str, str]
    fallback_options: List[str]


@dataclass
class ChartGenerationPlan:
    """Plán generování grafů"""
    primary_visualizations: List[VisualizationRecommendation]
    alternative_visualizations: List[VisualizationRecommendation]
    unsupported_analyses: List[str]
    total_charts_estimate: int
    datawrapper_compatibility: float  # 0-1


class VisualizationMapper:
    """Mapuje analýzy na vizualizace"""
    
    def __init__(self, metadata_loader: MetadataLoader):
        """
        Inicializace mapperu
        
        Args:
            metadata_loader: Načtený metadata loader
        """
        self.metadata = metadata_loader
        
        # Rozšířené mapování na Datawrapper typy
        self.datawrapper_mapping = {
            # Základní grafy (priorita 1)
            'BAR': 'd3-bars',
            'HBA': 'd3-bars',
            'PIE': 'd3-pies', 
            'TAB': 'tables',
            'HIS': 'd3-bars',
            'BOX': 'd3-scatter-plot',
            'SCP': 'd3-scatter-plot',
            'TFT': 'tables',
            'WCS': None,  # Word cloud - externí generátor
            
            # Pokročilé grafy (priorita 2-3)
            'HMP': 'd3-maps',  # Heat mapa
            'GBA': 'd3-bars',  # Grouped bar
            'DIV': 'd3-bars',  # Diverging bars
            'DUM': 'd3-scatter-plot',  # Dumbbell
            'PAR': 'd3-bars',  # Pareto
            'TSL': 'd3-lines',  # Časová osa
            'TML': 'd3-lines',  # Timeline
            'CTT': 'tables',   # Kontingenční tabulka
            
            # Specializované (priorita 4-5)
            'RAD': None,  # Radar - externí
            'SAN': None,  # Sankey - externí
            'NET': None,  # Network - externí
            'TRM': None,  # Treemap - externí
        }
        
        # Konfigurace pro Datawrapper typy
        self.datawrapper_configs = {
            'd3-bars': {
                'sort_bars': True,
                'show_color_key': True,
                'padding': '25px'
            },
            'd3-pies': {
                'show_color_key': True,
                'sort_values': True
            },
            'tables': {
                'show_totals': True,
                'sort_columns': True
            },
            'd3-scatter-plot': {
                'show_regression': False,
                'point_size': 'medium'
            }
        }
        
        logger.info("VisualizationMapper inicializován")
    
    def map_analyses_to_visualizations(self, analysis_recommendations: List[AnalysisRecommendation]) -> ChartGenerationPlan:
        """
        Mapuje analýzy na vizualizace
        
        Args:
            analysis_recommendations: Seznam doporučených analýz
            
        Returns:
            Plán generování grafů
        """
        primary_visualizations = []
        alternative_visualizations = []
        unsupported_analyses = []
        
        for analysis_rec in analysis_recommendations:
            # Získání vizualizací pro analýzu
            visualizations = self.metadata.get_visualizations_for_analysis(analysis_rec.analysis_type.id)
            
            if not visualizations:
                unsupported_analyses.append(analysis_rec.analysis_type.id)
                continue
            
            # Seřazení podle priority a Datawrapper kompatibility
            sorted_viz = self._sort_visualizations_by_priority(visualizations)
            
            # Primární vizualizace (nejlepší volba)
            if sorted_viz:
                primary_viz = self._create_visualization_recommendation(
                    sorted_viz[0], analysis_rec.analysis_type.id, analysis_rec
                )
                primary_visualizations.append(primary_viz)
                
                # Alternativní vizualizace
                for viz in sorted_viz[1:3]:  # Max 2 alternativy
                    alt_viz = self._create_visualization_recommendation(
                        viz, analysis_rec.analysis_type.id, analysis_rec, is_alternative=True
                    )
                    alternative_visualizations.append(alt_viz)
        
        # Odhad počtu grafů
        total_charts = len(primary_visualizations)
        for analysis_rec in analysis_recommendations:
            total_charts += len(analysis_rec.applicable_questions)
        
        # Datawrapper kompatibilita
        compatible_count = len([v for v in primary_visualizations if v.datawrapper_type])
        compatibility = compatible_count / len(primary_visualizations) if primary_visualizations else 0
        
        plan = ChartGenerationPlan(
            primary_visualizations=primary_visualizations,
            alternative_visualizations=alternative_visualizations,
            unsupported_analyses=unsupported_analyses,
            total_charts_estimate=total_charts,
            datawrapper_compatibility=compatibility
        )
        
        logger.info(f"Vygenerován plán s {len(primary_visualizations)} primárními vizualizacemi")
        return plan
    
    def _sort_visualizations_by_priority(self, visualizations: List[VisualizationType]) -> List[VisualizationType]:
        """Seřadí vizualizace podle priority a kompatibility"""
        def priority_score(viz):
            # Základní skóre podle priority (nižší priorita = vyšší skóre)
            base_score = (6 - viz.priority) / 5.0
            
            # Bonus za Datawrapper kompatibilitu
            datawrapper_bonus = 0.3 if self.datawrapper_mapping.get(viz.id) else 0
            
            return base_score + datawrapper_bonus
        
        return sorted(visualizations, key=priority_score, reverse=True)
    
    def _create_visualization_recommendation(self, 
                                           visualization: VisualizationType,
                                           analysis_id: str,
                                           analysis_rec: AnalysisRecommendation,
                                           is_alternative: bool = False) -> VisualizationRecommendation:
        """Vytvoří doporučení vizualizace"""
        
        datawrapper_type = self.datawrapper_mapping.get(visualization.id)
        
        # Výpočet priority
        priority_score = self._calculate_visualization_priority(
            visualization, analysis_rec, is_alternative
        )
        
        # Datové požadavky
        data_requirements = self._get_data_requirements(visualization, analysis_id)
        
        # Konfigurační tipy
        config_hints = self._get_configuration_hints(visualization, datawrapper_type)
        
        # Fallback možnosti
        fallback_options = self._get_fallback_options(visualization)
        
        return VisualizationRecommendation(
            visualization_type=visualization,
            analysis_id=analysis_id,
            datawrapper_type=datawrapper_type,
            priority_score=priority_score,
            data_requirements=data_requirements,
            configuration_hints=config_hints,
            fallback_options=fallback_options
        )
    
    def _calculate_visualization_priority(self, 
                                        visualization: VisualizationType,
                                        analysis_rec: AnalysisRecommendation,
                                        is_alternative: bool) -> float:
        """Vypočítá prioritu vizualizace"""
        # Základní skóre podle priority vizualizace
        base_score = (6 - visualization.priority) / 5.0
        
        # Bonus za Datawrapper kompatibilitu
        datawrapper_bonus = 0.2 if self.datawrapper_mapping.get(visualization.id) else 0
        
        # Malus pro alternativní vizualizace
        alternative_malus = 0.3 if is_alternative else 0
        
        # Bonus podle priority analýzy
        analysis_bonus = analysis_rec.priority_score * 0.1
        
        final_score = base_score + datawrapper_bonus - alternative_malus + analysis_bonus
        return min(max(final_score, 0), 1.0)
    
    def _get_data_requirements(self, visualization: VisualizationType, analysis_id: str) -> List[str]:
        """Vrátí datové požadavky pro vizualizaci"""
        requirements = []
        
        # Podle typu vizualizace
        if visualization.id in ['BAR', 'HBA', 'PIE']:
            requirements.extend(['Kategoriální data', 'Číselné hodnoty'])
        elif visualization.id in ['HIS', 'BOX']:
            requirements.extend(['Kontinuální data', 'Číselné hodnoty'])
        elif visualization.id in ['SCP', 'BUB']:
            requirements.extend(['Dvě číselné proměnné', 'Dostatečný vzorek'])
        elif visualization.id in ['WCS', 'THA']:
            requirements.extend(['Textová data', 'Tokenizace'])
        elif visualization.id in ['HMP', 'CTT']:
            requirements.extend(['Kategoriální proměnné', 'Kontingenční tabulka'])
        
        # Podle analýzy
        if analysis_id in ['CRA', 'FAA']:
            requirements.append('Korelační matice')
        elif analysis_id in ['THA', 'WCA']:
            requirements.append('Preprocessovaný text')
        
        return requirements
    
    def _get_configuration_hints(self, visualization: VisualizationType, datawrapper_type: Optional[str]) -> Dict[str, str]:
        """Vrátí konfigurační tipy"""
        hints = {}
        
        # Obecné tipy podle vizualizace
        if visualization.id == 'BAR':
            hints.update({
                'sorting': 'Seřadit podle hodnot (sestupně)',
                'colors': 'Použít konzistentní barevnou paletu',
                'labels': 'Zobrazit hodnoty na sloupcích'
            })
        elif visualization.id == 'PIE':
            hints.update({
                'threshold': 'Seskupit malé kategorie do "Ostatní"',
                'labels': 'Zobrazit procenta',
                'colors': 'Odlišit hlavní kategorie'
            })
        elif visualization.id == 'WCS':
            hints.update({
                'size': 'Velikost podle četnosti',
                'colors': 'Barevné kódování podle sentiment',
                'layout': 'Kompaktní rozložení'
            })
        
        # Datawrapper specifické tipy
        if datawrapper_type and datawrapper_type in self.datawrapper_configs:
            config = self.datawrapper_configs[datawrapper_type]
            hints.update({f'datawrapper_{k}': str(v) for k, v in config.items()})
        
        return hints
    
    def _get_fallback_options(self, visualization: VisualizationType) -> List[str]:
        """Vrátí fallback možnosti"""
        fallbacks = []
        
        # Univerzální fallback
        fallbacks.append('TXT')  # Textový výstup
        fallbacks.append('TAB')  # Tabulka
        
        # Specifické fallbacks
        if visualization.id in ['WCS', 'NET', 'SAN']:
            fallbacks.append('BAR')  # Sloupcový graf jako fallback
        elif visualization.id in ['RAD', 'BUB']:
            fallbacks.append('SCP')  # Scatter plot
        elif visualization.id in ['HMP', 'CTT']:
            fallbacks.append('GBA')  # Grouped bar
        
        return fallbacks
    
    def get_datawrapper_chart_config(self, visualization_rec: VisualizationRecommendation) -> Optional[Dict]:
        """Vrátí konfiguraci pro Datawrapper graf"""
        if not visualization_rec.datawrapper_type:
            return None
        
        config = {
            'type': visualization_rec.datawrapper_type,
            'metadata': {}
        }
        
        # Základní konfigurace podle typu
        if visualization_rec.datawrapper_type in self.datawrapper_configs:
            config['metadata'].update(self.datawrapper_configs[visualization_rec.datawrapper_type])
        
        # Specifické konfigurace z hints
        for key, value in visualization_rec.configuration_hints.items():
            if key.startswith('datawrapper_'):
                config_key = key.replace('datawrapper_', '')
                config['metadata'][config_key] = value
        
        return config
    
    def get_external_generator_info(self, visualization_rec: VisualizationRecommendation) -> Optional[Dict]:
        """Vrátí informace pro externí generátory"""
        if visualization_rec.datawrapper_type:
            return None  # Datawrapper zvládne
        
        external_info = {
            'visualization_id': visualization_rec.visualization_type.id,
            'generator_type': 'external',
            'requirements': visualization_rec.data_requirements,
            'fallback_options': visualization_rec.fallback_options
        }
        
        # Specifické generátory
        if visualization_rec.visualization_type.id == 'WCS':
            external_info.update({
                'generator': 'wordcloud',
                'library': 'wordcloud',
                'output_format': 'png'
            })
        elif visualization_rec.visualization_type.id in ['NET', 'SAN']:
            external_info.update({
                'generator': 'networkx',
                'library': 'networkx + matplotlib',
                'output_format': 'png'
            })
        elif visualization_rec.visualization_type.id == 'RAD':
            external_info.update({
                'generator': 'plotly',
                'library': 'plotly',
                'output_format': 'html'
            })
        
        return external_info
