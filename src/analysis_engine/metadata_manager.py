"""
Metadata Manager

Wrapper pro Metadata Generator s integrací do Analysis Engine.
Umožňuje správu, validaci a generování nových typů analýz a vizualizací.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import asdict

from .metadata_loader import MetadataLoader, AnalysisType, VisualizationType

logger = logging.getLogger(__name__)


class MetadataManager:
    """Správce metadata pro Analysis Engine"""
    
    def __init__(self, metadata_loader: MetadataLoader):
        """
        Inicializace metadata manageru
        
        Args:
            metadata_loader: Načtený metadata loader
        """
        self.metadata_loader = metadata_loader
        self.metadata_dir = metadata_loader.metadata_dir
        self.generated_dir = self.metadata_dir / "generated"
        
        # Vytvoření výstupního adresáře
        os.makedirs(self.generated_dir, exist_ok=True)
        
        logger.info(f"MetadataManager inicializován s adresářem: {self.metadata_dir}")
    
    def validate_metadata_consistency(self) -> Dict[str, List[str]]:
        """
        Validuje konzistenci metadata
        
        Returns:
            Slovník s chybami podle kategorií
        """
        errors = {
            'missing_analyses': [],
            'missing_visualizations': [],
            'broken_references': [],
            'duplicate_ids': []
        }
        
        try:
            # Kontrola chybějících analýz v mapování
            for question_id, analysis_ids in self.metadata_loader.question_to_analyses.items():
                for analysis_id in analysis_ids:
                    if analysis_id not in self.metadata_loader.analysis_types:
                        errors['missing_analyses'].append(f"Analýza '{analysis_id}' v mapování otázky '{question_id}' neexistuje")
            
            # Kontrola chybějících vizualizací
            for analysis in self.metadata_loader.analysis_types.values():
                for viz_id in analysis.supported_visualizations:
                    if viz_id not in self.metadata_loader.visualization_types:
                        errors['missing_visualizations'].append(f"Vizualizace '{viz_id}' v analýze '{analysis.id}' neexistuje")
            
            # Kontrola duplicitních ID
            all_ids = []
            for collection in [self.metadata_loader.question_types, self.metadata_loader.analysis_types, self.metadata_loader.visualization_types]:
                for item_id in collection.keys():
                    if item_id in all_ids:
                        errors['duplicate_ids'].append(f"Duplicitní ID: '{item_id}'")
                    all_ids.append(item_id)
            
            logger.info(f"Validace dokončena. Nalezeno {sum(len(v) for v in errors.values())} problémů")
            
        except Exception as e:
            logger.error(f"Chyba při validaci metadata: {str(e)}")
            errors['broken_references'].append(f"Chyba validace: {str(e)}")
        
        return errors
    
    def generate_analysis_registry(self) -> bool:
        """
        Generuje registr všech analýz do YAML souboru
        
        Returns:
            True při úspěchu
        """
        try:
            registry = {
                'metadata': {
                    'version': '1.0.0',
                    'generated_by': 'LimWrapp Analysis Engine',
                    'total_analyses': len(self.metadata_loader.analysis_types)
                },
                'analyses': {}
            }
            
            for analysis_id, analysis in self.metadata_loader.analysis_types.items():
                detailed_desc = self.metadata_loader.get_detailed_analysis_description(analysis_id)
                
                registry['analyses'][analysis_id] = {
                    'id': analysis.id,
                    'code': analysis.code,
                    'name': analysis.name,
                    'description': analysis.description,
                    'detailed_description': detailed_desc or analysis.detailed_description,
                    'analysis_type': analysis.analysis_type,
                    'priority': analysis.priority,
                    'supported_visualizations': analysis.supported_visualizations,
                    'supported_question_types': analysis.supported_question_types,
                    'implementation_status': self._get_implementation_status(analysis_id),
                    'complexity_score': self._calculate_complexity_score(analysis),
                    'estimated_time_hours': self._estimate_implementation_time(analysis)
                }
            
            # Export do YAML
            output_file = self.generated_dir / "analysis_registry.yaml"
            
            import yaml
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(registry, f, default_flow_style=False, allow_unicode=True, sort_keys=False)
            
            logger.info(f"✅ Registr analýz vygenerován: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při generování registru analýz: {str(e)}")
            return False
    
    def generate_visualization_registry(self) -> bool:
        """
        Generuje registr všech vizualizací do YAML souboru
        
        Returns:
            True při úspěchu
        """
        try:
            registry = {
                'metadata': {
                    'version': '1.0.0',
                    'generated_by': 'LimWrapp Analysis Engine',
                    'total_visualizations': len(self.metadata_loader.visualization_types)
                },
                'visualizations': {}
            }
            
            for viz_id, viz in self.metadata_loader.visualization_types.items():
                detailed_desc = self.metadata_loader.get_detailed_visualization_description(viz_id)
                
                registry['visualizations'][viz_id] = {
                    'id': viz.id,
                    'code': viz.code,
                    'name': viz.name,
                    'description': viz.description,
                    'detailed_description': detailed_desc or getattr(viz, 'detailed_description', None),
                    'priority': viz.priority,
                    'datawrapper_type': viz.datawrapper_type,
                    'datawrapper_compatible': viz.datawrapper_type is not None,
                    'implementation_status': self._get_viz_implementation_status(viz_id),
                    'complexity_score': self._calculate_viz_complexity_score(viz),
                    'estimated_time_hours': self._estimate_viz_implementation_time(viz)
                }
            
            # Export do YAML
            output_file = self.generated_dir / "visualization_registry.yaml"
            
            import yaml
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(registry, f, default_flow_style=False, allow_unicode=True, sort_keys=False)
            
            logger.info(f"✅ Registr vizualizací vygenerován: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při generování registru vizualizací: {str(e)}")
            return False
    
    def add_new_analysis_type(self, analysis_data: Dict[str, Any]) -> bool:
        """
        Přidá nový typ analýzy
        
        Args:
            analysis_data: Data nové analýzy
            
        Returns:
            True při úspěchu
        """
        try:
            # Validace povinných polí
            required_fields = ['id', 'name', 'description', 'analysis_type']
            for field in required_fields:
                if field not in analysis_data:
                    logger.error(f"❌ Chybí povinné pole: {field}")
                    return False
            
            analysis_id = analysis_data['id']
            
            # Kontrola duplicity
            if analysis_id in self.metadata_loader.analysis_types:
                logger.error(f"❌ Analýza s ID '{analysis_id}' již existuje")
                return False
            
            # Vytvoření nové analýzy
            new_analysis = AnalysisType(
                id=analysis_id,
                code=analysis_data.get('code', analysis_id),
                name=analysis_data['name'],
                description=analysis_data['description'],
                detailed_description=analysis_data.get('detailed_description', ''),
                analysis_type=analysis_data['analysis_type'],
                priority=analysis_data.get('priority', 3),
                supported_visualizations=analysis_data.get('supported_visualizations', []),
                supported_question_types=analysis_data.get('supported_question_types', [])
            )
            
            # Přidání do metadata
            self.metadata_loader.analysis_types[analysis_id] = new_analysis
            
            # Uložení do souboru
            self._save_analysis_to_file(new_analysis)
            
            logger.info(f"✅ Nová analýza '{analysis_id}' přidána")
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při přidávání analýzy: {str(e)}")
            return False
    
    def add_new_visualization_type(self, viz_data: Dict[str, Any]) -> bool:
        """
        Přidá nový typ vizualizace
        
        Args:
            viz_data: Data nové vizualizace
            
        Returns:
            True při úspěchu
        """
        try:
            # Validace povinných polí
            required_fields = ['id', 'name', 'description']
            for field in required_fields:
                if field not in viz_data:
                    logger.error(f"❌ Chybí povinné pole: {field}")
                    return False
            
            viz_id = viz_data['id']
            
            # Kontrola duplicity
            if viz_id in self.metadata_loader.visualization_types:
                logger.error(f"❌ Vizualizace s ID '{viz_id}' již existuje")
                return False
            
            # Vytvoření nové vizualizace
            new_viz = VisualizationType(
                id=viz_id,
                code=viz_data.get('code', viz_id),
                name=viz_data['name'],
                description=viz_data['description'],
                priority=viz_data.get('priority', 3),
                datawrapper_type=viz_data.get('datawrapper_type'),
                detailed_description=viz_data.get('detailed_description', '')
            )
            
            # Přidání do metadata
            self.metadata_loader.visualization_types[viz_id] = new_viz
            
            # Uložení do souboru
            self._save_visualization_to_file(new_viz)
            
            logger.info(f"✅ Nová vizualizace '{viz_id}' přidána")
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při přidávání vizualizace: {str(e)}")
            return False
    
    def export_metadata_summary(self) -> Dict[str, Any]:
        """
        Exportuje souhrn všech metadata
        
        Returns:
            Slovník se souhrnem
        """
        try:
            # Statistiky
            stats = {
                'total_question_types': len(self.metadata_loader.question_types),
                'total_analysis_types': len(self.metadata_loader.analysis_types),
                'total_visualization_types': len(self.metadata_loader.visualization_types),
                'total_mappings': len(self.metadata_loader.question_to_analyses),
                'detailed_descriptions': {
                    'analyses': len(self.metadata_loader.analysis_descriptions),
                    'visualizations': len(self.metadata_loader.visualization_descriptions)
                }
            }
            
            # Analýzy podle typu
            analysis_by_type = {}
            for analysis in self.metadata_loader.analysis_types.values():
                if analysis.analysis_type not in analysis_by_type:
                    analysis_by_type[analysis.analysis_type] = 0
                analysis_by_type[analysis.analysis_type] += 1
            
            # Vizualizace podle priority
            viz_by_priority = {}
            for viz in self.metadata_loader.visualization_types.values():
                if viz.priority not in viz_by_priority:
                    viz_by_priority[viz.priority] = 0
                viz_by_priority[viz.priority] += 1
            
            # Datawrapper kompatibilita
            datawrapper_compatible = len([v for v in self.metadata_loader.visualization_types.values() if v.datawrapper_type])
            datawrapper_compatibility = datawrapper_compatible / len(self.metadata_loader.visualization_types) if self.metadata_loader.visualization_types else 0
            
            summary = {
                'metadata_statistics': stats,
                'analysis_breakdown': {
                    'by_type': analysis_by_type,
                    'high_priority': len([a for a in self.metadata_loader.analysis_types.values() if a.priority <= 2]),
                    'medium_priority': len([a for a in self.metadata_loader.analysis_types.values() if a.priority == 3]),
                    'low_priority': len([a for a in self.metadata_loader.analysis_types.values() if a.priority >= 4])
                },
                'visualization_breakdown': {
                    'by_priority': viz_by_priority,
                    'datawrapper_compatible': datawrapper_compatible,
                    'datawrapper_compatibility_rate': datawrapper_compatibility,
                    'external_generators_needed': len(self.metadata_loader.visualization_types) - datawrapper_compatible
                },
                'validation_results': self.validate_metadata_consistency()
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ Chyba při exportu souhrnu: {str(e)}")
            return {}
    
    def _get_implementation_status(self, analysis_id: str) -> str:
        """Vrátí status implementace analýzy"""
        # Základní implementace podle priority
        analysis = self.metadata_loader.analysis_types.get(analysis_id)
        if not analysis:
            return 'unknown'
        
        if analysis.priority <= 2:
            return 'implemented'
        elif analysis.priority == 3:
            return 'planned'
        else:
            return 'future'
    
    def _get_viz_implementation_status(self, viz_id: str) -> str:
        """Vrátí status implementace vizualizace"""
        viz = self.metadata_loader.visualization_types.get(viz_id)
        if not viz:
            return 'unknown'
        
        if viz.datawrapper_type:
            return 'implemented'
        elif viz.priority <= 3:
            return 'planned'
        else:
            return 'future'
    
    def _calculate_complexity_score(self, analysis: AnalysisType) -> int:
        """Vypočítá skóre složitosti analýzy (1-10)"""
        base_score = analysis.priority
        
        # Úprava podle typu
        if analysis.analysis_type == 'section':
            base_score += 1
        
        # Úprava podle počtu podporovaných vizualizací
        if len(analysis.supported_visualizations) > 5:
            base_score += 1
        
        return min(max(base_score, 1), 10)
    
    def _calculate_viz_complexity_score(self, viz: VisualizationType) -> int:
        """Vypočítá skóre složitosti vizualizace (1-10)"""
        base_score = viz.priority
        
        # Datawrapper kompatibilní jsou jednodušší
        if viz.datawrapper_type:
            base_score -= 1
        else:
            base_score += 2  # Externí generátory jsou složitější
        
        return min(max(base_score, 1), 10)
    
    def _estimate_implementation_time(self, analysis: AnalysisType) -> float:
        """Odhadne čas implementace analýzy v hodinách"""
        base_time = analysis.priority * 2  # 2-10 hodin podle priority
        
        if analysis.analysis_type == 'section':
            base_time *= 1.5
        
        return base_time
    
    def _estimate_viz_implementation_time(self, viz: VisualizationType) -> float:
        """Odhadne čas implementace vizualizace v hodinách"""
        if viz.datawrapper_type:
            return viz.priority * 0.5  # 0.5-2.5 hodin pro Datawrapper
        else:
            return viz.priority * 2  # 2-10 hodin pro externí generátory
    
    def _save_analysis_to_file(self, analysis: AnalysisType):
        """Uloží analýzu do souboru"""
        # Pro jednoduchost uložíme do JSON
        output_file = self.generated_dir / f"analysis_{analysis.id}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(analysis), f, ensure_ascii=False, indent=2)
    
    def _save_visualization_to_file(self, viz: VisualizationType):
        """Uloží vizualizaci do souboru"""
        # Pro jednoduchost uložíme do JSON
        output_file = self.generated_dir / f"visualization_{viz.id}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(viz), f, ensure_ascii=False, indent=2)
