"""
Question Analyzer

Analyzuje LimeSurvey otázky a mapuje je na typy z metadata systému.
"""

import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from .metadata_loader import MetadataLoader, QuestionType

logger = logging.getLogger(__name__)


@dataclass
class LimeSurveyQuestion:
    """Reprezentace LimeSurvey otázky"""
    qid: str
    question_code: str
    question_text: str
    question_type: str  # LimeSurvey typ (L, M, 5, atd.)
    subquestions: List[Dict] = None
    answer_options: List[Dict] = None
    attributes: Dict = None


@dataclass
class AnalyzedQuestion:
    """Analyzovaná otázka s doporučeními"""
    original_question: LimeSurveyQuestion
    matched_type: Optional[QuestionType]
    confidence: float
    recommended_analyses: List[str]
    recommended_visualizations: List[str]
    analysis_notes: str


class QuestionAnalyzer:
    """Analyzuje LimeSurvey otázky a doporučuje analýzy"""
    
    def __init__(self, metadata_loader: MetadataLoader):
        """
        Inicializace analyzéru
        
        Args:
            metadata_loader: Načtený metadata loader
        """
        self.metadata = metadata_loader
        
        # Mapování LimeSurvey typů na naše typy
        self.limesurvey_mapping = {
            'L': 'L',  # List (Radio)
            'M': 'M',  # Multiple choice
            '5': '5',  # 5 Point Choice
            'A': 'A',  # Array (5 Point)
            'B': 'B',  # Array (10 Point)
            'C': 'C',  # Array (Y/N/U)
            'D': 'D',  # Date
            'E': 'E',  # Array (I/S/D)
            'F': 'F',  # Array (Flex. Labels)
            'G': 'G',  # Gender
            'H': 'H',  # Array (Flex. by Col)
            'I': 'I',  # Language Switch
            'K': 'K',  # Multiple Numerical
            'N': 'N',  # Numerical Input
            'O': 'O',  # List With Comment
            'P': 'P',  # Multiple choice + comments
            'Q': 'Q',  # Multiple Short Text
            'R': 'R',  # Ranking
            'S': 'S',  # Short Free Text
            'T': 'T',  # Long Free Text
            'U': 'U',  # Huge Free Text
            'X': 'X',  # Boilerplate Question
            'Y': 'Y',  # Yes/No
            '!': '!',  # List (Dropdown)
            ':': ':',  # Array (Flex. DD)
            ';': ';',  # Array (Flex. Text)
            '|': '|',  # File Upload
            '1': '1',  # Array Dual Scale
        }
        
        logger.info("QuestionAnalyzer inicializován")
    
    def analyze_question(self, question: LimeSurveyQuestion) -> AnalyzedQuestion:
        """
        Analyzuje jednu otázku
        
        Args:
            question: LimeSurvey otázka
            
        Returns:
            Analyzovaná otázka s doporučeními
        """
        # Mapování typu otázky
        matched_type, confidence = self._match_question_type(question)
        
        # Doporučení analýz
        recommended_analyses = []
        recommended_visualizations = []
        
        if matched_type:
            # Získání doporučených analýz
            analyses = self.metadata.get_analyses_for_question(matched_type.id)
            recommended_analyses = [a.id for a in analyses]
            
            # Získání doporučených vizualizací (z první analýzy s nejvyšší prioritou)
            if analyses:
                best_analysis = min(analyses, key=lambda a: a.priority)
                visualizations = self.metadata.get_visualizations_for_analysis(best_analysis.id)
                recommended_visualizations = [v.id for v in visualizations]
        
        # Poznámky k analýze
        analysis_notes = self._generate_analysis_notes(question, matched_type, confidence)
        
        return AnalyzedQuestion(
            original_question=question,
            matched_type=matched_type,
            confidence=confidence,
            recommended_analyses=recommended_analyses,
            recommended_visualizations=recommended_visualizations,
            analysis_notes=analysis_notes
        )
    
    def analyze_survey_questions(self, questions: List[LimeSurveyQuestion]) -> List[AnalyzedQuestion]:
        """
        Analyzuje všechny otázky průzkumu
        
        Args:
            questions: Seznam LimeSurvey otázek
            
        Returns:
            Seznam analyzovaných otázek
        """
        analyzed_questions = []
        
        for question in questions:
            try:
                analyzed = self.analyze_question(question)
                analyzed_questions.append(analyzed)
                
                logger.debug(f"Analyzována otázka {question.qid}: {analyzed.matched_type.name if analyzed.matched_type else 'Nerozpoznáno'}")
                
            except Exception as e:
                logger.error(f"Chyba při analýze otázky {question.qid}: {str(e)}")
                
                # Vytvoření fallback analyzované otázky
                fallback = AnalyzedQuestion(
                    original_question=question,
                    matched_type=None,
                    confidence=0.0,
                    recommended_analyses=[],
                    recommended_visualizations=[],
                    analysis_notes=f"Chyba při analýze: {str(e)}"
                )
                analyzed_questions.append(fallback)
        
        logger.info(f"Analyzováno {len(analyzed_questions)} otázek")
        return analyzed_questions
    
    def _match_question_type(self, question: LimeSurveyQuestion) -> Tuple[Optional[QuestionType], float]:
        """
        Mapuje LimeSurvey otázku na typ z metadata
        
        Args:
            question: LimeSurvey otázka
            
        Returns:
            Tuple (matched_type, confidence)
        """
        # Přímé mapování podle typu
        mapped_id = self.limesurvey_mapping.get(question.question_type)
        
        if mapped_id and mapped_id in self.metadata.question_types:
            return self.metadata.question_types[mapped_id], 1.0
        
        # Pokud přímé mapování neexistuje, zkusíme heuristiky
        return self._heuristic_matching(question)
    
    def _heuristic_matching(self, question: LimeSurveyQuestion) -> Tuple[Optional[QuestionType], float]:
        """
        Heuristické mapování pro neznámé typy
        
        Args:
            question: LimeSurvey otázka
            
        Returns:
            Tuple (matched_type, confidence)
        """
        # Základní heuristiky podle obsahu otázky
        text = question.question_text.lower() if question.question_text else ""
        
        # Textové otázky
        if any(keyword in text for keyword in ['komentář', 'poznámka', 'vysvětlete', 'popište']):
            if 'T' in self.metadata.question_types:
                return self.metadata.question_types['T'], 0.7
        
        # Číselné otázky
        if any(keyword in text for keyword in ['počet', 'kolik', 'číslo', 'hodnota']):
            if 'N' in self.metadata.question_types:
                return self.metadata.question_types['N'], 0.6
        
        # Ano/Ne otázky
        if any(keyword in text for keyword in ['ano', 'ne', 'souhlasíte']):
            if 'Y' in self.metadata.question_types:
                return self.metadata.question_types['Y'], 0.8
        
        # Fallback na nejčastější typ
        if 'L' in self.metadata.question_types:
            return self.metadata.question_types['L'], 0.3
        
        return None, 0.0
    
    def _generate_analysis_notes(self, question: LimeSurveyQuestion, 
                                matched_type: Optional[QuestionType], 
                                confidence: float) -> str:
        """
        Generuje poznámky k analýze
        
        Args:
            question: Původní otázka
            matched_type: Mapovaný typ
            confidence: Spolehlivost mapování
            
        Returns:
            Textové poznámky
        """
        notes = []
        
        # Informace o mapování
        if matched_type:
            notes.append(f"Mapováno na typ: {matched_type.name}")
            notes.append(f"Spolehlivost: {confidence:.1%}")
            
            if confidence < 0.8:
                notes.append("⚠️ Nízká spolehlivost - doporučujeme manuální kontrolu")
        else:
            notes.append("❌ Typ otázky nebyl rozpoznán")
            notes.append("💡 Doporučujeme manuální klasifikaci")
        
        # Informace o otázce
        if question.subquestions:
            notes.append(f"📊 Obsahuje {len(question.subquestions)} podotázek")
        
        if question.answer_options:
            notes.append(f"🔘 Obsahuje {len(question.answer_options)} možností odpovědí")
        
        return " | ".join(notes)
    
    def get_analysis_summary(self, analyzed_questions: List[AnalyzedQuestion]) -> Dict:
        """
        Vytvoří souhrn analýzy
        
        Args:
            analyzed_questions: Seznam analyzovaných otázek
            
        Returns:
            Slovník se souhrnem
        """
        total_questions = len(analyzed_questions)
        recognized_questions = len([q for q in analyzed_questions if q.matched_type])
        
        # Statistiky podle typů
        type_stats = {}
        for question in analyzed_questions:
            if question.matched_type:
                type_name = question.matched_type.name
                if type_name not in type_stats:
                    type_stats[type_name] = 0
                type_stats[type_name] += 1
        
        # Doporučené analýzy
        all_analyses = set()
        for question in analyzed_questions:
            all_analyses.update(question.recommended_analyses)
        
        return {
            'total_questions': total_questions,
            'recognized_questions': recognized_questions,
            'recognition_rate': recognized_questions / total_questions if total_questions > 0 else 0,
            'question_types': type_stats,
            'recommended_analyses': list(all_analyses),
            'confidence_distribution': {
                'high': len([q for q in analyzed_questions if q.confidence >= 0.8]),
                'medium': len([q for q in analyzed_questions if 0.5 <= q.confidence < 0.8]),
                'low': len([q for q in analyzed_questions if 0 < q.confidence < 0.5]),
                'none': len([q for q in analyzed_questions if q.confidence == 0])
            }
        }
