"""
Analysis Recommender

Doporučuje vhodné analýzy na základě analyzovaných otázek a kontextu průzkumu.
"""

import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

from .metadata_loader import MetadataLoader, AnalysisType
from .question_analyzer import AnalyzedQuestion

logger = logging.getLogger(__name__)


@dataclass
class AnalysisRecommendation:
    """Doporučení analýzy"""
    analysis_type: AnalysisType
    applicable_questions: List[str]  # QID otázek
    priority_score: float
    reasoning: str
    estimated_complexity: int  # 1-5
    prerequisites: List[str]


@dataclass
class SurveyAnalysisPlan:
    """Plán analýz pro celý průzkum"""
    question_level_analyses: List[AnalysisRecommendation]
    section_level_analyses: List[AnalysisRecommendation]
    cross_question_analyses: List[AnalysisRecommendation]
    total_estimated_time: int  # minuty
    complexity_score: float  # 1-10


class AnalysisRecommender:
    """Doporučuje analýzy pro průzkum"""
    
    def __init__(self, metadata_loader: MetadataLoader):
        """
        Inicializace doporučovače
        
        Args:
            metadata_loader: Načtený metadata loader
        """
        self.metadata = metadata_loader
        
        # Váhy pro výpočet priority
        self.priority_weights = {
            'question_count': 0.3,      # Počet aplikovatelných otázek
            'analysis_priority': 0.4,   # Priorita analýzy v metadata
            'data_richness': 0.2,       # Bohatost dat (podotázky, možnosti)
            'user_preference': 0.1      # Uživatelské preference (zatím default)
        }
        
        logger.info("AnalysisRecommender inicializován")
    
    def recommend_analyses(self, analyzed_questions: List[AnalyzedQuestion], 
                          survey_context: Optional[Dict] = None) -> SurveyAnalysisPlan:
        """
        Doporučí analýzy pro celý průzkum
        
        Args:
            analyzed_questions: Seznam analyzovaných otázek
            survey_context: Kontext průzkumu (volitelné)
            
        Returns:
            Plán analýz pro průzkum
        """
        # Rozdělení otázek podle typů
        question_groups = self._group_questions_by_type(analyzed_questions)
        
        # Doporučení analýz na úrovni otázek
        question_analyses = self._recommend_question_analyses(analyzed_questions)
        
        # Doporučení analýz na úrovni sekcí
        section_analyses = self._recommend_section_analyses(question_groups)
        
        # Doporučení cross-question analýz
        cross_analyses = self._recommend_cross_question_analyses(analyzed_questions)
        
        # Výpočet celkové složitosti a času
        total_time, complexity = self._estimate_analysis_effort(
            question_analyses + section_analyses + cross_analyses
        )
        
        plan = SurveyAnalysisPlan(
            question_level_analyses=question_analyses,
            section_level_analyses=section_analyses,
            cross_question_analyses=cross_analyses,
            total_estimated_time=total_time,
            complexity_score=complexity
        )
        
        logger.info(f"Vygenerován plán s {len(question_analyses + section_analyses + cross_analyses)} analýzami")
        return plan
    
    def _group_questions_by_type(self, analyzed_questions: List[AnalyzedQuestion]) -> Dict[str, List[AnalyzedQuestion]]:
        """Seskupí otázky podle typů"""
        groups = defaultdict(list)
        
        for question in analyzed_questions:
            if question.matched_type:
                groups[question.matched_type.id].append(question)
            else:
                groups['unknown'].append(question)
        
        return dict(groups)
    
    def _recommend_question_analyses(self, analyzed_questions: List[AnalyzedQuestion]) -> List[AnalysisRecommendation]:
        """Doporučí analýzy na úrovni jednotlivých otázek"""
        recommendations = []
        
        # Seskupení podle doporučených analýz
        analysis_to_questions = defaultdict(list)
        
        for question in analyzed_questions:
            for analysis_id in question.recommended_analyses:
                analysis_to_questions[analysis_id].append(question.original_question.qid)
        
        # Vytvoření doporučení
        for analysis_id, question_ids in analysis_to_questions.items():
            if analysis_id in self.metadata.analysis_types:
                analysis_type = self.metadata.analysis_types[analysis_id]
                
                # Pouze question-level analýzy
                if analysis_type.analysis_type == 'question':
                    priority_score = self._calculate_priority_score(analysis_type, question_ids)
                    
                    recommendation = AnalysisRecommendation(
                        analysis_type=analysis_type,
                        applicable_questions=question_ids,
                        priority_score=priority_score,
                        reasoning=self._generate_reasoning(analysis_type, question_ids, 'question'),
                        estimated_complexity=self._estimate_complexity(analysis_type, len(question_ids)),
                        prerequisites=self._get_prerequisites(analysis_type)
                    )
                    recommendations.append(recommendation)
        
        # Seřazení podle priority
        recommendations.sort(key=lambda r: r.priority_score, reverse=True)
        
        logger.info(f"Doporučeno {len(recommendations)} analýz na úrovni otázek")
        return recommendations
    
    def _recommend_section_analyses(self, question_groups: Dict[str, List[AnalyzedQuestion]]) -> List[AnalysisRecommendation]:
        """Doporučí analýzy na úrovni sekcí"""
        recommendations = []
        
        # Analýzy, které vyžadují více otázek
        section_analysis_candidates = [
            'CRA',  # Korelační analýza
            'CTA',  # Kontingenční analýza
            'CMA',  # Komparativní analýza
            'FAA',  # Faktorová analýza
            'CLA',  # Klastrová analýza
        ]
        
        for analysis_id in section_analysis_candidates:
            if analysis_id in self.metadata.analysis_types:
                analysis_type = self.metadata.analysis_types[analysis_id]
                
                # Najdeme aplikovatelné otázky
                applicable_questions = []
                for question_group in question_groups.values():
                    for question in question_group:
                        if analysis_id in question.recommended_analyses:
                            applicable_questions.append(question.original_question.qid)
                
                # Pouze pokud máme dostatek otázek
                if len(applicable_questions) >= 2:
                    priority_score = self._calculate_priority_score(analysis_type, applicable_questions)
                    
                    recommendation = AnalysisRecommendation(
                        analysis_type=analysis_type,
                        applicable_questions=applicable_questions,
                        priority_score=priority_score,
                        reasoning=self._generate_reasoning(analysis_type, applicable_questions, 'section'),
                        estimated_complexity=self._estimate_complexity(analysis_type, len(applicable_questions)),
                        prerequisites=self._get_prerequisites(analysis_type)
                    )
                    recommendations.append(recommendation)
        
        # Seřazení podle priority
        recommendations.sort(key=lambda r: r.priority_score, reverse=True)
        
        logger.info(f"Doporučeno {len(recommendations)} analýz na úrovni sekcí")
        return recommendations
    
    def _recommend_cross_question_analyses(self, analyzed_questions: List[AnalyzedQuestion]) -> List[AnalysisRecommendation]:
        """Doporučí analýzy napříč otázkami"""
        recommendations = []
        
        # Identifikace možností pro cross-question analýzy
        text_questions = [q for q in analyzed_questions if q.matched_type and q.matched_type.id in ['S', 'T', 'U']]
        scale_questions = [q for q in analyzed_questions if q.matched_type and q.matched_type.id in ['5', 'A', 'B']]
        
        # Tematická analýza pro textové otázky
        if len(text_questions) >= 2:
            if 'THA' in self.metadata.analysis_types:
                analysis_type = self.metadata.analysis_types['THA']
                question_ids = [q.original_question.qid for q in text_questions]
                
                recommendation = AnalysisRecommendation(
                    analysis_type=analysis_type,
                    applicable_questions=question_ids,
                    priority_score=self._calculate_priority_score(analysis_type, question_ids),
                    reasoning=f"Tematická analýza napříč {len(text_questions)} textovými otázkami",
                    estimated_complexity=min(5, len(text_questions)),
                    prerequisites=['Kvalitní textová data']
                )
                recommendations.append(recommendation)
        
        # Korelační analýza pro škálové otázky
        if len(scale_questions) >= 3:
            if 'CRA' in self.metadata.analysis_types:
                analysis_type = self.metadata.analysis_types['CRA']
                question_ids = [q.original_question.qid for q in scale_questions]
                
                recommendation = AnalysisRecommendation(
                    analysis_type=analysis_type,
                    applicable_questions=question_ids,
                    priority_score=self._calculate_priority_score(analysis_type, question_ids),
                    reasoning=f"Korelační analýza mezi {len(scale_questions)} škálovými otázkami",
                    estimated_complexity=min(4, len(scale_questions) // 2),
                    prerequisites=['Číselná data', 'Dostatečný vzorek']
                )
                recommendations.append(recommendation)
        
        logger.info(f"Doporučeno {len(recommendations)} cross-question analýz")
        return recommendations
    
    def _calculate_priority_score(self, analysis_type: AnalysisType, question_ids: List[str]) -> float:
        """Vypočítá skóre priority pro analýzu"""
        # Základní skóre podle priority v metadata (invertováno - nižší priorita = vyšší skóre)
        base_score = (6 - analysis_type.priority) / 5.0
        
        # Bonus za počet aplikovatelných otázek
        question_bonus = min(len(question_ids) / 10.0, 1.0)
        
        # Kombinace podle vah
        final_score = (
            base_score * self.priority_weights['analysis_priority'] +
            question_bonus * self.priority_weights['question_count'] +
            0.5 * self.priority_weights['data_richness'] +  # Default hodnota
            0.5 * self.priority_weights['user_preference']  # Default hodnota
        )
        
        return min(final_score, 1.0)
    
    def _generate_reasoning(self, analysis_type: AnalysisType, question_ids: List[str], level: str) -> str:
        """Generuje zdůvodnění pro doporučení"""
        reasons = []
        
        # Základní zdůvodnění
        reasons.append(f"{analysis_type.name} je vhodná pro {len(question_ids)} otázek")
        
        # Podle úrovně
        if level == 'question':
            reasons.append("Analýza na úrovni jednotlivých otázek")
        elif level == 'section':
            reasons.append("Analýza vztahů mezi otázkami")
        
        # Podle priority
        if analysis_type.priority <= 2:
            reasons.append("Vysoká priorita implementace")
        elif analysis_type.priority <= 3:
            reasons.append("Střední priorita implementace")
        
        return " | ".join(reasons)
    
    def _estimate_complexity(self, analysis_type: AnalysisType, question_count: int) -> int:
        """Odhadne složitost analýzy (1-5)"""
        base_complexity = analysis_type.priority  # Vyšší priorita = nižší složitost
        
        # Úprava podle počtu otázek
        if question_count > 10:
            base_complexity += 1
        elif question_count > 20:
            base_complexity += 2
        
        return min(max(base_complexity, 1), 5)
    
    def _get_prerequisites(self, analysis_type: AnalysisType) -> List[str]:
        """Vrátí předpoklady pro analýzu"""
        prerequisites = []
        
        # Podle typu analýzy
        if analysis_type.id in ['CRA', 'FAA']:
            prerequisites.extend(['Číselná data', 'Normální distribuce'])
        elif analysis_type.id in ['THA', 'WCA']:
            prerequisites.extend(['Textová data', 'Dostatečný objem textu'])
        elif analysis_type.id in ['CTA', 'CMA']:
            prerequisites.extend(['Kategoriální data', 'Dostatečný vzorek'])
        
        return prerequisites
    
    def _estimate_analysis_effort(self, recommendations: List[AnalysisRecommendation]) -> Tuple[int, float]:
        """Odhadne celkový čas a složitost"""
        total_time = 0
        complexity_scores = []
        
        # Základní časy podle složitosti (minuty)
        time_per_complexity = {1: 15, 2: 30, 3: 60, 4: 120, 5: 240}
        
        for rec in recommendations:
            time = time_per_complexity.get(rec.estimated_complexity, 60)
            total_time += time
            complexity_scores.append(rec.estimated_complexity)
        
        # Průměrná složitost
        avg_complexity = sum(complexity_scores) / len(complexity_scores) if complexity_scores else 1.0
        
        return total_time, avg_complexity
