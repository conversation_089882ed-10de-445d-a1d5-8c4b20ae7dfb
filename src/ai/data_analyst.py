"""
AI Data Analyst
Automatická analýza dat a generování závěrů pro grafy
BEZPEČNÝ - voliteln<PERSON> funkce, neovlivňuje stávající workflow
"""

import logging
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import json

logger = logging.getLogger(__name__)


@dataclass
class AnalysisResult:
    """Výsledek AI analýzy dat"""
    summary: str
    key_insights: List[str]
    sentiment_score: Optional[float] = None
    confidence: float = 0.0
    metadata_text: str = ""
    chart_recommendations: List[str] = None
    statistical_notes: List[str] = None
    model_used: str = ""
    cost: float = 0.0
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()
        if self.chart_recommendations is None:
            self.chart_recommendations = []
        if self.statistical_notes is None:
            self.statistical_notes = []


class AIDataAnalyst:
    """
    AI analytik pro automatickou analýzu dat
    BEZPEČNÝ - funguje jako opt-in funkce
    """
    
    # Prompt templates pro různé typy analýz
    ANALYSIS_PROMPTS = {
        'wordcloud': {
            'system': """Jsi datový analytik specializující se na analýzu WordCloud dat. 
Analyzuj frekvence slov a vytvoř stručný, faktický závěr.""",
            'user': """Analyzuj následující WordCloud data:

Frekvence slov: {frequencies}
Počet respondentů: {total_responses}
Typ otázky: {question_type}

Vytvoř stručný závěr (max 2 věty) pro patičku grafu obsahující:
1. Nejčastější témata/slova
2. Celkový sentiment nebo trend
3. Klíčové pozorování

Formát odpovědi:
ZÁVĚR: [stručný závěr pro patičku]
POZOROVÁNÍ: [3 klíčová pozorování]"""
        },
        
        'column': {
            'system': """Jsi datový analytik specializující se na sloupcové grafy. 
Analyzuj distribuce hodnot a identifikuj trendy.""",
            'user': """Analyzuj následující data ze sloupcového grafu:

Data: {values}
Kategorie: {categories}
Počet respondentů: {total_responses}

Vytvoř stručný závěr (max 2 věty) obsahující:
1. Nejvyšší/nejnižší hodnoty
2. Distribuce a trendy
3. Statistické pozorování

Formát odpovědi:
ZÁVĚR: [stručný závěr pro patičku]
POZOROVÁNÍ: [3 klíčová pozorování]"""
        },
        
        'pie': {
            'system': """Jsi datový analytik specializující se na koláčové grafy. 
Analyzuj proporce a rozložení dat.""",
            'user': """Analyzuj následující data z koláčového grafu:

Hodnoty: {values}
Kategorie: {categories}
Celkem: {total}

Vytvoř stručný závěr (max 2 věty) obsahující:
1. Dominantní kategorie
2. Rozložení proporcí
3. Významné rozdíly

Formát odpovědi:
ZÁVĚR: [stručný závěr pro patičku]
POZOROVÁNÍ: [3 klíčová pozorování]"""
        },
        
        'table': {
            'system': """Jsi datový analytik specializující se na tabulková data. 
Analyzuj kategorie a jejich četnosti.""",
            'user': """Analyzuj následující tabulková data:

Kategorie a četnosti: {category_data}
Celkový počet: {total_count}

Vytvoř stručný závěr (max 2 věty) obsahující:
1. Nejčastější kategorie
2. Rozložení odpovědí
3. Zajímavé vzorce

Formát odpovědi:
ZÁVĚR: [stručný závěr pro patičku]
POZOROVÁNÍ: [3 klíčová pozorování]"""
        }
    }
    
    def __init__(self, ai_manager=None):
        """
        Initialize AI data analyst
        
        Args:
            ai_manager: AI manager instance for LLM calls
        """
        self.ai_manager = ai_manager
        self.analysis_cache: Dict[str, AnalysisResult] = {}
        
        logger.info("AIDataAnalyst initialized")
    
    def is_available(self) -> bool:
        """Check if AI analysis is available"""
        return self.ai_manager is not None
    
    def analyze_chart_data(
        self,
        chart_data: Dict[str, Any],
        chart_type: str,
        question_context: Dict[str, Any] = None,
        use_cache: bool = True
    ) -> Optional[AnalysisResult]:
        """
        Analyzuje data grafu pomocí AI
        
        Args:
            chart_data: Data grafu
            chart_type: Typ grafu (wordcloud, column, pie, table)
            question_context: Kontext otázky
            use_cache: Použít cache
            
        Returns:
            AnalysisResult nebo None pokud AI není dostupné
        """
        if not self.is_available():
            logger.warning("AI analysis not available")
            return None
        
        try:
            # Generate cache key
            cache_key = self._generate_cache_key(chart_data, chart_type, question_context)
            
            # Check cache
            if use_cache and cache_key in self.analysis_cache:
                logger.info(f"Using cached analysis for {chart_type}")
                return self.analysis_cache[cache_key]
            
            # Prepare analysis prompt
            prompt_data = self._prepare_prompt_data(chart_data, chart_type, question_context)
            
            # Get AI analysis
            analysis_text = self._get_ai_analysis(chart_type, prompt_data)
            
            # Parse AI response
            analysis_result = self._parse_ai_response(analysis_text, chart_type)
            
            # Cache result
            if use_cache:
                self.analysis_cache[cache_key] = analysis_result
            
            logger.info(f"Generated AI analysis for {chart_type}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            return self._create_fallback_analysis(chart_data, chart_type)
    
    def _generate_cache_key(
        self, 
        chart_data: Dict[str, Any], 
        chart_type: str, 
        question_context: Dict[str, Any] = None
    ) -> str:
        """Generate cache key for analysis"""
        import hashlib
        
        # Create deterministic string from data
        data_str = json.dumps(chart_data, sort_keys=True)
        context_str = json.dumps(question_context or {}, sort_keys=True)
        combined = f"{chart_type}:{data_str}:{context_str}"
        
        return hashlib.md5(combined.encode()).hexdigest()
    
    def _prepare_prompt_data(
        self, 
        chart_data: Dict[str, Any], 
        chart_type: str, 
        question_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Prepare data for AI prompt"""
        prompt_data = {
            'chart_type': chart_type,
            'question_type': question_context.get('question_type', 'unknown') if question_context else 'unknown'
        }
        
        if chart_type == 'wordcloud':
            prompt_data.update({
                'frequencies': chart_data.get('frequencies', {}),
                'total_responses': chart_data.get('metadata', {}).get('total_responses', 0)
            })
        
        elif chart_type == 'column':
            prompt_data.update({
                'values': list(chart_data.get('values', [])),
                'categories': list(chart_data.get('categories', [])),
                'total_responses': sum(chart_data.get('values', []))
            })
        
        elif chart_type == 'pie':
            prompt_data.update({
                'values': list(chart_data.get('values', [])),
                'categories': list(chart_data.get('categories', [])),
                'total': sum(chart_data.get('values', []))
            })
        
        elif chart_type == 'table':
            prompt_data.update({
                'category_data': chart_data.get('category_counts', {}),
                'total_count': chart_data.get('total_responses', 0)
            })
        
        return prompt_data
    
    def _get_ai_analysis(self, chart_type: str, prompt_data: Dict[str, Any]) -> str:
        """Get AI analysis using prompt template"""
        if chart_type not in self.ANALYSIS_PROMPTS:
            chart_type = 'column'  # Default fallback
        
        prompt_template = self.ANALYSIS_PROMPTS[chart_type]
        
        # Format prompt
        system_prompt = prompt_template['system']
        user_prompt = prompt_template['user'].format(**prompt_data)
        
        # Create messages
        messages = [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': user_prompt}
        ]
        
        # Get AI response
        response = self.ai_manager.openai_client.chat_completion(
            messages=messages,
            temperature=0.3,
            max_tokens=500
        )
        
        return response.content
    
    def _parse_ai_response(self, analysis_text: str, chart_type: str) -> AnalysisResult:
        """Parse AI response into AnalysisResult"""
        try:
            # Extract conclusion and observations
            lines = analysis_text.strip().split('\n')
            
            summary = ""
            insights = []
            
            for line in lines:
                line = line.strip()
                if line.startswith('ZÁVĚR:'):
                    summary = line.replace('ZÁVĚR:', '').strip()
                elif line.startswith('POZOROVÁNÍ:'):
                    observations = line.replace('POZOROVÁNÍ:', '').strip()
                    # Split by numbers or bullets
                    import re
                    insights = [obs.strip() for obs in re.split(r'[0-9]+\.|\-|\•', observations) if obs.strip()]
            
            # Fallback parsing
            if not summary and analysis_text:
                # Take first sentence as summary
                sentences = analysis_text.split('.')
                summary = sentences[0].strip() if sentences else analysis_text[:100]
            
            if not insights:
                # Extract remaining text as insights
                remaining_text = analysis_text.replace(summary, '').strip()
                if remaining_text:
                    insights = [remaining_text[:200]]
            
            return AnalysisResult(
                summary=summary or "Analýza dat dokončena",
                key_insights=insights[:3],  # Max 3 insights
                metadata_text=summary,
                confidence=0.8,  # Default confidence
                model_used=getattr(self.ai_manager.openai_client, 'default_model', 'unknown'),
                cost=0.0  # Will be updated by AI manager
            )
            
        except Exception as e:
            logger.error(f"Failed to parse AI response: {e}")
            return self._create_fallback_analysis({}, chart_type)
    
    def _create_fallback_analysis(self, chart_data: Dict[str, Any], chart_type: str) -> AnalysisResult:
        """Create fallback analysis when AI fails"""
        fallback_summaries = {
            'wordcloud': "Analýza nejčastějších slov v odpovědích",
            'column': "Srovnání hodnot mezi kategoriemi",
            'pie': "Rozložení odpovědí podle kategorií",
            'table': "Přehled kategorií a jejich četností"
        }
        
        return AnalysisResult(
            summary=fallback_summaries.get(chart_type, "Analýza dat"),
            key_insights=["Automatická analýza není dostupná"],
            metadata_text=fallback_summaries.get(chart_type, "Analýza dat"),
            confidence=0.0,
            model_used="fallback"
        )
    
    def embed_analysis_in_chart_config(
        self, 
        chart_config: Dict[str, Any], 
        analysis: AnalysisResult,
        location: str = 'footer'
    ) -> Dict[str, Any]:
        """
        Vloží AI analýzu do konfigurace grafu
        
        Args:
            chart_config: Konfigurace grafu
            analysis: Výsledek analýzy
            location: Umístění ('footer', 'header', 'subtitle')
            
        Returns:
            Aktualizovaná konfigurace grafu
        """
        try:
            # Ensure annotate section exists
            if 'annotate' not in chart_config:
                chart_config['annotate'] = {}
            
            # Add analysis based on location
            if location == 'footer':
                chart_config['annotate']['notes'] = analysis.metadata_text
            elif location == 'header':
                chart_config['annotate']['intro'] = analysis.metadata_text
            elif location == 'subtitle':
                chart_config['title'] = chart_config.get('title', {})
                chart_config['title']['subtitle'] = analysis.metadata_text
            
            # Add AI metadata
            chart_config['ai_analysis'] = {
                'enabled': True,
                'summary': analysis.summary,
                'insights': analysis.key_insights,
                'confidence': analysis.confidence,
                'model_used': analysis.model_used,
                'generated_at': analysis.timestamp
            }
            
            logger.info(f"Embedded AI analysis in chart config ({location})")
            return chart_config
            
        except Exception as e:
            logger.error(f"Failed to embed analysis in chart config: {e}")
            return chart_config
    
    def analyze_multiple_charts(
        self, 
        chart_data_list: List[Dict[str, Any]], 
        chart_types: List[str],
        question_contexts: List[Dict[str, Any]] = None
    ) -> List[Optional[AnalysisResult]]:
        """Analyze multiple charts"""
        if question_contexts is None:
            question_contexts = [None] * len(chart_data_list)
        
        results = []
        for i, (data, chart_type) in enumerate(zip(chart_data_list, chart_types)):
            context = question_contexts[i] if i < len(question_contexts) else None
            analysis = self.analyze_chart_data(data, chart_type, context)
            results.append(analysis)
        
        return results
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """Get statistics about performed analyses"""
        total_analyses = len(self.analysis_cache)
        
        chart_type_counts = {}
        confidence_scores = []
        
        for analysis in self.analysis_cache.values():
            # Count by chart type (extract from cache key or analysis)
            confidence_scores.append(analysis.confidence)
        
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        return {
            'total_analyses': total_analyses,
            'average_confidence': round(avg_confidence, 2),
            'cache_size': total_analyses,
            'chart_type_distribution': chart_type_counts
        }
    
    def clear_cache(self):
        """Clear analysis cache"""
        self.analysis_cache.clear()
        logger.info("Analysis cache cleared")


def create_ai_data_analyst(ai_manager=None) -> AIDataAnalyst:
    """
    Factory function to create AI data analyst
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    return AIDataAnalyst(ai_manager)
