"""
AI Integration Module for LimWrapp
Integrace AI komponent s existujícím systémem
"""

import logging
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

import sys
import os

# Přidání src do path pro absolutní importy
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from ai.ai_manager import AIManager

# ConfigManager může neexistovat, použijeme fallback
try:
    from core.config_manager import ConfigManager
except ImportError:
    # Fallback - vytvoříme dummy ConfigManager
    class ConfigManager:
        def __init__(self):
            pass
        def get_config(self, key, default=None):
            return default

logger = logging.getLogger(__name__)


class AIIntegration:
    """Integration layer between AI components and LimWrapp system"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize AI integration
        
        Args:
            config_manager: LimWrapp configuration manager
        """
        self.config_manager = config_manager
        self.ai_manager = None
        self._initialized = False
        
    def initialize(self) -> bool:
        """
        Initialize AI components
        
        Returns:
            True if initialization successful
        """
        try:
            # Get AI configuration
            ai_config = self._get_ai_config()
            
            # Initialize AI manager
            self.ai_manager = AIManager(
                api_key=ai_config.get('openai', {}).get('api_key'),
                config_dir=ai_config.get('config_dir', 'config/ai'),
                enable_cache=ai_config.get('enable_cache', True),
                redis_url=ai_config.get('redis', {}).get('url')
            )
            
            self._initialized = True
            logger.info("AI integration initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize AI integration: {e}")
            return False
    
    def is_available(self) -> bool:
        """Check if AI functionality is available"""
        return self._initialized and self.ai_manager is not None
    
    def generate_wordcloud_for_survey(
        self,
        survey_id: str,
        question_ids: List[str] = None,
        use_ai: bool = True
    ) -> Dict[str, Any]:
        """
        Generate word cloud for survey text responses
        
        Args:
            survey_id: Survey identifier
            question_ids: Specific question IDs to analyze (None for all text questions)
            use_ai: Whether to use AI enhancement
            
        Returns:
            Word cloud generation result
        """
        if not self.is_available():
            return {'success': False, 'error': 'AI not available'}
        
        try:
            # Get survey data
            survey_data = self._get_survey_text_data(survey_id, question_ids)
            
            if not survey_data:
                return {'success': False, 'error': 'No text data found'}
            
            # Combine all text responses
            all_text = ' '.join(survey_data)
            
            # Generate word cloud
            result = self.ai_manager.generate_wordcloud(
                text=all_text,
                use_ai=use_ai,
                language='cs'
            )
            
            # Save result if successful
            if result.get('success'):
                self._save_wordcloud_result(survey_id, result)
            
            return result
            
        except Exception as e:
            logger.error(f"WordCloud generation failed for survey {survey_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def suggest_analysis_scenarios(
        self,
        survey_id: str,
        user_objectives: List[str] = None
    ) -> Dict[str, Any]:
        """
        Suggest analysis scenarios for a survey
        
        Args:
            survey_id: Survey identifier
            user_objectives: User-specified analysis objectives
            
        Returns:
            Analysis scenario suggestions
        """
        if not self.is_available():
            return {'success': False, 'error': 'AI not available'}
        
        try:
            # Get survey characteristics
            survey_info = self._get_survey_characteristics(survey_id)
            
            # Generate scenarios
            result = self.ai_manager.generate_analysis_scenarios(
                survey_data=survey_info,
                user_objectives=user_objectives
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Scenario generation failed for survey {survey_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def analyze_text_responses(
        self,
        survey_id: str,
        question_id: str,
        analysis_type: str = 'general'
    ) -> Dict[str, Any]:
        """
        Analyze text responses using AI
        
        Args:
            survey_id: Survey identifier
            question_id: Question identifier
            analysis_type: Type of analysis (general, sentiment, themes)
            
        Returns:
            Text analysis results
        """
        if not self.is_available():
            return {'success': False, 'error': 'AI not available'}
        
        try:
            # Get text responses for the question
            text_data = self._get_question_text_data(survey_id, question_id)
            
            if not text_data:
                return {'success': False, 'error': 'No text data found'}
            
            # Combine responses
            combined_text = ' '.join(text_data)
            
            # Analyze with AI
            result = self.ai_manager.analyze_text_with_ai(
                text=combined_text,
                analysis_type=analysis_type,
                language='cs'
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Text analysis failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_parameter_suggestions(
        self,
        analysis_type: str,
        data_characteristics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Get AI-suggested parameters for analysis
        
        Args:
            analysis_type: Type of analysis
            data_characteristics: Characteristics of the data
            
        Returns:
            Parameter suggestions
        """
        if not self.is_available():
            return {'success': False, 'error': 'AI not available'}
        
        try:
            context = {
                'analysis_type': analysis_type,
                **data_characteristics
            }
            
            result = self.ai_manager.suggest_parameters(context)
            return result
            
        except Exception as e:
            logger.error(f"Parameter suggestion failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """Get AI usage statistics"""
        if not self.is_available():
            return {'success': False, 'error': 'AI not available'}
        
        return self.ai_manager.get_usage_statistics()
    
    def _get_ai_config(self) -> Dict[str, Any]:
        """Get AI configuration from config manager"""
        # Try to get from config manager first
        try:
            ai_config = self.config_manager.get_config('ai', {})
        except:
            ai_config = {}
        
        # Fallback to environment variables
        import os
        return {
            'openai': {
                'api_key': ai_config.get('openai_api_key') or os.getenv('OPENAI_API_KEY'),
                'organization_id': ai_config.get('openai_org_id') or os.getenv('OPENAI_ORG_ID')
            },
            'redis': {
                'url': ai_config.get('redis_url') or os.getenv('REDIS_URL', 'redis://localhost:6379/0')
            },
            'config_dir': ai_config.get('config_dir', 'config/ai'),
            'enable_cache': ai_config.get('enable_cache', True)
        }
    
    def _get_survey_text_data(self, survey_id: str, question_ids: List[str] = None) -> List[str]:
        """Get text data from survey responses"""
        # This would integrate with your existing data loading system
        # For now, return empty list as placeholder
        logger.warning("Survey text data loading not implemented yet")
        return []
    
    def _get_question_text_data(self, survey_id: str, question_id: str) -> List[str]:
        """Get text data for specific question"""
        # This would integrate with your existing data loading system
        logger.warning("Question text data loading not implemented yet")
        return []
    
    def _get_survey_characteristics(self, survey_id: str) -> Dict[str, Any]:
        """Get survey characteristics for scenario generation"""
        # This would integrate with your existing survey analysis
        logger.warning("Survey characteristics extraction not implemented yet")
        return {
            'total_responses': 0,
            'total_questions': 0,
            'question_types': {},
            'has_likert_scales': False,
            'has_text_responses': False,
            'has_demographic_data': False
        }
    
    def _save_wordcloud_result(self, survey_id: str, result: Dict[str, Any]):
        """Save word cloud result to appropriate location"""
        try:
            # Create output directory
            output_dir = Path(f"charts/{survey_id}/wordclouds")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Save image data
            if 'image_data' in result:
                image_path = output_dir / "wordcloud.png"
                with open(image_path, 'wb') as f:
                    f.write(result['image_data'])
                
                logger.info(f"WordCloud saved to {image_path}")
            
            # Save metadata
            import json
            metadata_path = output_dir / "metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'metadata': result.get('metadata', {}),
                    'frequencies': result.get('frequencies', {}),
                    'ai_analysis': result.get('ai_analysis', {})
                }, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Failed to save wordcloud result: {e}")
    
    def close(self):
        """Close AI integration and cleanup"""
        if self.ai_manager:
            self.ai_manager.close()
        self._initialized = False
        logger.info("AI integration closed")


# Global AI integration instance
_ai_integration = None

def get_ai_integration(config_manager: ConfigManager = None) -> Optional[AIIntegration]:
    """
    Get global AI integration instance
    
    Args:
        config_manager: Configuration manager (required for first call)
        
    Returns:
        AI integration instance or None if not available
    """
    global _ai_integration
    
    if _ai_integration is None and config_manager is not None:
        _ai_integration = AIIntegration(config_manager)
        if not _ai_integration.initialize():
            _ai_integration = None
    
    return _ai_integration

def is_ai_available() -> bool:
    """Check if AI functionality is available"""
    global _ai_integration

    # Pokud ještě není inicializováno, zkusíme to s dummy config managerem
    if _ai_integration is None:
        try:
            # Vytvoříme dummy config manager
            dummy_config = ConfigManager()
            _ai_integration = AIIntegration(dummy_config)
            if not _ai_integration.initialize():
                _ai_integration = None
        except Exception as e:
            logger.error(f"Failed to initialize AI integration: {e}")
            _ai_integration = None

    return _ai_integration is not None and _ai_integration.is_available()
