"""
AI Manager - Central coordinator for all AI components
Koordinuje všechny AI komponenty v LimWrapp projektu
"""

import os
import logging
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

from .enhanced_openai_client import EnhancedOpenAIClient
from .wordcloud_generator import Word<PERSON>loudGenerator, WordCloudConfig
from .parameter_manager import ParameterManager
from .prompt_manager import PromptManager
from .scenario_generator import ScenarioGenerator, SurveyCharacteristics

logger = logging.getLogger(__name__)


class AIManager:
    """Central manager for all AI components"""
    
    def __init__(
        self,
        api_key: str = None,
        config_dir: str = None,
        enable_cache: bool = True,
        redis_url: str = None
    ):
        """
        Initialize AI Manager
        
        Args:
            api_key: OpenAI API key
            config_dir: Configuration directory
            enable_cache: Enable response caching
            redis_url: Redis URL for caching
        """
        self.config_dir = Path(config_dir) if config_dir else Path("config/ai")
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize core components
        self.openai_client = EnhancedOpenAIClient(
            api_key=api_key,
            enable_cache=enable_cache,
            redis_url=redis_url
        )
        
        self.parameter_manager = ParameterManager(
            config_dir=str(self.config_dir / "parameters")
        )
        
        self.prompt_manager = PromptManager(
            prompts_dir=str(self.config_dir / "prompts")
        )
        
        self.wordcloud_generator = WordCloudGenerator(
            ai_client=self.openai_client
        )
        
        self.scenario_generator = ScenarioGenerator(
            ai_client=self.openai_client,
            prompt_manager=self.prompt_manager
        )
        
        # Usage tracking
        self.usage_stats = {
            'total_requests': 0,
            'total_cost': 0.0,
            'components_used': set()
        }
        
        logger.info("AI Manager initialized with all components")
    
    def generate_wordcloud(
        self,
        text: Union[str, List[str]],
        use_ai: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate word cloud with optional AI enhancement
        
        Args:
            text: Input text or list of texts
            use_ai: Whether to use AI for text analysis
            **kwargs: Additional configuration parameters
            
        Returns:
            Dictionary with word cloud result and metadata
        """
        try:
            # Get parameters from parameter manager
            wc_params = self.parameter_manager.get_parameters_by_category('wordcloud')
            viz_params = self.parameter_manager.get_parameters_by_category('visualization')
            
            # Merge with provided kwargs
            config_dict = {**wc_params, **viz_params, **kwargs}
            config_dict['use_ai'] = use_ai
            
            # Create configuration
            config = WordCloudConfig(**{
                k: v for k, v in config_dict.items()
                if k in WordCloudConfig.__dataclass_fields__
            })
            
            # Generate word cloud
            result = self.wordcloud_generator.generate(text, config)
            
            # Update usage stats
            self._update_usage_stats('wordcloud')
            
            return {
                'image_data': result.image_data,
                'frequencies': result.frequencies,
                'metadata': result.metadata,
                'ai_analysis': result.ai_analysis,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"WordCloud generation failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_analysis_scenarios(
        self,
        survey_data: Dict[str, Any],
        user_objectives: Optional[List[str]] = None,
        complexity: str = 'auto'
    ) -> Dict[str, Any]:
        """
        Generate analysis scenarios for survey data
        
        Args:
            survey_data: Survey data characteristics
            user_objectives: User-specified objectives
            complexity: Complexity level preference
            
        Returns:
            Dictionary with generated scenarios
        """
        try:
            # Create survey characteristics
            characteristics = SurveyCharacteristics(
                total_responses=survey_data.get('total_responses', 0),
                total_questions=survey_data.get('total_questions', 0),
                question_types=survey_data.get('question_types', {}),
                has_likert_scales=survey_data.get('has_likert_scales', False),
                has_text_responses=survey_data.get('has_text_responses', False),
                has_demographic_data=survey_data.get('has_demographic_data', False),
                survey_topic=survey_data.get('survey_topic'),
                target_audience=survey_data.get('target_audience')
            )
            
            # Generate scenarios
            scenarios = self.scenario_generator.generate_scenarios(
                characteristics,
                user_objectives,
                complexity
            )
            
            # Update usage stats
            self._update_usage_stats('scenario_generation')
            
            return {
                'scenarios': [self._scenario_to_dict(s) for s in scenarios],
                'characteristics': self._characteristics_to_dict(characteristics),
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Scenario generation failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def analyze_text_with_ai(
        self,
        text: str,
        analysis_type: str = 'general',
        language: str = 'cs'
    ) -> Dict[str, Any]:
        """
        Analyze text using AI
        
        Args:
            text: Text to analyze
            analysis_type: Type of analysis (general, sentiment, themes)
            language: Language of the text
            
        Returns:
            Analysis results
        """
        try:
            # Get appropriate prompt template
            if analysis_type == 'sentiment':
                template_name = 'sentiment_analysis'
            elif analysis_type == 'themes':
                template_name = 'theme_extraction'
            else:
                template_name = 'text_analysis'
            
            # Check if template exists, use general if not
            if not self.prompt_manager.get_template(template_name):
                template_name = 'wordcloud_analysis'
            
            # Prepare parameters
            params = {
                'text': text[:2000],  # Limit text length
                'language': language,
                'analysis_type': analysis_type
            }
            
            # Create messages
            messages = self.prompt_manager.create_messages(template_name, params)
            
            # Get AI response
            response = self.openai_client.chat_completion(
                messages=messages,
                temperature=0.3
            )
            
            # Update usage stats
            self._update_usage_stats('text_analysis', response.cost)
            
            return {
                'analysis': response.content,
                'tokens_used': response.tokens_used,
                'cost': response.cost,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Text analysis failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def suggest_parameters(
        self,
        context: Dict[str, Any],
        category: str = None
    ) -> Dict[str, Any]:
        """
        Get AI-suggested parameters for analysis
        
        Args:
            context: Context information for suggestions
            category: Parameter category to focus on
            
        Returns:
            Parameter suggestions
        """
        try:
            # Prepare context for AI
            context_str = f"""
            Typ analýzy: {context.get('analysis_type', 'obecná')}
            Počet záznamů: {context.get('record_count', 'neznámý')}
            Typ dat: {context.get('data_type', 'smíšený')}
            Cílová skupina: {context.get('target_audience', 'obecná')}
            """
            
            # Get parameter suggestions using AI
            params = {
                'analysis_type': context.get('analysis_type', 'obecná'),
                'data_type': context.get('data_type', 'smíšený'),
                'record_count': context.get('record_count', 'neznámý'),
                'target_audience': context.get('target_audience', 'obecná'),
                'requirements': context.get('requirements', 'standardní analýza')
            }
            
            messages = self.prompt_manager.create_messages('parameter_generation', params)
            response = self.openai_client.chat_completion(
                messages=messages,
                temperature=0.5
            )
            
            # Update usage stats
            self._update_usage_stats('parameter_suggestions', response.cost)
            
            return {
                'suggestions': response.content,
                'context': context,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Parameter suggestion failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """Get comprehensive usage statistics"""
        openai_stats = self.openai_client.get_usage_stats()
        
        return {
            'openai': openai_stats,
            'manager': self.usage_stats.copy(),
            'components': {
                'parameter_manager': len(self.parameter_manager.get_all_parameters()),
                'prompt_templates': len(self.prompt_manager.list_templates()),
                'scenario_templates': len(self.scenario_generator.get_scenario_templates())
            }
        }
    
    def reset_statistics(self):
        """Reset all usage statistics"""
        self.openai_client.reset_stats()
        self.usage_stats = {
            'total_requests': 0,
            'total_cost': 0.0,
            'components_used': set()
        }
        logger.info("Usage statistics reset")
    
    def _update_usage_stats(self, component: str, cost: float = 0.0):
        """Update internal usage statistics"""
        self.usage_stats['total_requests'] += 1
        self.usage_stats['total_cost'] += cost
        self.usage_stats['components_used'].add(component)
    
    def _scenario_to_dict(self, scenario) -> Dict[str, Any]:
        """Convert scenario object to dictionary"""
        return {
            'scenario_id': scenario.scenario_id,
            'title': scenario.title,
            'description': scenario.description,
            'objective': scenario.objective,
            'complexity_level': scenario.complexity_level,
            'estimated_duration': scenario.estimated_duration,
            'steps': [
                {
                    'step_id': step.step_id,
                    'analysis_type': step.analysis_type.value,
                    'title': step.title,
                    'description': step.description,
                    'estimated_time': step.estimated_time
                }
                for step in scenario.steps
            ],
            'created_at': scenario.created_at.isoformat()
        }
    
    def _characteristics_to_dict(self, characteristics) -> Dict[str, Any]:
        """Convert characteristics object to dictionary"""
        return {
            'total_responses': characteristics.total_responses,
            'total_questions': characteristics.total_questions,
            'question_types': characteristics.question_types,
            'has_likert_scales': characteristics.has_likert_scales,
            'has_text_responses': characteristics.has_text_responses,
            'has_demographic_data': characteristics.has_demographic_data,
            'survey_topic': characteristics.survey_topic,
            'target_audience': characteristics.target_audience
        }
    
    def close(self):
        """Close all components and cleanup"""
        self.openai_client.close()
        logger.info("AI Manager closed")
