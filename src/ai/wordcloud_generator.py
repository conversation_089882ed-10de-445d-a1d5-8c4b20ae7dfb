"""
WordCloud Generator with AI Enhancement
Převzato z existujícího projektu a adaptováno pro LimWrapp
Supports multiple visualization types and AI-powered text analysis
"""

import os
import io
import json
import logging
import hashlib
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from dataclasses import dataclass

import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from wordcloud import WordCloud

logger = logging.getLogger(__name__)


@dataclass
class WordCloudConfig:
    """Configuration for WordCloud generation"""
    max_words: int = 200
    min_word_length: int = 3
    width: int = 800
    height: int = 400
    background_color: str = 'white'
    min_font_size: int = 10
    max_font_size: int = 100
    collocations: bool = True
    visualization_type: str = 'WCS'  # WCS, WCT, WCH
    color_scheme: str = 'default'
    output_format: str = 'png'
    dpi: int = 300
    quality: int = 90
    use_ai: bool = False
    language: str = 'cs'
    seed: Optional[int] = None
    mask_path: Optional[str] = None
    hierarchy_levels: int = 3


@dataclass
class WordCloudResult:
    """Result of WordCloud generation"""
    image_data: bytes
    frequencies: Dict[str, int]
    metadata: Dict[str, Any]
    ai_analysis: Optional[Dict[str, Any]] = None


class WordCloudGenerator:
    """
    Advanced WordCloud generator with AI enhancement
    
    Supports three types of visualizations:
    - WCS (Word Cloud Standard) - classic word cloud
    - WCT (Word Cloud Shaped) - word cloud in defined shape
    - WCH (Word Cloud Hierarchical) - hierarchical word cloud
    """
    
    COLOR_SCHEMES = {
        'default': ['#1f77b4', '#ff7f0e', '#2ca02c'],
        'blue': ['#08519c', '#3182bd', '#6baed6'],
        'red': ['#a50f15', '#de2d26', '#fb6a4a'],
        'green': ['#238b45', '#41ab5d', '#74c476'],
        'purple': ['#6a51a3', '#807dba', '#9e9ac8']
    }
    
    SUPPORTED_FORMATS = ['png', 'svg', 'jpg', 'webp']
    PIL_FORMAT_MAP = {
        'png': 'PNG',
        'jpg': 'JPEG',
        'webp': 'WEBP'
    }
    
    def __init__(self, ai_client=None):
        """
        Initialize WordCloud generator
        
        Args:
            ai_client: Optional AI client for enhanced text analysis
        """
        self.ai_client = ai_client
        self._word_cloud = None
        self._analyzed_data = None
        
    def generate(
        self,
        text: Union[str, List[str]],
        config: WordCloudConfig = None
    ) -> WordCloudResult:
        """
        Generate WordCloud from text
        
        Args:
            text: Input text or list of texts
            config: WordCloud configuration
            
        Returns:
            WordCloudResult with image data and metadata
        """
        config = config or WordCloudConfig()
        
        # Convert text to string if it's a list
        if isinstance(text, list):
            text = ' '.join(text)
        
        # AI-enhanced analysis if enabled
        use_ai = config.use_ai and self.ai_client is not None
        
        if use_ai:
            try:
                ai_analysis = self._ai_analyze_text(text, config)
                frequencies = ai_analysis.get('frequencies', {})
                self._analyzed_data = ai_analysis
            except Exception as e:
                logger.error(f"AI analysis failed: {e}")
                use_ai = False
        
        if not use_ai:
            # Standard text processing
            words = self._preprocess_text(text)
            frequencies = self._calculate_frequencies(words)
            self._analyzed_data = {
                "frequencies": frequencies,
                "categories": {},
                "collocations": {},
                "hierarchy": {"root": {}},
                "excluded_words": []
            }
        
        # Filter words based on configuration
        filtered_frequencies = self._filter_words(frequencies, config)
        
        # Ensure we have some words
        if not filtered_frequencies:
            filtered_frequencies = {'žádná data': 1}
        
        # Generate visualization based on type
        if config.visualization_type == 'WCT':
            image_data = self._generate_shaped_visualization(filtered_frequencies, config)
        elif config.visualization_type == 'WCH':
            image_data = self._generate_hierarchical_visualization(
                self._analyzed_data.get('hierarchy', {}).get('root', {}), config
            )
        else:  # WCS
            image_data = self._generate_standard_visualization(filtered_frequencies, config)
        
        # Prepare metadata
        metadata = {
            "total_words": sum(filtered_frequencies.values()),
            "unique_words": len(filtered_frequencies),
            "max_words": config.max_words,
            "min_word_length": config.min_word_length,
            "visualization_type": config.visualization_type,
            "output_format": config.output_format,
            "color_scheme": config.color_scheme,
            "ai_used": use_ai
        }
        
        return WordCloudResult(
            image_data=image_data,
            frequencies=filtered_frequencies,
            metadata=metadata,
            ai_analysis=self._analyzed_data if use_ai else None
        )
    
    def _ai_analyze_text(self, text: str, config: WordCloudConfig) -> Dict[str, Any]:
        """Use AI to analyze text and extract meaningful words"""
        prompt = f"""
        Analyzuj následující text a extrahuj klíčová slova s jejich důležitostí.
        
        Text: {text[:2000]}...
        
        Požadavky:
        - Minimální délka slova: {config.min_word_length}
        - Maximální počet slov: {config.max_words}
        - Jazyk: {config.language}
        
        Vrať výsledek jako JSON s následující strukturou:
        {{
            "frequencies": {{"slovo": četnost, ...}},
            "categories": {{"kategorie": ["slovo1", "slovo2"], ...}},
            "excluded_words": ["vyloučené_slovo1", ...]
        }}
        """
        
        response = self.ai_client.chat_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3
        )
        
        try:
            return json.loads(response.content)
        except json.JSONDecodeError:
            # Fallback to standard processing
            logger.warning("Failed to parse AI response, using standard processing")
            return {}
    
    def _preprocess_text(self, text: str) -> List[str]:
        """Basic text preprocessing"""
        import re
        
        # Remove special characters and normalize
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = text.split()
        
        # Remove common Czech stop words
        stop_words = {
            'a', 'aby', 'ale', 'ani', 'ano', 'až', 'bez', 'být', 'co', 'či',
            'do', 'ho', 'i', 'já', 'je', 'jeho', 'její', 'jejich', 'jen',
            'ji', 'již', 'k', 'kam', 'kde', 'kdo', 'kdy', 'ke', 'má', 'mají',
            'má', 'mi', 'my', 'na', 'nad', 'nás', 'náš', 'ne', 'nebo', 'ní',
            'o', 'od', 'po', 'pod', 'pro', 's', 'se', 'si', 'ta', 'tak',
            'také', 'te', 'to', 'tu', 'ty', 'u', 'už', 'v', 've', 'více',
            'všech', 'z', 'za', 'ze'
        }
        
        return [word for word in words if word not in stop_words and len(word) >= 3]
    
    def _calculate_frequencies(self, words: List[str]) -> Dict[str, int]:
        """Calculate word frequencies"""
        frequencies = {}
        for word in words:
            frequencies[word] = frequencies.get(word, 0) + 1
        return frequencies
    
    def _filter_words(self, frequencies: Dict[str, int], config: WordCloudConfig) -> Dict[str, int]:
        """Filter words based on configuration"""
        # Filter by minimum word length
        filtered = {
            word: freq for word, freq in frequencies.items()
            if len(word) >= config.min_word_length
        }
        
        # Sort by frequency and take top words
        sorted_words = sorted(filtered.items(), key=lambda x: x[1], reverse=True)
        return dict(sorted_words[:config.max_words])
    
    def _generate_standard_visualization(
        self, 
        frequencies: Dict[str, int], 
        config: WordCloudConfig
    ) -> bytes:
        """Generate standard word cloud (WCS)"""
        # Create or reuse word cloud
        if self._word_cloud is None:
            self._word_cloud = WordCloud(
                width=config.width,
                height=config.height,
                background_color=config.background_color,
                min_font_size=config.min_font_size,
                max_font_size=config.max_font_size,
                max_words=config.max_words,
                collocations=config.collocations,
                random_state=config.seed,
                color_func=self._get_color_function(config.color_scheme)
            )
            self._word_cloud.generate_from_frequencies(frequencies)
        
        return self._save_visualization(self._word_cloud, config)
    
    def _generate_shaped_visualization(
        self, 
        frequencies: Dict[str, int], 
        config: WordCloudConfig
    ) -> bytes:
        """Generate shaped word cloud (WCT)"""
        if not config.mask_path or not os.path.exists(config.mask_path):
            raise ValueError("Pro WCT je vyžadována cesta k masce")
        
        # Load mask
        mask = np.array(Image.open(config.mask_path))
        
        # Create word cloud with mask
        wc = WordCloud(
            width=config.width,
            height=config.height,
            background_color=config.background_color,
            min_font_size=config.min_font_size,
            max_font_size=config.max_font_size,
            max_words=config.max_words,
            mask=mask,
            random_state=config.seed,
            color_func=self._get_color_function(config.color_scheme)
        )
        wc.generate_from_frequencies(frequencies)
        
        return self._save_visualization(wc, config)

    def _generate_hierarchical_visualization(
        self,
        hierarchy: Dict[str, Dict[str, int]],
        config: WordCloudConfig
    ) -> bytes:
        """Generate hierarchical word cloud (WCH)"""
        # Create hierarchical word cloud
        num_categories = len(hierarchy)
        if num_categories == 0:
            return self._generate_standard_visualization({}, config)

        # Calculate subplot layout
        cols = min(3, num_categories)
        rows = (num_categories + cols - 1) // cols

        plt.figure(figsize=(config.width/100, config.height/100))

        # Generate word clouds for each category
        for i, (category, words) in enumerate(hierarchy.items()):
            plt.subplot(rows, cols, i+1)
            plt.title(category)

            # Create word cloud for category
            wc = WordCloud(
                width=config.width//cols,
                height=config.height//rows,
                background_color=config.background_color,
                min_font_size=config.min_font_size,
                max_font_size=config.max_font_size,
                max_words=config.max_words,
                random_state=config.seed,
                color_func=self._get_color_function(config.color_scheme)
            )
            wc.generate_from_frequencies(words)

            plt.imshow(wc, interpolation='bilinear')
            plt.axis('off')

        plt.tight_layout(pad=3)

        # Save to buffer
        img_buffer = io.BytesIO()
        if config.output_format == 'svg':
            plt.savefig(img_buffer, format='svg', bbox_inches='tight', pad_inches=0)
        else:
            save_params = {}
            if config.output_format == 'png':
                save_params['dpi'] = config.dpi
            elif config.output_format == 'jpg':
                save_params['quality'] = config.quality
            elif config.output_format == 'webp':
                save_params['quality'] = config.quality
            plt.savefig(img_buffer, format=config.output_format, **save_params)

        plt.close()

        return img_buffer.getvalue()

    def _save_visualization(self, wc: WordCloud, config: WordCloudConfig) -> bytes:
        """Save visualization in requested format"""
        img_buffer = io.BytesIO()

        if config.output_format == 'svg':
            plt.figure(figsize=(wc.width/100, wc.height/100))
            plt.imshow(wc, interpolation='bilinear')
            plt.axis('off')
            plt.savefig(img_buffer, format='svg', bbox_inches='tight', pad_inches=0)
            plt.close()
        else:
            wc_image = wc.to_image()
            pil_format = self.PIL_FORMAT_MAP[config.output_format]
            save_params = {}

            if pil_format == 'JPEG':
                save_params['quality'] = config.quality
            elif pil_format == 'PNG':
                save_params['dpi'] = (config.dpi,) * 2
            elif pil_format == 'WEBP':
                save_params['quality'] = config.quality

            wc_image.save(img_buffer, format=pil_format, **save_params)

        return img_buffer.getvalue()

    def _get_color_function(self, color_scheme: str):
        """Create color function for word cloud"""
        colors = self.COLOR_SCHEMES.get(color_scheme, self.COLOR_SCHEMES['default'])
        color_index = 0

        def color_func(word, font_size, position, orientation, random_state=None, **kwargs):
            nonlocal color_index
            color = colors[color_index % len(colors)]
            color_index += 1
            return color

        return color_func


def create_wordcloud(
    text: Union[str, List[str]],
    ai_client=None,
    **kwargs
) -> WordCloudResult:
    """
    Convenience function to create word cloud

    Args:
        text: Input text or list of texts
        ai_client: Optional AI client for enhanced analysis
        **kwargs: Configuration parameters

    Returns:
        WordCloudResult
    """
    config = WordCloudConfig(**kwargs)
    generator = WordCloudGenerator(ai_client)
    return generator.generate(text, config)
