"""
Prompt Management System
Převzato z existujícího projektu a adaptováno pro LimWrapp
Manages AI prompts with templates and dynamic generation
"""

import os
import yaml
import json
import logging
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from dataclasses import dataclass
from string import Template

logger = logging.getLogger(__name__)


@dataclass
class PromptTemplate:
    """Template for AI prompts"""
    name: str
    system_prompt: str
    user_prompt: str
    description: str = ""
    parameters: Dict[str, Any] = None
    examples: List[Dict[str, str]] = None
    category: str = "general"
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.examples is None:
            self.examples = []


class PromptManager:
    """Manages AI prompts and templates"""
    
    def __init__(self, prompts_dir: Optional[str] = None):
        """
        Initialize prompt manager
        
        Args:
            prompts_dir: Directory containing prompt templates
        """
        self.prompts_dir = Path(prompts_dir) if prompts_dir else Path("src/ai/prompts")
        self.prompts_dir.mkdir(parents=True, exist_ok=True)
        
        self.templates: Dict[str, PromptTemplate] = {}
        self._load_default_prompts()
        self._load_prompts_from_files()
        
        logger.info(f"PromptManager initialized with {len(self.templates)} templates")
    
    def _load_default_prompts(self):
        """Load default prompt templates"""
        # WordCloud analysis prompt
        self.add_template(PromptTemplate(
            name="wordcloud_analysis",
            system_prompt="""Jsi expert na analýzu textu a vytváření word cloudů. 
Tvým úkolem je analyzovat text a extrahovat klíčová slova s jejich důležitostí.
Zaměř se na obsahově významná slova a ignoruj stop words.""",
            user_prompt="""Analyzuj následující text a extrahuj klíčová slova:

Text: {text}

Požadavky:
- Minimální délka slova: {min_word_length}
- Maximální počet slov: {max_words}
- Jazyk: {language}

Vrať výsledek jako JSON s následující strukturou:
{{
    "frequencies": {{"slovo": četnost, ...}},
    "categories": {{"kategorie": ["slovo1", "slovo2"], ...}},
    "excluded_words": ["vyloučené_slovo1", ...]
}}""",
            description="Analýza textu pro word cloud",
            category="wordcloud",
            parameters={
                "text": "Text k analýze",
                "min_word_length": "Minimální délka slova",
                "max_words": "Maximální počet slov",
                "language": "Jazyk textu"
            }
        ))
        
        # Survey analysis prompt
        self.add_template(PromptTemplate(
            name="survey_analysis",
            system_prompt="""Jsi expert na analýzu průzkumů a dotazníků.
Tvým úkolem je analyzovat strukturu průzkumu a navrhnout vhodné analýzy a vizualizace.""",
            user_prompt="""Analyzuj následující průzkum a navrhni analýzy:

Struktura průzkumu:
{survey_structure}

Počet respondentů: {respondent_count}
Typ průzkumu: {survey_type}

Navrhni:
1. Vhodné typy analýz
2. Doporučené vizualizace
3. Klíčové metriky k sledování

Vrať výsledek jako JSON.""",
            description="Analýza struktury průzkumu",
            category="survey",
            parameters={
                "survey_structure": "Struktura průzkumu",
                "respondent_count": "Počet respondentů",
                "survey_type": "Typ průzkumu"
            }
        ))
        
        # Parameter generation prompt
        self.add_template(PromptTemplate(
            name="parameter_generation",
            system_prompt="""Jsi expert na konfiguraci analýz a vizualizací.
Tvým úkolem je navrhnout optimální parametry na základě dat a požadavků.""",
            user_prompt="""Navrhni parametry pro následující analýzu:

Typ analýzy: {analysis_type}
Typ dat: {data_type}
Počet záznamů: {record_count}
Cílová skupina: {target_audience}

Požadavky:
{requirements}

Navrhni optimální hodnoty pro:
- Vizualizační parametry (barvy, velikosti, formáty)
- Analytické parametry (prahy, metody)
- Výstupní parametry (formáty, kvalita)

Vrať jako JSON s vysvětlením voleb.""",
            description="Generování parametrů pro analýzy",
            category="parameters",
            parameters={
                "analysis_type": "Typ analýzy",
                "data_type": "Typ dat",
                "record_count": "Počet záznamů",
                "target_audience": "Cílová skupina",
                "requirements": "Specifické požadavky"
            }
        ))
        
        # Translation prompt
        self.add_template(PromptTemplate(
            name="translation",
            system_prompt="""Jsi expert na překlady v kontextu průzkumů a analýz.
Překládej přesně a zachovávej odbornou terminologii.""",
            user_prompt="""Přelož následující text z {source_language} do {target_language}:

{text}

Kontext: {context}

Zachovej:
- Odbornou terminologii
- Formátování
- Číselné hodnoty
- Strukturu textu""",
            description="Překlad textů",
            category="translation",
            parameters={
                "text": "Text k překladu",
                "source_language": "Zdrojový jazyk",
                "target_language": "Cílový jazyk",
                "context": "Kontext překladu"
            }
        ))
    
    def _load_prompts_from_files(self):
        """Load prompts from YAML files"""
        for prompt_file in self.prompts_dir.glob("*.yaml"):
            try:
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                
                # Load prompts from file
                for prompt_name, prompt_data in data.items():
                    if isinstance(prompt_data, dict) and 'system_prompt' in prompt_data:
                        template = PromptTemplate(
                            name=prompt_name,
                            system_prompt=prompt_data.get('system_prompt', ''),
                            user_prompt=prompt_data.get('user_prompt', ''),
                            description=prompt_data.get('description', ''),
                            parameters=prompt_data.get('parameters', {}),
                            examples=prompt_data.get('examples', []),
                            category=prompt_data.get('category', 'file')
                        )
                        self.templates[prompt_name] = template
                        
            except Exception as e:
                logger.error(f"Failed to load prompts from {prompt_file}: {e}")
    
    def add_template(self, template: PromptTemplate):
        """Add prompt template"""
        self.templates[template.name] = template
        logger.debug(f"Added prompt template: {template.name}")
    
    def get_template(self, name: str) -> Optional[PromptTemplate]:
        """Get prompt template by name"""
        return self.templates.get(name)
    
    def get_templates_by_category(self, category: str) -> Dict[str, PromptTemplate]:
        """Get all templates in a category"""
        return {
            name: template for name, template in self.templates.items()
            if template.category == category
        }
    
    def list_templates(self) -> List[str]:
        """List all template names"""
        return list(self.templates.keys())
    
    def list_categories(self) -> List[str]:
        """List all categories"""
        return list(set(template.category for template in self.templates.values()))
    
    def format_prompt(
        self, 
        template_name: str, 
        parameters: Dict[str, Any],
        include_examples: bool = False
    ) -> Dict[str, str]:
        """
        Format prompt with parameters
        
        Args:
            template_name: Name of the template
            parameters: Parameters to substitute
            include_examples: Whether to include examples in the prompt
            
        Returns:
            Dictionary with formatted system_prompt and user_prompt
            
        Raises:
            ValueError: If template not found or parameters missing
        """
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
        
        try:
            # Format system prompt
            system_template = Template(template.system_prompt)
            formatted_system = system_template.safe_substitute(parameters)
            
            # Format user prompt
            user_template = Template(template.user_prompt)
            formatted_user = user_template.safe_substitute(parameters)
            
            # Add examples if requested
            if include_examples and template.examples:
                examples_text = "\n\nPříklady:\n"
                for i, example in enumerate(template.examples, 1):
                    examples_text += f"\nPříklad {i}:\n"
                    examples_text += f"Vstup: {example.get('input', '')}\n"
                    examples_text += f"Výstup: {example.get('output', '')}\n"
                
                formatted_user += examples_text
            
            return {
                'system_prompt': formatted_system,
                'user_prompt': formatted_user
            }
            
        except KeyError as e:
            raise ValueError(f"Missing parameter for template '{template_name}': {e}")
    
    def create_messages(
        self, 
        template_name: str, 
        parameters: Dict[str, Any],
        include_examples: bool = False
    ) -> List[Dict[str, str]]:
        """
        Create OpenAI messages format from template
        
        Args:
            template_name: Name of the template
            parameters: Parameters to substitute
            include_examples: Whether to include examples
            
        Returns:
            List of message dictionaries for OpenAI API
        """
        formatted = self.format_prompt(template_name, parameters, include_examples)
        
        messages = []
        
        if formatted['system_prompt'].strip():
            messages.append({
                'role': 'system',
                'content': formatted['system_prompt']
            })
        
        if formatted['user_prompt'].strip():
            messages.append({
                'role': 'user',
                'content': formatted['user_prompt']
            })
        
        return messages
    
    def save_template(self, template: PromptTemplate, filename: Optional[str] = None) -> bool:
        """
        Save template to YAML file
        
        Args:
            template: Template to save
            filename: Optional filename, defaults to template name
            
        Returns:
            True if saved successfully
        """
        try:
            filename = filename or f"{template.name}.yaml"
            filepath = self.prompts_dir / filename
            
            data = {
                template.name: {
                    'system_prompt': template.system_prompt,
                    'user_prompt': template.user_prompt,
                    'description': template.description,
                    'category': template.category,
                    'parameters': template.parameters,
                    'examples': template.examples
                }
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"Template '{template.name}' saved to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save template '{template.name}': {e}")
            return False
    
    def validate_template(self, template: PromptTemplate) -> List[str]:
        """
        Validate template for common issues
        
        Args:
            template: Template to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        if not template.name:
            errors.append("Template name is required")
        
        if not template.system_prompt and not template.user_prompt:
            errors.append("At least one of system_prompt or user_prompt is required")
        
        # Check for undefined parameters in prompts
        try:
            Template(template.system_prompt).safe_substitute({})
            Template(template.user_prompt).safe_substitute({})
        except Exception as e:
            errors.append(f"Template syntax error: {e}")
        
        return errors
    
    def get_template_info(self, name: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a template"""
        template = self.get_template(name)
        if not template:
            return None
        
        return {
            'name': template.name,
            'description': template.description,
            'category': template.category,
            'parameters': template.parameters,
            'examples_count': len(template.examples),
            'system_prompt_length': len(template.system_prompt),
            'user_prompt_length': len(template.user_prompt)
        }
