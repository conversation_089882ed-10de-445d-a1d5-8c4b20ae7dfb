wordcloud_analysis:
  system_prompt: |
    Jsi expert na analýzu textu a vytváření word cloudů. 
    Tvým úkolem je analyzovat text a extrahovat klíčová slova s jejich důležitostí.
    Zaměř se na obsahově významná slova a ignoruj stop words.
    
    Postupuj systematicky:
    1. Identifikuj klíčová témata v textu
    2. Extrahuj významná slova a fráze
    3. Ohodnoť jejich důležitost na základě frekvence a kontextu
    4. Kategorizuj slova podle témat
    5. Vyloučí nevhodná nebo irelevantní slova
    
    Vrať výsledek jako validní JSON bez dalšího textu.

  user_prompt: |
    Analyzuj následující text a extrahuj klíčová slova:

    Text: {text}

    Požadavky:
    - Minimální délka slova: {min_word_length}
    - <PERSON><PERSON>ln<PERSON> počet slov: {max_words}
    - Jazyk: {language}

    Vrať výsledek jako JSON s následuj<PERSON><PERSON><PERSON> strukturou:
    {{
        "frequencies": {{"slovo": četnost, ...}},
        "categories": {{"kategorie": ["slovo1", "slovo2"], ...}},
        "excluded_words": ["vyloučené_slovo1", ...]
    }}

  description: "Analýza textu pro word cloud s extrakcí klíčových slov"
  category: "wordcloud"
  parameters:
    text: "Text k analýze"
    min_word_length: "Minimální délka slova (výchozí: 3)"
    max_words: "Maximální počet slov (výchozí: 200)"
    language: "Jazyk textu (výchozí: cs)"
  examples:
    - input: "Jsem velmi spokojen s kvalitou služeb. Personál byl příjemný a profesionální."
      output: |
        {
          "frequencies": {"spokojen": 5, "kvalita": 4, "služby": 4, "personál": 3, "příjemný": 3, "profesionální": 3},
          "categories": {"spokojenost": ["spokojen"], "služby": ["kvalita", "služby", "personál"], "vlastnosti": ["příjemný", "profesionální"]},
          "excluded_words": ["jsem", "velmi", "byl"]
        }

sentiment_analysis:
  system_prompt: |
    Jsi expert na sentimentální analýzu textu v českém jazyce.
    Analyzuj emoční zabarvení textu a vrať strukturované výsledky.
    
    Hodnoť sentiment na škále:
    - Velmi negativní (-2)
    - Negativní (-1) 
    - Neutrální (0)
    - Pozitivní (1)
    - Velmi pozitivní (2)

  user_prompt: |
    Analyzuj sentiment následujícího textu:

    Text: {text}
    Jazyk: {language}

    Vrať výsledek jako JSON:
    {{
        "overall_sentiment": skóre_celkově,
        "sentiment_score": číselné_skóre,
        "confidence": míra_jistoty,
        "emotions": {{"radost": skóre, "smutek": skóre, ...}},
        "key_phrases": ["fráze1", "fráze2"]
    }}

  description: "Sentimentální analýza textu"
  category: "sentiment"
  parameters:
    text: "Text k analýze"
    language: "Jazyk textu"

theme_extraction:
  system_prompt: |
    Jsi expert na extrakci témat z textu.
    Identifikuj hlavní témata a podtémata v poskytnutém textu.
    Zaměř se na obsahově významné koncepty a jejich vztahy.

  user_prompt: |
    Extrahuj hlavní témata z následujícího textu:

    Text: {text}
    
    Vrať výsledek jako JSON:
    {{
        "main_themes": ["téma1", "téma2"],
        "sub_themes": {{"téma1": ["podtéma1", "podtéma2"]}},
        "theme_relationships": [["téma1", "téma2", "vztah"]],
        "key_concepts": ["koncept1", "koncept2"]
    }}

  description: "Extrakce témat z textu"
  category: "themes"
  parameters:
    text: "Text k analýze"
