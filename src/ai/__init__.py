"""
AI Module for LimWrapp
Převzato a adaptováno z existujících projektů

Provides comprehensive AI functionality including:
- Enhanced OpenAI client with rate limiting and caching
- WordCloud generation with AI enhancement
- Parameter management system
- Prompt management and templates
- Scenario generation for analysis planning
- Central AI manager for coordination
"""

from .enhanced_openai_client import (
    EnhancedOpenAIClient,
    APIRequest,
    APIResponse,
    ModelConfig,
    UsageStats
)

from .wordcloud_generator import (
    WordCloudGenerator,
    WordCloudConfig,
    WordCloudResult,
    create_wordcloud
)

from .parameter_manager import (
    ParameterManager,
    ParameterDefinition,
    ParameterSchema
)

from .prompt_manager import (
    PromptManager,
    PromptTemplate
)

from .scenario_generator import (
    ScenarioGenerator,
    AnalysisScenario,
    AnalysisStep,
    SurveyCharacteristics,
    AnalysisType,
    create_survey_characteristics
)

from .ai_manager import AIManager

__all__ = [
    # Main manager
    'AIManager',
    
    # OpenAI client
    'EnhancedOpenAIClient',
    'APIRequest',
    'APIResponse',
    'ModelConfig',
    'UsageStats',
    
    # WordCloud
    'WordCloudGenerator',
    'WordCloudConfig',
    'WordCloudResult',
    'create_wordcloud',
    
    # Parameter management
    'ParameterManager',
    'ParameterDefinition',
    'ParameterSchema',
    
    # Prompt management
    'PromptManager',
    'PromptTemplate',
    
    # Scenario generation
    'ScenarioGenerator',
    'AnalysisScenario',
    'AnalysisStep',
    'SurveyCharacteristics',
    'AnalysisType',
    'create_survey_characteristics'
]

# Version info
__version__ = '1.0.0'
__author__ = 'LimWrapp Team'
__description__ = 'AI components for LimeSurvey data analysis'
