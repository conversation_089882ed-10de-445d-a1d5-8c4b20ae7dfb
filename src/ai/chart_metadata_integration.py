"""
Chart Metadata Integration
Integrace AI analýz do chart metadat pro Datawrapper a externí generátory
BEZPEČNÝ - rozšiřuje metadata bez ovlivnění základní funkcionality
"""

import logging
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import json

logger = logging.getLogger(__name__)


@dataclass
class MetadataPlacement:
    """Konfigurace umístění metadat v grafu"""
    location: str  # 'footer', 'header', 'subtitle', 'annotation'
    text: str
    style: Dict[str, Any] = None
    enabled: bool = True
    
    def __post_init__(self):
        if self.style is None:
            self.style = {}


@dataclass
class ChartMetadata:
    """Kompletní metadata pro graf"""
    title: Optional[str] = None
    subtitle: Optional[str] = None
    intro: Optional[str] = None
    notes: Optional[str] = None
    source: Optional[str] = None
    ai_analysis: Optional[Dict[str, Any]] = None
    custom_annotations: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.custom_annotations is None:
            self.custom_annotations = []


class ChartMetadataIntegrator:
    """
    Integrátor AI analýz do chart metadat
    BEZPEČNÝ - neovlivňuje stávající Datawrapper workflow
    """
    
    # Mapování umístění pro různé generátory
    LOCATION_MAPPING = {
        'datawrapper': {
            'footer': 'annotate.notes',
            'header': 'annotate.intro', 
            'subtitle': 'title.subtitle',
            'source': 'annotate.byline'
        },
        'internal_wordcloud': {
            'footer': 'metadata.footer_text',
            'header': 'metadata.header_text',
            'subtitle': 'metadata.subtitle'
        },
        'internal_table': {
            'footer': 'table.footer',
            'header': 'table.header',
            'caption': 'table.caption'
        }
    }
    
    # Styly pro různé typy analýz
    ANALYSIS_STYLES = {
        'summary': {
            'font_size': '12px',
            'color': '#666666',
            'style': 'italic'
        },
        'insight': {
            'font_size': '11px', 
            'color': '#333333',
            'style': 'normal'
        },
        'warning': {
            'font_size': '11px',
            'color': '#ff6b35',
            'style': 'bold'
        },
        'recommendation': {
            'font_size': '12px',
            'color': '#2e8b57',
            'style': 'normal'
        }
    }
    
    def __init__(self, ai_analyst=None):
        """
        Initialize metadata integrator
        
        Args:
            ai_analyst: AI data analyst instance
        """
        self.ai_analyst = ai_analyst
        self.metadata_cache: Dict[str, ChartMetadata] = {}
        
        logger.info("ChartMetadataIntegrator initialized")
    
    def integrate_ai_analysis(
        self,
        chart_config: Dict[str, Any],
        chart_data: Dict[str, Any],
        analysis_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Integruje AI analýzu do chart konfigurace
        
        Args:
            chart_config: Konfigurace grafu
            chart_data: Data grafu
            analysis_config: Konfigurace AI analýzy
            
        Returns:
            Aktualizovaná konfigurace grafu
        """
        if not self.ai_analyst or not self.ai_analyst.is_available():
            logger.warning("AI analyst not available for metadata integration")
            return chart_config
        
        try:
            # Default analysis config
            if analysis_config is None:
                analysis_config = {
                    'enabled': True,
                    'location': 'footer',
                    'include_insights': True,
                    'include_recommendations': False
                }
            
            if not analysis_config.get('enabled', True):
                return chart_config
            
            # Get AI analysis
            chart_type = chart_config.get('chart_type', 'column')
            question_context = chart_config.get('question_context', {})
            
            analysis = self.ai_analyst.analyze_chart_data(
                chart_data, 
                chart_type, 
                question_context
            )
            
            if not analysis:
                logger.warning("No AI analysis available")
                return chart_config
            
            # Integrate analysis into metadata
            updated_config = self._embed_analysis_metadata(
                chart_config, 
                analysis, 
                analysis_config
            )
            
            logger.info(f"Integrated AI analysis into {chart_type} chart metadata")
            return updated_config
            
        except Exception as e:
            logger.error(f"Failed to integrate AI analysis: {e}")
            return chart_config
    
    def _embed_analysis_metadata(
        self,
        chart_config: Dict[str, Any],
        analysis,
        analysis_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Embed analysis into chart metadata"""
        generator = chart_config.get('generator', 'datawrapper')
        location = analysis_config.get('location', 'footer')
        
        # Prepare metadata text
        metadata_text = self._format_analysis_text(analysis, analysis_config)
        
        # Get location mapping for generator
        location_mapping = self.LOCATION_MAPPING.get(generator, self.LOCATION_MAPPING['datawrapper'])
        metadata_path = location_mapping.get(location, location_mapping['footer'])
        
        # Embed metadata based on generator type
        if generator == 'datawrapper':
            chart_config = self._embed_datawrapper_metadata(
                chart_config, metadata_text, metadata_path
            )
        else:
            chart_config = self._embed_internal_metadata(
                chart_config, metadata_text, metadata_path
            )
        
        # Add AI analysis metadata
        chart_config['ai_metadata'] = {
            'analysis_summary': analysis.summary,
            'key_insights': analysis.key_insights,
            'confidence': analysis.confidence,
            'model_used': analysis.model_used,
            'generated_at': analysis.timestamp,
            'location': location
        }
        
        return chart_config
    
    def _format_analysis_text(self, analysis, config: Dict[str, Any]) -> str:
        """Format analysis into readable text"""
        parts = []
        
        # Add summary
        if analysis.summary:
            parts.append(analysis.summary)
        
        # Add insights if requested
        if config.get('include_insights', True) and analysis.key_insights:
            insights_text = " • ".join(analysis.key_insights[:2])  # Max 2 insights
            if insights_text:
                parts.append(f"Klíčová pozorování: {insights_text}")
        
        # Add recommendations if requested
        if config.get('include_recommendations', False) and hasattr(analysis, 'recommendations'):
            if analysis.recommendations:
                rec_text = analysis.recommendations[0]  # First recommendation
                parts.append(f"Doporučení: {rec_text}")
        
        # Add confidence if low
        if analysis.confidence < 0.7:
            parts.append("(Automatická analýza)")
        
        return " | ".join(parts)
    
    def _embed_datawrapper_metadata(
        self, 
        chart_config: Dict[str, Any], 
        metadata_text: str, 
        metadata_path: str
    ) -> Dict[str, Any]:
        """Embed metadata for Datawrapper charts"""
        # Parse metadata path (e.g., 'annotate.notes')
        path_parts = metadata_path.split('.')
        
        # Ensure nested structure exists
        current = chart_config
        for part in path_parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]
        
        # Set the metadata
        current[path_parts[-1]] = metadata_text
        
        return chart_config
    
    def _embed_internal_metadata(
        self, 
        chart_config: Dict[str, Any], 
        metadata_text: str, 
        metadata_path: str
    ) -> Dict[str, Any]:
        """Embed metadata for internal generators"""
        # Parse metadata path
        path_parts = metadata_path.split('.')
        
        # Ensure nested structure exists
        current = chart_config
        for part in path_parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]
        
        # Set the metadata
        current[path_parts[-1]] = metadata_text
        
        return chart_config
    
    def create_custom_metadata(
        self,
        title: str = None,
        subtitle: str = None,
        intro: str = None,
        notes: str = None,
        source: str = None
    ) -> ChartMetadata:
        """Create custom metadata object"""
        return ChartMetadata(
            title=title,
            subtitle=subtitle,
            intro=intro,
            notes=notes,
            source=source
        )
    
    def apply_metadata_template(
        self,
        chart_config: Dict[str, Any],
        template_name: str,
        variables: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Apply predefined metadata template"""
        templates = self._get_metadata_templates()
        
        if template_name not in templates:
            logger.warning(f"Metadata template '{template_name}' not found")
            return chart_config
        
        template = templates[template_name]
        variables = variables or {}
        
        # Apply template
        for location, text_template in template.items():
            try:
                formatted_text = text_template.format(**variables)
                
                # Apply to chart config
                if location == 'footer':
                    chart_config.setdefault('annotate', {})['notes'] = formatted_text
                elif location == 'header':
                    chart_config.setdefault('annotate', {})['intro'] = formatted_text
                elif location == 'subtitle':
                    chart_config.setdefault('title', {})['subtitle'] = formatted_text
                    
            except KeyError as e:
                logger.error(f"Missing variable in template: {e}")
        
        return chart_config
    
    def _get_metadata_templates(self) -> Dict[str, Dict[str, str]]:
        """Get predefined metadata templates"""
        return {
            'survey_standard': {
                'footer': 'Průzkum: {survey_name} | Respondenti: {response_count} | Datum: {date}',
                'source': 'Zdroj: {organization}'
            },
            'satisfaction_analysis': {
                'footer': 'Analýza spokojenosti | Průměr: {average_score} | Respondenti: {response_count}',
                'subtitle': 'Hodnocení na škále {scale_range}'
            },
            'text_analysis': {
                'footer': 'Analýza textových odpovědí | Zpracováno: {processed_responses} z {total_responses}',
                'subtitle': 'Nejčastější témata a klíčová slova'
            },
            'comparison': {
                'footer': 'Srovnání kategorií | Nejvyšší: {max_category} ({max_value}) | Nejnižší: {min_category} ({min_value})',
                'subtitle': 'Rozdíly mezi skupinami'
            }
        }
    
    def validate_metadata(self, chart_config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean metadata"""
        validation_result = {
            'valid': True,
            'warnings': [],
            'errors': []
        }
        
        # Check metadata length limits
        metadata_limits = {
            'title': 100,
            'subtitle': 150,
            'notes': 500,
            'intro': 300
        }
        
        annotate = chart_config.get('annotate', {})
        title_info = chart_config.get('title', {})
        
        # Check lengths
        for field, limit in metadata_limits.items():
            if field in ['title', 'subtitle']:
                text = title_info.get(field, '')
            else:
                text = annotate.get(field, '')
            
            if text and len(text) > limit:
                validation_result['warnings'].append(
                    f"{field} exceeds recommended length ({len(text)} > {limit})"
                )
        
        # Check for HTML/special characters
        for field in ['notes', 'intro']:
            text = annotate.get(field, '')
            if text and ('<' in text or '>' in text):
                validation_result['warnings'].append(
                    f"{field} contains HTML-like content"
                )
        
        return validation_result
    
    def export_metadata_summary(self, chart_configs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Export summary of metadata across multiple charts"""
        summary = {
            'total_charts': len(chart_configs),
            'charts_with_ai_analysis': 0,
            'charts_with_custom_metadata': 0,
            'metadata_locations': {},
            'ai_models_used': set(),
            'average_confidence': 0.0
        }
        
        confidence_scores = []
        
        for config in chart_configs:
            # Check for AI analysis
            if 'ai_metadata' in config:
                summary['charts_with_ai_analysis'] += 1
                ai_meta = config['ai_metadata']
                
                if 'model_used' in ai_meta:
                    summary['ai_models_used'].add(ai_meta['model_used'])
                
                if 'confidence' in ai_meta:
                    confidence_scores.append(ai_meta['confidence'])
                
                location = ai_meta.get('location', 'unknown')
                summary['metadata_locations'][location] = summary['metadata_locations'].get(location, 0) + 1
            
            # Check for custom metadata
            if 'annotate' in config or 'title' in config:
                summary['charts_with_custom_metadata'] += 1
        
        # Calculate average confidence
        if confidence_scores:
            summary['average_confidence'] = sum(confidence_scores) / len(confidence_scores)
        
        summary['ai_models_used'] = list(summary['ai_models_used'])
        
        return summary


def create_metadata_integrator(ai_analyst=None) -> ChartMetadataIntegrator:
    """
    Factory function to create metadata integrator
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    return ChartMetadataIntegrator(ai_analyst)
