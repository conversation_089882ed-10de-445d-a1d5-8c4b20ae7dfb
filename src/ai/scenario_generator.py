"""
Scenario Generator for Analysis Planning
Převzato z existujícího projektu a adaptováno pro LimWrapp
Generates analysis scenarios based on survey characteristics
"""

import logging
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)


class AnalysisType(Enum):
    """Types of analysis available"""
    DESCRIPTIVE = "descriptive"
    FREQUENCY = "frequency"
    CORRELATION = "correlation"
    REGRESSION = "regression"
    CLUSTERING = "clustering"
    TEXT_ANALYSIS = "text_analysis"
    WORDCLOUD = "wordcloud"
    SENTIMENT = "sentiment"
    RELIABILITY = "reliability"
    FACTOR_ANALYSIS = "factor_analysis"


@dataclass
class SurveyCharacteristics:
    """Characteristics of survey data"""
    total_responses: int
    total_questions: int
    question_types: Dict[str, int]  # type -> count
    has_likert_scales: bool
    has_text_responses: bool
    has_demographic_data: bool
    response_rate: Optional[float] = None
    completion_rate: Optional[float] = None
    survey_topic: Optional[str] = None
    target_audience: Optional[str] = None


@dataclass
class AnalysisStep:
    """Individual step in an analysis scenario"""
    step_id: str
    analysis_type: AnalysisType
    title: str
    description: str
    target_variables: List[str]
    parameters: Dict[str, Any]
    dependencies: List[str]
    expected_output: str
    estimated_time: int  # in minutes


@dataclass
class AnalysisScenario:
    """Complete analysis scenario with multiple steps"""
    scenario_id: str
    title: str
    description: str
    objective: str
    target_audience: str
    complexity_level: str  # 'basic', 'intermediate', 'advanced'
    estimated_duration: int  # in minutes
    steps: List[AnalysisStep]
    prerequisites: List[str]
    expected_insights: List[str]
    created_at: datetime
    created_by: str = "AI"


class ScenarioGenerator:
    """Generates analysis scenarios based on survey characteristics"""
    
    def __init__(self, ai_client=None, prompt_manager=None):
        """
        Initialize scenario generator
        
        Args:
            ai_client: Optional AI client for enhanced scenario generation
            prompt_manager: Optional prompt manager for AI prompts
        """
        self.ai_client = ai_client
        self.prompt_manager = prompt_manager
        
        # Predefined scenario templates
        self.scenario_templates = {
            'basic_descriptive': {
                'title': 'Základní popisná analýza',
                'description': 'Komplexní přehled základních charakteristik dat',
                'complexity': 'basic',
                'min_responses': 10
            },
            'satisfaction_analysis': {
                'title': 'Analýza spokojenosti',
                'description': 'Detailní analýza úrovně spokojenosti respondentů',
                'complexity': 'intermediate',
                'min_responses': 30
            },
            'segmentation_analysis': {
                'title': 'Segmentační analýza',
                'description': 'Identifikace různých skupin respondentů',
                'complexity': 'advanced',
                'min_responses': 100
            },
            'text_analysis': {
                'title': 'Analýza textových odpovědí',
                'description': 'AI-powered analýza otevřených odpovědí',
                'complexity': 'intermediate',
                'min_responses': 20
            },
            'reliability_assessment': {
                'title': 'Hodnocení spolehlivosti škál',
                'description': 'Analýza psychometrických vlastností měřicích nástrojů',
                'complexity': 'intermediate',
                'min_responses': 50
            }
        }
    
    def generate_scenarios(
        self,
        survey_characteristics: SurveyCharacteristics,
        user_objectives: Optional[List[str]] = None,
        complexity_preference: str = 'auto',
        max_scenarios: int = 3
    ) -> List[AnalysisScenario]:
        """
        Generate analysis scenarios based on survey characteristics
        
        Args:
            survey_characteristics: Characteristics of the survey data
            user_objectives: Optional user-specified objectives
            complexity_preference: Preferred complexity level
            max_scenarios: Maximum number of scenarios to generate
            
        Returns:
            List of generated analysis scenarios
        """
        try:
            # Determine appropriate complexity level
            if complexity_preference == 'auto':
                complexity_level = self._determine_complexity_level(survey_characteristics)
            else:
                complexity_level = complexity_preference
            
            scenarios = []
            
            # Generate AI-powered scenarios if available
            if self.ai_client and self.prompt_manager:
                ai_scenarios = self._generate_ai_scenarios(
                    survey_characteristics,
                    user_objectives,
                    complexity_level,
                    max_scenarios // 2
                )
                scenarios.extend(ai_scenarios)
            
            # Add template-based scenarios
            template_scenarios = self._generate_template_scenarios(
                survey_characteristics,
                complexity_level
            )
            scenarios.extend(template_scenarios)
            
            # Limit to max_scenarios
            return scenarios[:max_scenarios]
            
        except Exception as e:
            logger.error(f"Failed to generate scenarios: {e}")
            # Return basic fallback scenario
            return [self._create_fallback_scenario(survey_characteristics)]
    
    def _determine_complexity_level(self, characteristics: SurveyCharacteristics) -> str:
        """Determine appropriate complexity level based on data characteristics"""
        score = 0
        
        # Response count factor
        if characteristics.total_responses >= 100:
            score += 2
        elif characteristics.total_responses >= 50:
            score += 1
        
        # Question complexity factor
        if characteristics.total_questions >= 20:
            score += 1
        
        # Data type diversity factor
        if characteristics.has_likert_scales:
            score += 1
        if characteristics.has_text_responses:
            score += 1
        if characteristics.has_demographic_data:
            score += 1
        
        # Determine level
        if score >= 5:
            return 'advanced'
        elif score >= 3:
            return 'intermediate'
        else:
            return 'basic'
    
    def _generate_ai_scenarios(
        self,
        characteristics: SurveyCharacteristics,
        user_objectives: Optional[List[str]],
        complexity_level: str,
        max_scenarios: int
    ) -> List[AnalysisScenario]:
        """Generate scenarios using AI"""
        try:
            # Prepare prompt parameters
            prompt_params = {
                'survey_structure': self._format_survey_structure(characteristics),
                'respondent_count': characteristics.total_responses,
                'survey_type': characteristics.survey_topic or 'obecný průzkum',
                'complexity_level': complexity_level,
                'user_objectives': user_objectives or [],
                'max_scenarios': max_scenarios
            }
            
            # Get AI response
            messages = self.prompt_manager.create_messages(
                'survey_analysis',
                prompt_params
            )
            
            response = self.ai_client.chat_completion(
                messages=messages,
                temperature=0.7
            )
            
            # Parse AI response and create scenarios
            return self._parse_ai_scenarios(response.content, characteristics)
            
        except Exception as e:
            logger.error(f"AI scenario generation failed: {e}")
            return []
    
    def _generate_template_scenarios(
        self,
        characteristics: SurveyCharacteristics,
        complexity_level: str
    ) -> List[AnalysisScenario]:
        """Generate scenarios from predefined templates"""
        scenarios = []
        
        for template_id, template in self.scenario_templates.items():
            # Check if template is appropriate
            if (template['complexity'] == complexity_level and
                characteristics.total_responses >= template['min_responses']):
                
                # Check specific requirements
                if template_id == 'text_analysis' and not characteristics.has_text_responses:
                    continue
                if template_id == 'reliability_assessment' and not characteristics.has_likert_scales:
                    continue
                
                scenario = self._create_scenario_from_template(
                    template_id,
                    template,
                    characteristics
                )
                scenarios.append(scenario)
        
        return scenarios
    
    def _create_scenario_from_template(
        self,
        template_id: str,
        template: Dict[str, Any],
        characteristics: SurveyCharacteristics
    ) -> AnalysisScenario:
        """Create scenario from template"""
        steps = []
        
        if template_id == 'basic_descriptive':
            steps = [
                AnalysisStep(
                    step_id="desc_1",
                    analysis_type=AnalysisType.DESCRIPTIVE,
                    title="Základní statistiky",
                    description="Výpočet základních popisných statistik",
                    target_variables=["all_numeric"],
                    parameters={"include_outliers": True},
                    dependencies=[],
                    expected_output="Tabulka základních statistik",
                    estimated_time=10
                ),
                AnalysisStep(
                    step_id="desc_2",
                    analysis_type=AnalysisType.FREQUENCY,
                    title="Frekvenční analýza",
                    description="Analýza kategorických proměnných",
                    target_variables=["all_categorical"],
                    parameters={"show_percentages": True},
                    dependencies=[],
                    expected_output="Frekvenční tabulky",
                    estimated_time=15
                )
            ]
        
        elif template_id == 'text_analysis':
            steps = [
                AnalysisStep(
                    step_id="text_1",
                    analysis_type=AnalysisType.WORDCLOUD,
                    title="Word Cloud analýza",
                    description="Vizualizace nejčastějších slov",
                    target_variables=["all_text"],
                    parameters={"max_words": 200, "use_ai": True},
                    dependencies=[],
                    expected_output="Word cloud vizualizace",
                    estimated_time=20
                ),
                AnalysisStep(
                    step_id="text_2",
                    analysis_type=AnalysisType.SENTIMENT,
                    title="Sentimentální analýza",
                    description="Analýza emočního zabarvení odpovědí",
                    target_variables=["all_text"],
                    parameters={"language": "cs"},
                    dependencies=["text_1"],
                    expected_output="Sentimentální skóre",
                    estimated_time=25
                )
            ]
        
        elif template_id == 'satisfaction_analysis':
            steps = [
                AnalysisStep(
                    step_id="sat_1",
                    analysis_type=AnalysisType.DESCRIPTIVE,
                    title="Analýza spokojenosti",
                    description="Základní statistiky spokojenosti",
                    target_variables=["satisfaction_scales"],
                    parameters={"calculate_means": True},
                    dependencies=[],
                    expected_output="Průměrné hodnoty spokojenosti",
                    estimated_time=15
                ),
                AnalysisStep(
                    step_id="sat_2",
                    analysis_type=AnalysisType.CORRELATION,
                    title="Korelační analýza",
                    description="Vztahy mezi faktory spokojenosti",
                    target_variables=["satisfaction_scales"],
                    parameters={"method": "pearson"},
                    dependencies=["sat_1"],
                    expected_output="Korelační matice",
                    estimated_time=20
                )
            ]
        
        # Calculate total duration
        total_duration = sum(step.estimated_time for step in steps)
        
        return AnalysisScenario(
            scenario_id=f"template_{template_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            title=template['title'],
            description=template['description'],
            objective=f"Provést {template['title'].lower()} dat z průzkumu",
            target_audience="Analytici a výzkumníci",
            complexity_level=template['complexity'],
            estimated_duration=total_duration,
            steps=steps,
            prerequisites=["Validní data z průzkumu"],
            expected_insights=[f"Poznatky z {template['title'].lower()}"],
            created_at=datetime.now()
        )

    def _create_fallback_scenario(self, characteristics: SurveyCharacteristics) -> AnalysisScenario:
        """Create basic fallback scenario when other methods fail"""
        steps = [
            AnalysisStep(
                step_id="fallback_1",
                analysis_type=AnalysisType.DESCRIPTIVE,
                title="Základní přehled dat",
                description="Základní statistický přehled dat",
                target_variables=["all"],
                parameters={},
                dependencies=[],
                expected_output="Základní statistiky",
                estimated_time=15
            )
        ]

        return AnalysisScenario(
            scenario_id=f"fallback_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            title="Základní analýza",
            description="Základní analýza dat z průzkumu",
            objective="Získat základní přehled o datech",
            target_audience="Všichni uživatelé",
            complexity_level="basic",
            estimated_duration=15,
            steps=steps,
            prerequisites=["Data z průzkumu"],
            expected_insights=["Základní charakteristiky dat"],
            created_at=datetime.now()
        )

    def _format_survey_structure(self, characteristics: SurveyCharacteristics) -> str:
        """Format survey structure for AI prompt"""
        structure = f"""
        Počet respondentů: {characteristics.total_responses}
        Počet otázek: {characteristics.total_questions}
        Typy otázek: {characteristics.question_types}
        Likertovy škály: {'Ano' if characteristics.has_likert_scales else 'Ne'}
        Textové odpovědi: {'Ano' if characteristics.has_text_responses else 'Ne'}
        Demografická data: {'Ano' if characteristics.has_demographic_data else 'Ne'}
        """

        if characteristics.response_rate:
            structure += f"Míra odpovědí: {characteristics.response_rate:.1%}\n"
        if characteristics.completion_rate:
            structure += f"Míra dokončení: {characteristics.completion_rate:.1%}\n"
        if characteristics.survey_topic:
            structure += f"Téma průzkumu: {characteristics.survey_topic}\n"
        if characteristics.target_audience:
            structure += f"Cílová skupina: {characteristics.target_audience}\n"

        return structure.strip()

    def _parse_ai_scenarios(self, ai_response: str, characteristics: SurveyCharacteristics) -> List[AnalysisScenario]:
        """Parse AI response and create scenarios"""
        try:
            import json

            # Try to extract JSON from AI response
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1

            if start_idx == -1 or end_idx == 0:
                logger.warning("No JSON found in AI response")
                return []

            json_str = ai_response[start_idx:end_idx]
            data = json.loads(json_str)

            scenarios = []
            for scenario_data in data.get('scenarios', []):
                scenario = self._create_scenario_from_ai_data(scenario_data, characteristics)
                if scenario:
                    scenarios.append(scenario)

            return scenarios

        except Exception as e:
            logger.error(f"Failed to parse AI scenarios: {e}")
            return []

    def _create_scenario_from_ai_data(self, data: Dict[str, Any], characteristics: SurveyCharacteristics) -> Optional[AnalysisScenario]:
        """Create scenario from AI-generated data"""
        try:
            steps = []
            for step_data in data.get('steps', []):
                step = AnalysisStep(
                    step_id=step_data.get('id', f"ai_step_{len(steps)}"),
                    analysis_type=AnalysisType(step_data.get('type', 'descriptive')),
                    title=step_data.get('title', ''),
                    description=step_data.get('description', ''),
                    target_variables=step_data.get('variables', []),
                    parameters=step_data.get('parameters', {}),
                    dependencies=step_data.get('dependencies', []),
                    expected_output=step_data.get('output', ''),
                    estimated_time=step_data.get('time', 15)
                )
                steps.append(step)

            return AnalysisScenario(
                scenario_id=f"ai_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                title=data.get('title', 'AI Generated Scenario'),
                description=data.get('description', ''),
                objective=data.get('objective', ''),
                target_audience=data.get('audience', 'Analytici'),
                complexity_level=data.get('complexity', 'intermediate'),
                estimated_duration=sum(step.estimated_time for step in steps),
                steps=steps,
                prerequisites=data.get('prerequisites', []),
                expected_insights=data.get('insights', []),
                created_at=datetime.now(),
                created_by="AI"
            )

        except Exception as e:
            logger.error(f"Failed to create scenario from AI data: {e}")
            return None

    def get_scenario_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get available scenario templates"""
        return self.scenario_templates.copy()

    def add_custom_template(self, template_id: str, template_data: Dict[str, Any]):
        """Add custom scenario template"""
        self.scenario_templates[template_id] = template_data
        logger.info(f"Added custom template: {template_id}")

    def validate_scenario(self, scenario: AnalysisScenario) -> List[str]:
        """Validate scenario for common issues"""
        errors = []

        if not scenario.steps:
            errors.append("Scenario must have at least one step")

        # Check for circular dependencies
        step_ids = {step.step_id for step in scenario.steps}
        for step in scenario.steps:
            for dep in step.dependencies:
                if dep not in step_ids:
                    errors.append(f"Step {step.step_id} depends on non-existent step {dep}")

        # Check for reasonable time estimates
        if scenario.estimated_duration <= 0:
            errors.append("Scenario duration must be positive")

        return errors


def create_survey_characteristics(
    total_responses: int,
    total_questions: int,
    question_types: Dict[str, int],
    **kwargs
) -> SurveyCharacteristics:
    """
    Convenience function to create SurveyCharacteristics

    Args:
        total_responses: Number of survey responses
        total_questions: Number of questions in survey
        question_types: Dictionary of question type counts
        **kwargs: Additional characteristics

    Returns:
        SurveyCharacteristics object
    """
    return SurveyCharacteristics(
        total_responses=total_responses,
        total_questions=total_questions,
        question_types=question_types,
        has_likert_scales=kwargs.get('has_likert_scales', False),
        has_text_responses=kwargs.get('has_text_responses', False),
        has_demographic_data=kwargs.get('has_demographic_data', False),
        response_rate=kwargs.get('response_rate'),
        completion_rate=kwargs.get('completion_rate'),
        survey_topic=kwargs.get('survey_topic'),
        target_audience=kwargs.get('target_audience')
    )
