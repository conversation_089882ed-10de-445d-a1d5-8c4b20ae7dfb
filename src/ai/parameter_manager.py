"""
Parameter Management System
Převzato z existujícího projektu a adaptováno pro LimWrapp
Manages parameters for analysis and visualizations with AI assistance
"""

import json
import logging
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from copy import deepcopy
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class ParameterDefinition:
    """Definition of a parameter"""
    name: str
    type: str  # 'int', 'float', 'str', 'bool', 'list', 'dict'
    default_value: Any
    description: str
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    allowed_values: Optional[List[Any]] = None
    category: str = 'general'
    
    def validate(self, value: Any) -> bool:
        """Validate parameter value"""
        try:
            # Type validation
            if self.type == 'int':
                value = int(value)
                if self.min_value is not None and value < self.min_value:
                    return False
                if self.max_value is not None and value > self.max_value:
                    return False
            elif self.type == 'float':
                value = float(value)
                if self.min_value is not None and value < self.min_value:
                    return False
                if self.max_value is not None and value > self.max_value:
                    return False
            elif self.type == 'str':
                value = str(value)
            elif self.type == 'bool':
                if not isinstance(value, bool):
                    return False
            elif self.type == 'list':
                if not isinstance(value, list):
                    return False
            elif self.type == 'dict':
                if not isinstance(value, dict):
                    return False
            
            # Allowed values validation
            if self.allowed_values and value not in self.allowed_values:
                return False
                
            return True
        except (ValueError, TypeError):
            return False


class ParameterSchema:
    """Schema for parameter definitions"""
    
    def __init__(self):
        self.parameters: Dict[str, ParameterDefinition] = {}
        self._load_default_schema()
    
    def _load_default_schema(self):
        """Load default parameter schema"""
        # Visualization parameters
        self.add_parameter(ParameterDefinition(
            name='viz_font_size',
            type='int',
            default_value=12,
            description='Font size for visualizations',
            min_value=8,
            max_value=24,
            category='visualization'
        ))
        
        self.add_parameter(ParameterDefinition(
            name='viz_color_scheme',
            type='str',
            default_value='default',
            description='Color scheme for visualizations',
            allowed_values=['default', 'blue', 'red', 'green', 'purple'],
            category='visualization'
        ))
        
        self.add_parameter(ParameterDefinition(
            name='viz_width',
            type='int',
            default_value=800,
            description='Width of visualizations in pixels',
            min_value=400,
            max_value=2000,
            category='visualization'
        ))
        
        self.add_parameter(ParameterDefinition(
            name='viz_height',
            type='int',
            default_value=600,
            description='Height of visualizations in pixels',
            min_value=300,
            max_value=1500,
            category='visualization'
        ))
        
        # Analysis parameters
        self.add_parameter(ParameterDefinition(
            name='analysis_significance_level',
            type='float',
            default_value=0.05,
            description='Statistical significance level',
            min_value=0.001,
            max_value=0.1,
            category='analysis'
        ))
        
        self.add_parameter(ParameterDefinition(
            name='analysis_min_sample_size',
            type='int',
            default_value=30,
            description='Minimum sample size for analysis',
            min_value=10,
            max_value=1000,
            category='analysis'
        ))
        
        # WordCloud parameters
        self.add_parameter(ParameterDefinition(
            name='wordcloud_max_words',
            type='int',
            default_value=200,
            description='Maximum number of words in word cloud',
            min_value=50,
            max_value=500,
            category='wordcloud'
        ))
        
        self.add_parameter(ParameterDefinition(
            name='wordcloud_min_word_length',
            type='int',
            default_value=3,
            description='Minimum word length for word cloud',
            min_value=2,
            max_value=10,
            category='wordcloud'
        ))
        
        # AI parameters
        self.add_parameter(ParameterDefinition(
            name='ai_temperature',
            type='float',
            default_value=0.7,
            description='AI model temperature',
            min_value=0.0,
            max_value=2.0,
            category='ai'
        ))
        
        self.add_parameter(ParameterDefinition(
            name='ai_max_tokens',
            type='int',
            default_value=1000,
            description='Maximum tokens for AI responses',
            min_value=100,
            max_value=4000,
            category='ai'
        ))
    
    def add_parameter(self, param_def: ParameterDefinition):
        """Add parameter definition"""
        self.parameters[param_def.name] = param_def
    
    def get_parameter(self, name: str) -> Optional[ParameterDefinition]:
        """Get parameter definition"""
        return self.parameters.get(name)
    
    def get_parameters_by_category(self, category: str) -> Dict[str, ParameterDefinition]:
        """Get all parameters in a category"""
        return {
            name: param for name, param in self.parameters.items()
            if param.category == category
        }
    
    def get_all_categories(self) -> List[str]:
        """Get all parameter categories"""
        return list(set(param.category for param in self.parameters.values()))


class ParameterManager:
    """Manages parameters for analysis and visualizations"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        Initialize parameter manager
        
        Args:
            config_dir: Directory for storing parameter configurations
        """
        self.schema = ParameterSchema()
        self.config_dir = Path(config_dir) if config_dir else Path("config/parameters")
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Current parameter values
        self.current_parameters: Dict[str, Any] = {}
        
        # Parameter history for undo functionality
        self.parameter_history: List[Dict[str, Any]] = []
        self.max_history_size = 50
        
        # Load default parameters
        self._load_defaults()
        
        logger.info(f"ParameterManager initialized with {len(self.current_parameters)} parameters")
    
    def _load_defaults(self):
        """Load default parameter values"""
        for name, param_def in self.schema.parameters.items():
            self.current_parameters[name] = param_def.default_value
    
    def get_parameter(self, name: str) -> Any:
        """
        Get parameter value
        
        Args:
            name: Parameter name
            
        Returns:
            Parameter value or None if not found
        """
        return self.current_parameters.get(name)
    
    def set_parameter(self, name: str, value: Any, validate: bool = True) -> bool:
        """
        Set parameter value
        
        Args:
            name: Parameter name
            value: New parameter value
            validate: Whether to validate the value
            
        Returns:
            True if parameter was set successfully
            
        Raises:
            ValueError: If validation fails
        """
        param_def = self.schema.get_parameter(name)
        if param_def is None:
            raise ValueError(f"Unknown parameter: {name}")
        
        if validate and not param_def.validate(value):
            raise ValueError(f"Invalid value for parameter {name}: {value}")
        
        # Save current state to history
        self._save_to_history()
        
        # Set new value
        self.current_parameters[name] = value
        
        logger.debug(f"Parameter {name} set to {value}")
        return True
    
    def set_parameters(self, parameters: Dict[str, Any], validate: bool = True) -> Dict[str, bool]:
        """
        Set multiple parameters
        
        Args:
            parameters: Dictionary of parameter name-value pairs
            validate: Whether to validate values
            
        Returns:
            Dictionary of parameter names and success status
        """
        results = {}
        
        # Save current state to history once
        self._save_to_history()
        
        for name, value in parameters.items():
            try:
                param_def = self.schema.get_parameter(name)
                if param_def is None:
                    results[name] = False
                    continue
                
                if validate and not param_def.validate(value):
                    results[name] = False
                    continue
                
                self.current_parameters[name] = value
                results[name] = True
                
            except Exception as e:
                logger.error(f"Failed to set parameter {name}: {e}")
                results[name] = False
        
        return results
    
    def get_parameters_by_category(self, category: str) -> Dict[str, Any]:
        """Get all parameters in a category with their current values"""
        category_params = self.schema.get_parameters_by_category(category)
        return {
            name: self.current_parameters.get(name, param_def.default_value)
            for name, param_def in category_params.items()
        }
    
    def reset_to_defaults(self, category: Optional[str] = None):
        """
        Reset parameters to default values
        
        Args:
            category: Optional category to reset, if None resets all
        """
        self._save_to_history()
        
        if category:
            category_params = self.schema.get_parameters_by_category(category)
            for name, param_def in category_params.items():
                self.current_parameters[name] = param_def.default_value
        else:
            self._load_defaults()
        
        logger.info(f"Parameters reset to defaults" + (f" for category {category}" if category else ""))
    
    def undo_last_change(self) -> bool:
        """
        Undo last parameter change
        
        Returns:
            True if undo was successful
        """
        if not self.parameter_history:
            return False
        
        self.current_parameters = self.parameter_history.pop()
        logger.info("Last parameter change undone")
        return True
    
    def save_configuration(self, name: str, description: str = "") -> bool:
        """
        Save current configuration to file
        
        Args:
            name: Configuration name
            description: Optional description
            
        Returns:
            True if saved successfully
        """
        try:
            config_data = {
                'name': name,
                'description': description,
                'created_at': datetime.now().isoformat(),
                'parameters': deepcopy(self.current_parameters)
            }
            
            config_file = self.config_dir / f"{name}.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Configuration '{name}' saved to {config_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save configuration '{name}': {e}")
            return False
    
    def load_configuration(self, name: str) -> bool:
        """
        Load configuration from file
        
        Args:
            name: Configuration name
            
        Returns:
            True if loaded successfully
        """
        try:
            config_file = self.config_dir / f"{name}.json"
            if not config_file.exists():
                logger.error(f"Configuration '{name}' not found")
                return False
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # Save current state to history
            self._save_to_history()
            
            # Load parameters
            self.current_parameters.update(config_data['parameters'])
            
            logger.info(f"Configuration '{name}' loaded from {config_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load configuration '{name}': {e}")
            return False
    
    def list_configurations(self) -> List[Dict[str, str]]:
        """List all saved configurations"""
        configs = []
        
        for config_file in self.config_dir.glob("*.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                configs.append({
                    'name': config_data.get('name', config_file.stem),
                    'description': config_data.get('description', ''),
                    'created_at': config_data.get('created_at', ''),
                    'file': str(config_file)
                })
            except Exception as e:
                logger.error(f"Failed to read configuration {config_file}: {e}")
        
        return sorted(configs, key=lambda x: x['created_at'], reverse=True)
    
    def _save_to_history(self):
        """Save current parameter state to history"""
        self.parameter_history.append(deepcopy(self.current_parameters))
        
        # Limit history size
        if len(self.parameter_history) > self.max_history_size:
            self.parameter_history.pop(0)
    
    def get_all_parameters(self) -> Dict[str, Any]:
        """Get all current parameters"""
        return deepcopy(self.current_parameters)
    
    def get_parameter_info(self, name: str) -> Optional[Dict[str, Any]]:
        """Get parameter definition and current value"""
        param_def = self.schema.get_parameter(name)
        if not param_def:
            return None
        
        return {
            'definition': asdict(param_def),
            'current_value': self.current_parameters.get(name, param_def.default_value)
        }
