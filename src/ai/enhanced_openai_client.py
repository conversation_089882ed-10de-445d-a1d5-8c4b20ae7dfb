"""
Enhanced OpenAI API Client with Advanced Features
Převzato z existujícího projektu a adaptováno pro LimWrapp
Provides robust API communication with rate limiting, caching, and fallback mechanisms
"""

import os
import json
import time
import hashlib
import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
import threading

import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


@dataclass
class ModelConfig:
    """Configuration for OpenAI models"""
    name: str
    max_tokens: int
    context_window: int
    input_cost_per_1k: float  # USD per 1K tokens
    output_cost_per_1k: float  # USD per 1K tokens
    cached_cost_per_1k: float  # USD per 1K cached tokens
    rpm_limit: int  # Requests per minute
    tpm_limit: int  # Tokens per minute


@dataclass
class APIRequest:
    """API request configuration"""
    messages: List[Dict[str, str]]
    model: str
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    stop: Optional[List[str]] = None
    stream: bool = False
    user: Optional[str] = None


@dataclass
class APIResponse:
    """Enhanced API response with comprehensive metadata"""
    content: str
    model: str
    tokens_used: int
    prompt_tokens: int
    completion_tokens: int
    cost: float
    finish_reason: str
    created_at: datetime
    response_time: float
    cached: bool = False
    cache_hit: bool = False
    request_id: Optional[str] = None
    rate_limit_remaining: Optional[int] = None
    rate_limit_reset: Optional[datetime] = None


@dataclass
class UsageStats:
    """Usage statistics tracking"""
    total_requests: int = 0
    total_tokens: int = 0
    total_cost: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    errors: int = 0
    average_response_time: float = 0.0
    last_reset: datetime = None


class RateLimiter:
    """Advanced rate limiter with token bucket algorithm"""
    
    def __init__(self, requests_per_minute: int, tokens_per_minute: int):
        self.rpm_limit = requests_per_minute
        self.tpm_limit = tokens_per_minute
        self.request_tokens = requests_per_minute
        self.token_tokens = tokens_per_minute
        self.last_refill = time.time()
        self.lock = threading.Lock()
    
    def can_proceed(self, estimated_tokens: int = 0) -> Tuple[bool, float]:
        """Check if request can proceed and return wait time if not"""
        with self.lock:
            now = time.time()
            time_passed = now - self.last_refill
            
            # Refill tokens based on time passed
            self.request_tokens = min(
                self.rpm_limit,
                self.request_tokens + (time_passed * self.rpm_limit / 60)
            )
            self.token_tokens = min(
                self.tpm_limit,
                self.token_tokens + (time_passed * self.tpm_limit / 60)
            )
            self.last_refill = now
            
            # Check if we have enough tokens
            if self.request_tokens >= 1 and self.token_tokens >= estimated_tokens:
                self.request_tokens -= 1
                self.token_tokens -= estimated_tokens
                return True, 0.0
            
            # Calculate wait time
            request_wait = (1 - self.request_tokens) * 60 / self.rpm_limit if self.request_tokens < 1 else 0
            token_wait = (estimated_tokens - self.token_tokens) * 60 / self.tpm_limit if self.token_tokens < estimated_tokens else 0
            
            return False, max(request_wait, token_wait)


class SimpleCache:
    """Simple in-memory cache as fallback when Redis is not available"""
    
    def __init__(self, default_ttl: int = 3600, max_size: int = 1000):
        self.default_ttl = default_ttl
        self.max_size = max_size
        self.cache: Dict[str, Tuple[Any, float]] = {}  # key -> (value, expiry_time)
        self.lock = threading.Lock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get cached value"""
        with self.lock:
            if key in self.cache:
                value, expiry = self.cache[key]
                if time.time() < expiry:
                    return value
                else:
                    del self.cache[key]
        return None
    
    def set(self, key: str, value: Any, ttl: int = None) -> None:
        """Set cached value"""
        with self.lock:
            # Remove oldest entries if cache is full
            if len(self.cache) >= self.max_size:
                oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
                del self.cache[oldest_key]
            
            expiry_time = time.time() + (ttl or self.default_ttl)
            self.cache[key] = (value, expiry_time)


class ResponseCache:
    """Response caching with fallback to simple cache"""
    
    def __init__(self, redis_url: str = None, default_ttl: int = 3600):
        self.default_ttl = default_ttl
        self.redis_client = None
        self.simple_cache = SimpleCache(default_ttl)
        
        # Try to initialize Redis
        if redis_url or os.getenv('REDIS_URL'):
            try:
                import redis
                self.redis_client = redis.from_url(
                    redis_url or os.getenv('REDIS_URL', 'redis://localhost:6379/0')
                )
                self.redis_client.ping()
                logger.info("Redis cache enabled")
            except Exception as e:
                logger.warning(f"Redis cache disabled, using simple cache: {e}")
                self.redis_client = None
        else:
            logger.info("Using simple in-memory cache")
    
    def _generate_key(self, request: APIRequest) -> str:
        """Generate cache key from request"""
        request_dict = asdict(request)
        request_str = json.dumps(request_dict, sort_keys=True)
        return f"openai_cache:{hashlib.md5(request_str.encode()).hexdigest()}"
    
    def get(self, request: APIRequest) -> Optional[APIResponse]:
        """Get cached response"""
        try:
            key = self._generate_key(request)
            
            if self.redis_client:
                cached_data = self.redis_client.get(key)
                if cached_data:
                    data = json.loads(cached_data)
                    response = APIResponse(**data)
                    response.cached = True
                    response.cache_hit = True
                    return response
            else:
                cached_data = self.simple_cache.get(key)
                if cached_data:
                    response = APIResponse(**cached_data)
                    response.cached = True
                    response.cache_hit = True
                    return response
        except Exception as e:
            logger.error(f"Cache get error: {e}")
        
        return None
    
    def set(self, request: APIRequest, response: APIResponse, ttl: int = None) -> None:
        """Cache response"""
        try:
            key = self._generate_key(request)
            response_dict = asdict(response)
            
            # Convert datetime to ISO string
            response_dict['created_at'] = response.created_at.isoformat()
            if response.rate_limit_reset:
                response_dict['rate_limit_reset'] = response.rate_limit_reset.isoformat()
            
            if self.redis_client:
                self.redis_client.setex(
                    key,
                    ttl or self.default_ttl,
                    json.dumps(response_dict, default=str)
                )
            else:
                self.simple_cache.set(key, response_dict, ttl or self.default_ttl)
                
        except Exception as e:
            logger.error(f"Cache set error: {e}")


class EnhancedOpenAIClient:
    """Enhanced OpenAI API client with advanced features"""
    
    # Model configurations
    MODELS = {
        'gpt-4o-mini': ModelConfig(
            name='gpt-4o-mini',
            max_tokens=16384,
            context_window=128000,
            input_cost_per_1k=0.000150,  # $0.15 per 1M tokens = $0.000150 per 1K tokens
            output_cost_per_1k=0.000600,  # $0.60 per 1M tokens = $0.000600 per 1K tokens
            cached_cost_per_1k=0.000075,  # 50% discount for cached
            rpm_limit=500,
            tpm_limit=200000
        ),
        'gpt-4o': ModelConfig(
            name='gpt-4o',
            max_tokens=4096,
            context_window=128000,
            input_cost_per_1k=0.0025,  # $2.50 per 1M tokens = $0.0025 per 1K tokens
            output_cost_per_1k=0.010,   # $10.00 per 1M tokens = $0.010 per 1K tokens
            cached_cost_per_1k=0.00125, # 50% discount for cached
            rpm_limit=500,
            tpm_limit=30000
        ),
        'gpt-4': ModelConfig(
            name='gpt-4',
            max_tokens=8192,
            context_window=8192,
            input_cost_per_1k=0.030,  # $30.00 per 1M tokens = $0.030 per 1K tokens
            output_cost_per_1k=0.060,  # $60.00 per 1M tokens = $0.060 per 1K tokens
            cached_cost_per_1k=0.015,  # 50% discount for cached
            rpm_limit=500,
            tpm_limit=10000
        ),
        'o1-mini': ModelConfig(
            name='o1-mini',
            max_tokens=65536,
            context_window=128000,
            input_cost_per_1k=0.003,  # $3.00 per 1M tokens = $0.003 per 1K tokens
            output_cost_per_1k=0.012,  # $12.00 per 1M tokens = $0.012 per 1K tokens
            cached_cost_per_1k=0.0015, # 50% discount for cached
            rpm_limit=50,
            tpm_limit=20000
        )
    }

    def __init__(
        self,
        api_key: str = None,
        organization_id: str = None,
        default_model: str = 'gpt-4o-mini',
        cache_ttl: int = 3600,
        enable_cache: bool = True,
        redis_url: str = None,
        max_retries: int = 3,
        timeout: float = 60.0
    ):
        """
        Initialize enhanced OpenAI client

        Args:
            api_key: OpenAI API key
            organization_id: OpenAI organization ID
            default_model: Default model to use
            cache_ttl: Cache TTL in seconds
            enable_cache: Enable response caching
            redis_url: Redis URL for caching
            max_retries: Maximum retry attempts
            timeout: Request timeout in seconds
        """
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        self.organization_id = organization_id or os.getenv('OPENAI_ORG_ID')
        self.default_model = default_model
        self.max_retries = max_retries
        self.timeout = timeout

        if not self.api_key:
            raise ValueError("OpenAI API key is required")

        # Initialize HTTP client
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'LimWrapp/1.0'
        }

        if self.organization_id:
            headers['OpenAI-Organization'] = self.organization_id

        self.client = httpx.Client(
            base_url='https://api.openai.com/v1',
            headers=headers,
            timeout=timeout
        )

        # Initialize components
        self.cache = ResponseCache(redis_url, cache_ttl) if enable_cache else None
        self.rate_limiters = {
            model: RateLimiter(config.rpm_limit, config.tpm_limit)
            for model, config in self.MODELS.items()
        }

        # Usage statistics
        self.stats = UsageStats(last_reset=datetime.now())
        self.stats_lock = threading.Lock()

        # Fallback chain
        self.fallback_chain = [
            'gpt-4o-mini',
            'gpt-4o',
            'gpt-4'
        ]

        logger.info(f"Enhanced OpenAI client initialized with model: {default_model}")

    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation)"""
        # Rough estimation: 1 token ≈ 4 characters for English text
        return max(1, len(text) // 4)

    def _calculate_cost(
        self,
        prompt_tokens: int,
        completion_tokens: int,
        model: str,
        cached: bool = False
    ) -> float:
        """Calculate cost for API usage"""
        if model not in self.MODELS:
            logger.warning(f"Unknown model {model}, using gpt-4o-mini pricing")
            model = 'gpt-4o-mini'

        config = self.MODELS[model]

        if cached:
            return (prompt_tokens + completion_tokens) * config.cached_cost_per_1k / 1000
        else:
            return (
                (prompt_tokens * config.input_cost_per_1k / 1000) +
                (completion_tokens * config.output_cost_per_1k / 1000)
            )

    def _update_stats(self, response: APIResponse, error: bool = False) -> None:
        """Update usage statistics"""
        with self.stats_lock:
            if error:
                self.stats.errors += 1
            else:
                self.stats.total_requests += 1
                self.stats.total_tokens += response.tokens_used
                self.stats.total_cost += response.cost

                if response.cache_hit:
                    self.stats.cache_hits += 1
                else:
                    self.stats.cache_misses += 1

                # Update average response time
                if self.stats.total_requests == 1:
                    self.stats.average_response_time = response.response_time
                else:
                    self.stats.average_response_time = (
                        (self.stats.average_response_time * (self.stats.total_requests - 1) +
                         response.response_time) / self.stats.total_requests
                    )

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.HTTPError, httpx.TimeoutException))
    )
    def _make_request(self, request: APIRequest) -> APIResponse:
        """Make API request with retry logic"""
        start_time = time.time()

        # Estimate tokens for rate limiting
        estimated_tokens = sum(self.estimate_tokens(msg['content']) for msg in request.messages)
        estimated_tokens += request.max_tokens or self.MODELS[request.model].max_tokens // 4

        # Check rate limits
        rate_limiter = self.rate_limiters.get(request.model)
        if rate_limiter:
            can_proceed, wait_time = rate_limiter.can_proceed(estimated_tokens)
            if not can_proceed:
                logger.info(f"Rate limit hit, waiting {wait_time:.2f} seconds")
                time.sleep(wait_time)

        # Prepare request payload
        payload = {
            'model': request.model,
            'messages': request.messages,
            'temperature': request.temperature,
            'top_p': request.top_p,
            'frequency_penalty': request.frequency_penalty,
            'presence_penalty': request.presence_penalty,
            'stream': request.stream
        }

        if request.max_tokens:
            payload['max_tokens'] = request.max_tokens
        if request.stop:
            payload['stop'] = request.stop
        if request.user:
            payload['user'] = request.user

        logger.debug(f"Making API request to model {request.model}")

        # Make the request
        response = self.client.post('/chat/completions', json=payload)
        response.raise_for_status()

        response_time = time.time() - start_time
        data = response.json()

        # Extract rate limit headers
        rate_limit_remaining = response.headers.get('x-ratelimit-remaining-requests')
        rate_limit_reset = response.headers.get('x-ratelimit-reset-requests')

        rate_limit_reset_dt = None
        if rate_limit_reset:
            try:
                rate_limit_reset_dt = datetime.fromisoformat(rate_limit_reset.replace('Z', '+00:00'))
            except:
                pass

        # Create response object
        choice = data['choices'][0]
        usage = data['usage']

        api_response = APIResponse(
            content=choice['message']['content'],
            model=data['model'],
            tokens_used=usage['total_tokens'],
            prompt_tokens=usage['prompt_tokens'],
            completion_tokens=usage['completion_tokens'],
            cost=self._calculate_cost(
                usage['prompt_tokens'],
                usage['completion_tokens'],
                data['model']
            ),
            finish_reason=choice['finish_reason'],
            created_at=datetime.fromtimestamp(data['created']),
            response_time=response_time,
            request_id=response.headers.get('x-request-id'),
            rate_limit_remaining=int(rate_limit_remaining) if rate_limit_remaining else None,
            rate_limit_reset=rate_limit_reset_dt
        )

        logger.debug(
            f"API response: model={api_response.model}, "
            f"tokens={api_response.tokens_used}, "
            f"cost=${api_response.cost:.4f}, "
            f"time={api_response.response_time:.2f}s"
        )

        return api_response

    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = None,
        temperature: float = None,
        max_tokens: int = None,
        top_p: float = None,
        frequency_penalty: float = None,
        presence_penalty: float = None,
        stop: List[str] = None,
        user: str = None,
        use_cache: bool = True,
        fallback_on_error: bool = True
    ) -> APIResponse:
        """
        Enhanced chat completion with caching and fallback

        Args:
            messages: List of message dictionaries
            model: Model to use (defaults to default_model)
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            top_p: Nucleus sampling parameter
            frequency_penalty: Frequency penalty
            presence_penalty: Presence penalty
            stop: Stop sequences
            user: User identifier
            use_cache: Whether to use response caching
            fallback_on_error: Whether to fallback to other models on error

        Returns:
            APIResponse with comprehensive metadata
        """
        # Prepare request
        request = APIRequest(
            messages=messages,
            model=model or self.default_model,
            temperature=temperature if temperature is not None else 0.7,
            max_tokens=max_tokens,
            top_p=top_p if top_p is not None else 1.0,
            frequency_penalty=frequency_penalty if frequency_penalty is not None else 0.0,
            presence_penalty=presence_penalty if presence_penalty is not None else 0.0,
            stop=stop,
            user=user
        )

        # Check cache first
        if use_cache and self.cache:
            cached_response = self.cache.get(request)
            if cached_response:
                self._update_stats(cached_response)
                return cached_response

        # Try primary model
        try:
            response = self._make_request(request)

            # Cache successful response
            if use_cache and self.cache:
                self.cache.set(request, response)

            self._update_stats(response)
            return response

        except Exception as e:
            logger.error(f"Error with model {request.model}: {e}")
            self._update_stats(None, error=True)

            # Try fallback models if enabled
            if fallback_on_error and request.model in self.fallback_chain:
                fallback_models = self.fallback_chain[self.fallback_chain.index(request.model) + 1:]

                for fallback_model in fallback_models:
                    try:
                        logger.info(f"Trying fallback model: {fallback_model}")
                        request.model = fallback_model
                        response = self._make_request(request)

                        # Cache successful fallback response
                        if use_cache and self.cache:
                            self.cache.set(request, response)

                        self._update_stats(response)
                        return response

                    except Exception as fallback_error:
                        logger.error(f"Fallback model {fallback_model} also failed: {fallback_error}")
                        self._update_stats(None, error=True)
                        continue

            # All models failed
            raise Exception(f"All models failed. Last error: {e}")

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get comprehensive usage statistics"""
        with self.stats_lock:
            cache_hit_rate = (
                self.stats.cache_hits / (self.stats.cache_hits + self.stats.cache_misses)
                if (self.stats.cache_hits + self.stats.cache_misses) > 0 else 0
            )

            return {
                'total_requests': self.stats.total_requests,
                'total_tokens': self.stats.total_tokens,
                'total_cost': round(self.stats.total_cost, 4),
                'cache_hits': self.stats.cache_hits,
                'cache_misses': self.stats.cache_misses,
                'cache_hit_rate': round(cache_hit_rate * 100, 2),
                'errors': self.stats.errors,
                'average_response_time': round(self.stats.average_response_time, 2),
                'last_reset': self.stats.last_reset.isoformat() if self.stats.last_reset else None
            }

    def reset_stats(self) -> None:
        """Reset usage statistics"""
        with self.stats_lock:
            self.stats = UsageStats(last_reset=datetime.now())
        logger.info("Usage statistics reset")

    def get_model_info(self, model: str = None) -> Dict[str, Any]:
        """Get information about a model"""
        model = model or self.default_model
        if model not in self.MODELS:
            raise ValueError(f"Unknown model: {model}")

        config = self.MODELS[model]
        return {
            'name': config.name,
            'max_tokens': config.max_tokens,
            'context_window': config.context_window,
            'input_cost_per_1k': config.input_cost_per_1k,
            'output_cost_per_1k': config.output_cost_per_1k,
            'cached_cost_per_1k': config.cached_cost_per_1k,
            'rpm_limit': config.rpm_limit,
            'tpm_limit': config.tpm_limit
        }

    def list_models(self) -> List[str]:
        """List available models"""
        return list(self.MODELS.keys())

    def close(self) -> None:
        """Close the client and cleanup resources"""
        self.client.close()
        logger.info("OpenAI client closed")
