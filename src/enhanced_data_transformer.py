"""
Enhanced Data Transformer
Roz<PERSON><PERSON><PERSON><PERSON><PERSON> transformer pro různé typy LimeSurvey otázek
"""

try:
    import pandas as pd
except ImportError:
    pd = None

import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from data_transformer import DataTransformer
from lss_question_analyzer import LSSQuestionAnalyzer
from logger import get_logger

logger = get_logger(__name__)

class EnhancedDataTransformer(DataTransformer):
    """Rozšířený transformer pro různé typy otázek"""
    
    def __init__(self, mapping_file: Optional[str] = None):
        super().__init__(mapping_file)
        self.analyzer = LSSQuestionAnalyzer()
        self.logger = logger
    
    def prepare_chart_data_by_analysis(self, df, question_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Příprava dat podle analýzy otázky
        
        Args:
            df: DataFrame s daty
            question_analysis: Výsledek analýzy ot<PERSON><PERSON><PERSON>
            
        Returns:
            Dict s připravenými daty pro graf
        """
        try:
            data_type = question_analysis.get('data_type', 'categorical')
            
            if data_type == 'categorical':
                return self._prepare_categorical_data(df, question_analysis)
            elif data_type == 'binary':
                return self._prepare_binary_data(df, question_analysis)
            elif data_type == 'scale':
                return self._prepare_scale_data(df, question_analysis)
            elif data_type == 'multiple_choice':
                return self._prepare_multiple_choice_data(df, question_analysis)
            elif data_type == 'array':
                return self._prepare_array_data(df, question_analysis)
            elif data_type == 'array_scale':
                return self._prepare_array_scale_data(df, question_analysis)
            elif data_type == 'array_binary':
                return self._prepare_array_binary_data(df, question_analysis)
            elif data_type == 'array_choice':
                return self._prepare_array_choice_data(df, question_analysis)
            elif data_type == 'array_dual':
                return self._prepare_array_dual_data(df, question_analysis)
            elif data_type == 'numerical':
                return self._prepare_numerical_data(df, question_analysis)
            elif data_type == 'multiple_numerical':
                return self._prepare_multiple_numerical_data(df, question_analysis)
            elif data_type == 'array_numerical':
                return self._prepare_array_numerical_data(df, question_analysis)
            elif data_type == 'ranking':
                return self._prepare_ranking_data(df, question_analysis)
            elif data_type == 'text':
                return self._prepare_text_data(df, question_analysis)
            elif data_type == 'date':
                return self._prepare_date_data(df, question_analysis)
            else:
                # Fallback na základní kategorická data
                return self._prepare_categorical_data(df, question_analysis)
                
        except Exception as e:
            self.logger.error(f"Chyba při přípravě dat pro typ '{data_type}': {str(e)}")
            return self._prepare_fallback_data(df, question_analysis)
    
    def _prepare_categorical_data(self, df, question_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Příprava dat pro kategorické otázky (L, !, O)"""
        try:
            # Počítání odpovědí
            if 'response' in df.columns:
                response_counts = df['response'].value_counts()
            else:
                # Pokud není sloupec response, použijeme první dostupný sloupec
                response_counts = df.iloc[:, 0].value_counts()
            
            chart_data = {
                'chart_type': question_analysis.get('chart_type', 'column-chart'),
                'data_type': 'categorical',
                'data': []
            }
            
            for response, count in response_counts.items():
                if pd.notna(response) and response != '':
                    chart_data['data'].append({
                        'label': str(response),
                        'value': int(count)
                    })
            
            # Seřazení podle hodnoty (sestupně)
            chart_data['data'].sort(key=lambda x: x['value'], reverse=True)
            
            self.logger.debug(f"Připravena kategorická data: {len(chart_data['data'])} kategorií")
            return chart_data
            
        except Exception as e:
            self.logger.error(f"Chyba při přípravě kategorických dat: {str(e)}")
            return self._prepare_fallback_data(df, question_analysis)
    
    def _prepare_binary_data(self, df, question_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Příprava dat pro binární otázky (Y, G)"""
        try:
            if 'response' in df.columns:
                response_counts = df['response'].value_counts()
            else:
                response_counts = df.iloc[:, 0].value_counts()
            
            chart_data = {
                'chart_type': question_analysis.get('chart_type', 'pie-chart'),
                'data_type': 'binary',
                'data': []
            }
            
            # Mapování pro Yes/No otázky
            binary_mapping = {
                'Y': 'Ano', 'N': 'Ne',
                '1': 'Ano', '0': 'Ne',
                'Yes': 'Ano', 'No': 'Ne',
                'M': 'Muž', 'F': 'Žena'
            }
            
            for response, count in response_counts.items():
                if pd.notna(response) and response != '':
                    label = binary_mapping.get(str(response), str(response))
                    chart_data['data'].append({
                        'label': label,
                        'value': int(count)
                    })
            
            self.logger.debug(f"Připravena binární data: {len(chart_data['data'])} možnosti")
            return chart_data
            
        except Exception as e:
            self.logger.error(f"Chyba při přípravě binárních dat: {str(e)}")
            return self._prepare_fallback_data(df, question_analysis)
    
    def _prepare_scale_data(self, df, question_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Příprava dat pro škálové otázky (5, A, B)"""
        try:
            if 'response' in df.columns:
                response_counts = df['response'].value_counts()
            else:
                response_counts = df.iloc[:, 0].value_counts()
            
            chart_data = {
                'chart_type': question_analysis.get('chart_type', 'column-chart'),
                'data_type': 'scale',
                'data': []
            }
            
            scale_info = question_analysis.get('scale_info', {})
            
            # Seřazení podle číselné hodnoty pro škály
            sorted_responses = sorted(response_counts.items(), 
                                    key=lambda x: self._parse_scale_value(x[0]))
            
            for response, count in sorted_responses:
                if pd.notna(response) and response != '':
                    chart_data['data'].append({
                        'label': str(response),
                        'value': int(count),
                        'scale_value': self._parse_scale_value(response)
                    })
            
            self.logger.debug(f"Připravena škálová data: {len(chart_data['data'])} bodů škály")
            return chart_data
            
        except Exception as e:
            self.logger.error(f"Chyba při přípravě škálových dat: {str(e)}")
            return self._prepare_fallback_data(df, question_analysis)
    
    def _prepare_multiple_choice_data(self, df, question_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Příprava dat pro otázky s více možnostmi (M, P)"""
        try:
            chart_data = {
                'chart_type': question_analysis.get('chart_type', 'grouped-column-chart'),
                'data_type': 'multiple_choice',
                'data': [],
                'series': []
            }
            
            # Pro multiple choice potřebujeme analyzovat více sloupců
            # Každý sloupec představuje jednu možnost
            choice_columns = [col for col in df.columns if col.startswith('response') or 'SQ' in col]
            
            if not choice_columns:
                # Fallback na základní zpracování
                return self._prepare_categorical_data(df, question_analysis)
            
            for col in choice_columns:
                if col in df.columns:
                    # Počítání odpovědí pro každou možnost
                    value_counts = df[col].value_counts()
                    
                    for value, count in value_counts.items():
                        if pd.notna(value) and value != '' and value != 'N':
                            chart_data['data'].append({
                                'category': col.replace('response_', '').replace('SQ', 'Možnost '),
                                'label': str(value),
                                'value': int(count)
                            })
            
            self.logger.debug(f"Připravena multiple choice data: {len(chart_data['data'])} odpovědí")
            return chart_data
            
        except Exception as e:
            self.logger.error(f"Chyba při přípravě multiple choice dat: {str(e)}")
            return self._prepare_fallback_data(df, question_analysis)
    
    def _prepare_array_data(self, df, question_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Příprava dat pro pole otázek (F)"""
        try:
            chart_data = {
                'chart_type': question_analysis.get('chart_type', 'stacked-column-chart'),
                'data_type': 'array',
                'data': [],
                'categories': [],
                'series': []
            }
            
            # Získání subquestions a answer_options z LSS
            subquestions = question_analysis.get('subquestions', [])
            answer_options = question_analysis.get('answer_options', [])
            
            # Vytvoření mapování kódů na texty
            subq_map = {sq['code']: sq['text'] for sq in subquestions}
            answer_map = {ao['code']: ao['text'] for ao in answer_options}
            
            # Identifikace sloupců pole
            array_columns = [col for col in df.columns if 'SQ' in col or col.startswith('response')]
            
            if not array_columns:
                return self._prepare_categorical_data(df, question_analysis)
            
            # Zpracování každého sloupce pole
            for col in array_columns:
                if col in df.columns:
                    value_counts = df[col].value_counts()
                    
                    # Extrakce kódu subquestion z názvu sloupce
                    subq_code = None
                    if 'SQ' in col:
                        # Formát: G4Q00003[Podotázka 001] -> SQ001
                        parts = col.split('SQ')
                        if len(parts) > 1:
                            subq_code = 'SQ' + parts[1].split(']')[0]
                    
                    # Použití skutečného textu nebo fallback
                    if subq_code and subq_code in subq_map:
                        category_name = subq_map[subq_code]
                    else:
                        category_name = col.replace('response_', '').replace('SQ', 'Podotázka ')
                    
                    chart_data['categories'].append(category_name)
                    
                    # OPRAVA: Zachování LSS pořadí odpovědí místo řazení podle četnosti
                    # Nejprve vytvoříme slovník s počty
                    counts_dict = dict(value_counts)

                    # Pak projdeme odpovědi v LSS pořadí
                    if answer_options:
                        # Použijeme pořadí z LSS
                        for answer_option in answer_options:
                            answer_code = answer_option['code']
                            answer_text = answer_option['text']

                            # Najdeme počet pro tuto odpověď
                            count = counts_dict.get(answer_code, 0)
                            if count > 0:  # Pouze pokud má nějaké odpovědi
                                chart_data['data'].append({
                                    'category': category_name,
                                    'label': answer_text,
                                    'value': int(count)
                                })
                    else:
                        # Fallback - pokud nemáme LSS pořadí, použijeme původní logiku
                        for value, count in value_counts.items():
                            if pd.notna(value) and value != '':
                                # Použití skutečného textu odpovědi nebo fallback
                                if str(value) in answer_map:
                                    label_text = answer_map[str(value)]
                                else:
                                    label_text = str(value)

                                chart_data['data'].append({
                                    'category': category_name,
                                    'label': label_text,
                                    'value': int(count)
                                })
            
            self.logger.debug(f"Připravena array data: {len(chart_data['categories'])} kategorií")
            return chart_data
            
        except Exception as e:
            self.logger.error(f"Chyba při přípravě array dat: {str(e)}")
            return self._prepare_fallback_data(df, question_analysis)
    
    def _prepare_array_scale_data(self, df, question_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Příprava dat pro škálová pole (A, B, H)"""
        try:
            chart_data = {
                'chart_type': question_analysis.get('chart_type', 'stacked-column-chart'),
                'data_type': 'array_scale',
                'data': [],
                'categories': [],
                'scale_labels': []
            }
            
            scale_info = question_analysis.get('scale_info', {})
            
            # Identifikace sloupců pole
            array_columns = [col for col in df.columns if 'SQ' in col]
            
            if not array_columns:
                return self._prepare_scale_data(df, question_analysis)
            
            # OPRAVA: Použití LSS pořadí místo automatického řazení
            answer_options = question_analysis.get('answer_options', [])

            if answer_options:
                # Použijeme pořadí z LSS
                sorted_values = [ao['code'] for ao in answer_options]
                chart_data['scale_labels'] = [ao['text'] for ao in answer_options]
            else:
                # Fallback - získání všech možných hodnot škály
                all_values = set()
                for col in array_columns:
                    if col in df.columns:
                        all_values.update(df[col].dropna().unique())

                # Seřazení hodnot škály
                sorted_values = sorted(all_values, key=lambda x: self._parse_scale_value(x))
                chart_data['scale_labels'] = [str(v) for v in sorted_values]
            
            # Zpracování každého sloupce pole
            for col in array_columns:
                if col in df.columns:
                    value_counts = df[col].value_counts()
                    category_name = col.replace('SQ', 'Podotázka ')
                    chart_data['categories'].append(category_name)
                    
                    # OPRAVA: Použití správných labelů z LSS
                    if answer_options:
                        # Použijeme LSS pořadí a texty
                        for i, answer_option in enumerate(answer_options):
                            answer_code = answer_option['code']
                            answer_text = answer_option['text']
                            count = value_counts.get(answer_code, 0)

                            chart_data['data'].append({
                                'category': category_name,
                                'label': answer_text,
                                'value': int(count),
                                'scale_value': i  # Pořadí v LSS
                            })
                    else:
                        # Fallback - původní logika
                        for value in sorted_values:
                            count = value_counts.get(value, 0)
                            chart_data['data'].append({
                                'category': category_name,
                                'label': str(value),
                                'value': int(count),
                                'scale_value': self._parse_scale_value(value)
                            })
            
            self.logger.debug(f"Připravena array scale data: {len(chart_data['categories'])} kategorií, "
                            f"{len(chart_data['scale_labels'])} bodů škály")
            return chart_data
            
        except Exception as e:
            self.logger.error(f"Chyba při přípravě array scale dat: {str(e)}")
            return self._prepare_fallback_data(df, question_analysis)
    
    def _prepare_numerical_data(self, df, question_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Příprava dat pro číselné otázky (N)"""
        try:
            if 'response' in df.columns:
                numeric_data = pd.to_numeric(df['response'], errors='coerce').dropna()
            else:
                numeric_data = pd.to_numeric(df.iloc[:, 0], errors='coerce').dropna()
            
            # Vytvoření histogramu
            hist, bin_edges = np.histogram(numeric_data, bins=10)
            
            chart_data = {
                'chart_type': question_analysis.get('chart_type', 'histogram'),
                'data_type': 'numerical',
                'data': [],
                'statistics': {
                    'mean': float(numeric_data.mean()),
                    'median': float(numeric_data.median()),
                    'std': float(numeric_data.std()),
                    'min': float(numeric_data.min()),
                    'max': float(numeric_data.max()),
                    'count': int(len(numeric_data))
                }
            }
            
            # Příprava dat pro histogram
            for i in range(len(hist)):
                bin_start = bin_edges[i]
                bin_end = bin_edges[i + 1]
                chart_data['data'].append({
                    'label': f"{bin_start:.1f} - {bin_end:.1f}",
                    'value': int(hist[i]),
                    'bin_start': float(bin_start),
                    'bin_end': float(bin_end)
                })
            
            self.logger.debug(f"Připravena číselná data: {len(chart_data['data'])} intervalů")
            return chart_data
            
        except Exception as e:
            self.logger.error(f"Chyba při přípravě číselných dat: {str(e)}")
            return self._prepare_fallback_data(df, question_analysis)
    
    def _prepare_ranking_data(self, df, question_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Příprava dat pro ranking otázky (R)"""
        try:
            chart_data = {
                'chart_type': question_analysis.get('chart_type', 'ranking-chart'),
                'data_type': 'ranking',
                'data': []
            }
            
            # Pro ranking potřebujeme analyzovat pořadí
            ranking_columns = [col for col in df.columns if 'rank' in col.lower() or 'SQ' in col]
            
            if not ranking_columns:
                return self._prepare_categorical_data(df, question_analysis)
            
            # Výpočet průměrného pořadí pro každou možnost
            ranking_scores = {}
            
            for col in ranking_columns:
                if col in df.columns:
                    # Převod na číselné hodnoty
                    numeric_ranks = pd.to_numeric(df[col], errors='coerce').dropna()
                    if len(numeric_ranks) > 0:
                        option_name = col.replace('rank_', '').replace('SQ', 'Možnost ')
                        ranking_scores[option_name] = {
                            'average_rank': float(numeric_ranks.mean()),
                            'count': int(len(numeric_ranks))
                        }
            
            # Seřazení podle průměrného pořadí (nižší = lepší)
            sorted_rankings = sorted(ranking_scores.items(), key=lambda x: x[1]['average_rank'])
            
            for rank, (option, scores) in enumerate(sorted_rankings, 1):
                chart_data['data'].append({
                    'label': option,
                    'value': scores['count'],
                    'average_rank': scores['average_rank'],
                    'final_rank': rank
                })
            
            self.logger.debug(f"Připravena ranking data: {len(chart_data['data'])} možností")
            return chart_data
            
        except Exception as e:
            self.logger.error(f"Chyba při přípravě ranking dat: {str(e)}")
            return self._prepare_fallback_data(df, question_analysis)
    
    def _prepare_text_data(self, df, question_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Příprava dat pro textové otázky (S, U)"""
        try:
            if 'response' in df.columns:
                text_responses = df['response'].dropna()
            else:
                text_responses = df.iloc[:, 0].dropna()
            
            # Základní analýza textu
            word_counts = {}
            for response in text_responses:
                if isinstance(response, str) and len(response.strip()) > 0:
                    # Jednoduché rozdělení na slova
                    words = response.lower().split()
                    for word in words:
                        # Odstranění interpunkce
                        clean_word = ''.join(c for c in word if c.isalnum())
                        if len(clean_word) > 2:  # Ignorovat krátká slova
                            word_counts[clean_word] = word_counts.get(clean_word, 0) + 1
            
            chart_data = {
                'chart_type': question_analysis.get('chart_type', 'word-cloud'),
                'data_type': 'text',
                'data': [],
                'total_responses': int(len(text_responses)),
                'total_words': sum(word_counts.values())
            }
            
            # Seřazení podle četnosti
            sorted_words = sorted(word_counts.items(), key=lambda x: x[1], reverse=True)
            
            # Vzít pouze top 50 slov
            for word, count in sorted_words[:50]:
                chart_data['data'].append({
                    'label': word,
                    'value': count
                })
            
            self.logger.debug(f"Připravena textová data: {len(chart_data['data'])} slov")
            return chart_data
            
        except Exception as e:
            self.logger.error(f"Chyba při přípravě textových dat: {str(e)}")
            return self._prepare_fallback_data(df, question_analysis)
    
    def _prepare_date_data(self, df, question_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Příprava dat pro datové otázky (D)"""
        try:
            if 'response' in df.columns:
                date_responses = pd.to_datetime(df['response'], errors='coerce').dropna()
            else:
                date_responses = pd.to_datetime(df.iloc[:, 0], errors='coerce').dropna()
            
            # Seskupení podle měsíce/roku
            date_counts = date_responses.dt.to_period('M').value_counts().sort_index()
            
            chart_data = {
                'chart_type': question_analysis.get('chart_type', 'timeline'),
                'data_type': 'date',
                'data': []
            }
            
            for period, count in date_counts.items():
                chart_data['data'].append({
                    'label': str(period),
                    'value': int(count),
                    'date': period.start_time.isoformat()
                })
            
            self.logger.debug(f"Připravena datová data: {len(chart_data['data'])} období")
            return chart_data
            
        except Exception as e:
            self.logger.error(f"Chyba při přípravě datových dat: {str(e)}")
            return self._prepare_fallback_data(df, question_analysis)
    
    def _parse_scale_value(self, value) -> float:
        """Parsuje hodnotu škály na číselnou hodnotu pro seřazení"""
        try:
            # Pokus o přímý převod na číslo
            return float(value)
        except (ValueError, TypeError):
            # Mapování textových hodnot
            text_mapping = {
                'strongly disagree': 1, 'disagree': 2, 'neutral': 3, 'agree': 4, 'strongly agree': 5,
                'velmi nesouhlasím': 1, 'nesouhlasím': 2, 'neutrální': 3, 'souhlasím': 4, 'velmi souhlasím': 5,
                'increase': 3, 'same': 2, 'decrease': 1,
                'nárůst': 3, 'stejné': 2, 'pokles': 1,
                'yes': 2, 'no': 1, 'uncertain': 1.5,
                'ano': 2, 'ne': 1, 'nevím': 1.5
            }
            
            return text_mapping.get(str(value).lower(), 0)
    
    def _prepare_fallback_data(self, df, question_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback příprava dat pro neznámé typy"""
        try:
            return {
                'chart_type': 'column-chart',
                'data_type': 'fallback',
                'data': [
                    {'label': 'Bez dat', 'value': 0}
                ],
                'error': 'Nepodařilo se zpracovat data pro tento typ otázky'
            }
        except Exception:
            return {
                'chart_type': 'column-chart',
                'data_type': 'error',
                'data': [],
                'error': 'Kritická chyba při zpracování dat'
            }
    
    # Placeholder metody pro budoucí implementaci
    def _prepare_array_binary_data(self, df, question_analysis):
        """Placeholder pro array binary data"""
        return self._prepare_array_data(df, question_analysis)
    
    def _prepare_array_choice_data(self, df, question_analysis):
        """Placeholder pro array choice data"""
        return self._prepare_array_data(df, question_analysis)
    
    def _prepare_array_dual_data(self, df, question_analysis):
        """Placeholder pro array dual data"""
        return self._prepare_array_data(df, question_analysis)
    
    def _prepare_multiple_numerical_data(self, df, question_analysis):
        """Placeholder pro multiple numerical data"""
        return self._prepare_numerical_data(df, question_analysis)
    
    def _prepare_array_numerical_data(self, df, question_analysis):
        """Placeholder pro array numerical data"""
        return self._prepare_numerical_data(df, question_analysis)