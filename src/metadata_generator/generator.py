import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
from collections import defaultdict
import json

import pandas as pd
import yaml

from metadata_generator.parsers import (
    parse_questions,
    parse_section_analyses,
    parse_question_analyses,
    parse_visualizations,
    parse_question_mappings
)

class MetadataGenerationError(Exception):
    pass

class ValidationError(MetadataGenerationError):
    pass

class FileSystemError(MetadataGenerationError):
    pass

class MetadataGenerator:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize data stores
        self.questions = {}
        self.section_analyses = {}
        self.question_analyses = {}
        self.visualizations = {}
        self.mappings = {}
        self.analysis_to_questions = defaultdict(list)
        
        # Store ID to Code mappings
        self.question_codes = {}
        self.analysis_codes = {}
        self.visualization_codes = {}
        
        # Load detailed descriptions
        self.analysis_descriptions = self._load_analysis_descriptions()
        self.visualization_descriptions = self._load_visualization_descriptions()
        
    def _load_analysis_descriptions(self) -> Dict[str, str]:
        """Load analysis descriptions from JSON file"""
        try:
            with open(self.config['input_dir'] / 'descriptions_analysis.json', 'r') as f:
                descriptions = json.load(f)
                return {item['název']: item['popis'] for item in descriptions}
        except Exception as e:
            self.logger.warning(f"Failed to load analysis descriptions: {e}")
            return {}

    def _load_visualization_descriptions(self) -> Dict[str, str]:
        """Load visualization descriptions from JSON file"""
        try:
            with open(self.config['input_dir'] / 'descriptions_visualization.json', 'r') as f:
                descriptions = json.load(f)
                return descriptions
        except Exception as e:
            self.logger.warning(f"Failed to load visualization descriptions: {e}")
            return {}

    def _get_analysis_description(self, name: str) -> str:
        """Get detailed description for analysis by name"""
        return self.analysis_descriptions.get(name, "")

    def _get_visualization_description(self, id: str) -> str:
        """Get detailed description for visualization by ID"""
        return self.visualization_descriptions.get(id, "")

    def generate(self):
        """Main generation process"""
        self.read_input_files()
        self.validate_data()
        self.generate_directory_structure()
        self.generate_yaml_files()
        
    def read_input_files(self):
        """Read and parse all input markdown files"""
        input_files = {
            'questions': 'otazky.md',
            'section_analyses': 'analyzy-sekce.md',
            'question_analyses': 'analyzy-otazky.md',
            'visualizations': 'vizualizace.md',
            'mappings': 'otazky2analyzy.md'
        }
        
        for key, filename in input_files.items():
            file_path = self.config['input_dir'] / filename
            self.logger.info(f"Processing input file: {file_path}")
            
            if not file_path.exists():
                msg = f"Required input file not found: {file_path}"
                self.logger.error(msg)
                raise FileNotFoundError(msg)
                
        try:
            # Parse data and build ID to Code mappings
            self.questions, self.question_codes = parse_questions(self.config['input_dir'] / input_files['questions'])
            self.logger.info(f"Parsed {len(self.questions)} questions")
            
            self.section_analyses, section_codes = parse_section_analyses(self.config['input_dir'] / input_files['section_analyses'])
            self.analysis_codes.update(section_codes)
            self.logger.info(f"Parsed {len(self.section_analyses)} section analyses")
            
            self.question_analyses, question_codes = parse_question_analyses(self.config['input_dir'] / input_files['question_analyses'])
            self.analysis_codes.update(question_codes)
            self.logger.info(f"Parsed {len(self.question_analyses)} question analyses")
            
            self.visualizations, self.visualization_codes = parse_visualizations(self.config['input_dir'] / input_files['visualizations'])
            self.logger.info(f"Parsed {len(self.visualizations)} visualizations")
            
            self.mappings = parse_question_mappings(self.config['input_dir'] / input_files['mappings'])
            self.logger.info(f"Parsed mappings for {len(self.mappings)} questions")
            
            # Create reverse mapping from analyses to questions
            for qid, analyses in self.mappings.items():
                for aid in analyses:
                    self.analysis_to_questions[aid].append(qid)
            
        except Exception as e:
            self.logger.error(f"Failed to read input files: {str(e)}")
            raise MetadataGenerationError(f"Failed to read input files: {str(e)}")

    def validate_data(self):
        """Validate relationships between components"""
        self._validate_analysis_references()
        self._validate_visualization_references()
        
    def _validate_analysis_references(self):
        """Validate that all referenced analyses exist"""
        for qid, analyses in self.mappings.items():
            if qid not in self.questions:
                raise ValidationError(f"Question ID {qid} referenced in mappings not found")
            for aid in analyses:
                if (aid not in self.section_analyses and 
                    aid not in self.question_analyses):
                    raise ValidationError(f"Analysis ID {aid} referenced in mappings not found")

    def _validate_visualization_references(self):
        """Validate that all referenced visualizations exist"""
        for analysis in self.section_analyses.values():
            for viz in analysis['visualizations']:
                if viz not in self.visualizations:
                    raise ValidationError(f"Visualization {viz} referenced in analysis not found")

    def generate_directory_structure(self):
        """Create the required directory structure"""
        try:
            base_dirs = [
                'base-types',
                'parameters',
                'analyses/question_analyses',
                'analyses/section_analyses',
                'visualizations'
            ]
            
            for dir_path in base_dirs:
                full_path = self.config['output_dir'] / dir_path
                full_path.mkdir(parents=True, exist_ok=True)
                
        except Exception as e:
            raise FileSystemError(f"Failed to create directory structure: {e}")

    def generate_yaml_files(self):
        """Generate all YAML files"""
        self._generate_base_types()
        self._generate_registries()
        self._generate_analysis_files()
        self._generate_visualization_files()
        self._generate_implementation_status()

    def _generate_base_types(self):
        """Generate base type YAML files"""
        # Generate question-types.yaml
        question_types = {
            'questionTypes': [
                {
                    'id': qid,
                    'code': self.question_codes[qid],
                    'name': data['name'],
                    'description': data['description'],
                    'properties': [],
                    'supportedAnalyses': [
                        {'id': aid, 'code': self.analysis_codes[aid]}
                        for aid in self.mappings.get(qid, [])
                    ],
                    'status': 'active'
                }
                for qid, data in self.questions.items()
            ]
        }
        self._write_yaml('base-types/question-types.yaml', question_types)

        # Generate analysis-types.yaml
        analysis_types = {
            'analysisTypes': [
                {
                    'id': aid,
                    'code': self.analysis_codes[aid],
                    'name': data['name'],
                    'description': data['description'],
                    'detailedDescription': self._get_analysis_description(data['name']),
                    'type': 'section',
                    'supportedQuestionTypes': [
                        {'id': qid, 'code': self.question_codes[qid]}
                        for qid in sorted(self.analysis_to_questions.get(aid, []))
                    ],
                    'supportedVisualizations': [
                        {'id': vid, 'code': self.visualization_codes[vid]}
                        for vid in data['visualizations']
                    ],
                    'parameters': [],
                    'status': 'active' if data['priority'] <= 3 else 'planned'
                }
                for aid, data in self.section_analyses.items()
            ] + [
                {
                    'id': aid,
                    'code': self.analysis_codes[aid],
                    'name': data['name'],
                    'description': data['description'],
                    'detailedDescription': self._get_analysis_description(data['name']),
                    'type': 'question',
                    'supportedQuestionTypes': [
                        {'id': qid, 'code': self.question_codes[qid]}
                        for qid in sorted(self.analysis_to_questions.get(aid, []))
                    ],
                    'supportedVisualizations': [
                        {'id': vid, 'code': self.visualization_codes[vid]}
                        for vid in data['visualizations']
                    ],
                    'parameters': [],
                    'status': 'active' if data['priority'] <= 3 else 'planned'
                }
                for aid, data in self.question_analyses.items()
            ]
        }
        self._write_yaml('base-types/analysis-types.yaml', analysis_types)

        # Generate visualization-types.yaml
        visualization_types = {
            'visualizationTypes': [
                {
                    'id': vid,
                    'code': self.visualization_codes[vid],
                    'name': data['name'],
                    'description': data['description'],
                    'detailedDescription': self._get_visualization_description(vid),
                    'supportedDataStructures': [],
                    'parameters': [],
                    'status': 'active' if data['priority'] <= 3 else 'planned'
                }
                for vid, data in self.visualizations.items()
            ]
        }
        self._write_yaml('base-types/visualization-types.yaml', visualization_types)

    def _generate_registries(self):
        """Generate registry YAML files"""
        # Generate analyses/_registry.yaml
        analyses_registry = {
            'analyses': {
                'question': [
                    {
                        'id': aid,
                        'code': self.analysis_codes[aid],
                        'path': f'question_analyses/{self.analysis_codes[aid]}',
                        'status': 'active' if data['priority'] <= 3 else 'planned'
                    }
                    for aid, data in self.question_analyses.items()
                ],
                'section': [
                    {
                        'id': aid,
                        'code': self.analysis_codes[aid],
                        'path': f'section_analyses/{self.analysis_codes[aid]}',
                        'status': 'active' if data['priority'] <= 3 else 'planned'
                    }
                    for aid, data in self.section_analyses.items()
                ]
            }
        }
        self._write_yaml('analyses/_registry.yaml', analyses_registry)

        # Generate visualizations/_registry.yaml
        visualizations_registry = {
            'visualizations': [
                {
                    'id': vid,
                    'code': self.visualization_codes[vid],
                    'path': self.visualization_codes[vid],
                    'status': 'active' if data['priority'] <= 3 else 'planned'
                }
                for vid, data in self.visualizations.items()
            ]
        }
        self._write_yaml('visualizations/_registry.yaml', visualizations_registry)

    def _generate_analysis_files(self):
        """Generate analysis metadata files"""
        # Generate question analysis files
        for aid, data in self.question_analyses.items():
            metadata = {
                'id': aid,
                'code': self.analysis_codes[aid],
                'name': data['name'],
                'description': data['description'],
                'detailedDescription': self._get_analysis_description(data['name']),
                'version': self.config['default_version'],
                'author': self.config['default_author'],
                'created': datetime.now().isoformat(),
                'modified': datetime.now().isoformat(),
                'dependencies': [],
                'inputRequirements': [],
                'outputFormat': {
                    'type': 'object',
                    'schema': {}
                },
                'supportedVisualizations': [
                    {
                        'id': viz,
                        'code': self.visualization_codes[viz],
                        'mapping': {}
                    }
                    for viz in data['visualizations']
                ]
            }
            self._write_yaml(f'analyses/question_analyses/{self.analysis_codes[aid]}/metadata.yaml', metadata)

        # Generate section analysis files
        for aid, data in self.section_analyses.items():
            metadata = {
                'id': aid,
                'code': self.analysis_codes[aid],
                'name': data['name'],
                'description': data['description'],
                'detailedDescription': self._get_analysis_description(data['name']),
                'version': self.config['default_version'],
                'author': self.config['default_author'],
                'created': datetime.now().isoformat(),
                'modified': datetime.now().isoformat(),
                'dependencies': [],
                'inputRequirements': [],
                'outputFormat': {
                    'type': 'object',
                    'schema': {}
                },
                'supportedVisualizations': [
                    {
                        'id': viz,
                        'code': self.visualization_codes[viz],
                        'mapping': {}
                    }
                    for viz in data['visualizations']
                ]
            }
            self._write_yaml(f'analyses/section_analyses/{self.analysis_codes[aid]}/metadata.yaml', metadata)

    def _generate_visualization_files(self):
        """Generate visualization metadata files"""
        for vid, data in self.visualizations.items():
            metadata = {
                'id': vid,
                'code': self.visualization_codes[vid],
                'name': data['name'],
                'version': self.config['default_version'],
                'description': data['description'],
                'detailedDescription': self._get_visualization_description(vid),
                'author': self.config['default_author'],
                'created': datetime.now().isoformat(),
                'modified': datetime.now().isoformat(),
                'supportedDataStructures': [
                    {
                        'type': 'object',
                        'mapping': {
                            'required': {},
                            'optional': {}
                        }
                    }
                ],
                'inputValidation': {},
                'parameters': {
                    'inherits': [],
                    'specific': []
                }
            }
            self._write_yaml(f'visualizations/{self.visualization_codes[vid]}/metadata.yaml', metadata)

    def _generate_implementation_status(self):
        """Generate implementation status YAML"""
        status = {
            'status': {
                'questionTypes': {
                    self.question_codes[qid]: 'active'
                    for qid in self.questions.keys()
                },
                'analyses': {
                    **{
                        self.analysis_codes[aid]: 'active' if data['priority'] <= 3 else 'planned'
                        for aid, data in self.section_analyses.items()
                    },
                    **{
                        self.analysis_codes[aid]: 'active' if data['priority'] <= 3 else 'planned'
                        for aid, data in self.question_analyses.items()
                    }
                },
                'visualizations': {
                    self.visualization_codes[vid]: 'active' if data['priority'] <= 3 else 'planned'
                    for vid, data in self.visualizations.items()
                }
            }
        }
        self._write_yaml('implementation-status.yaml', status)

    def _write_yaml(self, relative_path: str, data: Dict[str, Any]):
        """Write YAML data to file"""
        try:
            file_path = self.config['output_dir'] / relative_path
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w') as f:
                yaml.safe_dump(data, f, allow_unicode=True, sort_keys=False)
                
        except Exception as e:
            raise FileSystemError(f"Failed to write YAML file {relative_path}: {e}")
