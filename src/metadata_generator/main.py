#!/usr/bin/env python3

import logging
import sys
import shutil
from pathlib import Path
from typing import Dict, Any

try:
    from .generator import MetadataGenerator
    from .config import load_config
except ImportError:
    from generator import MetadataGenerator
    from config import load_config

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('metadata_generation.log')
        ]
    )

def confirm_output_directory_cleanup(output_dir: Path) -> bool:
    """Ask user to confirm deletion of output directory"""
    if output_dir.exists():
        print(f"\nVýstupní adresář {output_dir} již existuje.")
        print("Pro čistou generaci je potřeba ho smazat.")
        response = input("Chcete pokračovat a smazat adresář? [y/N] ").strip().lower()
        return response == 'y'
    return True

def main():
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        config = load_config()
        output_dir = Path(config['output_dir'])

        if not confirm_output_directory_cleanup(output_dir):
            logger.info("Generování přerušeno uživatelem")
            sys.exit(0)

        if output_dir.exists():
            shutil.rmtree(output_dir)
            logger.info(f"Smazán výstupní adresář: {output_dir}")

        generator = MetadataGenerator(config)
        generator.generate()
        logger.info("Metadata structure generated successfully")
    except Exception as e:
        logger.error(f"Failed to generate metadata structure: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
