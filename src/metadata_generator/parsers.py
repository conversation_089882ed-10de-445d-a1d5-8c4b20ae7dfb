import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple
from io import StringIO
import pandas as pd


def parse_markdown_table(file_path: Path) -> pd.DataFrame:
    """Parse a markdown table file into a pandas DataFrame"""
    logger = logging.getLogger(__name__)
    if not file_path.exists():
        logger.error(f"Input file not found: {file_path}")
        raise FileNotFoundError(f"Input file not found: {file_path}")

    logger.info(f"Parsing markdown table from {file_path}")

    with open(file_path) as f:
        content = f.read()

    # Split content into lines and find table sections
    lines = content.strip().split('\n')
    logger.debug(f"Found {len(lines)} lines in {file_path}")

    # Get first table only - ignore any duplicates
    table_start = None
    table_end = None
    for i, line in enumerate(lines):
        if line.strip().startswith('|'):
            if table_start is None:
                table_start = i
                logger.debug(f"Found table start at line {i}")
            table_end = i
        elif table_start is not None and table_end is not None:
            break

    if table_start is None or table_end is None:
        logger.error(f"No valid table found in {file_path}")
        raise ValueError(f"No valid table found in {file_path}")

    table_lines = lines[table_start:table_end + 1]
    
    # Process header separately
    header = table_lines[0].strip().split('|')[1:-1]  # Remove first/last empty cells
    header = [col.strip() for col in header]

    # Pre-process data rows to handle escaped pipes and ensure consistent fields
    data = []
    for line in table_lines[2:]:  # Skip header and separator
        # Replace escaped pipes with a temporary marker
        line = line.replace('\\|', '{{PIPE}}')
        # Split by unescaped pipes and clean up cells
        cells = [cell.strip().replace('{{PIPE}}', '|') for cell in line.strip().split('|')[1:-1]]
        # Ensure we have the right number of cells
        while len(cells) < len(header):
            cells.append('')
        data.append(cells[:len(header)])  # Truncate if too many cells

    # Create DataFrame from processed data
    df = pd.DataFrame(data, columns=header)
    
    if df.empty:
        raise ValueError("No valid data rows found in table")

    # Replace empty strings with None
    df = df.replace(r'^\s*$', None, regex=True)

    # Convert priority to int, handling any non-numeric values
    if 'Priorita' in df.columns:
        df['Priorita'] = pd.to_numeric(df['Priorita'], errors='coerce').fillna(1).astype(int)
    elif 'priority' in df.columns:
        df['priority'] = pd.to_numeric(df['priority'], errors='coerce').fillna(1).astype(int)
    
    return df

def parse_questions(file_path: Path) -> Tuple[Dict[str, Any], Dict[str, str]]:
    """Parse questions from otazky.md"""
    df = parse_markdown_table(file_path)
    questions = {}
    codes = {}
    
    for _, row in df.iterrows():
        qid = str(row['ID']).strip()
        questions[qid] = {
            'name': str(row['Název']).strip(),
            'description': str(row['Popis a použití']).strip()
        }
        codes[qid] = str(row['Code']).strip()

    return questions, codes

def parse_section_analyses(file_path: Path) -> Tuple[Dict[str, Any], Dict[str, str]]:
    """Parse section analyses from analyzy-sekce.md"""
    df = parse_markdown_table(file_path)
    analyses = {}
    codes = {}

    for _, row in df.iterrows():
        aid = str(row['ID']).strip()
        analyses[aid] = {
            'name': str(row['Název']).strip(),
            'visualizations': ([v.strip() for v in row['Možné vizualizace (ID)'].split(',')]
                             if 'Možné vizualizace (ID)' in row else []),
            'description': str(row['Popis']).strip(),
            'priority': int(row['Priorita']) if 'Priorita' in row else 1
        }
        codes[aid] = str(row['Code']).strip()

    return analyses, codes

def parse_question_analyses(file_path: Path) -> Tuple[Dict[str, Any], Dict[str, str]]:
    """Parse question analyses from analyzy-otazky.md"""
    df = parse_markdown_table(file_path)
    analyses = {}
    codes = {}

    for _, row in df.iterrows():
        aid = str(row['ID']).strip()
        analyses[aid] = {
            'name': str(row['Název']).strip(),
            'visualizations': [v.strip() for v in row['Možné vizualizace (ID)'].split(',')],
            'description': str(row['Popis']).strip(),
            'priority': int(row['Priorita'])
        }
        codes[aid] = str(row['Code']).strip()

    return analyses, codes

def parse_visualizations(file_path: Path) -> Tuple[Dict[str, Any], Dict[str, str]]:
    """Parse visualizations from vizualizace.md"""
    df = parse_markdown_table(file_path)
    visualizations = {}
    codes = {}

    for _, row in df.iterrows():
        vid = str(row['ID']).strip()
        visualizations[vid] = {
            'name': str(row['Název']).strip(),
            'description': str(row['Popis']).strip(),
            'priority': int(row['Priorita'])
        }
        codes[vid] = str(row['Code']).strip()

    return visualizations, codes

def parse_question_mappings(file_path: Path) -> Dict[str, List[str]]:
    """Parse question to analysis mappings from otazky2analyzy.md"""
    df = parse_markdown_table(file_path)
    mappings = {}
    
    for _, row in df.iterrows():
        qid = str(row['Question ID']).strip()
        aid = str(row['Analysis ID']).strip()
        
        if qid not in mappings:
            mappings[qid] = []
            
        if aid and aid != '-':  # Skip empty mappings
            mappings[qid].append(aid)
            
    return mappings
