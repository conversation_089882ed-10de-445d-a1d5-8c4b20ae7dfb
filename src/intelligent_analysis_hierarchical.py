"""
Hierarchical Analysis Module

Rozsireni pro Menu 13 - hierarchicka analyza s respektovanim poradi.
"""

import os
import json
import logging
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)


class HierarchicalAnalysisManager:
    """Spravce hierarchicke analyzy pruzkumu"""
    
    def __init__(self):
        """Inicializace manageru"""
        self.question_analyzer = None
        logger.info("HierarchicalAnalysisManager inicializovan")
        
        # Pokus o nacteni analysis_engine (volitelne)
        try:
            from analysis_engine import MetadataLoader, QuestionAnalyzer
            self.metadata_loader = MetadataLoader()
            self.metadata_loader.load_all_metadata()
            self.question_analyzer = QuestionAnalyzer(self.metadata_loader)
            logger.info("Analysis Engine nacten")
        except ImportError:
            logger.warning("Analysis Engine neni dostupny - pouzije se zjednodusena analyza")
    
    def analyze_survey_hierarchical(self, lss_file_path: str, csv_file_path: Optional[str] = None) -> Optional[Dict]:
        """
        Analyzuje pruzkum s respektovanim hierarchie a poradi
        
        Args:
            lss_file_path: Cesta k LSS souboru
            csv_file_path: Volitelna cesta k CSV souboru s odpovědmi
            
        Returns:
            Slovnik s hierarchickou analyzou nebo None pri chybe
        """
        try:
            logger.info(f"Hierarchicka analyza pruzkumu z LSS: {lss_file_path}")
            
            # 1. Nacteni struktury z LSS s hierarchii
            survey_structure = self._extract_hierarchical_structure_from_lss(lss_file_path)
            
            if not survey_structure:
                logger.error("Nepodarilo se nacist hierarchickou strukturu z LSS souboru")
                return None
            
            logger.info(f"Nacteno {survey_structure['total_questions']} otazek ve {survey_structure['total_groups']} skupinach")
            
            # 2. Analyza otazek s hierarchii
            analyzed_structure = self._analyze_hierarchical_structure(survey_structure)
            
            # 3. Doporuceni analyz na urovni otazek a skupin
            analysis_plan = self._recommend_hierarchical_analyses(analyzed_structure)
            
            # 4. Mapovani vizualizaci
            chart_plan = self._map_hierarchical_visualizations(analysis_plan)
            
            # 5. Analyza CSV dat (pokud je k dispozici)
            data_analysis = None
            if csv_file_path and os.path.exists(csv_file_path):
                data_analysis = self._analyze_csv_data(csv_file_path)
            
            # 6. Sestaveni hierarchickeho vysledku
            result = {
                'survey_info': {
                    'lss_file': lss_file_path,
                    'csv_file': csv_file_path,
                    'total_groups': survey_structure['total_groups'],
                    'total_questions': survey_structure['total_questions'],
                    'has_data': data_analysis is not None,
                    'analysis_type': 'hierarchical'
                },
                'hierarchical_structure': analyzed_structure,
                'analysis_plan': analysis_plan,
                'chart_plan': chart_plan,
                'data_analysis': data_analysis,
                'summary': self._generate_hierarchical_summary(analyzed_structure, analysis_plan, chart_plan, data_analysis)
            }
            
            logger.info("Hierarchicka analyza pruzkumu dokoncena")
            return result
            
        except Exception as e:
            logger.error(f"Chyba pri hierarchicke analyze pruzkumu: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def _extract_hierarchical_structure_from_lss(self, lss_file_path: str) -> Optional[Dict]:
        """Extrahuje hierarchickou strukturu z LSS souboru"""
        try:
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from lss_parser import LSSParser

            parser = LSSParser()
            survey_data = parser.parse_lss_file(lss_file_path)

            if not survey_data:
                logger.error("LSS soubor neobsahuje validni data")
                return None

            # Ziskani statistik
            stats = parser.get_survey_statistics(survey_data)
            
            # Extrakce skupin a otazek s hierarchii
            groups = []
            all_questions = survey_data.get('questions', [])
            
            # Pokud jsou skupiny definovane v LSS
            lss_groups = survey_data.get('groups', [])
            if lss_groups:
                # Seradit skupiny podle logickeho poradi (Uvod prvni, pak podle sekci)
                def get_group_sort_key(group):
                    gid = int(group.get('gid', 999))
                    name = group.get('group_name', '').lower()
                    
                    # Normalizace diakritiky pro porovnani
                    import unicodedata
                    name_normalized = unicodedata.normalize('NFD', name)
                    name_ascii = ''.join(c for c in name_normalized if unicodedata.category(c) != 'Mn')
                    
                    # Uvod skupiny maji prioritu
                    if 'uvod' in name_ascii:
                        if len(name) < 10:  # "Uvod" (kratsi)
                            return (0, gid)  # Nejdriv kratsi Uvod
                        else:
                            return (1, gid)  # Pak delsi UVOD
                    
                    # Sekce podle cisla
                    if 'sekce' in name_ascii:
                        import re
                        match = re.search(r'sekce\s+(\d+)', name_ascii, re.IGNORECASE)
                        if match:
                            section_num = int(match.group(1))
                            return (section_num + 10, gid)  # Sekce 2 = 12, Sekce 3 = 13, atd.
                    
                    # Ostatni na konec
                    return (999, gid)
                
                lss_groups_sorted = sorted(lss_groups, key=get_group_sort_key)
                
                for group_index, group_data in enumerate(lss_groups_sorted):
                    group_id = str(group_data.get('gid', ''))
                    
                    # Najit otazky patrici do teto skupiny
                    group_questions = [q for q in all_questions if q.group_id == group_id]
                    
                    # Seradit otazky podle question_order
                    group_questions.sort(key=lambda q: q.question_order)
                    
                    # Pouzit group_order z LSS (uz je int) nebo index jako fallback
                    group_order = group_data.get('group_order', group_index)
                    if isinstance(group_order, str):
                        try:
                            group_order = int(group_order)
                        except ValueError:
                            group_order = group_index
                    
                    group = {
                        'group_id': group_id,
                        'group_name': group_data.get('group_name', f'Skupina {group_id}'),
                        'group_description': group_data.get('description', ''),
                        'group_order': group_order,
                        'question_count': len(group_questions),
                        'questions': [self._format_question_for_hierarchy(q) for q in group_questions]
                    }
                    groups.append(group)
            else:
                # Pokud nejsou skupiny definovane, vytvorit jednu skupinu
                all_questions.sort(key=lambda q: q.question_order)
                group = {
                    'group_id': '1',
                    'group_name': 'Vsechny otazky',
                    'group_description': 'Pruzkum bez definovanych skupin',
                    'group_order': 1,
                    'question_count': len(all_questions),
                    'questions': [self._format_question_for_hierarchy(q) for q in all_questions]
                }
                groups.append(group)

            return {
                'survey_id': survey_data.get('survey_id', 'unknown'),
                'total_groups': len(groups),
                'total_questions': stats['total_questions'],
                'total_subquestions': stats['total_subquestions'],
                'total_answer_options': stats['total_answer_options'],
                'groups': groups,
                'question_types_distribution': stats['question_types'],
                'metadata': survey_data.get('metadata', {})
            }
            
        except Exception as e:
            logger.error(f"Chyba pri extrakci hierarchicke struktury: {str(e)}")
            return None

    def _format_question_for_hierarchy(self, lss_question) -> Dict:
        """Formatuje otazku pro hierarchickou strukturu"""
        try:
            # Seradit subotazky podle order nebo scale_id
            subquestions = sorted(lss_question.subquestions, 
                                key=lambda sq: (sq.get('order', 0), sq.get('scale_id', 0)))
            
            # Seradit moznosti odpovedi podle order nebo scale_id
            answer_options = sorted(lss_question.answer_options,
                                  key=lambda ao: (ao.get('order', 0), ao.get('scale_id', 0)))
            
            return {
                'qid': lss_question.qid,
                'question_code': lss_question.question_code,
                'question_text': lss_question.question_text,
                'question_type': lss_question.question_type,
                'question_order': lss_question.question_order,
                'help_text': lss_question.help_text,
                'subquestions': subquestions,
                'answer_options': answer_options,
                'subquestion_count': len(subquestions),
                'answer_option_count': len(answer_options),
                'attributes': lss_question.attributes
            }
            
        except Exception as e:
            logger.warning(f"Chyba pri formatovani otazky {lss_question.qid}: {str(e)}")
            return {
                'qid': lss_question.qid,
                'question_code': lss_question.question_code,
                'question_text': lss_question.question_text,
                'question_type': lss_question.question_type,
                'question_order': getattr(lss_question, 'question_order', 0),
                'error': str(e)
            }

    def _analyze_hierarchical_structure(self, survey_structure: Dict) -> Dict:
        """Analyzuje hierarchickou strukturu a doporuci analyzy"""
        try:
            analyzed_groups = []
            all_analyzed_questions = []
            
            for group in survey_structure['groups']:
                analyzed_questions = []
                
                for question_data in group['questions']:
                    # Zjednodusena analyza bez analysis_engine
                    analyzed_question = self._simple_analyze_question(question_data)
                    analyzed_questions.append(analyzed_question)
                    all_analyzed_questions.append(analyzed_question)
                
                # Analyza skupiny
                group_analysis = {
                    'group_id': group['group_id'],
                    'group_name': group['group_name'],
                    'group_description': group['group_description'],
                    'group_order': group['group_order'],
                    'question_count': len(analyzed_questions),
                    'questions': analyzed_questions,
                    'group_statistics': self._calculate_group_statistics(analyzed_questions),
                    'recommended_group_analyses': self._recommend_group_analyses(analyzed_questions)
                }
                analyzed_groups.append(group_analysis)
            
            # Seradit skupiny podle group_order pro finalni vystup
            analyzed_groups.sort(key=lambda g: g['group_order'])
            
            return {
                'survey_id': survey_structure['survey_id'],
                'total_groups': len(analyzed_groups),
                'total_questions': len(all_analyzed_questions),
                'groups': analyzed_groups,
                'overall_statistics': self._calculate_overall_statistics(all_analyzed_questions),
                'question_types_distribution': survey_structure['question_types_distribution']
            }
            
        except Exception as e:
            logger.error(f"Chyba pri analyze hierarchicke struktury: {str(e)}")
            return {}

    def _simple_analyze_question(self, question_data: Dict) -> Dict:
        """Zjednodusena analyza otazky bez analysis_engine"""
        question_type = question_data.get('question_type', '')
        
        # Zakladni mapovani typu otazek
        type_mapping = {
            'T': {'name': 'Long Free Text', 'confidence': 1.0, 'analyses': ['THA', 'COA']},
            'S': {'name': 'Short Free Text', 'confidence': 1.0, 'analyses': ['THA', 'WCA']},
            'M': {'name': 'Multiple choice', 'confidence': 1.0, 'analyses': ['FRA', 'THA']},
            'Y': {'name': 'Yes/No', 'confidence': 1.0, 'analyses': ['FRA', 'CTA']},
            'L': {'name': 'List (Radio)', 'confidence': 1.0, 'analyses': ['FRA', 'DIA']},
            '5': {'name': '5 Point Choice', 'confidence': 1.0, 'analyses': ['STA', 'DIA']},
            'A': {'name': 'Array (5 Point)', 'confidence': 1.0, 'analyses': ['STA', 'CRA']},
            'F': {'name': 'Array (Flex. Labels)', 'confidence': 1.0, 'analyses': ['FRA', 'CMA']},
        }
        
        matched_type = type_mapping.get(question_type, {
            'name': f'Unknown ({question_type})',
            'confidence': 0.5,
            'analyses': ['FRA']
        })
        
        return {
            'qid': question_data['qid'],
            'question_code': question_data['question_code'],
            'question_text': question_data['question_text'],
            'question_type': question_type,
            'matched_type': {
                'id': question_type,
                'name': matched_type['name'],
                'description': f'Typ otazky {question_type}'
            },
            'confidence': matched_type['confidence'],
            'recommended_analyses': matched_type['analyses'],
            'subquestion_count': question_data.get('subquestion_count', 0),
            'answer_option_count': question_data.get('answer_option_count', 0),
            'analysis_notes': f"Mapovano na typ: {matched_type['name']} | Spolehlivost: {matched_type['confidence']:.1%}"
        }

    def _calculate_group_statistics(self, analyzed_questions: List) -> Dict:
        """Vypocita statistiky pro skupinu"""
        if not analyzed_questions:
            return {}
        
        recognized = len([q for q in analyzed_questions if q.get('matched_type')])
        total = len(analyzed_questions)
        
        question_types = {}
        for q in analyzed_questions:
            q_type = q.get('question_type', 'unknown')
            question_types[q_type] = question_types.get(q_type, 0) + 1
        
        return {
            'total_questions': total,
            'recognized_questions': recognized,
            'recognition_rate': recognized / total if total > 0 else 0,
            'question_types': question_types,
            'avg_confidence': sum(q.get('confidence', 0) for q in analyzed_questions) / total if total > 0 else 0
        }

    def _recommend_group_analyses(self, analyzed_questions: List) -> List[str]:
        """Doporuci analyzy pro celou skupinu"""
        question_types = set(q.get('question_type', '') for q in analyzed_questions)
        
        group_analyses = []
        
        # Pokud skupina obsahuje vice otazek, doporucit komparativni analyzy
        if len(analyzed_questions) > 1:
            group_analyses.append('CMA')  # Comparative Analysis
            
        # Pokud jsou ve skupine skalove otazky, doporucit korelacni analyzu
        scale_types = {'5', 'A', 'B', 'C', 'H', 'E', '1', ':', ';'}
        if any(qt in scale_types for qt in question_types):
            group_analyses.append('CRA')  # Correlation Analysis
            
        # Pokud jsou ve skupine multiple choice otazky, doporucit kontingencni analyzu
        if any(qt in {'M', 'P'} for qt in question_types):
            group_analyses.append('CTA')  # Contingency Analysis
            
        return group_analyses

    def _calculate_overall_statistics(self, all_analyzed_questions: List) -> Dict:
        """Vypocita celkove statistiky pruzkumu"""
        return self._calculate_group_statistics(all_analyzed_questions)

    def _recommend_hierarchical_analyses(self, analyzed_structure: Dict) -> Dict:
        """Doporuci analyzy na zaklade hierarchicke struktury"""
        try:
            group_analyses = []
            for group in analyzed_structure['groups']:
                group_analyses.extend(group.get('recommended_group_analyses', []))
            
            return {
                'question_level_analyses': [],
                'section_level_analyses': [],
                'group_level_analyses': list(set(group_analyses)),
                'cross_question_analyses': [],
                'total_estimated_time': len(group_analyses) * 30,  # 30 min na analyzu
                'complexity_score': min(len(group_analyses) * 0.5, 10)  # Max 10
            }
            
        except Exception as e:
            logger.error(f"Chyba pri doporucovani hierarchickych analyz: {str(e)}")
            return {}

    def _map_hierarchical_visualizations(self, analysis_plan: Dict) -> Dict:
        """Mapuje analyzy na vizualizace pro hierarchickou strukturu"""
        try:
            total_analyses = (len(analysis_plan.get('question_level_analyses', [])) + 
                            len(analysis_plan.get('group_level_analyses', [])))
            
            return {
                'primary_visualizations': [],
                'alternative_visualizations': [],
                'group_visualizations': [],
                'unsupported_analyses': [],
                'total_charts_estimate': total_analyses * 2,
                'datawrapper_compatibility': 0.7
            }
            
        except Exception as e:
            logger.error(f"Chyba pri mapovani hierarchickych vizualizaci: {str(e)}")
            return {}

    def _analyze_csv_data(self, csv_file_path: str) -> Optional[Dict]:
        """Analyzuje CSV data (pokud jsou k dispozici)"""
        try:
            file_size = os.path.getsize(csv_file_path)
            
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                line_count = sum(1 for line in f)
            
            return {
                'file_path': csv_file_path,
                'file_size': file_size,
                'estimated_responses': max(0, line_count - 1),
                'has_data': line_count > 1,
                'analysis_possible': line_count > 1
            }
            
        except Exception as e:
            logger.warning(f"Chyba pri analyze CSV dat: {str(e)}")
            return None

    def _generate_hierarchical_summary(self, analyzed_structure: Dict, analysis_plan: Dict, 
                                     chart_plan: Dict, data_analysis: Optional[Dict]) -> Dict:
        """Generuje souhrn hierarchicke analyzy"""
        try:
            total_analyses = (len(analysis_plan.get('question_level_analyses', [])) + 
                            len(analysis_plan.get('section_level_analyses', [])) + 
                            len(analysis_plan.get('group_level_analyses', [])) + 
                            len(analysis_plan.get('cross_question_analyses', [])))
            
            overall_stats = analyzed_structure.get('overall_statistics', {})
            
            return {
                'total_groups': analyzed_structure.get('total_groups', 0),
                'total_questions': analyzed_structure.get('total_questions', 0),
                'recognized_questions': overall_stats.get('recognized_questions', 0),
                'recognition_rate': overall_stats.get('recognition_rate', 0),
                'total_analyses': total_analyses,
                'total_visualizations': chart_plan.get('total_charts_estimate', 0),
                'estimated_time_hours': analysis_plan.get('total_estimated_time', 0) / 60,
                'complexity_score': analysis_plan.get('complexity_score', 0),
                'datawrapper_compatibility': chart_plan.get('datawrapper_compatibility', 0),
                'implementation_priority': 'Vysoka' if analysis_plan.get('complexity_score', 0) <= 3 else 'Stredni' if analysis_plan.get('complexity_score', 0) <= 6 else 'Nizka',
                'has_response_data': data_analysis is not None,
                'estimated_responses': data_analysis.get('estimated_responses', 0) if data_analysis else 0
            }
            
        except Exception as e:
            logger.error(f"Chyba pri generovani hierarchickeho souhrnu: {str(e)}")
            return {}

    def export_analysis_report(self, analysis_result: Dict, output_path: str) -> bool:
        """
        Exportuje analyzu do JSON souboru
        
        Args:
            analysis_result: Vysledek analyzy
            output_path: Cesta k vystupnimu souboru
            
        Returns:
            True pri uspechu
        """
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Analyza exportovana do: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Chyba pri exportu analyzy: {str(e)}")
            return False