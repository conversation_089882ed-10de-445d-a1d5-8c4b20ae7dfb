try:
    import pandas as pd
except ImportError:
    pd = None

try:
    from datawrapper_client import DatawrapperClient
except ImportError:
    DatawrapperClient = None

from logger import get_logger
from typing import Dict, List
import json
import os

logger = get_logger(__name__)

class ChartGenerator:
    def __init__(self, survey_title="", data_source="", data_source_link="", byline="",
                 png_width=600, png_border=10, png_scale=2, auto_height=True,
                 full_header_footer=False, transparent_bg=False):
        if DatawrapperClient is None:
            logger.error("DatawrapperClient není dostupný. Zkontrolujte, zda existuje src/datawrapper_client.py")
            self.dw = None
        else:
            self.dw = DatawrapperClient()
        self.chart_types = {
            'single_choice': 'd3-bars',
            'multiple_choice': 'd3-bars-stacked',
            'scale': 'd3-bars',
            'text': 'd3-bars',
            'array': 'd3-bars-stacked'  # Pro otázky typu pole
        }
        # Metadata pro grafy
        self.survey_title = survey_title
        self.data_source = data_source
        self.data_source_link = data_source_link
        self.byline = byline
        # PNG export nastavení
        self.png_width = png_width
        self.png_border = png_border
        self.png_scale = png_scale
        self.auto_height = auto_height
        self.full_header_footer = full_header_footer
        self.transparent_bg = transparent_bg
        
    def _parse_survey_id(self, chart_data_path: str) -> str:
        """Parsuje ID průzkumu z cesty k souboru"""
        import re
        # Hledá pattern data/XXXXXX/ nebo survey_XXXXXX_
        match = re.search(r'(?:data/|survey_)(\d{6})(?:/|_)', chart_data_path)
        if match:
            return match.group(1)
        # Pokud nenajde pattern, zkusí extrahovat číslo z cesty
        numbers = re.findall(r'\d{6}', chart_data_path)
        return numbers[0] if numbers else 'unknown'

    def generate_charts(self, chart_data_path: str, output_base: str, survey_id: str = None, question_codes: List[str] = None) -> List[Dict]:
        """Generuje grafy pro vybrané otázky"""
        if pd is None:
            logger.error("Pandas knihovna není dostupná. Pro generování grafů je potřeba nainstalovat pandas.")
            return []
            
        if self.dw is None:
            logger.error("DatawrapperClient není dostupný. Generování grafů není možné.")
            return []
            
        try:
            # Načtení dat
            with open(chart_data_path, 'r') as f:
                chart_data = json.load(f)
                
            results = []
            
            # Použití předaného survey_id nebo parsování z cesty
            if not survey_id:
                survey_id = self._parse_survey_id(chart_data_path)
            
            # Vytvoření lokální složky pro PNG
            output_dir = f"{output_base}"  # Nebudeme duplikovat survey_id
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"Vytvořena lokální složka: {output_dir}")
            
            # Vytvoření/získání složky na serveru
            folder = self.dw.create_folder(survey_id)
            if not folder:
                logger.error(f"Nepodařilo se vytvořit/najít složku na serveru, ale pokračuji dál")
                
            # Generování grafů
            for question in chart_data:
                if question_codes and question['code'] not in question_codes:
                    continue
                chart_type = self.chart_types.get(question['type'], 'd3-bars')
                
                # Vytvoření grafu s metadaty
                chart_params = {
                    'title': question['name'],
                    'chart_type': chart_type,
                    'description': self.survey_title,
                    'data_source': self.data_source,
                    'data_source_link': self.data_source_link,
                    'byline': self.byline
                }
                if folder:
                    chart_params['folder_id'] = folder['id']
                    
                chart = self.dw.create_chart(**chart_params)
                if not chart:
                    continue
                    
                # Převod dat do DataFrame
                df = pd.DataFrame(question['data'])
                
                # Aktualizace dat grafu
                if not self.dw.update_chart_data(chart['id'], df):
                    continue
                
                # Publikování grafu (nutné před exportem)
                publish_result = self.dw.publish_chart(chart['id'])
                if not publish_result:
                    continue
                    
                # Export do PNG s vypočítanou výškou podle poměru stran
                png_data = self.dw.export_chart(
                    chart['id'],
                    export_format='png',
                    target_width=self.png_width or 600,  # Cílová šířka
                    border_width=self.png_border,
                    zoom=self.png_scale,  # Pro PNG se používá zoom místo scale
                    plain=not self.full_header_footer,  # True = jen graf, False = s hlavičkou
                    mode='rgba' if self.transparent_bg else 'rgb'
                )
                if png_data:
                    # Vytvoření složky pro PNG
                    output_dir = f"{output_base}/{survey_id}"
                    os.makedirs(output_dir, exist_ok=True)
                    png_path = f"{output_dir}/{question['code']}.png"
                    with open(png_path, 'wb') as f:
                        f.write(png_data)
                        
                results.append({
                    'question_code': question['code'],
                    'chart_id': chart['id'],
                    'chart_url': publish_result['url'],
                    'png_path': png_path
                })
                
            return results
            
        except Exception as e:
            logger.error(f"Chyba při generování grafů: {str(e)}")
            return []
