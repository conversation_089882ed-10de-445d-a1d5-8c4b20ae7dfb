"""
HTML Generator

Generování HTML souboru s grafy pomocí Jinja2 template engine.
Responsive design, JavaScript filtrování, chart embedding.
"""

import os
import re
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
from jinja2 import Environment, FileSystemLoader, Template

from .datawrapper_client import ChartInfo

logger = logging.getLogger(__name__)


class HTMLGenerator:
    """Generátor HTML souborů s grafy"""
    
    def __init__(self, template_dir: Optional[str] = None):
        """
        Inicializace HTML generátoru
        
        Args:
            template_dir: Cesta k složce s templates (None = použije default)
        """
        if template_dir is None:
            # Default template directory
            current_dir = Path(__file__).parent
            template_dir = current_dir / "templates"
        
        self.template_dir = Path(template_dir)
        
        # Inicializace Jinja2 environment
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            autoescape=True,
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # Přidání custom filtrů
        self.jinja_env.filters['lower'] = str.lower
        
        logger.info(f"HTMLGenerator inicializován s template dir: {self.template_dir}")

    def _remove_version_from_url(self, url: str) -> str:
        """
        Odstraní číslo verze z Datawrapper URL pro stabilní odkazy

        Args:
            url: Původní URL (např. https://datawrapper.dwcdn.net/14J5J/2/)

        Returns:
            URL bez verze (např. https://datawrapper.dwcdn.net/14J5J/)
        """
        if not url:
            return url

        # Pattern pro Datawrapper URL s verzí: /chartId/version/
        pattern = r'(https://datawrapper\.dwcdn\.net/[^/]+)/\d+/?$'
        match = re.match(pattern, url)

        if match:
            # Vrátíme URL bez verze, ale s koncovým lomítkem
            return match.group(1) + '/'

        # Pokud pattern nesedí, vrátíme původní URL
        return url

    def generate_export_html(self,
                           charts: List[ChartInfo],
                           survey_id: str,
                           output_path: str,
                           survey_name: Optional[str] = None,
                           template_name: str = "chart_export.html") -> bool:
        """
        Generuje HTML soubor s grafy
        
        Args:
            charts: Seznam grafů k zobrazení
            survey_id: ID průzkumu
            output_path: Cesta k výstupnímu HTML souboru
            survey_name: Název průzkumu (volitelné)
            template_name: Název template souboru
            
        Returns:
            True pokud bylo generování úspěšné
        """
        try:
            logger.info(f"Generuji HTML export pro {len(charts)} grafů")
            
            # Načtení template
            template = self.jinja_env.get_template(template_name)
            
            # Příprava dat pro template
            template_data = self._prepare_template_data(
                charts, survey_id, survey_name
            )
            
            # Renderování HTML
            html_content = template.render(**template_data)
            
            # Uložení do souboru
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML export úspěšně vygenerován: {output_path}")
            logger.info(f"Velikost souboru: {output_path.stat().st_size / 1024:.1f} KB")
            
            return True
            
        except Exception as e:
            logger.error(f"Chyba při generování HTML exportu: {str(e)}")
            return False
    
    def _prepare_template_data(self, 
                             charts: List[ChartInfo], 
                             survey_id: str,
                             survey_name: Optional[str]) -> Dict:
        """Připraví data pro template"""
        
        # Filtrování grafů - pouze ty s share_url
        valid_charts = [chart for chart in charts if chart.share_url]
        
        if len(valid_charts) < len(charts):
            logger.warning(f"Filtrováno {len(charts) - len(valid_charts)} grafů bez share_url")
        
        # Příprava dat o grafech
        chart_data = []
        for chart in valid_charts:
            # Odstranění verze z URL pro stabilní odkazy
            stable_url = self._remove_version_from_url(chart.share_url)

            chart_info = {
                'id': chart.id,
                'title': chart.title or 'Bez názvu',
                'type': chart.type or 'unknown',
                'share_url': stable_url,
                'language': getattr(chart, 'language', None)
            }
            chart_data.append(chart_info)
        
        # Statistiky
        chart_types = {}
        languages = {}
        
        for chart in valid_charts:
            # Počítání typů grafů
            chart_type = chart.type or 'unknown'
            chart_types[chart_type] = chart_types.get(chart_type, 0) + 1
            
            # Počítání jazyků
            language = getattr(chart, 'language', 'unknown')
            languages[language] = languages.get(language, 0) + 1
        
        template_data = {
            'survey_id': survey_id,
            'survey_name': survey_name,
            'charts': chart_data,
            'total_count': len(valid_charts),
            'generated_at': datetime.now().strftime('%d.%m.%Y %H:%M'),
            'chart_types': chart_types,
            'languages': languages
        }
        
        logger.debug(f"Template data připravena: {len(chart_data)} grafů")
        return template_data
    
    def generate_custom_html(self,
                           template_content: str,
                           data: Dict,
                           output_path: str) -> bool:
        """
        Generuje HTML z custom template stringu
        
        Args:
            template_content: Obsah template jako string
            data: Data pro template
            output_path: Cesta k výstupnímu souboru
            
        Returns:
            True pokud bylo generování úspěšné
        """
        try:
            template = Template(template_content)
            html_content = template.render(**data)
            
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"Custom HTML úspěšně vygenerován: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Chyba při generování custom HTML: {str(e)}")
            return False
    
    def validate_charts_for_export(self, charts: List[ChartInfo]) -> Dict:
        """
        Validuje grafy pro export
        
        Returns:
            Slovník s výsledky validace
        """
        total_charts = len(charts)
        valid_charts = []
        invalid_charts = []
        
        for chart in charts:
            issues = []
            
            if not chart.share_url:
                issues.append("Chybí share_url")
            
            if not chart.title or chart.title.strip() == '':
                issues.append("Chybí název grafu")
            
            if not chart.id:
                issues.append("Chybí ID grafu")
            
            if issues:
                invalid_charts.append({
                    'chart': chart,
                    'issues': issues
                })
            else:
                valid_charts.append(chart)
        
        validation_result = {
            'total_charts': total_charts,
            'valid_charts': len(valid_charts),
            'invalid_charts': len(invalid_charts),
            'validation_passed': len(invalid_charts) == 0,
            'invalid_chart_details': invalid_charts
        }
        
        if invalid_charts:
            logger.warning(f"Validace selhala: {len(invalid_charts)} neplatných grafů")
            for invalid in invalid_charts:
                logger.warning(f"Graf {invalid['chart'].id}: {', '.join(invalid['issues'])}")
        else:
            logger.info(f"Validace úspěšná: všech {total_charts} grafů je platných")
        
        return validation_result
    
    def get_template_info(self, template_name: str = "chart_export.html") -> Dict:
        """Vrátí informace o template"""
        try:
            template_path = self.template_dir / template_name
            
            if not template_path.exists():
                return {
                    'exists': False,
                    'error': f"Template {template_name} neexistuje"
                }
            
            stat = template_path.stat()
            
            return {
                'exists': True,
                'path': str(template_path),
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%d.%m.%Y %H:%M'),
                'template_dir': str(self.template_dir)
            }
            
        except Exception as e:
            return {
                'exists': False,
                'error': f"Chyba při načítání informací o template: {str(e)}"
            }
    
    def list_available_templates(self) -> List[str]:
        """Vrátí seznam dostupných templates"""
        try:
            if not self.template_dir.exists():
                return []
            
            templates = []
            for file_path in self.template_dir.glob("*.html"):
                templates.append(file_path.name)
            
            return sorted(templates)
            
        except Exception as e:
            logger.error(f"Chyba při načítání seznamu templates: {str(e)}")
            return []
