"""
Utility functions for Datawrapper Export Module

Error handling, logging, user feedback a pomocné funkce.
"""

import os
import logging
import traceback
from typing import Dict, List, Optional, Any, Callable
from functools import wraps
from datetime import datetime
from pathlib import Path


class ExportLogger:
    """Centralizovaný logging pro export modul"""
    
    def __init__(self, name: str = "datawrapper_export", log_file: Optional[str] = None):
        self.logger = logging.getLogger(name)
        self.log_file = log_file
        
        # Nastavení loggeru pokud ještě nen<PERSON>
        if not self.logger.handlers:
            self._setup_logger()
    
    def _setup_logger(self):
        """Nastavení loggeru"""
        self.logger.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler (pokud je specifiko<PERSON>)
        if self.log_file:
            try:
                log_path = Path(self.log_file)
                log_path.parent.mkdir(parents=True, exist_ok=True)
                
                file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
                file_handler.setLevel(logging.DEBUG)
                file_handler.setFormatter(formatter)
                self.logger.addHandler(file_handler)
            except Exception as e:
                self.logger.warning(f"Nepodařilo se nastavit file logging: {e}")
    
    def info(self, message: str):
        """Info zpráva"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Warning zpráva"""
        self.logger.warning(message)
    
    def error(self, message: str, exc_info: bool = False):
        """Error zpráva"""
        self.logger.error(message, exc_info=exc_info)
    
    def debug(self, message: str):
        """Debug zpráva"""
        self.logger.debug(message)


def handle_exceptions(logger: Optional[ExportLogger] = None, 
                     return_value: Any = None,
                     user_message: Optional[str] = None):
    """
    Decorator pro handling exceptions s loggingem
    
    Args:
        logger: Logger instance
        return_value: Hodnota k vrácení při chybě
        user_message: User-friendly zpráva při chybě
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_msg = f"Chyba v {func.__name__}: {str(e)}"
                
                if logger:
                    logger.error(error_msg, exc_info=True)
                else:
                    logging.error(error_msg, exc_info=True)
                
                if user_message:
                    print(f"❌ {user_message}")
                
                return return_value
        return wrapper
    return decorator


def validate_environment() -> Dict[str, Any]:
    """
    Validace prostředí pro Datawrapper export
    
    Returns:
        Slovník s výsledky validace
    """
    validation_result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'config': {}
    }
    
    # Kontrola API klíče
    api_key = os.getenv('DATAWRAPPER_API_KEY')
    if not api_key:
        validation_result['valid'] = False
        validation_result['errors'].append("DATAWRAPPER_API_KEY není nastaven v .env")
    else:
        validation_result['config']['api_key'] = "✅ Nastaven"
    
    # Kontrola team ID
    team_id = os.getenv('DATAWRAPPER_TEAM_ID')
    if not team_id:
        validation_result['warnings'].append("DATAWRAPPER_TEAM_ID není nastaven - složky se budou vytvářet v private sekci")
        validation_result['config']['team_id'] = "⚠️ Nenastaven"
    else:
        validation_result['config']['team_id'] = f"✅ {team_id}"
    
    # Kontrola folder ID
    folder_id = os.getenv('DATAWRAPPER_LIMESURVEY_FOLDER_ID')
    if not folder_id:
        validation_result['warnings'].append("DATAWRAPPER_LIMESURVEY_FOLDER_ID není nastaven - složky se budou vytvářet v root týmu")
        validation_result['config']['folder_id'] = "⚠️ Nenastaven"
    else:
        validation_result['config']['folder_id'] = f"✅ {folder_id}"
    
    # Kontrola závislostí
    try:
        import jinja2
        validation_result['config']['jinja2'] = f"✅ {jinja2.__version__}"
    except ImportError:
        validation_result['valid'] = False
        validation_result['errors'].append("Jinja2 není nainstalován - spusťte: pip install jinja2>=3.1.0")
        validation_result['config']['jinja2'] = "❌ Chybí"
    
    try:
        import requests
        validation_result['config']['requests'] = f"✅ {requests.__version__}"
    except ImportError:
        validation_result['valid'] = False
        validation_result['errors'].append("Requests není nainstalován")
        validation_result['config']['requests'] = "❌ Chybí"
    
    return validation_result


def format_file_size(size_bytes: int) -> str:
    """Formátování velikosti souboru"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes / (1024 * 1024):.1f} MB"


def format_duration(seconds: float) -> str:
    """Formátování doby trvání"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours}h {minutes}m"


def safe_filename(filename: str) -> str:
    """Vytvoří bezpečný název souboru"""
    # Nahradí nebezpečné znaky
    unsafe_chars = '<>:"/\\|?*'
    safe_name = filename
    
    for char in unsafe_chars:
        safe_name = safe_name.replace(char, '_')
    
    # Ořízne délku
    if len(safe_name) > 200:
        safe_name = safe_name[:200]
    
    return safe_name


def create_backup_filename(original_path: str) -> str:
    """Vytvoří název pro záložní soubor"""
    path = Path(original_path)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    backup_name = f"{path.stem}_backup_{timestamp}{path.suffix}"
    return str(path.parent / backup_name)


def ensure_directory(path: str) -> bool:
    """Zajistí existenci složky"""
    try:
        Path(path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"Nepodařilo se vytvořit složku {path}: {e}")
        return False


def cleanup_old_files(directory: str, pattern: str, max_age_days: int = 7) -> int:
    """
    Vyčistí staré soubory ze složky
    
    Args:
        directory: Cesta ke složce
        pattern: Vzor názvu souboru (glob pattern)
        max_age_days: Maximální stáří v dnech
        
    Returns:
        Počet smazaných souborů
    """
    try:
        from datetime import timedelta
        
        directory_path = Path(directory)
        if not directory_path.exists():
            return 0
        
        cutoff_time = datetime.now() - timedelta(days=max_age_days)
        deleted_count = 0
        
        for file_path in directory_path.glob(pattern):
            if file_path.is_file():
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_time:
                    try:
                        file_path.unlink()
                        deleted_count += 1
                    except Exception as e:
                        logging.warning(f"Nepodařilo se smazat {file_path}: {e}")
        
        return deleted_count
        
    except Exception as e:
        logging.error(f"Chyba při čištění souborů: {e}")
        return 0


class ProgressTracker:
    """Sledování progress s user feedback"""
    
    def __init__(self, total_steps: int, show_percentage: bool = True):
        self.total_steps = total_steps
        self.current_step = 0
        self.show_percentage = show_percentage
        self.start_time = datetime.now()
        
    def update(self, step_name: str, increment: int = 1):
        """Aktualizace progress"""
        self.current_step += increment
        
        if self.show_percentage:
            percentage = (self.current_step / self.total_steps) * 100
            print(f"🔄 {step_name} ({percentage:.1f}%)")
        else:
            print(f"🔄 {step_name} ({self.current_step}/{self.total_steps})")
    
    def finish(self, success_message: str = "Dokončeno"):
        """Dokončení progress"""
        duration = (datetime.now() - self.start_time).total_seconds()
        print(f"✅ {success_message} za {format_duration(duration)}")


def user_confirm(message: str, default: bool = False) -> bool:
    """Požádá uživatele o potvrzení"""
    default_text = "Y/n" if default else "y/N"
    
    try:
        response = input(f"{message} ({default_text}): ").strip().lower()
        
        if not response:
            return default
        
        return response in ['y', 'yes', 'ano', 'a']
        
    except (EOFError, KeyboardInterrupt):
        return default


def display_validation_results(validation: Dict[str, Any]):
    """Zobrazí výsledky validace prostředí"""
    print("\n" + "=" * 50)
    print("🔍 VALIDACE PROSTŘEDÍ")
    print("=" * 50)
    
    # Konfigurace
    print("📋 Konfigurace:")
    for key, value in validation['config'].items():
        print(f"  {key}: {value}")
    
    # Warnings
    if validation['warnings']:
        print("\n⚠️ Varování:")
        for warning in validation['warnings']:
            print(f"  - {warning}")
    
    # Errors
    if validation['errors']:
        print("\n❌ Chyby:")
        for error in validation['errors']:
            print(f"  - {error}")
    
    # Celkový stav
    if validation['valid']:
        print("\n✅ Prostředí je připraveno pro export")
    else:
        print("\n❌ Prostředí není připraveno - opravte chyby výše")
    
    print("=" * 50)
