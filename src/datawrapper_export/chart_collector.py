"""
Chart Collector

Modul pro sběr vš<PERSON> grafů z Datawrapper složky podle survey ID.
Automatic<PERSON>é <PERSON>ění folder ID, sběr graf<PERSON>, filtrování podle typu/jazyka.
"""

import logging
from typing import List, Optional, Dict, Set
from dataclasses import dataclass
from .datawrapper_client import DatawrapperExportClient, ChartInfo

logger = logging.getLogger(__name__)


@dataclass
class CollectionStats:
    """Statistiky sběru grafů"""
    total_charts: int = 0
    czech_charts: int = 0
    english_charts: int = 0
    other_language_charts: int = 0
    chart_types: Dict[str, int] = None
    
    def __post_init__(self):
        if self.chart_types is None:
            self.chart_types = {}


class ChartCollector:
    """Sběr grafů z Datawrapper složky"""
    
    def __init__(self, client: DatawrapperExportClient):
        self.client = client
        self.stats = CollectionStats()
        
    def collect_charts_for_survey(self, survey_id: str, 
                                language_filter: Optional[str] = None,
                                chart_type_filter: Optional[str] = None) -> List[ChartInfo]:
        """
        Hlavní metoda pro sběr grafů podle survey ID
        
        Args:
            survey_id: ID průzkumu
            language_filter: Filtr podle jazyka ('cs', 'en', None pro všechny)
            chart_type_filter: Filtr podle typu grafu (None pro všechny)
            
        Returns:
            Seznam ChartInfo objektů
        """
        logger.info(f"Začínám sběr grafů pro survey {survey_id}")
        
        # Reset statistik
        self.stats = CollectionStats()
        
        # Najdeme složku pro survey
        folder_id = self.client.find_survey_folder(survey_id)
        if not folder_id:
            logger.error(f"Složka pro survey {survey_id} nebyla nalezena")
            return []
        
        # Získáme všechny grafy ze složky
        charts = self.client.get_charts_in_folder(folder_id)
        if not charts:
            logger.warning(f"Ve složce {folder_id} nebyly nalezeny žádné grafy")
            return []
        
        logger.info(f"Nalezeno {len(charts)} grafů ve složce {folder_id}")
        
        # Rychlá detekce jazyka z názvů (bez metadata API volání)
        charts_with_language = self._detect_language_from_titles(charts)

        # Aplikujeme filtry
        filtered_charts = self._apply_filters(
            charts_with_language,
            language_filter,
            chart_type_filter
        )
        
        # Aktualizujeme statistiky
        self._update_stats(enriched_charts)
        
        logger.info(f"Po filtrování zůstalo {len(filtered_charts)} grafů")
        self._log_collection_stats()
        
        return filtered_charts

    def _detect_language_from_titles(self, charts: List[ChartInfo]) -> List[ChartInfo]:
        """Rychlá detekce jazyka z názvů grafů (bez API volání)"""
        for chart in charts:
            # Detekce jazyka podle obsahu názvu
            title = chart.title.lower() if chart.title else ""

            # České indikátory
            czech_indicators = [
                'zájem', 'míra', 'názor', 'hodnocení', 'zkušenost', 'účast',
                'zapojení', 'konzultace', 'výstup', 'projekt', 'právní',
                'předpis', 'tvorba', 'proces', 'legislativ', 'úřad'
            ]

            # Anglické indikátory
            english_indicators = [
                'involvement', 'level', 'opinion', 'evaluation', 'experience',
                'participation', 'consultation', 'output', 'project', 'legal',
                'regulation', 'creation', 'process', 'legislative', 'office'
            ]

            # Počítání indikátorů
            czech_score = sum(1 for indicator in czech_indicators if indicator in title)
            english_score = sum(1 for indicator in english_indicators if indicator in title)

            # Rozhodnutí o jazyce
            if czech_score > english_score:
                chart.language = 'cs'
            elif english_score > czech_score:
                chart.language = 'en'
            else:
                # Fallback - pokud není jasné, zkusíme podle znaků
                if any(char in title for char in 'áčďéěíňóřšťúůýž'):
                    chart.language = 'cs'
                else:
                    chart.language = 'en'  # Default pro nerozpoznané

        return charts

    def _enrich_charts_info(self, charts: List[ChartInfo]) -> List[ChartInfo]:
        """Rozšíří informace o grafech o metadata"""
        enriched_charts = []
        
        for chart in charts:
            try:
                # Získáme metadata grafu
                metadata = self.client.get_chart_metadata(chart.id)
                if metadata:
                    chart.metadata = metadata
                    
                    # Detekce jazyka z metadata
                    language = self._detect_chart_language(metadata)
                    if not hasattr(chart, 'language'):
                        chart.language = language
                
                enriched_charts.append(chart)
                
            except Exception as e:
                logger.warning(f"Chyba při rozšiřování informací o grafu {chart.id}: {str(e)}")
                # Přidáme graf i bez metadata
                enriched_charts.append(chart)
        
        return enriched_charts
    
    def _detect_chart_language(self, metadata: Dict) -> str:
        """Detekuje jazyk grafu z metadata"""
        try:
            # Zkusíme locale z publish metadata
            publish_meta = metadata.get('publish', {})
            locale = publish_meta.get('locale', '')
            
            if locale.startswith('cs'):
                return 'cs'
            elif locale.startswith('en'):
                return 'en'
            
            # Zkusíme detekci z titulku nebo popisu
            title = metadata.get('describe', {}).get('title', '')
            intro = metadata.get('describe', {}).get('intro', '')
            
            # Jednoduchá detekce podle českých znaků
            czech_chars = set('áčďéěíňóřšťúůýž')
            text_to_check = (title + ' ' + intro).lower()
            
            if any(char in text_to_check for char in czech_chars):
                return 'cs'
            
            # Default je angličtina
            return 'en'
            
        except Exception as e:
            logger.debug(f"Chyba při detekci jazyka: {str(e)}")
            return 'unknown'
    
    def _apply_filters(self, charts: List[ChartInfo], 
                      language_filter: Optional[str],
                      chart_type_filter: Optional[str]) -> List[ChartInfo]:
        """Aplikuje filtry na seznam grafů"""
        filtered_charts = charts
        
        # Filtr podle jazyka
        if language_filter:
            filtered_charts = [
                chart for chart in filtered_charts 
                if getattr(chart, 'language', 'unknown') == language_filter
            ]
            logger.info(f"Po filtru jazyka ({language_filter}): {len(filtered_charts)} grafů")
        
        # Filtr podle typu grafu
        if chart_type_filter:
            filtered_charts = [
                chart for chart in filtered_charts 
                if chart.type == chart_type_filter
            ]
            logger.info(f"Po filtru typu ({chart_type_filter}): {len(filtered_charts)} grafů")
        
        return filtered_charts
    
    def _update_stats(self, charts: List[ChartInfo]):
        """Aktualizuje statistiky sběru"""
        self.stats.total_charts = len(charts)
        
        # Počítání podle jazyků
        for chart in charts:
            language = getattr(chart, 'language', 'unknown')
            if language == 'cs':
                self.stats.czech_charts += 1
            elif language == 'en':
                self.stats.english_charts += 1
            else:
                self.stats.other_language_charts += 1
            
            # Počítání podle typů grafů
            chart_type = chart.type
            if chart_type in self.stats.chart_types:
                self.stats.chart_types[chart_type] += 1
            else:
                self.stats.chart_types[chart_type] = 1
    
    def _log_collection_stats(self):
        """Zaloguje statistiky sběru"""
        logger.info("=== Statistiky sběru grafů ===")
        logger.info(f"Celkem grafů: {self.stats.total_charts}")
        logger.info(f"České grafy: {self.stats.czech_charts}")
        logger.info(f"Anglické grafy: {self.stats.english_charts}")
        logger.info(f"Ostatní jazyky: {self.stats.other_language_charts}")
        
        logger.info("Typy grafů:")
        for chart_type, count in self.stats.chart_types.items():
            logger.info(f"  {chart_type}: {count}")
    
    def get_available_languages(self, charts: List[ChartInfo]) -> Set[str]:
        """Vrátí seznam dostupných jazyků v grafech"""
        languages = set()
        for chart in charts:
            language = getattr(chart, 'language', 'unknown')
            languages.add(language)
        return languages
    
    def get_available_chart_types(self, charts: List[ChartInfo]) -> Set[str]:
        """Vrátí seznam dostupných typů grafů"""
        chart_types = set()
        for chart in charts:
            chart_types.add(chart.type)
        return chart_types
    
    def get_collection_stats(self) -> CollectionStats:
        """Vrátí statistiky posledního sběru"""
        return self.stats
