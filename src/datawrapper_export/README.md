# Datawrapper Export Module

## 🎯 **Přehled**

Datawrapper Export Module umožňuje export všech grafů z Datawrapper složky do HTML souboru s filtrováním a plnou funkcionalitou sdílení.

### **Hlavní funkce:**
- 📊 Export všech grafů ze složky do HTML
- 🔍 JavaScript filtrování podle názvu grafu
- 📱 Responsive design pro všechna zařízení
- 🔗 Share linky s download funkcemi (PNG, PDF, SVG)
- 🌍 Lokalizace cs-CZ
- ⚙️ Automatická konfigurace metadata grafů

## 🚀 **Rychlý start**

### **1. Instalace závislostí**
```bash
pip install jinja2>=3.1.0
```

### **2. Konfigurace prostředí**
Přidejte do `.env` souboru:
```bash
DATAWRAPPER_API_KEY=your_api_key_here
DATAWRAPPER_TEAM_ID=57Zj-Xbm
DATAWRAPPER_LIMESURVEY_FOLDER_ID=329499
```

### **3. Použití přes Menu 12**
```bash
python src/main.py
# Vyberte průzkum (Menu 1 a 2)
# Zvolte Menu 12 - Datawrapper Export
```

### **4. Programové použití**
```python
from datawrapper_export import ExportManager

manager = ExportManager()
result = manager.export_survey_charts(
    survey_id="123456",
    output_dir="exports",
    survey_name="Můj průzkum"
)

if result.success:
    print(f"Export úspěšný: {result.output_file}")
```

## 📋 **Výstup**

### **HTML soubor obsahuje:**
- ✅ Všechny grafy ze složky s iframe embedding
- ✅ JavaScript filtrování v reálném čase
- ✅ Responsive design pro mobily a tablety
- ✅ Share linky pro každý graf
- ✅ Download funkce (PNG, PDF, SVG, data)
- ✅ Embed kódy pro další použití

### **Název souboru:** `priloha-grafy-[SURVEY_ID].html`

## 🏗️ **Architektura**

```
datawrapper_export/
├── __init__.py              # Public API
├── datawrapper_client.py    # API komunikace
├── chart_collector.py       # Sběr grafů ze složky
├── chart_configurator.py    # Konfigurace metadata
├── html_generator.py        # HTML generování
├── export_manager.py        # Hlavní orchestrace
├── utils.py                 # Pomocné funkce
└── templates/
    └── chart_export.html    # HTML template
```

## 📊 **Workflow procesu**

```
1. 🔍 Zjištění Datawrapper složky podle Survey ID
2. 📊 Sběr všech grafů ze složky
3. ⚙️ Konfigurace metadata (locale, download options)
4. 🔄 Republish grafů s novými nastaveními
5. 🎨 Generování HTML s JavaScript filtrováním
6. ✅ Výstupní soubor připraven k použití
```

## 🎛️ **API Reference**

### **ExportManager - Hlavní třída**
```python
manager = ExportManager(progress_callback=None)

result = manager.export_survey_charts(
    survey_id: str,
    output_dir: str = ".",
    survey_name: Optional[str] = None,
    language_filter: Optional[str] = None,  # "cs", "en", None
    force_reconfigure: bool = True
)
```

### **Validace prostředí**
```python
from datawrapper_export import validate_environment, display_validation_results

validation = validate_environment()
display_validation_results(validation)
```

### **Custom progress tracking**
```python
def progress_callback(progress):
    print(f"🔄 {progress.current_step} ({progress.progress_percentage:.1f}%)")

manager = ExportManager(progress_callback=progress_callback)
```

## 🔧 **Konfigurace**

### **Environment Variables**
| Proměnná | Povinná | Popis |
|----------|---------|-------|
| `DATAWRAPPER_API_KEY` | ✅ | API klíč pro Datawrapper |
| `DATAWRAPPER_TEAM_ID` | ⚠️ | ID týmu (doporučené) |
| `DATAWRAPPER_LIMESURVEY_FOLDER_ID` | ⚠️ | ID parent složky |

### **Automatická konfigurace grafů**
Modul automaticky nastavuje pro všechny grafy:
- 🌍 Locale: `cs-CZ`
- 📥 Data download: povoleno
- 🖼️ Image download: PNG, PDF, SVG
- 🔗 Embed link: povolen
- 📱 Social sharing: povolen

## 🧪 **Testování**

### **Phase 1 - Single Chart Test**
```bash
python test/test_datawrapper_export_phase1.py
```
Testuje vytvoření, konfiguraci a HTML export jednoho grafu.

### **Phase 2 - Folder Integration Test**
```bash
python test/test_datawrapper_export_phase2.py
```
Testuje kompletní workflow s reálnou složkou.

### **Příklady použití**
```bash
python examples/datawrapper_export_examples.py
```
Interaktivní příklady různých způsobů použití.

## 🐛 **Troubleshooting**

### **Časté problémy:**

#### **❌ "DATAWRAPPER_API_KEY není nastaven"**
- Přidejte API klíč do `.env` souboru
- Získání: Datawrapper → Account Settings → API tokens

#### **❌ "Složka pro survey nebyla nalezena"**
- Zkontrolujte název složky v Datawrapper
- Složka musí obsahovat Survey ID v názvu

#### **❌ "Nebyly nalezeny žádné grafy"**
- Ujistěte se, že složka obsahuje publikované grafy
- Zkontrolujte správnost složky

#### **❌ "Jinja2 není nainstalován"**
```bash
pip install jinja2>=3.1.0
```

### **Debug kroky:**
```python
# 1. Test připojení
from datawrapper_export import ExportManager
manager = ExportManager()
print(manager.test_connection())

# 2. Zobrazení složek
from datawrapper_export import DatawrapperExportClient
client = DatawrapperExportClient()
folders = client.get_folders()
for folder in folders:
    print(f"{folder['name']} (ID: {folder['id']})")
```

## 📈 **Performance**

### **Doporučení podle velikosti:**
- **< 10 grafů:** Rychlé zpracování (< 30s)
- **10-50 grafů:** Optimální pro produkci (1-3 min)
- **> 50 grafů:** Zvažte filtrování podle jazyka

### **Optimalizace:**
- Použijte `language_filter` pro rychlejší zpracování
- Nastavte `force_reconfigure=False` pro rychlejší běh
- HTML používá lazy loading pro iframe elementy

## 📚 **Dokumentace**

- **[User Guide](../../docs/14_datawrapper_export_user_guide.md)** - Kompletní návod k použití
- **[API Reference](../../docs/15_datawrapper_export_api.md)** - Detailní API dokumentace
- **[Module Design](../../docs/12_datawrapper_export_module.md)** - Architektura modulu
- **[Task Breakdown](../../docs/13_datawrapper_export_tasks.md)** - Implementační plán

## 🔒 **Bezpečnost**

- API klíče jsou načítány z environment variables
- HTML soubory neobsahují citlivá data
- Grafy jsou embedovány přes iframe z Datawrapper
- Share URL jsou veřejně přístupné

## 📊 **Monitoring**

Modul poskytuje detailní logy a metriky:
- Počet zpracovaných grafů
- Doba zpracování jednotlivých kroků
- Úspěšnost konfigurace grafů
- Velikost výstupního souboru
- Statistiky podle jazyků a typů grafů

## 🤝 **Přispívání**

1. Fork repository
2. Vytvořte feature branch
3. Implementujte změny s testy
4. Aktualizujte dokumentaci
5. Vytvořte Pull Request

## 📄 **Licence**

Součást LimWrapp projektu - viz hlavní LICENSE soubor.

---

**💡 Pro nejlepší výsledky používejte konzistentní pojmenování grafů a složek v Datawrapper.**
