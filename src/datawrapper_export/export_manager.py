"""
Export Manager

Hlav<PERSON><PERSON> orchestrace Datawrapper export modulu:
- Koordinace všech komponent
- Workflow řízení
- Progress reporting
- Error aggregation
- File output management
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from datetime import datetime

from .datawrapper_client import DatawrapperExportClient, ChartInfo
from .chart_collector import ChartCollector, CollectionStats
from .chart_configurator import ChartConfigurator, ConfigurationResult
from .html_generator import HTMLGenerator

logger = logging.getLogger(__name__)


@dataclass
class ExportProgress:
    """Progress reporting pro export"""
    current_step: str
    step_number: int
    total_steps: int
    charts_processed: int = 0
    total_charts: int = 0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
    
    @property
    def progress_percentage(self) -> float:
        if self.total_steps == 0:
            return 0.0
        return (self.step_number / self.total_steps) * 100
    
    @property
    def charts_percentage(self) -> float:
        if self.total_charts == 0:
            return 0.0
        return (self.charts_processed / self.total_charts) * 100


@dataclass
class ExportResult:
    """Výsledek export procesu"""
    success: bool
    output_file: Optional[str] = None
    charts_exported: int = 0
    total_charts: int = 0
    collection_stats: Optional[CollectionStats] = None
    configuration_results: List[ConfigurationResult] = None
    errors: List[str] = None
    execution_time: float = 0.0
    
    def __post_init__(self):
        if self.configuration_results is None:
            self.configuration_results = []
        if self.errors is None:
            self.errors = []


class ExportManager:
    """Hlavní orchestrace Datawrapper export modulu"""
    
    def __init__(self, progress_callback: Optional[Callable[[ExportProgress], None]] = None):
        """
        Inicializace Export Manager
        
        Args:
            progress_callback: Callback funkce pro reporting progress
        """
        self.progress_callback = progress_callback
        
        # Inicializace komponent
        self.client = DatawrapperExportClient()
        self.collector = ChartCollector(self.client)
        self.configurator = ChartConfigurator(self.client)
        self.html_generator = HTMLGenerator()
        
        self.current_progress = None
        
        logger.info("ExportManager inicializován")
    
    def export_survey_charts(self,
                           survey_id: str,
                           output_dir: str = ".",
                           survey_name: Optional[str] = None,
                           language_filter: Optional[str] = None,
                           force_reconfigure: bool = True) -> ExportResult:
        """
        Hlavní metoda pro export grafů průzkumu
        
        Args:
            survey_id: ID průzkumu
            output_dir: Výstupní složka
            survey_name: Název průzkumu (volitelné)
            language_filter: Filtr podle jazyka ('cs', 'en', None)
            force_reconfigure: Zda vynutit rekonfiguraci grafů
            
        Returns:
            ExportResult s výsledky exportu
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"Začínám export grafů pro survey {survey_id}")
            
            # Inicializace progress
            self.current_progress = ExportProgress(
                current_step="Inicializace",
                step_number=0,
                total_steps=6
            )
            self._report_progress()
            
            # Krok 1: Sběr grafů
            self._update_progress("Sběr grafů ze složky", 1)
            logger.info(f"🔍 Hledám složku pro survey {survey_id}...")

            charts = self.collector.collect_charts_for_survey(
                survey_id, language_filter
            )

            if not charts:
                error_msg = f"Nebyly nalezeny žádné grafy pro survey {survey_id}"
                logger.error(error_msg)
                return ExportResult(
                    success=False,
                    errors=[error_msg],
                    execution_time=(datetime.now() - start_time).total_seconds()
                )

            self.current_progress.total_charts = len(charts)
            logger.info(f"✅ Načteno {len(charts)} grafů")

            # Zobrazení jazykového rozdělení
            czech_count = sum(1 for chart in charts if getattr(chart, 'language', '') == 'cs')
            english_count = sum(1 for chart in charts if getattr(chart, 'language', '') == 'en')
            logger.info(f"📊 České grafy: {czech_count}, Anglické grafy: {english_count}")

            self._report_progress()
            
            # Krok 2: Validace grafů
            self._update_progress("Validace grafů", 2)
            validation_result = self.html_generator.validate_charts_for_export(charts)
            
            if not validation_result['validation_passed']:
                logger.warning(f"Validace grafů selhala: {validation_result['invalid_charts']} neplatných grafů")
            
            # Krok 3: Konfigurace grafů
            self._update_progress("Konfigurace metadata grafů", 3)
            configuration_results = self.configurator.configure_charts_for_export(
                charts, force_reconfigure
            )
            
            # Krok 4: Aktualizace share URLs
            self._update_progress("Aktualizace share URLs", 4)
            self._update_chart_urls(charts, configuration_results)
            
            # Krok 5: Generování HTML
            self._update_progress("Generování HTML souboru", 5)
            output_file = self._generate_output_filename(survey_id, output_dir)
            
            html_success = self.html_generator.generate_export_html(
                charts=charts,
                survey_id=survey_id,
                output_path=output_file,
                survey_name=survey_name
            )
            
            if not html_success:
                error_msg = "Chyba při generování HTML souboru"
                logger.error(error_msg)
                return ExportResult(
                    success=False,
                    errors=[error_msg],
                    execution_time=(datetime.now() - start_time).total_seconds()
                )
            
            # Krok 6: Finalizace
            self._update_progress("Dokončování exportu", 6)
            
            # Sestavení výsledku
            execution_time = (datetime.now() - start_time).total_seconds()
            
            result = ExportResult(
                success=True,
                output_file=output_file,
                charts_exported=len(charts),
                total_charts=len(charts),
                collection_stats=self.collector.get_collection_stats(),
                configuration_results=configuration_results,
                execution_time=execution_time
            )
            
            logger.info(f"Export úspěšně dokončen za {execution_time:.1f}s")
            logger.info(f"Výstupní soubor: {output_file}")
            
            return result
            
        except Exception as e:
            error_msg = f"Neočekávaná chyba při exportu: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            return ExportResult(
                success=False,
                errors=[error_msg],
                execution_time=(datetime.now() - start_time).total_seconds()
            )
    
    def _update_progress(self, step_name: str, step_number: int):
        """Aktualizuje progress"""
        if self.current_progress:
            self.current_progress.current_step = step_name
            self.current_progress.step_number = step_number
            self._report_progress()
    
    def _report_progress(self):
        """Reportuje progress pomocí callback"""
        if self.progress_callback and self.current_progress:
            self.progress_callback(self.current_progress)
        
        # Také logujeme
        if self.current_progress:
            logger.info(f"Progress: {self.current_progress.current_step} "
                       f"({self.current_progress.step_number}/{self.current_progress.total_steps}) "
                       f"- {self.current_progress.progress_percentage:.1f}%")
    
    def _update_chart_urls(self, charts: List[ChartInfo], 
                          configuration_results: List[ConfigurationResult]):
        """Aktualizuje share URLs grafů podle výsledků konfigurace"""
        url_updates = {result.chart_id: result.new_share_url 
                      for result in configuration_results 
                      if result.success and result.new_share_url}
        
        updated_count = 0
        for chart in charts:
            if chart.id in url_updates:
                chart.share_url = url_updates[chart.id]
                updated_count += 1
        
        logger.info(f"Aktualizováno {updated_count} share URLs")
    
    def _generate_output_filename(self, survey_id: str, output_dir: str) -> str:
        """Generuje název výstupního souboru"""
        filename = f"priloha-grafy-{survey_id}.html"
        output_path = Path(output_dir) / filename
        return str(output_path)
    
    def get_export_summary(self, result: ExportResult) -> Dict:
        """Vytvoří souhrn exportu"""
        summary = {
            "success": result.success,
            "execution_time": f"{result.execution_time:.1f}s",
            "charts_exported": result.charts_exported,
            "total_charts": result.total_charts
        }
        
        if result.output_file:
            summary["output_file"] = result.output_file
            
            # Informace o souboru
            try:
                file_path = Path(result.output_file)
                if file_path.exists():
                    file_size = file_path.stat().st_size
                    summary["file_size"] = f"{file_size / 1024:.1f} KB"
            except Exception:
                pass
        
        # Statistiky sběru
        if result.collection_stats:
            summary["collection_stats"] = {
                "czech_charts": result.collection_stats.czech_charts,
                "english_charts": result.collection_stats.english_charts,
                "other_language_charts": result.collection_stats.other_language_charts,
                "chart_types": result.collection_stats.chart_types
            }
        
        # Statistiky konfigurace
        if result.configuration_results:
            successful_configs = sum(1 for r in result.configuration_results if r.success)
            summary["configuration_stats"] = {
                "successful": successful_configs,
                "failed": len(result.configuration_results) - successful_configs,
                "success_rate": f"{(successful_configs/len(result.configuration_results)*100):.1f}%"
            }
        
        # Chyby
        if result.errors:
            summary["errors"] = result.errors
        
        return summary
    
    def test_connection(self) -> Dict:
        """Testuje připojení k Datawrapper API"""
        try:
            folders = self.client.get_folders()
            return {
                "success": True,
                "folders_count": len(folders),
                "api_accessible": True
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "api_accessible": False
            }
