import pandas as pd
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QTableWidgetItem
import csv

class TableManager:
    def __init__(self, table_widget):
        self.table = table_widget
        self.current_data = None
        self.filtered_data = None
        self.current_page = 0
        self.sort_column = None
        self.sort_order = Qt.AscendingOrder
        self.default_separator = ';'  # Výchoz<PERSON> oddělov<PERSON> středník
        
    def load_data(self, data, separator=None):
        """Načte data do tabulky
        
        Args:
            data: Cesta k CSV souboru nebo DataFrame
            separator: <PERSON><PERSON><PERSON><PERSON> odd<PERSON> (výchozí je středník)
        """
        try:
            sep = separator if separator is not None else self.default_separator
            
            if isinstance(data, str):
                # Pokud je vstup cesta k souboru
                try:
                    # Nejdřív zkusíme standardní načtení
                    self.current_data = pd.read_csv(data, sep=sep)
                except pd.errors.ParserError:
                    # Pokud selže, zkusíme s parametry pro problematická CSV
                    self.current_data = pd.read_csv(
                        data,
                        sep=sep,  # Použít specifikovaný oddělovač
                        escapechar='\\',  # Použít zpětné lomítko pro escape sekvence
                        quoting=csv.QUOTE_MINIMAL,  # Minimální použití uvozovek
                        on_bad_lines='warn',  # Varovat při problémových řádcích
                        encoding='utf-8',  # Explicitní kódování
                        engine='python'  # Použít Python parser místo C
                    )
            elif isinstance(data, pd.DataFrame):
                # Pokud je vstup DataFrame
                self.current_data = data
            else:
                # Pokud je vstup jiný typ dat
                self.current_data = pd.DataFrame(data)
                
            # Vyčistit data
            self.current_data = self.clean_data(self.current_data)
            
            # Inicializovat filtrovaná data
            self.filtered_data = self.current_data.copy()
            self.update_table()
            
        except Exception as e:
            print(f"Chyba při načítání dat: {str(e)}")
            # Vytvořit prázdný DataFrame v případě chyby
            self.current_data = pd.DataFrame()
            self.filtered_data = pd.DataFrame()
            self.update_table()
            
    def save_data(self, file_path):
        """Uloží data do CSV souboru"""
        if self.current_data is not None:
            try:
                self.current_data.to_csv(file_path, sep=self.default_separator, index=False)
                return True
            except Exception as e:
                print(f"Chyba při ukládání dat: {str(e)}")
                return False
        return False
        
    def clean_data(self, df):
        """Vyčistí data od problémových hodnot"""
        # Nahradit NaN hodnoty prázdným řetězcem
        df = df.fillna('')
        
        # Odstranit duplicitní řádky
        df = df.drop_duplicates()
        
        # Převést všechny hodnoty na string a vyčistit
        for column in df.columns:
            df[column] = df[column].astype(str).apply(self.clean_string)
            
        return df
        
    def clean_string(self, value):
        """Vyčistí řetězec od problémových znaků"""
        if not isinstance(value, str):
            value = str(value)
            
        # Nahradit problémové znaky
        value = value.replace('\r\n', ' ')  # Nahradit konce řádků mezerou
        value = value.replace('\n', ' ')    # Nahradit nové řádky mezerou
        value = value.replace('\t', ' ')    # Nahradit tabulátory mezerou
        
        # Odstranit více mezer
        value = ' '.join(value.split())
        
        # Escapovat uvozovky
        value = value.replace('"', '\\"')
        
        # Nahradit oddělovač za bezpečný znak
        value = value.replace(self.default_separator, '|')
        
        return value
        
    def filter_data(self, text):
        """Filtruje data podle zadaného textu"""
        if not text:
            self.filtered_data = self.current_data.copy()
        else:
            try:
                # Převést vyhledávaný text na malá písmena
                search_text = text.lower()
                
                # Vytvořit masku pro filtrování
                mask = self.current_data.astype(str).apply(
                    lambda x: x.str.lower().str.contains(search_text, na=False)
                ).any(axis=1)
                
                self.filtered_data = self.current_data[mask]
            except Exception as e:
                print(f"Chyba při filtrování: {str(e)}")
                self.filtered_data = self.current_data.copy()
                
        self.current_page = 0
        self.update_table()
        
    def update_table(self):
        """Aktualizuje zobrazení tabulky"""
        if self.filtered_data is None:
            return
            
        try:
            # Získat velikost stránky a aktuální rozsah
            page_size = self.table.page_size
            start = self.current_page * page_size
            end = start + page_size
            
            # Získat data pro aktuální stránku
            page_data = self.filtered_data.iloc[start:end]
            
            # Nastavit počet řádků a sloupců
            self.table.setRowCount(len(page_data))
            self.table.setColumnCount(len(page_data.columns))
            
            # Nastavit hlavičky sloupců
            self.table.setHorizontalHeaderLabels(page_data.columns)
            
            # Naplnit tabulku daty
            for i, (_, row) in enumerate(page_data.iterrows()):
                for j, value in enumerate(row):
                    item = QTableWidgetItem(str(value))
                    self.table.setItem(i, j, item)
                    
            # Aktualizovat label stránkování
            total_pages = (len(self.filtered_data) + page_size - 1) // page_size
            self.table.page_label.setText(f"{self.current_page + 1}/{max(1, total_pages)}")
            
        except Exception as e:
            print(f"Chyba při aktualizaci tabulky: {str(e)}")
            
    def next_page(self):
        """Přejde na další stránku"""
        page_size = self.table.page_size
        if (self.current_page + 1) * page_size < len(self.filtered_data):
            self.current_page += 1
            self.update_table()
            
    def prev_page(self):
        """Přejde na předchozí stránku"""
        if self.current_page > 0:
            self.current_page -= 1
            self.update_table()
            
    def sort_by_column(self, column):
        """Seřadí data podle sloupce"""
        try:
            if self.sort_column == column:
                # Změnit směr řazení
                self.sort_order = Qt.DescendingOrder if self.sort_order == Qt.AscendingOrder else Qt.AscendingOrder
            else:
                # Nastavit nový sloupec a výchozí směr
                self.sort_column = column
                self.sort_order = Qt.AscendingOrder
                
            # Seřadit data
            ascending = self.sort_order == Qt.AscendingOrder
            self.filtered_data = self.filtered_data.sort_values(
                self.filtered_data.columns[column], 
                ascending=ascending,
                na_position='last'  # Prázdné hodnoty na konec
            )
            
            self.update_table()
            
        except Exception as e:
            print(f"Chyba při řazení: {str(e)}")
