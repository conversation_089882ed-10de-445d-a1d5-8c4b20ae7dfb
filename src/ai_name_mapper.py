import os
from typing import Dict, List
import openai
from .logger import get_logger

logger = get_logger(__name__)

class AINameMapper:
    def __init__(self):
        self.api_key = os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY není nastaven v .env")
        openai.api_key = self.api_key

    def map_names(self, questions: Dict[str, str]) -> Dict[str, str]:
        """
        Transformuje názvy otázek na kratší verze vhodné pro grafy
        
        Args:
            questions: Dict s původními názvy otázek {id: název}
            
        Returns:
            Dict s transformovanými názvy {id: nový_název}
        """
        logger.info(f"Mapuji {len(questions)} názvů otázek")
        
        try:
            prompt = self._create_prompt(questions)
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{
                    "role": "system",
                    "content": "Jsi expert na tvorbu stručných a výstižných názvů pro grafy."
                }, {
                    "role": "user", 
                    "content": prompt
                }]
            )
            
            mapped = self._parse_response(response.choices[0].message.content)
            logger.info("Mapování názvů dokončeno")
            return mapped
            
        except Exception as e:
            logger.error(f"Chyba při mapování názvů: {str(e)}")
            raise

    def _create_prompt(self, questions: Dict[str, str]) -> str:
        """Vytvoří prompt pro OpenAI"""
        items = [f"{id}: {name}" for id, name in questions.items()]
        return (
            "Převeď následující názvy otázek na stručné názvy vhodné pro grafy. "
            "Zachovej ID otázek. Odpověz ve formátu 'ID: nový název'\n\n"
            + "\n".join(items)
        )

    def _parse_response(self, response: str) -> Dict[str, str]:
        """Parsuje odpověď z OpenAI do dictionary"""
        mapped = {}
        for line in response.strip().split("\n"):
            if ":" not in line:
                continue
            id, name = line.split(":", 1)
            mapped[id.strip()] = name.strip()
        return mapped
