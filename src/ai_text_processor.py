"""
AI Text Processor pro kategorizaci textových odpovědí
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from config_loader import load_config

logger = logging.getLogger(__name__)

class AITextProcessor:
    def __init__(self):
        """Inicializace AI Text Processoru"""
        self.config = load_config()
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        
        if not self.openai_api_key:
            logger.warning("OPENAI_API_KEY není nastaven - AI funkce nebudou dostupné")
    
    def categorize_text_responses(self, responses: List[str], question_text: str, 
                                 categories: Optional[List[str]] = None,
                                 max_categories: int = 5) -> Dict[str, Any]:
        """
        Kategorizuje textové odpovědi pomocí AI
        
        Args:
            responses: <PERSON>z<PERSON> textových odpovědí
            question_text: Text otázky pro kontext
            categories: <PERSON><PERSON><PERSON><PERSON> definované kategor<PERSON> (volitelné)
            max_categories: Maximální počet kategorií
            
        Returns:
            Dict s kategorizovanými odpověďmi a statistikami
        """
        if not self.openai_api_key:
            return self._manual_categorization_fallback(responses, question_text)
        
        try:
            # Příprava dat pro AI
            unique_responses = list(set([r.strip() for r in responses if r.strip()]))
            
            if len(unique_responses) == 0:
                return {"categories": {}, "mapping": {}, "statistics": {}}
            
            # Vytvoření promptu
            prompt = self._create_categorization_prompt(
                unique_responses, question_text, categories, max_categories
            )
            
            # Volání AI API (simulace - implementace bude následovat)
            ai_result = self._call_ai_api(prompt)
            
            # Zpracování výsledku
            return self._process_ai_result(ai_result, responses)
            
        except Exception as e:
            logger.error(f"Chyba při AI kategorizaci: {str(e)}")
            return self._manual_categorization_fallback(responses, question_text)
    
    def _create_categorization_prompt(self, responses: List[str], question_text: str,
                                    categories: Optional[List[str]], max_categories: int) -> str:
        """Vytvoří prompt pro AI kategorizaci"""
        
        prompt = f"""Úkol: Kategorizuj následující textové odpovědi na otázku.

Otázka: {question_text}

Odpovědi k kategorizaci:
"""
        
        for i, response in enumerate(responses[:50], 1):  # Omezit na 50 odpovědí
            prompt += f"{i}. {response}\n"
        
        if categories:
            prompt += f"\nPoužij tyto předem definované kategorie: {', '.join(categories)}\n"
        else:
            prompt += f"\nVytvoř maximálně {max_categories} logických kategorií.\n"
        
        prompt += """
Požadavky:
1. Vytvoř jasné, stručné názvy kategorií
2. Přiřaď každou odpověď k nejvhodnější kategorii
3. Pokud odpověď nepatří nikam, použij kategorii "Ostatní"
4. Vrať výsledek ve formátu JSON:

{
  "categories": {
    "kategorie1": ["odpověď1", "odpověď2"],
    "kategorie2": ["odpověď3", "odpověď4"]
  },
  "category_descriptions": {
    "kategorie1": "Popis kategorie",
    "kategorie2": "Popis kategorie"
  }
}
"""
        return prompt
    
    def _call_ai_api(self, prompt: str) -> Dict[str, Any]:
        """Volání AI API - zatím simulace"""
        # TODO: Implementovat skutečné volání OpenAI API
        # Pro nyní vrátíme simulovaný výsledek
        
        logger.info("Simulace AI volání - implementace bude následovat")
        
        # Simulovaný výsledek
        return {
            "categories": {
                "Pozitivní": ["Velmi dobré", "Skvělé", "Perfektní"],
                "Negativní": ["Špatné", "Nevyhovující", "Problematické"],
                "Neutrální": ["Průměrné", "Standardní", "Běžné"],
                "Ostatní": ["Nevím", "Bez komentáře"]
            },
            "category_descriptions": {
                "Pozitivní": "Kladné hodnocení a spokojenost",
                "Negativní": "Záporné hodnocení a nespokojenost", 
                "Neutrální": "Neutrální nebo průměrné hodnocení",
                "Ostatní": "Nespecifikované nebo nejasné odpovědi"
            }
        }
    
    def _process_ai_result(self, ai_result: Dict[str, Any], original_responses: List[str]) -> Dict[str, Any]:
        """Zpracuje výsledek z AI a vytvoří finální mapování"""
        
        categories = ai_result.get("categories", {})
        descriptions = ai_result.get("category_descriptions", {})
        
        # Vytvoření mapování odpověď -> kategorie
        response_mapping = {}
        for category, responses in categories.items():
            for response in responses:
                response_mapping[response] = category
        
        # Statistiky
        category_counts = {}
        for response in original_responses:
            if response.strip():
                category = response_mapping.get(response.strip(), "Ostatní")
                category_counts[category] = category_counts.get(category, 0) + 1
        
        return {
            "categories": categories,
            "category_descriptions": descriptions,
            "response_mapping": response_mapping,
            "statistics": category_counts,
            "total_responses": len([r for r in original_responses if r.strip()])
        }
    
    def _manual_categorization_fallback(self, responses: List[str], question_text: str) -> Dict[str, Any]:
        """Fallback pro manuální kategorizaci když AI není dostupné"""
        
        unique_responses = list(set([r.strip() for r in responses if r.strip()]))
        
        return {
            "categories": {
                "Všechny odpovědi": unique_responses
            },
            "category_descriptions": {
                "Všechny odpovědi": "Nekategorizované textové odpovědi"
            },
            "response_mapping": {response: "Všechny odpovědi" for response in unique_responses},
            "statistics": {"Všechny odpovědi": len(unique_responses)},
            "total_responses": len(unique_responses),
            "note": "AI kategorizace není dostupná - použit fallback"
        }
    
    def save_categorization(self, categorization: Dict[str, Any], survey_id: str, 
                          question_id: str) -> str:
        """Uloží kategorizaci pro opakované použití"""
        
        categorization_dir = f"data/{survey_id}/categorizations"
        os.makedirs(categorization_dir, exist_ok=True)
        
        file_path = f"{categorization_dir}/{question_id}_categorization.json"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(categorization, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Kategorizace uložena: {file_path}")
        return file_path
    
    def load_categorization(self, survey_id: str, question_id: str) -> Optional[Dict[str, Any]]:
        """Načte uloženou kategorizaci"""
        
        file_path = f"data/{survey_id}/categorizations/{question_id}_categorization.json"
        
        if not os.path.exists(file_path):
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Chyba při načítání kategorizace: {str(e)}")
            return None
    
    def apply_categorization_to_data(self, categorization: Dict[str, Any], 
                                   responses: List[str]) -> List[str]:
        """Aplikuje kategorizaci na data a vrátí seznam kategorií"""
        
        response_mapping = categorization.get("response_mapping", {})
        
        categorized = []
        for response in responses:
            if response.strip():
                category = response_mapping.get(response.strip(), "Ostatní")
                categorized.append(category)
            else:
                categorized.append("")
        
        return categorized
