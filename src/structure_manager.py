import json
from PyQt5.QtWidgets import QTreeWidgetItem

class StructureManager:
    def __init__(self, tree_widget):
        self.tree = tree_widget
        
    def load_structure(self, structure_file):
        """Načte LSS strukturu ze souboru a zobrazí ji ve stromovém widgetu"""
        self.tree.clear()
        
        try:
            with open(structure_file, 'r', encoding='utf-8') as f:
                structure = json.load(f)
                
            root = QTreeWidgetItem(["Survey"])
            self.tree.addTopLevelItem(root)
            
            for group in structure.get('groups', []):
                group_item = QTreeWidgetItem([
                    str(group.get('group_name', '')),
                    'Group',
                    str(group.get('gid', ''))
                ])
                root.addChild(group_item)
                
                for question in group.get('questions', []):
                    question_item = QTreeWidgetItem([
                        str(question.get('question', '')),
                        'Question',
                        str(question.get('qid', ''))
                    ])
                    group_item.addChild(question_item)
                    
                    # <PERSON><PERSON><PERSON><PERSON><PERSON>
                    for subquestion in question.get('subquestions', []):
                        sub_item = QTreeWidgetItem([
                            str(subquestion.get('question', '')),
                            'Subquestion',
                            str(subquestion.get('qid', ''))
                        ])
                        question_item.addChild(sub_item)
                        
                    # Přidání dostupných odpovědí
                    available_answers = question.get('properties', {}).get('available_answers', {})
                    if isinstance(available_answers, dict):
                        answers_item = QTreeWidgetItem(["Odpovědi", "Answers", ""])
                        question_item.addChild(answers_item)
                        
                        for answer_key, answer_text in available_answers.items():
                            answer_item = QTreeWidgetItem([
                                str(answer_text),
                                'Answer',
                                str(answer_key)
                            ])
                            answers_item.addChild(answer_item)
                            
        except Exception as e:
            print(f"Chyba při načítání struktury: {str(e)}")
            
    def get_item_details(self, item):
        """Vrátí detaily o vybraném prvku ve stromě"""
        if not item:
            return None
            
        item_type = item.text(1)  # Sloupec s typem
        item_id = item.text(2)    # Sloupec s ID
        
        details = {
            'name': item.text(0),
            'type': item_type,
            'id': item_id
        }
        
        # Přidání dalších detailů podle typu
        if item_type == 'Question':
            parent = item.parent()
            if parent:
                details['group'] = parent.text(0)
                
            # Získání podotázek
            subquestions = []
            for i in range(item.childCount()):
                child = item.child(i)
                if child.text(1) == 'Subquestion':
                    subquestions.append({
                        'name': child.text(0),
                        'id': child.text(2)
                    })
            if subquestions:
                details['subquestions'] = subquestions
                
            # Získání odpovědí
            answers = []
            for i in range(item.childCount()):
                child = item.child(i)
                if child.text(1) == 'Answers':
                    for j in range(child.childCount()):
                        answer = child.child(j)
                        answers.append({
                            'text': answer.text(0),
                            'code': answer.text(2)
                        })
            if answers:
                details['answers'] = answers
                
        return details
        
    def find_item_by_id(self, item_id, start_item=None):
        """Najde položku ve stromě podle ID"""
        if start_item is None:
            root = self.tree.invisibleRootItem()
            for i in range(root.childCount()):
                result = self.find_item_by_id(item_id, root.child(i))
                if result:
                    return result
        else:
            if start_item.text(2) == str(item_id):
                return start_item
                
            for i in range(start_item.childCount()):
                result = self.find_item_by_id(item_id, start_item.child(i))
                if result:
                    return result
                    
        return None
        
    def expand_to_item(self, item):
        """Rozbalí strom až k dané položce"""
        if item:
            item.setExpanded(True)
            parent = item.parent()
            while parent:
                parent.setExpanded(True)
                parent = parent.parent()
