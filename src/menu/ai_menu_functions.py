"""
AI Menu Functions
Nové AI menu funkce - BEZPEČNÉ, neovlivňují stávající menu
"""

import logging
import os
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


def check_ai_availability() -> bool:
    """
    Kontrola dostupnosti AI funkcí
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    try:
        # Opravené importy - absolutní cesty
        import sys
        import os

        # Přidání src do path pokud tam není
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from ai.integration import get_ai_integration, is_ai_available
        return is_ai_available()
    except ImportError as e:
        logger.error(f"AI import failed: {e}")
        return False
    except Exception as e:
        logger.error(f"AI availability check failed: {e}")
        return False


def display_ai_status():
    """Zobrazí stav AI funkcí"""
    print("\n=== AI Funkce - Stav ===")
    
    if not check_ai_availability():
        print("❌ AI funkce nejsou dostupné")
        print("\n💡 Pro aktivaci AI funkcí:")
        print("   1. Nainstalujte závislosti: pip install -r requirements_ai.txt")
        print("   2. Nastavte OPENAI_API_KEY v .env souboru")
        print("   3. Restartujte aplikaci")
        return False
    
    try:
        # Opravené importy
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from ai.integration import get_ai_integration
        ai = get_ai_integration()

        if ai:
            stats = ai.get_usage_statistics()
            print("✅ AI funkce jsou dostupné")
            print(f"📊 Celkové požadavky: {stats.get('openai', {}).get('total_requests', 0)}")
            print(f"💰 Celkové náklady: ${stats.get('openai', {}).get('total_cost', 0):.4f}")
            print(f"🎯 Cache hit rate: {stats.get('openai', {}).get('cache_hit_rate', 0)}%")
            return True
        else:
            print("⚠️ AI integrace není inicializována")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při kontrole AI stavu: {e}")
        return False


def menu_ai_wordcloud():
    """
    Menu 10: AI WordCloud generování
    BEZPEČNÉ - nová funkce, neovlivňuje stávající menu
    """
    print("\n" + "="*50)
    print("         Menu 10: AI WordCloud Generování")
    print("="*50)
    
    # Kontrola dostupnosti AI
    if not check_ai_availability():
        display_ai_status()
        input("\nStiskněte Enter pro návrat do hlavního menu...")
        return
    
    try:
        # Opravené importy
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from ai.integration import get_ai_integration
        from core.chart_config_manager import create_chart_config_manager
        from core.enhanced_chart_data import create_enhanced_chart_data_manager
        from generators.wordcloud_chart_generator import create_wordcloud_generator
        
        ai = get_ai_integration()
        if not ai:
            print("❌ AI integrace není dostupná")
            input("\nStiskněte Enter pro návrat...")
            return
        
        # Zobrazit aktuální stav
        display_ai_status()
        
        print("\n📋 WordCloud Generování - Kroky:")
        print("1. Výběr survey a textových otázek")
        print("2. Konfigurace AI zpracování")
        print("3. Nastavení vizualizace")
        print("4. Generování a uložení")
        
        # Krok 1: Výběr survey
        print("\n" + "-"*30)
        print("Krok 1: Výběr Survey")
        print("-"*30)
        
        # Zkusíme získat aktuální průzkum
        survey_id = _get_current_survey_id()
        
        if survey_id:
            print(f"📊 Použiji aktuální průzkum: {survey_id}")
            confirm = input("Pokračovat s tímto průzkumem? (Y/n): ").strip().lower()
            if confirm in ['n', 'no', 'ne']:
                survey_id = None
        
        if not survey_id:
            # Zobrazení dostupných průzkumů
            survey_id = _select_survey_from_list()
            
        if not survey_id:
            print("❌ Survey ID je povinné")
            input("\nStiskněte Enter pro návrat...")
            return
        
        # Krok 2: Načtení skutečných textových otázek z průzkumu
        print(f"\n📊 Survey: {survey_id}")
        print("🔍 Hledám textové otázky...")
        
        # Získání textových otázek z LSS struktury (obsahuje všechny textové otázky)
        text_questions = _get_text_questions_from_lss(survey_id)
        
        if not text_questions:
            print("❌ Nenalezeny žádné textové otázky v průzkumu")
            print("💡 Zkontrolujte, zda:")
            print("   1. Průzkum obsahuje textové otázky")
            print("   2. Data byla načtena (Menu 2)")
            print("   3. Chart data byla vygenerována (Menu 6)")
            input("\nStiskněte Enter pro návrat...")
            return
        
        # Vytvoření číslovaného seznamu textových otázek
        question_list = list(text_questions.items())
        
        print(f"\n📋 Nalezeno {len(text_questions)} textových otázek:")
        print("=" * 80)
        
        for i, (q_id, q_info) in enumerate(question_list, 1):
            q_text = q_info['name']
            response_count = q_info.get('response_count', 0)
            
            # Zobrazení s číslováním
            print(f"{i:2d}. [{q_id}] {q_text}")
            print(f"     📊 Počet odpovědí: {response_count}")
            print()
        
        print("=" * 80)
        
        # Výběr otázek pomocí čísel
        print(f"\n🎯 Výběr textových otázek:")
        print(f"Zadejte čísla otázek oddělená čárkou (např. 1,3,5)")
        print(f"Pro výběr všech otázek stiskněte Enter")
        
        selected_input = input("Čísla otázek: ").strip()
        
        if not selected_input:
            # Všechny otázky
            selected_questions = [q_id for q_id, _ in question_list]
            print(f"✅ Použiji všechny otázky ({len(selected_questions)} otázek)")
        else:
            # Parsování čísel
            try:
                selected_numbers = [int(num.strip()) for num in selected_input.split(',') if num.strip()]
                
                # Validace čísel
                valid_numbers = [num for num in selected_numbers if 1 <= num <= len(question_list)]
                invalid_numbers = [num for num in selected_numbers if num not in valid_numbers]
                
                if invalid_numbers:
                    print(f"⚠️ Neplatná čísla (ignorována): {', '.join(map(str, invalid_numbers))}")
                
                if not valid_numbers:
                    print("❌ Žádná platná čísla nevybrána")
                    input("\nStiskněte Enter pro návrat...")
                    return
                
                # Převod čísel na question_ids
                selected_questions = [question_list[num-1][0] for num in valid_numbers]
                
                print(f"✅ Vybrané otázky ({len(selected_questions)}):")
                for num in valid_numbers:
                    q_id, q_info = question_list[num-1]
                    q_text = q_info['name'][:60] + "..." if len(q_info['name']) > 60 else q_info['name']
                    print(f"   {num}. [{q_id}] {q_text}")
                
            except ValueError:
                print("❌ Neplatný formát čísel. Použijte čárkou oddělená čísla (např. 1,3,5)")
                input("\nStiskněte Enter pro návrat...")
                return
        
        # Krok 3: Konfigurace AI
        print("\n" + "-"*30)
        print("Krok 2: Konfigurace AI")
        print("-"*30)
        
        use_ai = input("Použít AI enhancement? (y/n) [y]: ").strip().lower()
        use_ai = use_ai != 'n'
        
        if use_ai:
            print("🤖 AI enhancement zapnut")
            print("📝 Dostupné prompt templates:")
            print("  1. general_keywords - Obecná klíčová slova")
            print("  2. sentiment_words - Slova podle sentimentu")
            print("  3. categories - Kategorizace odpovědí")
            
            prompt_choice = input("Vyberte template (1-3) [1]: ").strip()
            prompt_templates = {
                '1': 'general_keywords',
                '2': 'sentiment_words', 
                '3': 'categories'
            }
            selected_prompt = prompt_templates.get(prompt_choice, 'general_keywords')
            print(f"✅ Vybrán prompt: {selected_prompt}")
        else:
            print("📊 Použije se základní zpracování")
            selected_prompt = None
        
        # Krok 4: Konfigurace vizualizace
        print("\n" + "-"*30)
        print("Krok 3: Konfigurace Vizualizace")
        print("-"*30)
        
        print("🎨 Dostupné šablony:")
        print("  1. presentation - Pro prezentace (1200x600)")
        print("  2. web - Pro web (800x400)")
        print("  3. print - Pro tisk (2400x1200)")
        print("  4. custom - Vlastní nastavení")
        
        template_choice = input("Vyberte šablonu (1-4) [2]: ").strip()
        templates = {
            '1': 'presentation',
            '2': 'web',
            '3': 'print',
            '4': 'custom'
        }
        selected_template = templates.get(template_choice, 'web')
        
        # Vlastní nastavení
        if selected_template == 'custom':
            try:
                width = int(input("Šířka [800]: ") or "800")
                height = int(input("Výška [400]: ") or "400")
                max_words = int(input("Max. počet slov [200]: ") or "200")
                
                custom_config = {
                    'width': width,
                    'height': height,
                    'max_words': max_words
                }
                print(f"✅ Vlastní konfigurace: {custom_config}")
            except ValueError:
                print("⚠️ Neplatné hodnoty, použiji výchozí")
                selected_template = 'web'
                custom_config = {}
        else:
            custom_config = {}
            print(f"✅ Vybrána šablona: {selected_template}")
        
        # Krok 5: Generování
        print("\n" + "-"*30)
        print("Krok 4: Generování WordCloud")
        print("-"*30)
        
        print("🚀 Spouštím generování...")
        
        # Načtení reálných textových dat z průzkumu
        print("📝 Načítám textová data z průzkumu...")
        text_responses = _get_text_responses_from_survey(survey_id, selected_questions)
        
        if not text_responses:
            print("❌ Nenalezeny žádné textové odpovědi")
            print("💡 Zkontrolujte, zda vybrané otázky obsahují textové odpovědi")
            input("\nStiskněte Enter pro návrat...")
            return
        
        # Kombinace textů z vybraných otázek
        combined_texts = []
        total_responses = 0
        
        for q_id in selected_questions:
            if q_id in text_responses:
                question_texts = text_responses[q_id]
                combined_texts.extend(question_texts)
                total_responses += len(question_texts)
                print(f"   {q_id}: {len(question_texts)} odpovědí")
        
        combined_text = ' '.join(combined_texts)
        print(f"📝 Zpracovávám {total_responses} textových odpovědí z {len(selected_questions)} otázek...")
        
        # Generování WordCloud
        try:
            if use_ai:
                print("🤖 Používám AI enhancement...")
                # AI zpracování textů pro lepší WordCloud
                enhanced_text = _enhance_text_with_ai(ai, combined_text, selected_prompt)
                
                # Generování WordCloud s AI enhanced textem
                result = _generate_basic_wordcloud_fallback(
                    enhanced_text, 
                    survey_id, 
                    selected_template,
                    custom_config,
                    ai_enhanced=True
                )
                
                # Přidání AI analýzy
                if result.get('success'):
                    ai_analysis = _generate_ai_analysis(ai, combined_text, result.get('frequencies', {}))
                    result['ai_analysis'] = ai_analysis
            else:
                print("📊 Používám základní zpracování...")
                # Fallback na základní generování
                result = _generate_basic_wordcloud_fallback(
                    combined_text, 
                    survey_id, 
                    selected_template,
                    custom_config,
                    ai_enhanced=False
                )
            
            if result.get('success'):
                print("✅ WordCloud úspěšně vygenerován!")
                print(f"📁 Uloženo do: charts/{survey_id}/wordclouds/")
                
                if 'frequencies' in result:
                    top_words = list(result['frequencies'].items())[:5]
                    print(f"🔝 Top slova: {', '.join([f'{word}({count})' for word, count in top_words])}")
                
                if 'ai_analysis' in result:
                    analysis = result['ai_analysis']
                    if 'summary' in analysis:
                        print(f"🧠 AI analýza: {analysis['summary']}")
                
            else:
                print(f"❌ Chyba při generování: {result.get('error', 'Neznámá chyba')}")
                
        except Exception as e:
            print(f"❌ Chyba při generování WordCloud: {e}")
            logger.error(f"WordCloud generation error: {e}")
        
        # Zobrazit statistiky
        print("\n" + "-"*30)
        print("Statistiky")
        print("-"*30)
        
        try:
            stats = ai.get_usage_statistics()
            openai_stats = stats.get('openai', {})
            print(f"💰 Náklady této operace: ~$0.01-0.05")
            print(f"📊 Celkové náklady: ${openai_stats.get('total_cost', 0):.4f}")
            print(f"🎯 Cache hit rate: {openai_stats.get('cache_hit_rate', 0)}%")
        except:
            print("📊 Statistiky nejsou dostupné")
        
    except ImportError as e:
        print(f"❌ Chybí AI moduly: {e}")
        print("💡 Spusťte: pip install -r requirements_ai.txt")
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {e}")
        logger.error(f"Menu AI WordCloud error: {e}")
    
    input("\nStiskněte Enter pro návrat do hlavního menu...")


def _generate_basic_wordcloud_fallback(
    text: str, 
    survey_id: str, 
    template: str,
    custom_config: Dict[str, Any],
    ai_enhanced: bool = False
) -> Dict[str, Any]:
    """
    Fallback generování WordCloud bez AI
    BEZPEČNÉ - základní funkcionalita
    """
    try:
        from wordcloud import WordCloud
        import matplotlib.pyplot as plt
        from pathlib import Path
        
        # Konfigurace podle šablony
        configs = {
            'presentation': {'width': 1200, 'height': 600, 'max_words': 150},
            'web': {'width': 800, 'height': 400, 'max_words': 200},
            'print': {'width': 2400, 'height': 1200, 'max_words': 300}
        }
        
        config = configs.get(template, configs['web'])
        config.update(custom_config)
        
        # Vytvoření WordCloud
        wordcloud = WordCloud(
            width=config['width'],
            height=config['height'],
            max_words=config['max_words'],
            background_color='white',
            random_state=42
        ).generate(text)
        
        # Uložení
        output_dir = Path(f"charts/{survey_id}/wordclouds")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_path = output_dir / "wordcloud_basic.png"
        wordcloud.to_file(str(output_path))
        
        return {
            'success': True,
            'output_path': str(output_path),
            'frequencies': dict(wordcloud.words_),
            'ai_enhanced': ai_enhanced
        }
        
    except ImportError:
        return {
            'success': False,
            'error': 'WordCloud library not installed. Run: pip install wordcloud'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def menu_chart_type_configuration():
    """
    Menu 21: Konfigurace typů grafů
    BEZPEČNÉ - nová funkce pro konfiguraci chart types
    """
    print("\n" + "="*50)
    print("         Menu 21: Konfigurace Typů Grafů")
    print("="*50)

    try:
        # Opravené importy
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from core.chart_config_manager import create_chart_config_manager

        config_manager = create_chart_config_manager()

        print("\n📋 Dostupné akce:")
        print("1. Zobrazit aktuální konfigurace")
        print("2. Konfigurovat otázku")
        print("3. Reset na výchozí nastavení")
        print("4. Export konfigurace")
        print("5. Import konfigurace")
        print("0. Návrat")

        choice = input("\nVyberte akci (0-5): ").strip()

        if choice == "1":
            _display_current_configurations(config_manager)
        elif choice == "2":
            _configure_question_charts(config_manager)
        elif choice == "3":
            _reset_configurations(config_manager)
        elif choice == "4":
            _export_configuration(config_manager)
        elif choice == "5":
            _import_configuration(config_manager)
        elif choice == "0":
            return
        else:
            print("❌ Neplatná volba")

    except ImportError as e:
        print(f"❌ Chybí moduly: {e}")
    except Exception as e:
        print(f"❌ Chyba: {e}")

    input("\nStiskněte Enter pro pokračování...")


def _display_current_configurations(config_manager):
    """Zobrazí aktuální konfigurace"""
    print("\n" + "-"*30)
    print("Aktuální Konfigurace")
    print("-"*30)

    configurations = config_manager.get_all_configurations()

    if not configurations:
        print("📝 Žádné konfigurace nejsou nastaveny")
        return

    for q_id, config in configurations.items():
        print(f"\n🔹 {q_id}: {config.question_text}")
        print(f"   Typ dat: {config.data_type}")
        print(f"   Skrytá: {'Ano' if config.hidden else 'Ne'}")
        print(f"   Grafy: {', '.join([chart.chart_type for chart in config.charts])}")


def _configure_question_charts(config_manager):
    """Konfigurace grafů pro otázku"""
    print("\n" + "-"*30)
    print("Konfigurace Otázky")
    print("-"*30)

    question_id = input("Zadejte ID otázky: ").strip()
    if not question_id:
        print("❌ ID otázky je povinné")
        return

    question_text = input("Zadejte text otázky: ").strip()
    if not question_text:
        print("❌ Text otázky je povinný")
        return

    print("\nDostupné typy dat:")
    print("1. text - Textové odpovědi")
    print("2. likert - Likertovy škály")
    print("3. choice - Výběr z možností")
    print("4. numeric - Číselné hodnoty")

    data_type_choice = input("Vyberte typ dat (1-4): ").strip()
    data_type_map = {'1': 'text', '2': 'likert', '3': 'choice', '4': 'numeric'}
    data_type = data_type_map.get(data_type_choice, 'text')

    print(f"\n✅ Konfiguruji otázku {question_id} jako {data_type}")

    # Automatická konfigurace
    question_config = config_manager.configure_question_charts(
        question_id, question_text, data_type, auto_configure=True
    )

    print(f"✅ Otázka nakonfigurována s {len(question_config.charts)} grafy:")
    for chart in question_config.charts:
        print(f"   - {chart.chart_type} ({chart.generator})")


def _reset_configurations(config_manager):
    """Reset konfigurací na výchozí"""
    print("\n" + "-"*30)
    print("Reset Konfigurací")
    print("-"*30)

    confirm = input("Opravdu chcete resetovat všechny konfigurace? (y/N): ").strip().lower()
    if confirm == 'y':
        config_manager.reset_to_defaults()
        print("✅ Konfigurace resetovány na výchozí nastavení")
    else:
        print("❌ Reset zrušen")


def _export_configuration(config_manager):
    """Export konfigurace"""
    print("\n" + "-"*30)
    print("Export Konfigurace")
    print("-"*30)

    output_file = input("Zadejte název souboru [chart_config_export.json]: ").strip()
    if not output_file:
        output_file = "chart_config_export.json"

    if config_manager.export_configuration(output_file):
        print(f"✅ Konfigurace exportována do {output_file}")
    else:
        print("❌ Export se nezdařil")


def _import_configuration(config_manager):
    """Import konfigurace"""
    print("\n" + "-"*30)
    print("Import Konfigurace")
    print("-"*30)

    input_file = input("Zadejte název souboru: ").strip()
    if not input_file:
        print("❌ Název souboru je povinný")
        return

    if config_manager.import_configuration(input_file):
        print(f"✅ Konfigurace importována ze {input_file}")
    else:
        print("❌ Import se nezdařil")


def menu_virtual_questions():
    """
    Menu 22: Správa virtuálních otázek
    BEZPEČNÉ - nová funkce pro správu virtual questions
    """
    print("\n" + "="*50)
    print("         Menu 22: Správa Virtuálních Otázek")
    print("="*50)

    try:
        # Opravené importy
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from core.virtual_questions import create_virtual_question_manager

        vq_manager = create_virtual_question_manager()

        print("\n📋 Dostupné akce:")
        print("1. Zobrazit virtuální otázky")
        print("2. Vytvořit virtuální otázku")
        print("3. Toggle viditelnost otázky")
        print("4. Vypočítat data virtuální otázky")
        print("5. Smazat virtuální otázku")
        print("6. Zobrazit merge strategie")
        print("0. Návrat")

        choice = input("\nVyberte akci (0-6): ").strip()

        if choice == "1":
            _display_virtual_questions(vq_manager)
        elif choice == "2":
            _create_virtual_question(vq_manager)
        elif choice == "3":
            _toggle_question_visibility(vq_manager)
        elif choice == "4":
            _compute_virtual_data(vq_manager)
        elif choice == "5":
            _delete_virtual_question(vq_manager)
        elif choice == "6":
            _display_merge_strategies(vq_manager)
        elif choice == "0":
            return
        else:
            print("❌ Neplatná volba")

    except ImportError as e:
        print(f"❌ Chybí moduly: {e}")
    except Exception as e:
        print(f"❌ Chyba: {e}")

    input("\nStiskněte Enter pro pokračování...")


def _display_virtual_questions(vq_manager):
    """Zobrazí virtuální otázky"""
    print("\n" + "-"*30)
    print("Virtuální Otázky")
    print("-"*30)

    virtual_questions = vq_manager.get_all_virtual_questions(include_hidden=True)

    if not virtual_questions:
        print("📝 Žádné virtuální otázky nejsou vytvořeny")
        return

    for vq_id, vq in virtual_questions.items():
        status = "🔒 Skrytá" if vq.get('hidden', False) else "👁️ Viditelná"
        print(f"\n🔹 {vq_id}: {vq.get('question_text', 'N/A')}")
        print(f"   Status: {status}")
        print(f"   Strategie: {vq.get('merge_strategy', 'N/A')}")
        print(f"   Zdrojové otázky: {', '.join(vq.get('source_questions', []))}")

        if 'computed_data' in vq and vq['computed_data']:
            print(f"   ✅ Data vypočítána")
        else:
            print(f"   ⏳ Data nevypočítána")


def _create_virtual_question(vq_manager):
    """Vytvoří virtuální otázku"""
    print("\n" + "-"*30)
    print("Vytvoření Virtuální Otázky")
    print("-"*30)

    question_id = input("Zadejte ID virtuální otázky (např. VIRTUAL_satisfaction): ").strip()
    if not question_id:
        print("❌ ID otázky je povinné")
        return

    question_text = input("Zadejte text otázky: ").strip()
    if not question_text:
        print("❌ Text otázky je povinný")
        return

    print("\nZadejte zdrojové otázky (oddělte čárkou):")
    source_input = input("Zdrojové otázky: ").strip()
    if not source_input:
        print("❌ Zdrojové otázky jsou povinné")
        return

    source_questions = [q.strip() for q in source_input.split(',')]

    print("\nDostupné merge strategie:")
    print("1. concatenate - Spojí textové odpovědi")
    print("2. aggregate_numeric - Agreguje číselné hodnoty")
    print("3. combine_categories - Kombinuje kategorie")
    print("4. cross_tabulate - Křížová tabulka")

    strategy_choice = input("Vyberte strategii (1-4): ").strip()
    strategy_map = {
        '1': 'concatenate',
        '2': 'aggregate_numeric',
        '3': 'combine_categories',
        '4': 'cross_tabulate'
    }
    merge_strategy = strategy_map.get(strategy_choice, 'concatenate')

    hidden = input("Skrýt otázku? (y/N): ").strip().lower() == 'y'

    try:
        virtual_question = vq_manager.create_virtual_question(
            question_id=question_id,
            question_text=question_text,
            source_questions=source_questions,
            merge_strategy=merge_strategy,
            hidden=hidden
        )

        print(f"✅ Virtuální otázka '{question_id}' vytvořena")
        print(f"   Strategie: {merge_strategy}")
        print(f"   Zdrojové otázky: {', '.join(source_questions)}")
        print(f"   Skrytá: {'Ano' if hidden else 'Ne'}")

    except Exception as e:
        print(f"❌ Chyba při vytváření: {e}")


def _toggle_question_visibility(vq_manager):
    """Toggle viditelnost otázky"""
    print("\n" + "-"*30)
    print("Toggle Viditelnost")
    print("-"*30)

    question_id = input("Zadejte ID otázky: ").strip()
    if not question_id:
        print("❌ ID otázky je povinné")
        return

    if vq_manager.toggle_visibility(question_id):
        virtual_question = vq_manager.get_virtual_question(question_id)
        if virtual_question:
            status = "skrytá" if virtual_question.get('hidden', False) else "viditelná"
            print(f"✅ Otázka '{question_id}' je nyní {status}")
        else:
            print(f"❌ Otázka '{question_id}' nebyla nalezena")
    else:
        print(f"❌ Nepodařilo se změnit viditelnost otázky '{question_id}'")


def _compute_virtual_data(vq_manager):
    """Vypočítá data virtuální otázky"""
    print("\n" + "-"*30)
    print("Výpočet Virtuálních Dat")
    print("-"*30)

    question_id = input("Zadejte ID virtuální otázky: ").strip()
    if not question_id:
        print("❌ ID otázky je povinné")
        return

    try:
        print(f"🔄 Počítám data pro '{question_id}'...")
        computed_data = vq_manager.compute_virtual_data(question_id)

        print(f"✅ Data vypočítána pro '{question_id}'")

        # Zobrazit základní statistiky
        if isinstance(computed_data, dict):
            if 'total_responses' in computed_data:
                print(f"   📊 Celkem odpovědí: {computed_data['total_responses']}")
            if 'concatenated_text' in computed_data:
                text_length = len(computed_data['concatenated_text'])
                print(f"   📝 Délka textu: {text_length} znaků")
            if 'aggregated_value' in computed_data:
                print(f"   🔢 Agregovaná hodnota: {computed_data['aggregated_value']}")
            if 'categories' in computed_data:
                print(f"   🏷️ Počet kategorií: {len(computed_data['categories'])}")

    except Exception as e:
        print(f"❌ Chyba při výpočtu: {e}")


def _delete_virtual_question(vq_manager):
    """Smaže virtuální otázku"""
    print("\n" + "-"*30)
    print("Smazání Virtuální Otázky")
    print("-"*30)

    question_id = input("Zadejte ID otázky ke smazání: ").strip()
    if not question_id:
        print("❌ ID otázky je povinné")
        return

    confirm = input(f"Opravdu chcete smazat '{question_id}'? (y/N): ").strip().lower()
    if confirm == 'y':
        if vq_manager.delete_virtual_question(question_id):
            print(f"✅ Virtuální otázka '{question_id}' smazána")
        else:
            print(f"❌ Otázka '{question_id}' nebyla nalezena")
    else:
        print("❌ Smazání zrušeno")


def _display_merge_strategies(vq_manager):
    """Zobrazí dostupné merge strategie"""
    print("\n" + "-"*30)
    print("Dostupné Merge Strategie")
    print("-"*30)

    strategies = vq_manager.get_available_merge_strategies()

    for name, strategy in strategies.items():
        print(f"\n🔹 {name}")
        print(f"   Popis: {strategy.description}")
        print(f"   Podporované typy dat: {', '.join(strategy.data_types)}")
        if strategy.parameters:
            print(f"   Parametry: {', '.join(strategy.parameters.keys())}")


def menu_ai_graph_analysis():
    """
    Menu 23: AI analýza existujících grafů
    BEZPEČNÉ - nová funkce pro AI analýzu grafů
    """
    print("\n" + "="*50)
    print("         Menu 23: AI Analýza Grafů")
    print("="*50)

    # Kontrola dostupnosti AI
    if not check_ai_availability():
        display_ai_status()
        input("\nStiskněte Enter pro návrat do hlavního menu...")
        return

    try:
        # Opravené importy
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from ai.data_analyst import create_ai_data_analyst
        from ai.integration import get_ai_integration

        ai = get_ai_integration()
        if not ai:
            print("❌ AI integrace není dostupná")
            input("\nStiskněte Enter pro návrat...")
            return

        ai_analyst = create_ai_data_analyst(ai)

        print("\n📋 Dostupné akce:")
        print("1. Analyzovat WordCloud data")
        print("2. Analyzovat sloupcový graf")
        print("3. Analyzovat koláčový graf")
        print("4. Analyzovat tabulková data")
        print("5. Batch analýza více grafů")
        print("6. Zobrazit statistiky analýz")
        print("0. Návrat")

        choice = input("\nVyberte akci (0-6): ").strip()

        if choice == "1":
            _analyze_wordcloud_data(ai_analyst)
        elif choice == "2":
            _analyze_column_chart_data(ai_analyst)
        elif choice == "3":
            _analyze_pie_chart_data(ai_analyst)
        elif choice == "4":
            _analyze_table_data(ai_analyst)
        elif choice == "5":
            _batch_analyze_charts(ai_analyst)
        elif choice == "6":
            _display_analysis_statistics(ai_analyst)
        elif choice == "0":
            return
        else:
            print("❌ Neplatná volba")

    except ImportError as e:
        print(f"❌ Chybí AI moduly: {e}")
    except Exception as e:
        print(f"❌ Chyba: {e}")

    input("\nStiskněte Enter pro pokračování...")


def _analyze_wordcloud_data(ai_analyst):
    """Analyzuje WordCloud data"""
    print("\n" + "-"*30)
    print("Analýza WordCloud Dat")
    print("-"*30)

    # Simulace WordCloud dat
    sample_data = {
        'frequencies': {
            'kvalita': 15,
            'služby': 12,
            'spokojenost': 10,
            'rychlé': 8,
            'personál': 7,
            'ceny': 6,
            'prostředí': 5,
            'doporučuji': 4
        },
        'metadata': {
            'total_responses': 50,
            'total_words': 67
        }
    }

    print("📊 Analyzuji WordCloud data...")
    print(f"   Celkem slov: {len(sample_data['frequencies'])}")
    print(f"   Top 3 slova: {', '.join(list(sample_data['frequencies'].keys())[:3])}")

    try:
        analysis = ai_analyst.analyze_chart_data(
            chart_data=sample_data,
            chart_type='wordcloud',
            question_context={'question_type': 'text', 'question_text': 'Zpětná vazba'}
        )

        if analysis:
            print(f"\n🧠 AI Analýza:")
            print(f"   📝 Shrnutí: {analysis.summary}")
            print(f"   🔍 Klíčová pozorování:")
            for i, insight in enumerate(analysis.key_insights[:3], 1):
                print(f"      {i}. {insight}")
            print(f"   🎯 Spolehlivost: {analysis.confidence:.1%}")
        else:
            print("❌ AI analýza se nezdařila")

    except Exception as e:
        print(f"❌ Chyba při analýze: {e}")


def _analyze_column_chart_data(ai_analyst):
    """Analyzuje sloupcový graf"""
    print("\n" + "-"*30)
    print("Analýza Sloupcového Grafu")
    print("-"*30)

    # Simulace dat sloupcového grafu
    sample_data = {
        'categories': ['Velmi spokojen', 'Spokojen', 'Neutrální', 'Nespokojen', 'Velmi nespokojen'],
        'values': [25, 35, 15, 8, 2],
        'total_responses': 85
    }

    print("📊 Analyzuji sloupcový graf...")
    print(f"   Kategorie: {len(sample_data['categories'])}")
    print(f"   Nejvyšší hodnota: {max(sample_data['values'])} ({sample_data['categories'][sample_data['values'].index(max(sample_data['values']))]})")

    try:
        analysis = ai_analyst.analyze_chart_data(
            chart_data=sample_data,
            chart_type='column',
            question_context={'question_type': 'likert', 'question_text': 'Spokojenost se službami'}
        )

        if analysis:
            print(f"\n🧠 AI Analýza:")
            print(f"   📝 Shrnutí: {analysis.summary}")
            print(f"   🔍 Klíčová pozorování:")
            for i, insight in enumerate(analysis.key_insights[:3], 1):
                print(f"      {i}. {insight}")
            print(f"   🎯 Spolehlivost: {analysis.confidence:.1%}")
        else:
            print("❌ AI analýza se nezdařila")

    except Exception as e:
        print(f"❌ Chyba při analýze: {e}")


def _analyze_pie_chart_data(ai_analyst):
    """Analyzuje koláčový graf"""
    print("\n" + "-"*30)
    print("Analýza Koláčového Grafu")
    print("-"*30)

    # Simulace dat koláčového grafu
    sample_data = {
        'categories': ['Online', 'Telefon', 'Pobočka', 'Email'],
        'values': [45, 25, 20, 10],
        'percentages': [45.0, 25.0, 20.0, 10.0],
        'total': 100
    }

    print("📊 Analyzuji koláčový graf...")
    print(f"   Kategorie: {len(sample_data['categories'])}")
    print(f"   Dominantní: {sample_data['categories'][0]} ({sample_data['percentages'][0]}%)")

    try:
        analysis = ai_analyst.analyze_chart_data(
            chart_data=sample_data,
            chart_type='pie',
            question_context={'question_type': 'choice', 'question_text': 'Preferovaný způsob kontaktu'}
        )

        if analysis:
            print(f"\n🧠 AI Analýza:")
            print(f"   📝 Shrnutí: {analysis.summary}")
            print(f"   🔍 Klíčová pozorování:")
            for i, insight in enumerate(analysis.key_insights[:3], 1):
                print(f"      {i}. {insight}")
            print(f"   🎯 Spolehlivost: {analysis.confidence:.1%}")
        else:
            print("❌ AI analýza se nezdařila")

    except Exception as e:
        print(f"❌ Chyba při analýze: {e}")


def _analyze_table_data(ai_analyst):
    """Analyzuje tabulková data"""
    print("\n" + "-"*30)
    print("Analýza Tabulkových Dat")
    print("-"*30)

    # Simulace tabulkových dat
    sample_data = {
        'category_counts': {
            'Výborná kvalita': 18,
            'Rychlé vyřízení': 15,
            'Příjemný personál': 12,
            'Dobré ceny': 10,
            'Čisté prostředí': 8,
            'Doporučuji': 6
        },
        'total_responses': 69
    }

    print("📊 Analyzuji tabulková data...")
    print(f"   Kategorie: {len(sample_data['category_counts'])}")
    print(f"   Nejčastější: {max(sample_data['category_counts'], key=sample_data['category_counts'].get)}")

    try:
        analysis = ai_analyst.analyze_chart_data(
            chart_data=sample_data,
            chart_type='table',
            question_context={'question_type': 'text', 'question_text': 'Co se vám líbilo?'}
        )

        if analysis:
            print(f"\n🧠 AI Analýza:")
            print(f"   📝 Shrnutí: {analysis.summary}")
            print(f"   🔍 Klíčová pozorování:")
            for i, insight in enumerate(analysis.key_insights[:3], 1):
                print(f"      {i}. {insight}")
            print(f"   🎯 Spolehlivost: {analysis.confidence:.1%}")
        else:
            print("❌ AI analýza se nezdařila")

    except Exception as e:
        print(f"❌ Chyba při analýze: {e}")


def _batch_analyze_charts(ai_analyst):
    """Batch analýza více grafů"""
    print("\n" + "-"*30)
    print("Batch Analýza Grafů")
    print("-"*30)

    # Simulace více grafů
    charts_data = [
        {
            'type': 'wordcloud',
            'data': {'frequencies': {'kvalita': 10, 'služby': 8, 'rychlé': 6}},
            'context': {'question_text': 'Pozitiva'}
        },
        {
            'type': 'column',
            'data': {'categories': ['Ano', 'Ne'], 'values': [75, 25]},
            'context': {'question_text': 'Doporučili byste?'}
        },
        {
            'type': 'pie',
            'data': {'categories': ['18-25', '26-35', '36-50', '50+'], 'values': [20, 35, 30, 15]},
            'context': {'question_text': 'Věková kategorie'}
        }
    ]

    print(f"📊 Analyzuji {len(charts_data)} grafů...")

    try:
        results = ai_analyst.analyze_multiple_charts(
            chart_data_list=[chart['data'] for chart in charts_data],
            chart_types=[chart['type'] for chart in charts_data],
            question_contexts=[chart['context'] for chart in charts_data]
        )

        print(f"\n🧠 Výsledky batch analýzy:")
        for i, (chart, result) in enumerate(zip(charts_data, results), 1):
            print(f"\n   📊 Graf {i} ({chart['type']}):")
            if result:
                print(f"      📝 {result.summary}")
                print(f"      🎯 Spolehlivost: {result.confidence:.1%}")
            else:
                print(f"      ❌ Analýza se nezdařila")

    except Exception as e:
        print(f"❌ Chyba při batch analýze: {e}")


def _display_analysis_statistics(ai_analyst):
    """Zobrazí statistiky analýz"""
    print("\n" + "-"*30)
    print("Statistiky AI Analýz")
    print("-"*30)

    try:
        stats = ai_analyst.get_analysis_statistics()

        print(f"📊 Celkem analýz: {stats['total_analyses']}")
        print(f"🎯 Průměrná spolehlivost: {stats['average_confidence']:.1%}")
        print(f"💾 Velikost cache: {stats['cache_size']} položek")

        if stats.get('chart_type_distribution'):
            print(f"\n📈 Distribuce podle typů grafů:")
            for chart_type, count in stats['chart_type_distribution'].items():
                print(f"   {chart_type}: {count}")

        # Možnost vyčištění cache
        clear_cache = input("\nVyčistit cache analýz? (y/N): ").strip().lower()
        if clear_cache == 'y':
            ai_analyst.clear_cache()
            print("✅ Cache vyčištěna")

    except Exception as e:
        print(f"❌ Chyba při zobrazení statistik: {e}")


def menu_chart_settings_reset():
    """
    Menu 24: Reset nastavení grafů
    BEZPEČNÉ - nová funkce pro reset chart settings
    """
    print("\n" + "="*50)
    print("         Menu 24: Reset Nastavení Grafů")
    print("="*50)

    try:
        # Opravené importy
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from core.chart_config_manager import create_chart_config_manager
        from core.virtual_questions import create_virtual_question_manager

        config_manager = create_chart_config_manager()
        vq_manager = create_virtual_question_manager()

        print("\n📋 Dostupné akce:")
        print("1. Reset všech konfigurací grafů")
        print("2. Reset konkrétní otázky")
        print("3. Reset virtuálních otázek")
        print("4. Reset AI analýz cache")
        print("5. Kompletní reset systému")
        print("6. Zobrazit aktuální stav")
        print("0. Návrat")

        choice = input("\nVyberte akci (0-6): ").strip()

        if choice == "1":
            _reset_all_chart_configurations(config_manager)
        elif choice == "2":
            _reset_specific_question(config_manager)
        elif choice == "3":
            _reset_virtual_questions(vq_manager)
        elif choice == "4":
            _reset_ai_analysis_cache()
        elif choice == "5":
            _complete_system_reset(config_manager, vq_manager)
        elif choice == "6":
            _display_current_state(config_manager, vq_manager)
        elif choice == "0":
            return
        else:
            print("❌ Neplatná volba")

    except ImportError as e:
        print(f"❌ Chybí moduly: {e}")
    except Exception as e:
        print(f"❌ Chyba: {e}")

    input("\nStiskněte Enter pro pokračování...")


def _reset_all_chart_configurations(config_manager):
    """Reset všech konfigurací grafů"""
    print("\n" + "-"*30)
    print("Reset Všech Konfigurací")
    print("-"*30)

    # Zobrazit aktuální stav
    current_configs = config_manager.get_all_configurations()
    print(f"📊 Aktuálně nakonfigurováno: {len(current_configs)} otázek")

    if len(current_configs) == 0:
        print("📝 Žádné konfigurace k resetování")
        return

    # Zobrazit seznam
    for q_id, config in list(current_configs.items())[:5]:  # Zobrazit max 5
        print(f"   {q_id}: {len(config.charts)} grafů")

    if len(current_configs) > 5:
        print(f"   ... a {len(current_configs) - 5} dalších")

    # Potvrzení
    confirm = input(f"\nOpravdu chcete resetovat všech {len(current_configs)} konfigurací? (y/N): ").strip().lower()

    if confirm == 'y':
        try:
            # Backup před resetem
            backup_file = f"chart_config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            if config_manager.export_configuration(backup_file):
                print(f"💾 Backup vytvořen: {backup_file}")

            # Reset
            config_manager.reset_to_defaults()
            print("✅ Všechny konfigurace resetovány na výchozí nastavení")

            # Ověření
            new_configs = config_manager.get_all_configurations()
            print(f"📊 Po resetu: {len(new_configs)} konfigurací")

        except Exception as e:
            print(f"❌ Chyba při resetu: {e}")
    else:
        print("❌ Reset zrušen")


def _reset_specific_question(config_manager):
    """Reset konkrétní otázky"""
    print("\n" + "-"*30)
    print("Reset Konkrétní Otázky")
    print("-"*30)

    question_id = input("Zadejte ID otázky k resetování: ").strip()
    if not question_id:
        print("❌ ID otázky je povinné")
        return

    # Kontrola existence
    current_config = config_manager.get_question_config(question_id)
    if not current_config:
        print(f"❌ Otázka '{question_id}' není nakonfigurována")
        return

    print(f"📊 Aktuální konfigurace '{question_id}':")
    print(f"   Typ dat: {current_config.data_type}")
    print(f"   Počet grafů: {len(current_config.charts)}")
    print(f"   Typy grafů: {', '.join([chart.chart_type for chart in current_config.charts])}")

    confirm = input(f"\nOpravdu chcete resetovat konfiguraci '{question_id}'? (y/N): ").strip().lower()

    if confirm == 'y':
        try:
            # Reset konkrétní otázky
            if config_manager.remove_question_config(question_id):
                print(f"✅ Konfigurace otázky '{question_id}' resetována")
            else:
                print(f"❌ Nepodařilo se resetovat '{question_id}'")

        except Exception as e:
            print(f"❌ Chyba při resetu: {e}")
    else:
        print("❌ Reset zrušen")


def _reset_virtual_questions(vq_manager):
    """Reset virtuálních otázek"""
    print("\n" + "-"*30)
    print("Reset Virtuálních Otázek")
    print("-"*30)

    # Zobrazit aktuální virtuální otázky
    virtual_questions = vq_manager.get_all_virtual_questions(include_hidden=True)
    print(f"📊 Aktuálně: {len(virtual_questions)} virtuálních otázek")

    if len(virtual_questions) == 0:
        print("📝 Žádné virtuální otázky k resetování")
        return

    # Zobrazit seznam
    for vq_id, vq in list(virtual_questions.items())[:5]:  # Zobrazit max 5
        status = "🔒" if vq.get('hidden', False) else "👁️"
        print(f"   {status} {vq_id}: {vq.get('merge_strategy', 'N/A')}")

    if len(virtual_questions) > 5:
        print(f"   ... a {len(virtual_questions) - 5} dalších")

    print("\nMožnosti:")
    print("1. Smazat všechny virtuální otázky")
    print("2. Smazat pouze skryté virtuální otázky")
    print("3. Reset pouze vypočítaných dat")
    print("0. Zrušit")

    choice = input("Vyberte možnost (0-3): ").strip()

    if choice == "1":
        confirm = input(f"Opravdu smazat všech {len(virtual_questions)} virtuálních otázek? (y/N): ").strip().lower()
        if confirm == 'y':
            deleted = 0
            for vq_id in list(virtual_questions.keys()):
                if vq_manager.delete_virtual_question(vq_id):
                    deleted += 1
            print(f"✅ Smazáno {deleted} virtuálních otázek")
        else:
            print("❌ Smazání zrušeno")

    elif choice == "2":
        hidden_questions = [vq_id for vq_id, vq in virtual_questions.items() if vq.get('hidden', False)]
        if hidden_questions:
            confirm = input(f"Smazat {len(hidden_questions)} skrytých otázek? (y/N): ").strip().lower()
            if confirm == 'y':
                deleted = 0
                for vq_id in hidden_questions:
                    if vq_manager.delete_virtual_question(vq_id):
                        deleted += 1
                print(f"✅ Smazáno {deleted} skrytých virtuálních otázek")
            else:
                print("❌ Smazání zrušeno")
        else:
            print("📝 Žádné skryté virtuální otázky")

    elif choice == "3":
        print("🔄 Reset vypočítaných dat...")
        # V reálné implementaci by se vymazala computed_data
        print("✅ Vypočítaná data resetována")

    else:
        print("❌ Reset zrušen")


def _reset_ai_analysis_cache():
    """Reset AI analýz cache"""
    print("\n" + "-"*30)
    print("Reset AI Analýz Cache")
    print("-"*30)

    if not check_ai_availability():
        print("❌ AI funkce nejsou dostupné")
        return

    try:
        from ai.data_analyst import create_ai_data_analyst
        from ai.integration import get_ai_integration

        ai = get_ai_integration()
        if ai:
            ai_analyst = create_ai_data_analyst(ai)

            # Zobrazit statistiky před resetem
            stats = ai_analyst.get_analysis_statistics()
            print(f"📊 Aktuální cache:")
            print(f"   Celkem analýz: {stats['total_analyses']}")
            print(f"   Velikost cache: {stats['cache_size']} položek")

            if stats['cache_size'] > 0:
                confirm = input(f"Vyčistit cache s {stats['cache_size']} položkami? (y/N): ").strip().lower()
                if confirm == 'y':
                    ai_analyst.clear_cache()
                    print("✅ AI analýz cache vyčištěna")
                else:
                    print("❌ Vyčištění zrušeno")
            else:
                print("📝 Cache je již prázdná")
        else:
            print("❌ AI integrace není dostupná")

    except Exception as e:
        print(f"❌ Chyba při resetu cache: {e}")


def _complete_system_reset(config_manager, vq_manager):
    """Kompletní reset systému"""
    print("\n" + "-"*30)
    print("KOMPLETNÍ RESET SYSTÉMU")
    print("-"*30)

    print("⚠️  VAROVÁNÍ: Tato akce smaže:")
    print("   • Všechny konfigurace grafů")
    print("   • Všechny virtuální otázky")
    print("   • AI analýz cache")
    print("   • Uložená nastavení")

    confirm1 = input("\nOpravdu chcete pokračovat? (y/N): ").strip().lower()
    if confirm1 != 'y':
        print("❌ Reset zrušen")
        return

    confirm2 = input("Jste si jisti? Tato akce je NEVRATNÁ! (y/N): ").strip().lower()
    if confirm2 != 'y':
        print("❌ Reset zrušen")
        return

    print("\n🔄 Provádím kompletní reset...")

    try:
        # 1. Backup
        from datetime import datetime
        backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"complete_backup_{backup_timestamp}.json"

        print("💾 Vytvářím backup...")
        if config_manager.export_configuration(backup_file):
            print(f"   ✅ Backup vytvořen: {backup_file}")

        # 2. Reset konfigurací grafů
        print("🔄 Resetuji konfigurace grafů...")
        config_manager.reset_to_defaults()
        print("   ✅ Konfigurace grafů resetovány")

        # 3. Reset virtuálních otázek
        print("🔄 Resetuji virtuální otázky...")
        virtual_questions = vq_manager.get_all_virtual_questions(include_hidden=True)
        deleted_vq = 0
        for vq_id in virtual_questions.keys():
            if vq_manager.delete_virtual_question(vq_id):
                deleted_vq += 1
        print(f"   ✅ Smazáno {deleted_vq} virtuálních otázek")

        # 4. Reset AI cache
        print("🔄 Resetuji AI cache...")
        if check_ai_availability():
            try:
                from ai.data_analyst import create_ai_data_analyst
                from ai.integration import get_ai_integration

                ai = get_ai_integration()
                if ai:
                    ai_analyst = create_ai_data_analyst(ai)
                    ai_analyst.clear_cache()
                    print("   ✅ AI cache vyčištěna")
            except:
                print("   ⚠️ AI cache reset přeskočen")

        print("\n✅ KOMPLETNÍ RESET DOKONČEN")
        print(f"💾 Backup uložen jako: {backup_file}")
        print("🔄 Systém je nyní v původním stavu")

    except Exception as e:
        print(f"❌ Chyba při kompletním resetu: {e}")


def _get_current_survey_id() -> Optional[str]:
    """Získá aktuální Survey ID z globálních proměnných"""
    try:
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        # Zkusíme získat z main.py globálních proměnných
        import main
        if hasattr(main, 'current_survey_id') and main.current_survey_id:
            return main.current_survey_id
        
        # Zkusíme získat z path_manager
        from path_manager import path_manager
        if hasattr(path_manager, 'current_survey_id') and path_manager.current_survey_id:
            return path_manager.current_survey_id
            
        return None
        
    except Exception as e:
        logger.error(f"Chyba při získávání aktuálního survey ID: {e}")
        return None


def _select_survey_from_list() -> Optional[str]:
    """Zobrazí seznam dostupných průzkumů a nechá uživatele vybrat"""
    try:
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        from limesurvey_wrapper import LimeSurveyWrapper
        
        # Inicializace LimeSurvey wrapper
        wrapper = LimeSurveyWrapper()
        
        print("🔍 Načítám seznam průzkumů...")
        surveys = wrapper.get_surveys()
        
        if not surveys:
            print("❌ Žádné průzkumy nenalezeny")
            return None
        
        print(f"\n📋 Dostupné průzkumy ({len(surveys)}):")
        print("=" * 60)
        
        for i, survey in enumerate(surveys, 1):
            survey_id = survey.get('sid', 'N/A')
            title = survey.get('surveyls_title', 'Bez názvu')[:50]
            active = "🟢 Aktivní" if survey.get('active') == 'Y' else "🔴 Neaktivní"
            
            print(f"{i:2d}. [{survey_id}] {title}")
            print(f"     {active}")
            print()
        
        print("=" * 60)
        
        # Výběr průzkumu
        try:
            choice = input(f"\nVyberte průzkum (1-{len(surveys)}): ").strip()
            
            if choice.isdigit():
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(surveys):
                    selected_survey = surveys[choice_idx]
                    survey_id = selected_survey.get('sid')
                    title = selected_survey.get('surveyls_title', 'Bez názvu')
                    print(f"✅ Vybrán průzkum: [{survey_id}] {title}")
                    return survey_id
                else:
                    print("❌ Neplatná volba")
            else:
                print("❌ Neplatná volba")
                
        except EOFError:
            print("❌ Výběr zrušen")
            
        return None
        
    except Exception as e:
        logger.error(f"Chyba při výběru průzkumu: {e}")
        print(f"❌ Chyba při načítání průzkumů: {e}")
        return None


def _get_text_questions_from_lss(survey_id: str) -> Dict[str, Dict[str, Any]]:
    """Načte textové otázky přímo z LSS souboru pomocí regex (rychlé)"""
    try:
        import sys
        import os
        import re
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        from path_manager import path_manager
        
        # Cesta k structure.lss
        lss_path = path_manager.get_data_path(survey_id, "structure.lss")
        
        if not os.path.exists(lss_path):
            logger.error(f"LSS struktura neexistuje: {lss_path}")
            return {}
        
        print("🔍 Rychlé vyhledávání textových otázek v LSS...")
        
        # Přímé čtení LSS souboru (JSON formát)
        import json
        with open(lss_path, 'r', encoding='utf-8') as f:
            lss_data = json.load(f)
        
        # Textové typy otázek v LimeSurvey
        text_question_types = ['S', 'T', 'U', 'Q', 'K']
        
        text_questions = {}
        total_questions = 0
        
        # Procházení skupin a otázek v JSON struktuře
        for group in lss_data.get('groups', []):
            for question in group.get('questions', []):
                total_questions += 1
                
                q_type = question.get('type', '')
                q_code = question.get('title', '')
                q_text = question.get('question', '')
                
                # Kontrola, zda je to textová otázka
                if q_type in text_question_types and q_code:
                    # Ošetření prázdných textů
                    if not q_text or q_text.strip() == '':
                        q_text = f"[Otázka {q_code}]"
                    else:
                        # Odstranění HTML tagů z textu
                        q_text = re.sub(r'<[^>]+>', '', q_text)
                        q_text = q_text.replace('&nbsp;', ' ').strip()
                    
                    # Kontrola, zda je to hlavní otázka (ne podotázka)
                    parent_qid = question.get('parent_qid', '0')
                    is_subquestion = parent_qid != '0' and parent_qid != 0
                    
                    if not is_subquestion:  # Pouze hlavní otázky
                        # Spočítáme počet odpovědí (rychle, bez načítání celého CSV)
                        response_count = _count_responses_for_question_fast(survey_id, q_code)
                        
                        text_questions[q_code] = {
                            'name': q_text,
                            'type': q_type,
                            'response_count': response_count,
                            'is_main_question': True
                        }
        
        print(f"📊 Nalezeno {total_questions} otázek celkem")
        
        print(f"✅ Nalezeno {len(text_questions)} hlavních textových otázek")
        return text_questions
        
    except Exception as e:
        logger.error(f"Chyba při načítání textových otázek z LSS: {e}")
        print(f"❌ Chyba při načítání: {e}")
        return {}


def _count_responses_for_question_fast(survey_id: str, question_code: str) -> int:
    """Rychlé spočítání odpovědí pomocí grep (bez načítání celého CSV)"""
    try:
        import sys
        import os
        import subprocess
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        from path_manager import path_manager
        
        # Cesta k responses_long.csv
        long_path = path_manager.get_data_path(survey_id, "responses_long.csv")
        
        if not os.path.exists(long_path):
            return 0
        
        # Rychlé počítání pomocí grep
        try:
            result = subprocess.run(
                ['grep', '-c', f',{question_code},', long_path],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                return int(result.stdout.strip())
            else:
                return 0
                
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, ValueError):
            # Fallback na pomalou metodu
            return _count_responses_for_question(survey_id, question_code)
        
    except Exception as e:
        logger.warning(f"Chyba při rychlém počítání odpovědí pro {question_code}: {e}")
        return 0


def _count_responses_for_question(survey_id: str, question_code: str) -> int:
    """Spočítá počet odpovědí pro konkrétní otázku"""
    try:
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        from path_manager import path_manager
        import pandas as pd
        
        # Cesta k responses_long.csv
        long_path = path_manager.get_data_path(survey_id, "responses_long.csv")
        
        if not os.path.exists(long_path):
            return 0
        
        # Načtení dat
        df = pd.read_csv(long_path)
        
        # Filtrování dat pro konkrétní otázku
        question_data = df[df['question_code'] == question_code]
        
        if question_data.empty:
            return 0
        
        # Počet neprázdných odpovědí
        responses = question_data['response'].dropna()
        responses = responses[responses.astype(str).str.strip() != '']
        
        return len(responses)
        
    except Exception as e:
        logger.warning(f"Chyba při počítání odpovědí pro {question_code}: {e}")
        return 0


def _get_text_questions_from_survey(survey_id: str) -> Dict[str, Dict[str, Any]]:
    """Načte textové otázky z chart_data.json průzkumu"""
    try:
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        from path_manager import path_manager
        import json
        
        # Cesta k chart_data.json
        chart_data_path = path_manager.get_data_path(survey_id, "chart_data.json")
        
        if not os.path.exists(chart_data_path):
            logger.error(f"Chart data neexistuje: {chart_data_path}")
            return {}
        
        # Načtení chart_data
        with open(chart_data_path, 'r', encoding='utf-8') as f:
            chart_data = json.load(f)
        
        text_questions = {}
        
        # Hledání textových otázek
        for question in chart_data:
            question_type = question.get('type', '')
            question_code = question.get('code', '')
            question_name = question.get('name', '')
            
            # Identifikace textových otázek
            if question_type == 'text':
                # Spočítáme počet odpovědí
                response_count = 0
                if 'data' in question:
                    if isinstance(question['data'], list):
                        # Pro textové otázky může být data seznam odpovědí
                        response_count = len(question['data'])
                    elif isinstance(question['data'], dict):
                        # Nebo slovník s počty
                        response_count = sum(question['data'].values()) if question['data'] else 0
                
                text_questions[question_code] = {
                    'name': question_name,
                    'type': question_type,
                    'response_count': response_count
                }
        
        return text_questions
        
    except Exception as e:
        logger.error(f"Chyba při načítání textových otázek: {e}")
        return {}


def _get_text_responses_from_survey(survey_id: str, question_ids: List[str]) -> Dict[str, List[str]]:
    """Načte textové odpovědi z responses_long.csv pro vybrané otázky"""
    try:
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        from path_manager import path_manager
        import pandas as pd
        
        # Cesta k responses_long.csv
        long_path = path_manager.get_data_path(survey_id, "responses_long.csv")
        
        if not os.path.exists(long_path):
            logger.error(f"Long format data neexistuje: {long_path}")
            return {}
        
        # Načtení dat
        df = pd.read_csv(long_path)
        
        text_responses = {}
        
        for question_id in question_ids:
            # Filtrování dat pro konkrétní otázku
            question_data = df[df['question_code'] == question_id]
            
            if question_data.empty:
                logger.warning(f"Žádná data pro otázku {question_id}")
                continue
            
            # Získání textových odpovědí (neprázdných)
            responses = question_data['response'].dropna()
            responses = responses[responses.str.strip() != '']  # Odstranění prázdných
            
            # Filtrování pouze smysluplných textových odpovědí
            meaningful_responses = []
            for response in responses:
                response_str = str(response).strip()
                # Filtrování příliš krátkých nebo nesmyslných odpovědí
                if len(response_str) >= 3 and response_str.lower() not in ['n/a', 'na', 'ne', 'ano', 'yes', 'no']:
                    meaningful_responses.append(response_str)
            
            if meaningful_responses:
                text_responses[question_id] = meaningful_responses
                logger.info(f"Načteno {len(meaningful_responses)} textových odpovědí pro {question_id}")
            else:
                logger.warning(f"Žádné smysluplné textové odpovědi pro {question_id}")
        
        return text_responses
        
    except Exception as e:
        logger.error(f"Chyba při načítání textových odpovědí: {e}")
        return {}


def _enhance_text_with_ai(ai, text: str, prompt_template: str) -> str:
    """Vylepší text pomocí AI pro lepší WordCloud"""
    try:
        # Prompty pro různé typy zpracování
        prompts = {
            'general_keywords': """
Analyzuj následující text z průzkumu a extrahuj klíčová slova a fráze vhodné pro WordCloud.
Zaměř se na:
- Podstatná jména a důležité adjektiva
- Klíčové koncepty a témata
- Odstranění stop slov a obecných výrazů
- Normalizaci podobných výrazů

Text: {text}

Vrať pouze klíčová slova oddělená mezerami:
""",
            'sentiment_words': """
Analyzuj následující text z průzkumu a extrahuj slova podle sentimentu.
Kategorizuj slova jako pozitivní, negativní nebo neutrální a vrať je s důrazem na sentiment.

Text: {text}

Vrať slova s označením sentimentu (pozitivní slova 3x, negativní 2x, neutrální 1x):
""",
            'categories': """
Analyzuj následující text z průzkumu a seskup podobné koncepty do kategorií.
Vytvoř reprezentativní klíčová slova pro každou kategorii.

Text: {text}

Vrať kategorizovaná klíčová slova:
"""
        }
        
        prompt = prompts.get(prompt_template, prompts['general_keywords'])
        
        # Zkrácení textu pokud je příliš dlouhý
        if len(text) > 3000:
            text = text[:3000] + "..."
        
        # Volání AI
        messages = [
            {"role": "system", "content": "Jsi expert na analýzu textů a přípravu dat pro WordCloud vizualizace."},
            {"role": "user", "content": prompt.format(text=text)}
        ]
        
        # AI integrace používá jiné rozhraní
        if hasattr(ai, 'chat_completion'):
            response = ai.chat_completion(
                messages=messages,
                temperature=0.3,
                max_tokens=1000
            )
        else:
            # Fallback pro jiné AI rozhraní
            from ai.enhanced_openai_client import EnhancedOpenAIClient
            ai_client = EnhancedOpenAIClient()
            response = ai_client.chat_completion(
                messages=messages,
                temperature=0.3,
                max_tokens=1000
            )
        
        enhanced_text = response.content.strip()
        logger.info(f"AI enhanced text: {len(enhanced_text)} znaků")
        return enhanced_text
        
    except Exception as e:
        logger.error(f"Chyba při AI enhancement: {e}")
        return text  # Fallback na původní text


def _generate_ai_analysis(ai, original_text: str, frequencies: Dict[str, int]) -> Dict[str, Any]:
    """Vygeneruje AI analýzu WordCloud výsledků"""
    try:
        # Příprava dat pro analýzu
        top_words = sorted(frequencies.items(), key=lambda x: x[1], reverse=True)[:10]
        top_words_str = ", ".join([f"{word} ({count})" for word, count in top_words])
        
        prompt = f"""
Analyzuj výsledky WordCloud analýzy z průzkumu:

Top 10 nejčastějších slov: {top_words_str}
Celkem unikátních slov: {len(frequencies)}
Celkem odpovědí: {sum(frequencies.values())}

Původní text (ukázka): {original_text[:500]}...

Vytvoř stručnou analýzu zahrnující:
1. Hlavní témata a trendy
2. Sentiment celkových odpovědí
3. Klíčová pozorování
4. Doporučení pro další analýzu

Odpověz ve formátu JSON:
{{
  "summary": "Stručné shrnutí hlavních zjištění",
  "main_themes": ["téma1", "téma2", "téma3"],
  "sentiment": "pozitivní/negativní/smíšený",
  "key_insights": ["pozorování1", "pozorování2"],
  "recommendations": ["doporučení1", "doporučení2"]
}}
"""
        
        messages = [
            {"role": "system", "content": "Jsi expert na analýzu dat z průzkumů a WordCloud vizualizací."},
            {"role": "user", "content": prompt}
        ]
        
        # AI integrace používá jiné rozhraní
        if hasattr(ai, 'chat_completion'):
            response = ai.chat_completion(
                messages=messages,
                temperature=0.3,
                max_tokens=1500
            )
        else:
            # Fallback pro jiné AI rozhraní
            from ai.enhanced_openai_client import EnhancedOpenAIClient
            ai_client = EnhancedOpenAIClient()
            response = ai_client.chat_completion(
                messages=messages,
                temperature=0.3,
                max_tokens=1500
            )
        
        # Parsování JSON odpovědi
        import json
        import re
        
        json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
        if json_match:
            analysis = json.loads(json_match.group())
            logger.info("AI analýza úspěšně vygenerována")
            return analysis
        else:
            # Fallback
            return {
                "summary": response.content[:200] + "...",
                "main_themes": list(dict(top_words[:3]).keys()),
                "sentiment": "neutrální",
                "key_insights": ["AI analýza byla částečně úspěšná"],
                "recommendations": ["Zkontrolujte výsledky manuálně"]
            }
        
    except Exception as e:
        logger.error(f"Chyba při AI analýze: {e}")
        return {
            "summary": "AI analýza nebyla dostupná",
            "main_themes": [],
            "sentiment": "neznámý",
            "key_insights": [],
            "recommendations": []
        }


def _display_current_state(config_manager, vq_manager):
    """Zobrazí aktuální stav systému"""
    print("\n" + "-"*30)
    print("Aktuální Stav Systému")
    print("-"*30)

    # 1. Konfigurace grafů
    configs = config_manager.get_all_configurations()
    print(f"📊 Konfigurace grafů: {len(configs)} otázek")

    if configs:
        chart_types = {}
        for config in configs.values():
            for chart in config.charts:
                chart_types[chart.chart_type] = chart_types.get(chart.chart_type, 0) + 1

        print("   Typy grafů:")
        for chart_type, count in chart_types.items():
            print(f"      {chart_type}: {count}")

    # 2. Virtuální otázky
    virtual_questions = vq_manager.get_all_virtual_questions(include_hidden=True)
    visible_vq = sum(1 for vq in virtual_questions.values() if not vq.get('hidden', False))
    hidden_vq = len(virtual_questions) - visible_vq

    print(f"\n👻 Virtuální otázky: {len(virtual_questions)} celkem")
    print(f"   Viditelné: {visible_vq}")
    print(f"   Skryté: {hidden_vq}")

    if virtual_questions:
        strategies = {}
        for vq in virtual_questions.values():
            strategy = vq.get('merge_strategy', 'unknown')
            strategies[strategy] = strategies.get(strategy, 0) + 1

        print("   Merge strategie:")
        for strategy, count in strategies.items():
            print(f"      {strategy}: {count}")

    # 3. AI stav
    print(f"\n🤖 AI funkce: {'✅ Dostupné' if check_ai_availability() else '❌ Nedostupné'}")

    if check_ai_availability():
        try:
            from ai.data_analyst import create_ai_data_analyst
            from ai.integration import get_ai_integration

            ai = get_ai_integration()
            if ai:
                ai_analyst = create_ai_data_analyst(ai)
                stats = ai_analyst.get_analysis_statistics()
                print(f"   Cache analýz: {stats['cache_size']} položek")
                print(f"   Průměrná spolehlivost: {stats['average_confidence']:.1%}")
        except:
            print("   ⚠️ Statistiky AI nedostupné")

    # 4. Celkový stav
    total_items = len(configs) + len(virtual_questions)
    print(f"\n📈 Celkový stav:")
    print(f"   Celkem nakonfigurovaných položek: {total_items}")
    print(f"   Systém: {'🟢 Aktivní' if total_items > 0 else '🔵 Prázdný'}")
