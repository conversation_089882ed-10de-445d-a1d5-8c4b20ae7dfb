import argparse
from datetime import datetime
from pathlib import Path
from src.data_transformer import DataTransformer
from src.logger import get_logger
from src.limesurvey_client import LimeSurveyClient

# CLI utility functions
def parse_date(date_str):
    try:
        return datetime.strptime(date_str, "%Y-%m-%d")
    except ValueError:
        raise argparse.ArgumentTypeError(f"Not a valid date: '{date_str}'. Expected YYYY-MM-DD")

def create_parser():
    parser = argparse.ArgumentParser(description='LimeSurvey CLI')
    parser.add_argument('survey_id', type=str, help='Survey ID')
    parser.add_argument('--completed-only', action='store_true', dest='completed_only', help='Only completed responses')
    parser.add_argument('--empty-graphs', action='store_true', dest='empty_graphs', help='Allow empty graphs')
    parser.add_argument('--publish', action='store_true', dest='publish', help='Publish charts')
    parser.add_argument('--export-png', action='store_true', dest='export_png', help='Export PNG')
    parser.add_argument('--start-date', type=parse_date, dest='start_date', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=parse_date, dest='end_date', help='End date (YYYY-MM-DD)')
    return parser

def validate_args(args):
    sid = args.survey_id
    if not sid.isdigit() or len(sid) != 6:
        raise ValueError("Invalid survey ID; must be 6 digits")
    if getattr(args, 'start_date', None) and getattr(args, 'end_date', None):
        if args.end_date < args.start_date:
            raise ValueError("End date must not be before start date")

logger = get_logger(__name__)

def load_survey_structure(survey_id: str):
    """Načte strukturu průzkumu"""
    client = LimeSurveyClient()
    try:
        structure = client.get_survey_structure(survey_id)
        logger.info(f"Struktura průzkumu {survey_id} načtena")
        return structure
    except Exception as e:
        logger.error(f"Chyba při načítání struktury: {str(e)}")
        return None

def main():
    parser = argparse.ArgumentParser(description='LimeSurvey data processing')
    subparsers = parser.add_subparsers(dest='command', required=True)
    
    # Parser pro get-structure
    get_structure_parser = subparsers.add_parser('get-structure', 
        help='Get survey structure')
    get_structure_parser.add_argument('survey_id', type=int, 
        help='Survey ID')
    get_structure_parser.add_argument('--output', type=str,
        help='Output file path')
        
    # Parser pro generování grafů
    charts_parser = subparsers.add_parser('charts',
        help='Generate charts')
    charts_parser.add_argument('data', type=str,
        help='Chart data JSON file path')
    charts_parser.add_argument('output', type=str,
        help='Output directory for charts')
    charts_parser.add_argument('--questions', type=str, nargs='+',
        help='List of question codes to generate charts for. If not provided, generates all charts.')
        
    # Parser pro standardní zpracování
    process_parser = subparsers.add_parser('process',
        help='Process survey data')
    process_parser.add_argument('input', type=str, 
        help='Input CSV file path')
    process_parser.add_argument('output', type=str, 
        help='Output directory path')
    process_parser.add_argument('--question-type', type=str, required=False,
        choices=['single_choice', 'multiple_choice', 'numeric'],
        help='Type of question to process')
    process_parser.add_argument('--mapping-file', type=str,
        help='Path to question mapping CSV file')
    process_parser.add_argument('--generate-mapping', type=str,
        help='Generate mapping file from LSS data')
    
    args = parser.parse_args()
    
    try:
        if args.command == 'get-structure':
            from limesurvey_client import LimeSurveyClient
            client = LimeSurveyClient()
            structure = client.get_survey_structure(args.survey_id)
            
            if args.output:
                output_path = Path(args.output)
                with open(output_path, 'w') as f:
                    f.write(structure)
                logger.info(f"Struktura průzkumu uložena do {output_path}")
            else:
                print(structure)
            return
                
        elif args.command == 'charts':
            # Generování grafů
            from chart_generator import ChartGenerator
            import json
            
            # Načtení dat
            with open(args.data, 'r') as f:
                chart_data = json.load(f)
                
            # Získání seznamu otázek
            if args.questions:
                question_codes = args.questions
                logger.info(f"Generuji grafy pro otázky: {', '.join(question_codes)}")
            else:
                question_codes = [q['code'] for q in chart_data]
                logger.info(f"Generuji grafy pro všechny otázky ({len(question_codes)})")
            
            # Generování grafů
            generator = ChartGenerator()
            results = generator.generate_charts(args.data, args.output, question_codes)
            
            if results:
                logger.info(f"Vygenerováno {len(results)} grafů")
                for result in results:
                    logger.info(f"Graf {result['question_code']}: {result['chart_url']}")
            else:
                logger.error("Nepodařilo se vygenerovat žádné grafy")
                
        elif args.command == 'process':
            # Kontrola vstupních souborů
            input_path = Path(args.input)
            if not input_path.exists():
                raise FileNotFoundError(f"Input file {args.input} not found")
                
            output_dir = Path(args.output)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            transformer = DataTransformer(args.mapping_file)
            
            if args.generate_mapping:
                # Generování transformačního souboru
                mapping_path = output_dir / 'question_mapping.csv'
                transformer.generate_mapping_file(args.generate_mapping, mapping_path)
                logger.info(f"Transformační soubor generován: {mapping_path}")
            else:
                # Standardní zpracování dat
                df = transformer.transform_responses(args.input)
                
                # Příprava dat pro graf
                chart_data = transformer.prepare_chart_data(df, args.question_type)
                
                # Uložení výsledků
                output_path = output_dir / 'transformed_data.csv'
                transformer.save_transformed_data(df, output_path)
                
                logger.info(f"Data úspěšně zpracována a uložena do {output_path}")
                logger.info(f"Typ grafu: {chart_data['chart_type']}")
        
    except Exception as e:
        logger.error(f"Chyba při zpracování dat: {str(e)}")
        raise

if __name__ == '__main__':
    main()
