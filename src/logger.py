import json
import os
import logging
from datetime import datetime
from typing import Dict, Any, Optional

def get_logger(name: str) -> logging.Logger:
    """
    Vytvoří logger pro daný modul
    
    Args:
        name: <PERSON><PERSON>zev modulu
        
    Returns:
        Logger instance
    """
    logger = logging.getLogger(name)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    return logger

class ProcessLogger:
    def __init__(self, survey_id: str):
        self.survey_id = survey_id
        self.log_dir = "logs"
        self.log_file = f"{self.log_dir}/survey_{survey_id}.json"
        self.state = {
            "survey_id": survey_id,
            "start_time": datetime.now().isoformat(),
            "status": "started",
            "completed_steps": [],
            "current_step": None,
            "errors": [],
            "charts": {}
        }
        self._ensure_log_dir()
        self._save_state()
    
    def _ensure_log_dir(self):
        """Vytvoří složku pro logy pokud neexistuje"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def _save_state(self):
        """Uloží aktuální stav do souboru"""
        with open(self.log_file, 'w', encoding='utf-8') as f:
            json.dump(self.state, f, ensure_ascii=False, indent=2)
    
    def _load_state(self) -> Optional[Dict[str, Any]]:
        """Načte stav ze souboru"""
        if os.path.exists(self.log_file):
            with open(self.log_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    
    def start_step(self, step_name: str):
        """Zaznamená začátek kroku"""
        self.state["current_step"] = {
            "name": step_name,
            "start_time": datetime.now().isoformat()
        }
        self._save_state()
    
    def complete_step(self, step_name: str):
        """Zaznamená dokončení kroku"""
        if self.state["current_step"] and self.state["current_step"]["name"] == step_name:
            completed_step = self.state["current_step"]
            completed_step["end_time"] = datetime.now().isoformat()
            self.state["completed_steps"].append(completed_step)
            self.state["current_step"] = None
            self._save_state()
    
    def log_error(self, error: str, step: Optional[str] = None):
        """Zaznamená chybu"""
        error_entry = {
            "time": datetime.now().isoformat(),
            "error": error,
            "step": step or self.state["current_step"]["name"] if self.state["current_step"] else None
        }
        self.state["errors"].append(error_entry)
        self._save_state()
    
    def log_chart_created(self, question_name: str, chart_id: str):
        """Zaznamená vytvoření grafu"""
        self.state["charts"][question_name] = {
            "chart_id": chart_id,
            "created_at": datetime.now().isoformat(),
            "status": "created"
        }
        self._save_state()
    
    def log_chart_updated(self, question_name: str, status: str):
        """Aktualizuje stav grafu"""
        if question_name in self.state["charts"]:
            self.state["charts"][question_name]["status"] = status
            self.state["charts"][question_name]["updated_at"] = datetime.now().isoformat()
            self._save_state()
    
    def complete_process(self):
        """Označí proces jako dokončený"""
        self.state["status"] = "completed"
        self.state["end_time"] = datetime.now().isoformat()
        self._save_state()
    
    def can_resume_from_step(self, step_name: str) -> bool:
        """Zkontroluje, zda lze pokračovat od daného kroku"""
        saved_state = self._load_state()
        if not saved_state:
            return False
            
        completed_steps = saved_state.get("completed_steps", [])
        return any(step["name"] == step_name for step in completed_steps)
