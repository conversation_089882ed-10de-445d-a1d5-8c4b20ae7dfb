from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QComboBox, QCheckBox,
    QSpinBox, QPushButton, QTabWidget, QWidget,
    QColorDialog, QFontDialog
)
from PyQt5.QtCore import Qt

class ChartMetadataDialog(QDialog):
    def __init__(self, chart_manager, chart_type=None, parent=None):
        super().__init__(parent)
        self.chart_manager = chart_manager
        self.chart_type = chart_type
        self.metadata = {}
        self.metadata_file = None  # Cesta k souboru s metadaty
        
        self.setWindowTitle("Nastavení grafu")
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Z<PERSON>ložky pro různé kategorie metadat
        tab_widget = QTabWidget()
        
        # <PERSON>ískání schématu metadat
        schema = self.chart_manager.get_chart_metadata_schema(self.chart_type)
        
        # Společná metadata
        common_tab = QWidget()
        common_layout = QFormLayout(common_tab)
        
        for key, value in schema['common'].items():
            if isinstance(value, dict) and 'czech_name' in value:
                group_name = value['czech_name']
                widget = self.create_metadata_widget(key, value)
                if widget:
                    common_layout.addRow(group_name, widget)
                    
        tab_widget.addTab(common_tab, "Obecná nastavení")
        
        # Specifická metadata pro typ grafu
        if 'specific' in schema:
            specific_tab = QWidget()
            specific_layout = QFormLayout(specific_tab)
            
            for key, value in schema['specific'].items():
                widget = self.create_metadata_widget(key, value)
                if widget:
                    specific_layout.addRow(key, widget)
                    
            tab_widget.addTab(specific_tab, f"Nastavení pro {self.chart_type}")
            
        layout.addWidget(tab_widget)
        
        # Tlačítka
        buttons = QHBoxLayout()
        
        save_button = QPushButton("Uložit...")
        save_button.clicked.connect(self.handle_save)
        buttons.addWidget(save_button)
        
        load_button = QPushButton("Načíst...")
        load_button.clicked.connect(self.handle_load)
        buttons.addWidget(load_button)
        
        ok_button = QPushButton("OK")
        ok_button.clicked.connect(self.accept)
        buttons.addWidget(ok_button)
        
        cancel_button = QPushButton("Zrušit")
        cancel_button.clicked.connect(self.reject)
        buttons.addWidget(cancel_button)
        
        layout.addLayout(buttons)
        
    def create_metadata_widget(self, key, schema):
        """Vytvoří widget pro zadání metadat podle schématu"""
        if isinstance(schema, list):
            # Výběr z možností
            combo = QComboBox()
            combo.addItems(schema)
            combo.currentTextChanged.connect(lambda text: self.update_metadata(key, text))
            return combo
            
        elif isinstance(schema, str):
            if schema == "string":
                # Textové pole
                line_edit = QLineEdit()
                line_edit.textChanged.connect(lambda text: self.update_metadata(key, text))
                return line_edit
                
            elif schema == "number":
                # Číselné pole
                spin_box = QSpinBox()
                spin_box.setRange(-1000, 1000)
                spin_box.valueChanged.connect(lambda value: self.update_metadata(key, value))
                return spin_box
                
            elif schema == "boolean":
                # Zaškrtávací pole
                check_box = QCheckBox()
                check_box.stateChanged.connect(lambda state: self.update_metadata(key, state == Qt.Checked))
                return check_box
                
            elif schema == "array":
                # Textové pole pro seznam (čárkou oddělené hodnoty)
                line_edit = QLineEdit()
                line_edit.setPlaceholderText("Hodnoty oddělené čárkou")
                line_edit.textChanged.connect(
                    lambda text: self.update_metadata(key, [x.strip() for x in text.split(',')])
                )
                return line_edit
                
        elif isinstance(schema, dict):
            if 'font_size' in schema:
                # Dialog pro výběr fontu
                button = QPushButton("Vybrat font...")
                button.clicked.connect(lambda: self.show_font_dialog(key))
                return button
                
            elif 'colors' in schema:
                # Dialog pro výběr barvy
                button = QPushButton("Vybrat barvu...")
                button.clicked.connect(lambda: self.show_color_dialog(key))
                return button
                
        return None
        
    def show_font_dialog(self, key):
        """Zobrazí dialog pro výběr fontu"""
        font, ok = QFontDialog.getFont()
        if ok:
            self.update_metadata(key, {
                'family': font.family(),
                'size': font.pointSize(),
                'weight': font.weight()
            })
            
    def show_color_dialog(self, key):
        """Zobrazí dialog pro výběr barvy"""
        color = QColorDialog.getColor()
        if color.isValid():
            self.update_metadata(key, color.name())
            
    def update_metadata(self, key, value):
        """Aktualizuje hodnotu v metadatech"""
        self.metadata[key] = value
        
    def get_metadata(self):
        """Vrátí nastavená metadata"""
        return self.metadata

    def save_metadata(self, file_path):
        """Uloží metadata do souboru"""
        import json
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
            self.metadata_file = file_path
            return True
        except Exception as e:
            print(f"Chyba při ukládání metadat: {str(e)}")
            return False

    def load_metadata(self, file_path):
        """Načte metadata ze souboru"""
        import json
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.metadata = json.load(f)
            self.metadata_file = file_path
            return True
        except Exception as e:
            print(f"Chyba při načítání metadat: {str(e)}")
            return False

    def handle_save(self):
        """Obsluha tlačítka pro uložení metadat"""
        from PyQt5.QtWidgets import QFileDialog
        options = QFileDialog.Options()
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Uložit metadata grafu",
            "",
            "JSON Files (*.json);;All Files (*)",
            options=options
        )
        if file_path:
            if not file_path.endswith('.json'):
                file_path += '.json'
            if self.save_metadata(file_path):
                print(f"Metadata úspěšně uložena do {file_path}")

    def handle_load(self):
        """Obsluha tlačítka pro načtení metadat"""
        from PyQt5.QtWidgets import QFileDialog
        options = QFileDialog.Options()
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Načíst metadata grafu",
            "",
            "JSON Files (*.json);;All Files (*)",
            options=options
        )
        if file_path:
            if self.load_metadata(file_path):
                print(f"Metadata úspěšně načtena z {file_path}")
                self.update_ui_from_metadata()
