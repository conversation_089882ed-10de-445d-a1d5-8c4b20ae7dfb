"""
HTML Generator pro Hierarchical Analysis

Generuje HTML reporty z výsledků hierarchické analýzy.
"""

import os
import json
from pathlib import Path
from typing import Dict, List
import logging
# Zakladni HTML generator
class BaseAnalysisHTMLGenerator:
    """Zakladni HTML generator"""
    
    def _get_css_styles(self) -> str:
        """Vrati zakladni CSS styly"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header h2 {
            font-size: 1.5rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .navigation {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .nav-link {
            background: white;
            color: #667eea;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .section {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .section h2 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.8rem;
        }

        .section-summary {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            border-left: 4px solid #667eea;
        }

        .footer {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
            margin-top: 2rem;
        }
        """
    
    def _get_javascript(self) -> str:
        """Vrati zakladni JavaScript"""
        return """
        // Smooth scrolling pro navigaci
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        """

logger = logging.getLogger(__name__)


class HierarchicalAnalysisHTMLGenerator(BaseAnalysisHTMLGenerator):
    """Generátor HTML reportů pro hierarchickou analýzu"""
    
    def __init__(self):
        """Inicializace generátoru"""
        # Mapování typů otázek pro tooltips
        self.question_type_tooltips = {
            'T': 'Long Free Text - Dlouhá textová odpověď',
            'S': 'Short Free Text - Krátká textová odpověď',
            'M': 'Multiple choice - Seznam s možností výběru více odpovědí',
            'Y': 'Yes/No - Jednoduchá otázka Ano/Ne',
            'L': 'List (Radio) - Výběr z možností (radio button)',
            '5': '5 Point Choice - Likertova škála (1-5)',
            'A': 'Array (5 Point) - Matice s 5bodovou škálou',
            'F': 'Array (Flex. Labels) - Matice s vlastními štítky',
            'B': 'Array (10 Point) - Matice s 10bodovou škálou',
            'C': 'Array (Yes/No/Uncertain) - Matice Ano/Ne/Nevím',
            'H': 'Array (Increase/Same/Decrease) - Matice Více/Stejně/Méně',
            'E': 'Array (Flex. Labels) Dual Scale - Dvojitá škála',
            'N': 'Numerical Input - Číselný vstup',
            'D': 'Date - Datum',
            'G': 'Gender - Pohlaví',
            'I': 'Language Switch - Přepínač jazyka',
            'P': 'Multiple choice with comments - Vícenásobný výběr s komentáři',
            'Q': 'Multiple Short Text - Více krátkých textů',
            'R': 'Ranking - Řazení podle priority',
            'U': 'Huge Free Text - Velmi dlouhý text',
            'X': 'Boilerplate Question - Informační text',
            '1': 'Array (Flex. Labels) Dual Scale - Dvojitá flexibilní škála',
            ':': 'Array (Numbers) - Matice s čísly',
            ';': 'Array (Text) - Matice s textem',
            '|': 'File Upload - Nahrání souboru'
        }
        
        # Mapování analýz pro tooltips
        self.analysis_tooltips = {
            'FRA': 'Frequency Analysis - Frekvenční analýza četností odpovědí',
            'STA': 'Statistical Analysis - Statistická analýza (průměr, medián, směrodatná odchylka)',
            'DIA': 'Distribution Analysis - Analýza distribuce dat',
            'THA': 'Thematic Analysis - Tematická analýza textových odpovědí',
            'COA': 'Content Analysis - Obsahová analýza textu',
            'WCA': 'Word Cloud Analysis - Analýza pomocí word cloudu',
            'NTA': 'Network Text Analysis - Síťová analýza textu',
            'CMA': 'Comparative Analysis - Komparativní analýza mezi skupinami',
            'CRA': 'Correlation Analysis - Korelační analýza mezi proměnnými',
            'CTA': 'Contingency Analysis - Kontingenční analýza (křížové tabulky)',
            'FAA': 'Factor Analysis - Faktorová analýza',
            'REA': 'Regression Analysis - Regresní analýza',
            'TSA': 'Time Series Analysis - Analýza časových řad',
            'GEO': 'Geographical Analysis - Geografická analýza',
            'SEN': 'Sentiment Analysis - Analýza sentimentu',
            'CLS': 'Classification Analysis - Klasifikační analýza'
        }
    
    def generate_hierarchical_analysis_report(self, analysis_result: Dict, output_path: str, 
                                            survey_name: str = "Průzkum") -> bool:
        """
        Generuje hierarchický HTML report z analýzy
        
        Args:
            analysis_result: Výsledek hierarchické analýzy
            output_path: Cesta k výstupnímu HTML souboru
            survey_name: Název průzkumu
            
        Returns:
            True při úspěchu
        """
        try:
            html_content = self._generate_hierarchical_html_content(analysis_result, survey_name)
            
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"✅ Hierarchický HTML report vygenerován: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při generování hierarchického HTML reportu: {str(e)}")
            return False
    
    def _generate_hierarchical_html_content(self, analysis_result: Dict, survey_name: str) -> str:
        """Generuje hierarchický HTML obsah"""
        
        summary = analysis_result.get('summary', {})
        hierarchical_structure = analysis_result.get('hierarchical_structure', {})
        analysis_plan = analysis_result.get('analysis_plan', {})
        chart_plan = analysis_result.get('chart_plan', {})
        data_analysis = analysis_result.get('data_analysis', {})
        
        html = f"""<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hierarchická analýza - {survey_name}</title>
    <style>
        {self._get_hierarchical_css_styles()}
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🧠 Hierarchická analýza průzkumu</h1>
            <h2>{survey_name}</h2>
            <div class="summary-stats">
                <div class="stat-card">
                    <div class="stat-number">{summary.get('total_groups', 0)}</div>
                    <div class="stat-label">Skupin otázek</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{summary.get('total_questions', 0)}</div>
                    <div class="stat-label">Celkem otázek</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{summary.get('recognition_rate', 0):.1%}</div>
                    <div class="stat-label">Úspěšnost rozpoznání</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{summary.get('estimated_responses', 0)}</div>
                    <div class="stat-label">Odpovědí v datech</div>
                </div>
            </div>
        </header>

        <nav class="navigation">
            <a href="#structure" class="nav-link">🏗️ Struktura průzkumu</a>
            <a href="#groups" class="nav-link">📊 Analýza skupin</a>
            <a href="#analyses" class="nav-link">📈 Doporučené analýzy</a>
            <a href="#implementation" class="nav-link">🚀 Implementace</a>
        </nav>

        <main class="content">
            {self._generate_structure_overview_section(hierarchical_structure)}
            {self._generate_groups_section(hierarchical_structure)}
            {self._generate_hierarchical_analyses_section(analysis_plan)}
            {self._generate_hierarchical_implementation_section(summary, analysis_plan, chart_plan, data_analysis)}
        </main>

        <footer class="footer">
            <p>Vygenerováno Analysis Engine v2.0 - Hierarchická analýza | LimWrapp</p>
        </footer>
    </div>

    <script>
        {self._get_hierarchical_javascript()}
    </script>
</body>
</html>"""
        
        return html
    
    def _generate_structure_overview_section(self, hierarchical_structure: Dict) -> str:
        """Generuje sekci přehledu struktury"""
        groups = hierarchical_structure.get('groups', [])
        overall_stats = hierarchical_structure.get('overall_statistics', {})
        question_types = hierarchical_structure.get('question_types_distribution', {})
        
        # Přehled typů otázek s tooltips
        types_html = ""
        for q_type, count in sorted(question_types.items(), key=lambda x: x[1], reverse=True):
            tooltip = self.question_type_tooltips.get(q_type, f'Typ otázky {q_type}')
            types_html += f"""
            <div class="type-item" title="{tooltip}">
                <span class="type-code">{q_type}</span>
                <span class="type-count">{count}</span>
            </div>
            """
        
        return f"""
        <section id="structure" class="section">
            <h2>🏗️ Struktura průzkumu</h2>
            <div class="structure-overview">
                <div class="overview-card">
                    <h3>📊 Celkové statistiky</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Skupiny:</span>
                            <span class="stat-value">{len(groups)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Otázky:</span>
                            <span class="stat-value">{overall_stats.get('total_questions', 0)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Rozpoznáno:</span>
                            <span class="stat-value">{overall_stats.get('recognized_questions', 0)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Úspěšnost:</span>
                            <span class="stat-value">{overall_stats.get('recognition_rate', 0):.1%}</span>
                        </div>
                    </div>
                </div>
                
                <div class="overview-card">
                    <h3>📋 Typy otázek</h3>
                    <div class="question-types">
                        {types_html}
                    </div>
                </div>
            </div>
        </section>
        """
    
    def _generate_groups_section(self, hierarchical_structure: Dict) -> str:
        """Generuje sekci analýzy skupin"""
        groups = hierarchical_structure.get('groups', [])
        
        groups_html = ""
        for group in groups:
            group_stats = group.get('group_statistics', {})
            questions = group.get('questions', [])
            
            # Zkrácený seznam otázek (max 5)
            questions_preview = ""
            for i, question in enumerate(questions[:5]):
                confidence_class = "high" if question['confidence'] >= 0.8 else "medium" if question['confidence'] >= 0.5 else "low"
                q_text = question['question_text'][:80] + "..." if len(question['question_text']) > 80 else question['question_text']
                
                # Tooltip pro typ otázky
                q_type = question['question_type']
                type_tooltip = self.question_type_tooltips.get(q_type, f'Typ otázky {q_type}')
                
                questions_preview += f"""
                <div class="question-preview">
                    <div class="question-preview-header">
                        <span class="question-id">{question['qid']}</span>
                        <span class="question-type" title="{type_tooltip}">{question['question_type']}</span>
                        <span class="confidence confidence-{confidence_class}">{question['confidence']:.1%}</span>
                    </div>
                    <div class="question-preview-text">{q_text}</div>
                    {f"<div class='question-preview-meta'>{question['subquestion_count']} subotázek, {question['answer_option_count']} možností</div>" if question['subquestion_count'] > 0 or question['answer_option_count'] > 0 else ""}
                </div>
                """
            
            if len(questions) > 5:
                questions_preview += f"""
                <div class="more-questions">
                    <span>... a dalších {len(questions) - 5} otázek</span>
                </div>
                """
            
            # Doporučené analýzy pro skupinu s tooltips
            group_analyses = group.get('recommended_group_analyses', [])
            analyses_html = ""
            if group_analyses:
                analysis_tags = []
                for analysis in group_analyses:
                    tooltip = self.analysis_tooltips.get(analysis, f'Analýza {analysis}')
                    analysis_tags.append(f'<span class="analysis-tag" title="{tooltip}">{analysis}</span>')
                
                analyses_html = f"""
                <div class="group-analyses">
                    <h4>Doporučené analýzy skupiny:</h4>
                    <div class="analyses-tags">
                        {' '.join(analysis_tags)}
                    </div>
                </div>
                """
            
            groups_html += f"""
            <div class="group-card">
                <div class="group-header">
                    <h3>{group['group_name']}</h3>
                    <div class="group-meta">
                        <span class="group-order">Pořadí: {group['group_order']}</span>
                        <span class="question-count">{group['question_count']} otázek</span>
                        <span class="recognition-rate">{group_stats.get('recognition_rate', 0):.1%} rozpoznáno</span>
                    </div>
                </div>
                
                {f"<div class='group-description'>{group['group_description']}</div>" if group.get('group_description') else ""}
                
                <div class="group-statistics">
                    <div class="stat-row">
                        <span class="stat-label">Typy otázek:</span>
                        <span class="stat-value">{', '.join([f'{k}({v})' for k, v in group_stats.get('question_types', {}).items()])}</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Průměrná spolehlivost:</span>
                        <span class="stat-value">{group_stats.get('avg_confidence', 0):.1%}</span>
                    </div>
                </div>
                
                {analyses_html}
                
                <div class="questions-in-group">
                    <h4>Otázky ve skupině (v pořadí):</h4>
                    {questions_preview}
                </div>
            </div>
            """
        
        return f"""
        <section id="groups" class="section">
            <h2>📊 Analýza skupin (v pořadí dotazníku)</h2>
            <div class="section-summary">
                <p>Struktura respektuje přesné pořadí skupin a otázek, jak je vidí respondent.</p>
            </div>
            <div class="groups-container">
                {groups_html}
            </div>
        </section>
        """
    
    def _generate_hierarchical_analyses_section(self, analysis_plan: Dict) -> str:
        """Generuje sekci doporučených analýz pro hierarchickou strukturu"""
        
        group_analyses = analysis_plan.get('group_level_analyses', [])
        
        group_analyses_html = ""
        if group_analyses:
            for analysis in group_analyses:
                tooltip = self.analysis_tooltips.get(analysis, f'Analýza {analysis}')
                group_analyses_html += f"""
                <div class="analysis-item" title="{tooltip}">
                    <span class="analysis-code">{analysis}</span>
                    <span class="analysis-description">Analýza na úrovni skupin</span>
                </div>
                """
        else:
            group_analyses_html = "<p class='no-items'>Žádné skupinové analýzy</p>"
        
        return f"""
        <section id="analyses" class="section">
            <h2>📈 Doporučené analýzy</h2>
            <div class="section-summary">
                <p>Odhadovaný čas: <strong>{analysis_plan.get('total_estimated_time', 0)} minut</strong> | 
                   Složitost: <strong>{analysis_plan.get('complexity_score', 0):.1f}/10</strong></p>
            </div>
            
            <div class="analyses-hierarchical">
                <h3>🏗️ Analýzy na úrovni skupin</h3>
                <div class="analyses-group">
                    {group_analyses_html}
                </div>
                
                <div class="analysis-recommendations">
                    <h4>💡 Doporučení:</h4>
                    <ul>
                        <li>Začněte analýzami jednotlivých otázek v rámci skupin</li>
                        <li>Pokračujte komparativními analýzami mezi skupinami</li>
                        <li>Využijte hierarchickou strukturu pro lepší interpretaci</li>
                        <li>Respektujte pořadí otázek při prezentaci výsledků</li>
                    </ul>
                </div>
            </div>
        </section>
        """
    
    def _generate_hierarchical_implementation_section(self, summary: Dict, analysis_plan: Dict, 
                                                    chart_plan: Dict, data_analysis: Dict) -> str:
        """Generuje sekci implementace pro hierarchickou analýzu"""
        
        priority = summary.get('implementation_priority', 'Střední')
        priority_class = priority.lower()
        
        has_data = data_analysis and data_analysis.get('has_data', False)
        data_status = "✅ Dostupná" if has_data else "❌ Nedostupná"
        
        recommendations = []
        
        # Doporučení podle dostupnosti dat
        if has_data:
            recommendations.append("✅ CSV data jsou k dispozici - možná plná analýza")
            recommendations.append(f"📊 Odhadovaný počet odpovědí: {data_analysis.get('estimated_responses', 0)}")
        else:
            recommendations.append("⚠️ CSV data nejsou k dispozici - pouze strukturální analýza")
            recommendations.append("💡 Pro plnou analýzu načtěte data průzkumu (Menu 2)")
        
        # Doporučení podle složitosti
        complexity = summary.get('complexity_score', 5)
        if complexity <= 3:
            recommendations.append("✅ Nízká složitost - vhodné pro okamžitou implementaci")
        elif complexity <= 6:
            recommendations.append("⚠️ Střední složitost - doporučujeme postupnou implementaci")
        else:
            recommendations.append("🔴 Vysoká složitost - vyžaduje pečlivé plánování")
        
        # Hierarchická doporučení
        recommendations.append("🏗️ Využijte hierarchickou strukturu pro postupnou implementaci")
        recommendations.append("📋 Implementujte analýzy po skupinách pro lepší organizaci")
        
        recommendations_html = "".join([f"<li>{rec}</li>" for rec in recommendations])
        
        return f"""
        <section id="implementation" class="section">
            <h2>🚀 Doporučení pro implementaci</h2>
            <div class="implementation-summary">
                <div class="priority-badge priority-{priority_class}">
                    Priorita: {priority}
                </div>
                <div class="implementation-stats">
                    <div class="stat">
                        <span class="stat-label">Odhadovaný čas:</span>
                        <span class="stat-value">{summary.get('estimated_time_hours', 0):.1f} hodin</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Složitost:</span>
                        <span class="stat-value">{complexity:.1f}/10</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Data:</span>
                        <span class="stat-value">{data_status}</span>
                    </div>
                </div>
            </div>

            <div class="recommendations">
                <h3>💡 Doporučení:</h3>
                <ul>
                    {recommendations_html}
                </ul>
            </div>

            <div class="next-steps">
                <h3>📋 Další kroky pro hierarchickou analýzu:</h3>
                <ol>
                    <li>Exportovat tento hierarchický report pro dokumentaci</li>
                    <li>Implementovat analýzy postupně po skupinách</li>
                    <li>Respektovat pořadí otázek při generování grafů</li>
                    <li>Využít Menu 8 pro automatické generování grafů</li>
                    <li>Vytvořit prezentaci výsledků podle struktury dotazníku</li>
                </ol>
            </div>
        </section>
        """

    def _get_hierarchical_css_styles(self) -> str:
        """Vrátí CSS styly pro hierarchickou analýzu"""
        base_css = self._get_css_styles()
        
        hierarchical_css = """
        
        /* Hierarchical specific styles */
        .structure-overview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .overview-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .overview-card h3 {
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            background: white;
            border-radius: 5px;
        }
        
        .stat-label {
            font-weight: 500;
            color: #6c757d;
        }
        
        .stat-value {
            font-weight: bold;
            color: #667eea;
        }
        
        .question-types {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        .type-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: white;
            padding: 0.5rem;
            border-radius: 5px;
            cursor: help;
            transition: all 0.3s ease;
        }
        
        .type-item:hover {
            background: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .type-code {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-weight: bold;
            font-size: 0.8rem;
        }
        
        .type-count {
            font-weight: bold;
            color: #495057;
        }
        
        .groups-container {
            display: grid;
            gap: 2rem;
        }
        
        .group-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 2rem;
            background: white;
            transition: all 0.3s ease;
        }
        
        .group-card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-3px);
        }
        
        .group-header {
            margin-bottom: 1.5rem;
        }
        
        .group-header h3 {
            color: #667eea;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }
        
        .group-meta {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .group-meta span {
            background: #e9ecef;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .group-order {
            background: #667eea !important;
            color: white !important;
        }
        
        .group-description {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            font-style: italic;
            color: #6c757d;
        }
        
        .group-statistics {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .stat-row:last-child {
            margin-bottom: 0;
        }
        
        .group-analyses {
            margin-bottom: 1.5rem;
        }
        
        .group-analyses h4 {
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .analyses-tags {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .analysis-tag {
            background: #28a745;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            cursor: help;
            transition: all 0.3s ease;
        }
        
        .analysis-tag:hover {
            background: #218838;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }
        
        .questions-in-group h4 {
            color: #495057;
            margin-bottom: 1rem;
        }
        
        .question-preview {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .question-preview:hover {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .question-preview-header {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            align-items: center;
        }
        
        .question-preview-header .question-type {
            background: #e9ecef;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: help;
            transition: all 0.3s ease;
        }
        
        .question-preview-header .question-type:hover {
            background: #667eea;
            color: white;
            transform: scale(1.05);
        }
        
        .question-preview-text {
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .question-preview-meta {
            font-size: 0.8rem;
            color: #6c757d;
            font-style: italic;
        }
        
        .more-questions {
            text-align: center;
            padding: 1rem;
            color: #6c757d;
            font-style: italic;
        }
        
        .analyses-hierarchical {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
        }
        
        .analyses-hierarchical h3 {
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .analysis-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            background: white;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            cursor: help;
            transition: all 0.3s ease;
        }
        
        .analysis-item:hover {
            background: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .analysis-code {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.8rem;
        }
        
        .analysis-description {
            color: #495057;
        }
        
        .analysis-recommendations {
            margin-top: 2rem;
        }
        
        .analysis-recommendations h4 {
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .analysis-recommendations ul {
            padding-left: 2rem;
        }
        
        .analysis-recommendations li {
            margin-bottom: 0.5rem;
            color: #495057;
        }
        
        @media (max-width: 768px) {
            .structure-overview {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .group-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .question-preview-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }
        }
        """
        
        return base_css + hierarchical_css

    def _get_hierarchical_javascript(self) -> str:
        """Vrátí JavaScript kód pro hierarchickou analýzu"""
        base_js = self._get_javascript()
        
        hierarchical_js = """
        
        // Hierarchical specific JavaScript
        
        // Rozbalování/sbalování skupin
        document.addEventListener('DOMContentLoaded', function() {
            const groupCards = document.querySelectorAll('.group-card');
            
            groupCards.forEach(card => {
                const header = card.querySelector('.group-header h3');
                if (header) {
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', function() {
                        const questionsSection = card.querySelector('.questions-in-group');
                        if (questionsSection) {
                            const isHidden = questionsSection.style.display === 'none';
                            questionsSection.style.display = isHidden ? 'block' : 'none';
                            header.textContent = header.textContent.replace(/[▼▶]/, '') + (isHidden ? ' ▼' : ' ▶');
                        }
                    });
                    header.textContent += ' ▼';
                }
            });
        });
        
        // Zvýraznění otázek při hover
        document.addEventListener('DOMContentLoaded', function() {
            const questionPreviews = document.querySelectorAll('.question-preview');
            
            questionPreviews.forEach(preview => {
                preview.addEventListener('mouseenter', function() {
                    this.style.borderColor = '#667eea';
                    this.style.borderWidth = '2px';
                });
                
                preview.addEventListener('mouseleave', function() {
                    this.style.borderColor = '#e9ecef';
                    this.style.borderWidth = '1px';
                });
            });
        });
        """
        
        return base_js + hierarchical_js