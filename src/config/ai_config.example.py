"""
AI Configuration Example for Report Canvas
Příklad konfigurace AI služeb pro Report Canvas

Zkopírujte tento soubor jako ai_config.py a vyplňte své API klíče.
"""

# OpenAI API Key
# Získejte na: https://platform.openai.com/api-keys
OPENAI_API_KEY = "your-openai-api-key-here"

# Model settings
DEFAULT_MODEL = "gpt-3.5-turbo"
MAX_TOKENS = 1000
TEMPERATURE = 0.3

# Rate limiting
MAX_REQUESTS_PER_MINUTE = 20

# Custom system prompts
CUSTOM_SYSTEM_PROMPTS = {
    "Marketingový analytik": """Jste zkušený marketingový analytik zaměřený na consumer insights a tržní trendy. 
    Vytvářejte analýzy, které pomáhají pochopit chování zákazníků a optimalizovat marketingové strategie.""",
    
    "Finanční analytik": """Jste finanční analytik specializující se na analýzu výkonnosti a rizik. 
    Zaměřujte se na klíčové finanční metriky, trendy a doporučení pro finanční rozhodování.""",
    
    "HR analytik": """Jste HR analytik zaměřený na analýzu lidských zdrojů a organizační efektivity. 
    Analyzujte data o zaměstnancích, spokojenosti a produktivitě."""
}

# Advanced settings
RETRY_ATTEMPTS = 3
CACHE_RESPONSES = True
DEBUG_MODE = False
TIMEOUT_SECONDS = 60