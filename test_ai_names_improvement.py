#!/usr/bin/env python3
"""
Test AI úpravy názvů pro grafy
"""

import os
import sys
import json

# Přidání src do path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ai_names_improvement():
    """Test AI úpravy názvů"""
    print("=== Test AI úpravy názvů pro grafy ===")
    
    # Test kontroly dostupnosti AI
    try:
        from menu.ai_menu_functions import check_ai_availability
        ai_available = check_ai_availability()
        print(f"AI dostupnost: {'✅ Ano' if ai_available else '❌ Ne'}")
        
        if not ai_available:
            print("💡 Pro test AI funkcí nastavte OPENAI_API_KEY v .env")
            return
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return
    
    # Test AI klienta
    try:
        from ai.enhanced_openai_client import EnhancedOpenAIClient
        
        print("\n🔧 Inicializuji AI klient...")
        ai_client = EnhancedOpenAIClient()
        print("✅ AI klient úspěšně inicializován")
        
        # Test základního volání
        print("\n🧪 Test základního AI volání...")
        messages = [
            {"role": "system", "content": "Jsi pomocník pro úpravu názvů."},
            {"role": "user", "content": "Uprav tento název pro graf: 'Jak hodnotíte kvalitu našich služeb v oblasti zákaznického servisu?' - udělej ho stručnější."}
        ]
        
        response = ai_client.chat_completion(
            messages=messages,
            temperature=0.3,
            max_tokens=100
        )
        
        print(f"✅ AI odpověď: {response.content}")
        print(f"💰 Náklady: ${response.cost:.4f}")
        print(f"🔢 Tokeny: {response.tokens_used}")
        
    except Exception as e:
        print(f"❌ Chyba AI klienta: {e}")
        return
    
    # Test helper funkcí
    print("\n🔧 Test helper funkcí...")
    
    # Import funkcí z main.py
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
    from main import create_ai_improvement_prompt, parse_ai_response
    
    # Test vytvoření promptu
    test_names = [
        "Jak hodnotíte kvalitu našich služeb v oblasti zákaznického servisu?",
        "Doporučili byste naše služby svým přátelům a známým?",
        "Jaká je vaše celková spokojenost s naší organizací?"
    ]
    
    prompt = create_ai_improvement_prompt(test_names, "Čeština", "cs-CZ")
    print(f"✅ Prompt vytvořen ({len(prompt)} znaků)")
    
    # Test parsování AI odpovědi
    test_ai_response = '''
    {
      "improvements": {
        "Jak hodnotíte kvalitu našich služeb v oblasti zákaznického servisu?": "Kvalita zákaznického servisu",
        "Doporučili byste naše služby svým přátelům a známým?": "Doporučení služeb",
        "Jaká je vaše celková spokojenost s naší organizací?": "Celková spokojenost"
      }
    }
    '''
    
    improvements = parse_ai_response(test_ai_response, test_names)
    print(f"✅ Parsování úspěšné, {len(improvements)} úprav")
    
    for original, improved in improvements.items():
        if original != improved:
            print(f"   '{original[:50]}...' → '{improved}'")
    
    print("\n✅ Všechny testy prošly úspěšně!")

if __name__ == "__main__":
    test_ai_names_improvement()