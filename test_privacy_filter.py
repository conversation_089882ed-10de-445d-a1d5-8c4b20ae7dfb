#!/usr/bin/env python3
"""
Test Privacy Filter
Testuje funkčnost privacy filtru pro vyloučení citlivých sloupců
"""

import os
import sys
import pandas as pd

# Přidání src do cesty
sys.path.insert(0, 'src')

def test_privacy_filter():
    """Test základní funkčnosti privacy filtru"""
    print("🧪 Test Privacy Filter")
    print("=" * 50)
    
    try:
        from privacy_filter import PrivacyFilter, get_privacy_filter, apply_privacy_filter
        
        # Test survey ID
        test_survey_id = "827822"
        
        print(f"📊 Testování s survey ID: {test_survey_id}")
        
        # 1. Inicializace filtru
        print("\n1. Inicializace privacy filtru...")
        privacy_filter = PrivacyFilter(test_survey_id)
        print(f"✅ Privacy filter inicializován pro survey {test_survey_id}")
        
        # 2. Test parsování rozsahů
        print("\n2. Test parsování rozsah<PERSON> sloupců...")
        test_ranges = [
            "1-4,8,64-78",
            "1,3,5",
            "10-15",
            "",
            "1-3,5-7,10"
        ]
        
        for range_str in test_ranges:
            parsed = privacy_filter.parse_column_range(range_str)
            print(f"   '{range_str}' → {sorted(list(parsed))}")
        
        # 3. Test nastavení vyloučených sloupců
        print("\n3. Test nastavení vyloučených sloupců...")
        test_exclusion = "1-4,8,64-78"
        if privacy_filter.set_excluded_columns(test_exclusion):
            print(f"✅ Nastaveno vyloučení: {test_exclusion}")
            print(f"   Vyloučené sloupce: {privacy_filter.get_excluded_columns_list()}")
        else:
            print("❌ Chyba při nastavování vyloučení")
        
        # 4. Test s ukázkovými daty
        print("\n4. Test filtrování DataFrame...")
        
        # Vytvoření ukázkového DataFrame
        sample_data = {
            'id': [1, 2, 3],
            'lastpage': [1, 1, 1],
            'submitdate': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'G1Q00001': ['Ano', 'Ne', 'Ano'],
            'G2Q00002': ['Spokojen', 'Nespokojen', 'Spokojen'],
            'G3Q00003': ['Text1', 'Text2', 'Text3'],
            'phone': ['+420123456789', '+420987654321', '+420555666777']
        }
        
        df_original = pd.DataFrame(sample_data)
        print(f"   Původní DataFrame: {len(df_original.columns)} sloupců")
        print(f"   Sloupce: {list(df_original.columns)}")
        
        # Aplikace filtru
        df_filtered = privacy_filter.filter_dataframe(df_original)
        print(f"   Filtrovaný DataFrame: {len(df_filtered.columns)} sloupců")
        print(f"   Sloupce: {list(df_filtered.columns)}")
        print(f"   Odstraněno: {len(df_original.columns) - len(df_filtered.columns)} sloupců")
        
        # 5. Test centrálních funkcí
        print("\n5. Test centrálních funkcí...")
        
        # Test get_privacy_filter
        pf2 = get_privacy_filter(test_survey_id)
        print(f"✅ get_privacy_filter: {len(pf2.excluded_columns)} vyloučených sloupců")
        
        # Test apply_privacy_filter
        df_filtered2 = apply_privacy_filter(df_original, test_survey_id)
        print(f"✅ apply_privacy_filter: {len(df_filtered2.columns)} sloupců po filtrování")
        
        # 6. Test souhrnu
        print("\n6. Test souhrnu nastavení...")
        summary = privacy_filter.get_filter_summary()
        print(f"   Survey ID: {summary['survey_id']}")
        print(f"   Filtr aktivní: {summary['filter_active']}")
        print(f"   Vyloučených sloupců: {summary['excluded_count']}")
        print(f"   Vyloučené pozice: {summary['excluded_columns']}")
        
        # 7. Test vymazání nastavení
        print("\n7. Test vymazání nastavení...")
        if privacy_filter.clear_filter():
            print("✅ Nastavení vymazáno")
            summary_after = privacy_filter.get_filter_summary()
            print(f"   Filtr aktivní po vymazání: {summary_after['filter_active']}")
        else:
            print("❌ Chyba při mazání nastavení")
        
        print("\n" + "=" * 50)
        print("✅ Všechny testy privacy filtru prošly úspěšně!")
        return True
        
    except ImportError as e:
        print(f"❌ Chyba importu: {e}")
        print("💡 Zkontrolujte, zda existuje src/privacy_filter.py")
        return False
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration_with_data_transformer():
    """Test integrace s data_transformer"""
    print("\n🔗 Test integrace s data_transformer")
    print("=" * 50)
    
    try:
        from data_transformer import transform_to_long_format, generate_chart_data
        
        # Simulace - vytvoříme ukázkový CSV soubor
        test_survey_id = "827822"
        test_csv_path = f"test_data_{test_survey_id}.csv"
        
        # Vytvoření ukázkového CSV
        sample_data = {
            'id': [1, 2, 3],
            'lastpage': [1, 1, 1],
            'submitdate': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'G1Q00001': ['Ano', 'Ne', 'Ano'],
            'G2Q00002': ['Spokojen', 'Nespokojen', 'Spokojen'],
            'phone': ['+420123456789', '+420987654321', '+420555666777']
        }
        
        df = pd.DataFrame(sample_data)
        df.to_csv(test_csv_path, sep=';', index=False)
        print(f"✅ Vytvořen testovací CSV: {test_csv_path}")
        
        # Test privacy filtru s data_transformer funkcemi
        print("\n📊 Test privacy filtru v data_transformer...")
        
        # Nastavení privacy filtru
        from privacy_filter import PrivacyFilter
        pf = PrivacyFilter(test_survey_id)
        pf.set_excluded_columns("4,7")  # email a phone sloupce
        
        print(f"   Nastaveno vyloučení sloupců: {pf.get_excluded_columns_list()}")
        
        # Test s apply_privacy_filter
        from privacy_filter import apply_privacy_filter
        df_original = pd.read_csv(test_csv_path, sep=';')
        df_filtered = apply_privacy_filter(df_original, test_survey_id)
        
        print(f"   Původní sloupce: {list(df_original.columns)}")
        print(f"   Filtrované sloupce: {list(df_filtered.columns)}")
        print(f"   Odstraněno: {len(df_original.columns) - len(df_filtered.columns)} sloupců")
        
        # Kontrola, že citlivé sloupce byly odstraněny
        sensitive_columns = ['email', 'phone']
        remaining_sensitive = [col for col in sensitive_columns if col in df_filtered.columns]
        
        if not remaining_sensitive:
            print("✅ Citlivé sloupce byly úspěšně odstraněny")
        else:
            print(f"❌ Citlivé sloupce stále přítomny: {remaining_sensitive}")
        
        # Úklid
        if os.path.exists(test_csv_path):
            os.remove(test_csv_path)
            print(f"🧹 Smazán testovací soubor: {test_csv_path}")
        
        # Vymazání nastavení
        pf.clear_filter()
        print("🧹 Vymazáno testovací nastavení privacy filtru")
        
        print("\n✅ Test integrace úspěšný!")
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testu integrace: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🧪 PRIVACY FILTER - KOMPLETNÍ TEST")
    print("=" * 60)
    
    success1 = test_privacy_filter()
    success2 = test_integration_with_data_transformer()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 VŠECHNY TESTY ÚSPĚŠNÉ!")
        print("💡 Privacy filter je připraven k použití")
        print("\n📋 Jak používat:")
        print("   1. Spusťte Menu 16 v main.py")
        print("   2. Vyberte průzkum")
        print("   3. Nakonfigurujte vyloučené sloupce")
        print("   4. Privacy filtr se automaticky aplikuje při zpracování dat")
    else:
        print("❌ NĚKTERÉ TESTY SELHALY")
        print("💡 Zkontrolujte chybové zprávy výše")
    
    sys.exit(0 if success1 and success2 else 1)