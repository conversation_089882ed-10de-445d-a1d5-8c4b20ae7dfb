<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inteligentní analýza - Test Survey</title>
    <style>
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header h2 {
            font-size: 1.5rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .navigation {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .nav-link {
            background: white;
            color: #667eea;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .section {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .section h2 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.8rem;
        }

        .section-summary {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            border-left: 4px solid #667eea;
        }

        .questions-grid {
            display: grid;
            gap: 1rem;
        }

        .question-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .question-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .question-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .question-id {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .question-code {
            background: #e9ecef;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .confidence {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .confidence-high { background: #d4edda; color: #155724; }
        .confidence-medium { background: #fff3cd; color: #856404; }
        .confidence-low { background: #f8d7da; color: #721c24; }

        .question-text {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            color: #495057;
        }

        .question-type {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .question-type strong {
            color: #667eea;
        }

        .type-description {
            display: block;
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }

        .question-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .question-meta span {
            background: #e9ecef;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
        }

        .question-notes {
            font-size: 0.9rem;
            color: #6c757d;
            font-style: italic;
        }

        .analyses-group, .visualizations-group {
            display: grid;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .analysis-card, .visualization-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .analysis-card:hover, .visualization-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .analysis-header, .viz-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .analysis-header h4, .viz-header h4 {
            color: #667eea;
            margin: 0;
        }

        .analysis-meta, .viz-meta {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .priority {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .priority-high { background: #d4edda; color: #155724; }
        .priority-medium { background: #fff3cd; color: #856404; }
        .priority-low { background: #f8d7da; color: #721c24; }

        .complexity {
            background: #e9ecef;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
        }

        .datawrapper-badge {
            background: #28a745;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .external-badge {
            background: #ffc107;
            color: #212529;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .analysis-description, .viz-description {
            color: #495057;
            margin-bottom: 1rem;
        }

        .analysis-details, .viz-details {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
        }

        .analysis-details > div, .viz-details > div {
            margin-bottom: 0.5rem;
        }

        .analysis-details > div:last-child, .viz-details > div:last-child {
            margin-bottom: 0;
        }

        .implementation-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .priority-badge {
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .priority-badge.priority-vysoká { background: #d4edda; color: #155724; }
        .priority-badge.priority-střední { background: #fff3cd; color: #856404; }
        .priority-badge.priority-nízká { background: #f8d7da; color: #721c24; }

        .implementation-stats {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .stat {
            text-align: center;
        }

        .stat-label {
            display: block;
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }

        .stat-value {
            display: block;
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
        }

        .recommendations, .next-steps {
            margin-bottom: 2rem;
        }

        .recommendations h3, .next-steps h3 {
            color: #667eea;
            margin-bottom: 1rem;
        }

        .recommendations ul, .next-steps ol {
            padding-left: 2rem;
        }

        .recommendations li, .next-steps li {
            margin-bottom: 0.5rem;
        }

        .no-items {
            color: #6c757d;
            font-style: italic;
            text-align: center;
            padding: 2rem;
        }

        .footer {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .summary-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .navigation {
                justify-content: center;
            }

            .implementation-summary {
                flex-direction: column;
                text-align: center;
            }

            .implementation-stats {
                justify-content: center;
            }
        }
        
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🧠 Inteligentní analýza průzkumu</h1>
            <h2>Test Survey</h2>
            <div class="summary-stats">
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">Celkem otázek</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100.0%</div>
                    <div class="stat-label">Úspěšnost rozpoznání</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1</div>
                    <div class="stat-label">Doporučené analýzy</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1</div>
                    <div class="stat-label">Vizualizace</div>
                </div>
            </div>
        </header>

        <nav class="navigation">
            <a href="#questions" class="nav-link">📊 Analýza otázek</a>
            <a href="#analyses" class="nav-link">📈 Doporučené analýzy</a>
            <a href="#visualizations" class="nav-link">🎨 Vizualizace</a>
            <a href="#implementation" class="nav-link">🚀 Implementace</a>
        </nav>

        <main class="content">
            
        <section id="questions" class="section">
            <h2>📊 Analýza otázek</h2>
            <div class="section-summary">
                <p>Rozpoznáno <strong>5</strong> z <strong>5</strong> otázek 
                   (úspěšnost: <strong>100.0%</strong>)</p>
            </div>
            <div class="questions-grid">
                
            <div class="question-card">
                <div class="question-header">
                    <span class="question-id">Q1</span>
                    <span class="question-code">satisfaction</span>
                    <span class="confidence confidence-high">100.0%</span>
                </div>
                <div class="question-text">Jak jste spokojeni s naší službou?</div>
                
                <div class="question-type">
                    <strong>5 Point Choice</strong>
                    <span class="type-description">Likertova škála</span>
                </div>
                
                <div class="question-meta">
                    <span class="limesurvey-type">LimeSurvey: 5</span>
                    <span class="analyses-count">2 analýz</span>
                </div>
                <div class="question-notes">Mapováno na typ: 5 Point Choice | Spolehlivost: 100.0%</div>
            </div>
            
            </div>
        </section>
        
            
        <section id="analyses" class="section">
            <h2>📈 Doporučené analýzy</h2>
            <div class="section-summary">
                <p>Odhadovaný čas: <strong>60 minut</strong> | 
                   Složitost: <strong>2.5/10</strong></p>
            </div>
            <h3>📊 Analýzy na úrovni otázek</h3><div class='analyses-group'>
                <div class="analysis-card">
                    <div class="analysis-header">
                        <h4>Frequency Analysis</h4>
                        <div class="analysis-meta">
                            <span class="priority priority-high">0.80</span>
                            <span class="complexity">⭐⭐</span>
                        </div>
                    </div>
                    <p class="analysis-description">Frekvenční analýza odpovědí</p>
                    <div class="analysis-details">
                        <div class="applicable-questions">
                            <strong>Aplikovatelné otázky:</strong> Q1
                        </div>
                        <div class="reasoning">
                            <strong>Zdůvodnění:</strong> Vhodné pro škálové otázky
                        </div>
                        <div class='prerequisites'><strong>Předpoklady:</strong> Číselná data</div>
                    </div>
                </div>
                </div>
            <h3>📈 Analýzy na úrovni sekcí</h3><p class='no-items'>Žádné analýzy tohoto typu</p>
            <h3>🔗 Cross-question analýzy</h3><p class='no-items'>Žádné analýzy tohoto typu</p>
        </section>
        
            
        <section id="visualizations" class="section">
            <h2>🎨 Vizualizace</h2>
            <div class="section-summary">
                <p>Odhad grafů: <strong>5</strong> | 
                   Datawrapper kompatibilita: <strong>80.0%</strong></p>
            </div>
            <h3>🎨 Primární vizualizace</h3><div class='visualizations-group'>
                <div class="visualization-card">
                    <div class="viz-header">
                        <h4>Sloupcový graf</h4>
                        <div class="viz-meta">
                            <span class='datawrapper-badge'>Datawrapper</span>
                            <span class="priority priority-high">0.90</span>
                        </div>
                    </div>
                    <p class="viz-description">Základní sloupcový graf</p>
                    <div class="viz-details">
                        <div class="viz-type">
                            <strong>Typ:</strong> d3-bars
                        </div>
                        <div class="data-requirements">
                            <strong>Požadavky:</strong> Kategoriální data
                        </div>
                        <div class='fallback-options'><strong>Alternativy:</strong> TAB</div>
                    </div>
                </div>
                </div>
            <h3>🔄 Alternativní vizualizace</h3><p class='no-items'>Žádné vizualizace</p>
        </section>
        
            
        <section id="implementation" class="section">
            <h2>🚀 Doporučení pro implementaci</h2>
            <div class="implementation-summary">
                <div class="priority-badge priority-vysoká">
                    Priorita: Vysoká
                </div>
                <div class="implementation-stats">
                    <div class="stat">
                        <span class="stat-label">Odhadovaný čas:</span>
                        <span class="stat-value">1.0 hodin</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Složitost:</span>
                        <span class="stat-value">2.5/10</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Kompatibilita:</span>
                        <span class="stat-value">80.0%</span>
                    </div>
                </div>
            </div>

            <div class="recommendations">
                <h3>💡 Doporučení:</h3>
                <ul>
                    <li>✅ Nízká složitost - vhodné pro okamžitou implementaci</li><li>🚀 Vysoká Datawrapper kompatibilita - většina grafů automaticky</li><li>⏱️ Rychlá implementace - do 2 hodin</li>
                </ul>
            </div>

            <div class="next-steps">
                <h3>📋 Další kroky:</h3>
                <ol>
                    <li>Exportovat tento report pro dokumentaci</li>
                    <li>Prioritizovat analýzy podle skóre</li>
                    <li>Implementovat Datawrapper kompatibilní vizualizace</li>
                    <li>Připravit externí generátory pro specializované grafy</li>
                    <li>Testovat na vzorových datech</li>
                </ol>
            </div>
        </section>
        
        </main>

        <footer class="footer">
            <p>Vygenerováno Analysis Engine | LimWrapp v2.0</p>
        </footer>
    </div>

    <script>
        
        // Smooth scrolling pro navigaci
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Zvýraznění aktivní sekce v navigaci
        function updateActiveNavigation() {
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.nav-link');

            let currentSection = '';

            sections.forEach(section => {
                const rect = section.getBoundingClientRect();
                if (rect.top <= 100 && rect.bottom >= 100) {
                    currentSection = section.id;
                }
            });

            navLinks.forEach(link => {
                link.style.background = link.getAttribute('href') === '#' + currentSection ? '#667eea' : 'white';
                link.style.color = link.getAttribute('href') === '#' + currentSection ? 'white' : '#667eea';
            });
        }

        window.addEventListener('scroll', updateActiveNavigation);
        window.addEventListener('load', updateActiveNavigation);

        // Animace při načtení
        window.addEventListener('load', function() {
            const cards = document.querySelectorAll('.question-card, .analysis-card, .visualization-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 50);
                }, index * 50);
            });
        });
        
    </script>
</body>
</html>