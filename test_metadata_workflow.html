<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> průzku<PERSON> METADATA_TEST</title>
    <style>
        /* Reset a základní styly */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header */
        header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 10px;
            text-align: center;
        }
        
        header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 300;
        }
        
        header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        /* Filter section */
        .filter-section {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .filter-input-group {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }
        
        #chartFilter {
            flex: 1;
            min-width: 300px;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        #chartFilter:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .filter-stats {
            background: #f8f9fa;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            color: #495057;
        }
        
        .clear-filter {
            background: #dc3545;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s ease;
        }
        
        .clear-filter:hover {
            background: #c82333;
        }
        
        /* Charts container */
        #chartsContainer {
            display: grid;
            gap: 2rem;
        }
        
        /* Chart item */
        .chart-item {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .chart-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .chart-header {
            padding: 1.5rem;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .chart-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .chart-meta {
            font-size: 0.9rem;
            color: #6c757d;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .chart-embed {
            position: relative;
            width: 100%;
            min-height: 400px;
        }
        
        .chart-embed iframe {
            width: 100%;
            height: 400px;
            border: none;
            display: block;
        }
        
        .chart-footer {
            padding: 1rem 1.5rem;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .chart-share-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 8px 16px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: background-color 0.3s ease;
        }
        
        .chart-share-link:hover {
            background: #5a6fd8;
            text-decoration: none;
            color: white;
        }
        
        /* Loading state */
        .loading {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* No results */
        .no-results {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
            background: white;
            border-radius: 10px;
            margin-top: 2rem;
        }
        
        .no-results h3 {
            margin-bottom: 1rem;
            color: #495057;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            header h1 {
                font-size: 2rem;
            }
            
            .filter-input-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            #chartFilter {
                min-width: auto;
            }
            
            .chart-footer {
                flex-direction: column;
                align-items: stretch;
            }
            
            .chart-share-link {
                justify-content: center;
            }
        }
        
        /* Print styles */
        @media print {
            body {
                background: white;
            }
            
            .filter-section {
                display: none;
            }
            
            .chart-item {
                break-inside: avoid;
                box-shadow: none;
                border: 1px solid #ddd;
                margin-bottom: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header>
            <h1>Přehled grafů - Test Metadata Workflow</h1>
            <p>Generováno: 27.06.2025 16:35 | Celkem grafů: 1</p>
        </header>
        
        <!-- Filter section -->
        <section class="filter-section">
            <div class="filter-input-group">
                <input type="text" id="chartFilter" placeholder="Filtrovat grafy podle názvu...">
                <div class="filter-stats">
                    Zobrazeno: <span id="visibleCount">1</span> z <span id="totalCount">1</span>
                </div>
                <button class="clear-filter" onclick="clearFilter()">Vymazat filtr</button>
            </div>
        </section>
        
        <!-- Charts container -->
        <main id="chartsContainer">
            <div class="chart-item" data-chart-name="test graf - metadata workflow">
                <div class="chart-header">
                    <h2 class="chart-title">Test Graf - Metadata Workflow</h2>
                    <div class="chart-meta">
                        <span>ID: tQ2P3</span>
                        <span>Typ: d3-bars</span>
                    </div>
                </div>
                <div class="chart-embed">
                    <iframe src="https://datawrapper.dwcdn.net/tQ2P3/2/" loading="lazy"></iframe>
                </div>
                <div class="chart-footer">
                    <a href="https://datawrapper.dwcdn.net/tQ2P3/2/" target="_blank" class="chart-share-link">
                        🔗 Sdílet graf
                    </a>
                </div>
            </div>
        </main>
        
        <!-- No results message (hidden by default) -->
        <div class="no-results" id="noResults" style="display: none;">
            <h3>Žádné grafy neodpovídají filtru</h3>
            <p>Zkuste změnit vyhledávací kritéria nebo vymazat filtr.</p>
        </div>
    </div>

    <script>
        // Chart filtering functionality
        function initChartFilter() {
            const filterInput = document.getElementById('chartFilter');
            const chartItems = document.querySelectorAll('.chart-item');
            const visibleCount = document.getElementById('visibleCount');
            const totalCount = document.getElementById('totalCount');
            const noResults = document.getElementById('noResults');
            
            totalCount.textContent = chartItems.length;
            
            function filterCharts() {
                const filterValue = filterInput.value.toLowerCase().trim();
                let visible = 0;
                
                chartItems.forEach(item => {
                    const chartName = item.dataset.chartName;
                    const isVisible = chartName.includes(filterValue);
                    
                    item.style.display = isVisible ? 'block' : 'none';
                    if (isVisible) visible++;
                });
                
                visibleCount.textContent = visible;
                
                // Show/hide no results message
                if (visible === 0 && filterValue !== '') {
                    noResults.style.display = 'block';
                } else {
                    noResults.style.display = 'none';
                }
            }
            
            filterInput.addEventListener('input', filterCharts);
            filterInput.addEventListener('keyup', function(e) {
                if (e.key === 'Escape') {
                    clearFilter();
                }
            });
        }
        
        function clearFilter() {
            const filterInput = document.getElementById('chartFilter');
            filterInput.value = '';
            filterInput.dispatchEvent(new Event('input'));
            filterInput.focus();
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initChartFilter();
            
            // Add loading states for iframes
            const iframes = document.querySelectorAll('.chart-embed iframe');
            iframes.forEach(iframe => {
                const container = iframe.parentElement;
                container.classList.add('loading');
                
                iframe.addEventListener('load', function() {
                    container.classList.remove('loading');
                });
                
                iframe.addEventListener('error', function() {
                    container.classList.remove('loading');
                    container.innerHTML = '<div style="padding: 2rem; text-align: center; color: #dc3545;">Chyba při načítání grafu</div>';
                });
            });
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                document.getElementById('chartFilter').focus();
            }
        });
    </script>
</body>
</html>