nazev_projektu: LimeSurvey Datawrapper Integrace
cil_a_ucel: Automatizace procesu stahovani dat z LimeSurvey, jej<PERSON> transformace a generovani grafu v Datawrapper
cileova_platforma: Python
programovaci_jazyk: Python
vzhled_ui: Moderni, minimalistick<PERSON>, responzivn<PERSON>
klicove_funkcionality:
  - Vyber pruzkumu s filtrací
  - Import,vyčištění,anonymizování a validace CSV dat na základě výběru průzkumů pomocí limesurvey API
  - Načtení a mapovani struktury pruzkumu, mapping názvů otázek a odpovědí na názvy v grafech
  - Transformace CSV dat do long formatu
  - Automaticke prirazeni typu grafu, možnost více grafů k jedné otázce
  - Generovani grafu pomoci Datawrapper API včetně metadat
  - Export grafu do PNG, PDF a interaktivnich odkazu
nefunkcni_pozadavky:
  vykon: Zpracovani velkych datovych sad do 1 minuty
  bezpecnost: možnost anonymizace citlivych dat
  skalovatelnost: Podpora vice uzivatelu soucasne
  spolehlivost: 99.9% dostupnost
  pouzitelnost: Intuitivni UI s minimalni nutnosti manualu
externi_zavislosti:
  - LimeSurvey API
  - Datawrapper API
  - Pandas
  - PyQt5
konvence_pro_kodovani: UTF-8
sprava_verzi: Git
asistent_kodovani: cline
