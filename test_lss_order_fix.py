#!/usr/bin/env python3
"""
Test opravy LSS pořadí odpovědí
"""

import sys
import os

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_lss_order_fix():
    """Test opravy funkce _get_lss_answer_order"""
    print("🧪 Test opravy LSS pořadí odpovědí...")
    
    try:
        from data_transformer import _get_lss_answer_order
        
        # Test s existujícími daty
        long_path = "data/dotazniky.urad.online/827822/responses_long.csv"
        
        # Test otázek, které způsobovaly varování
        test_questions = [
            "G3Q00001",
            "G3Q00002", 
            "G3Q00004",
            "G4Q00001",
            "G4Q00004",
            "G4Q00005",
            "G5Q00004",
            "G6Q00001"  # Tato by měla mít answeroptions
        ]
        
        for question_code in test_questions:
            print(f"\n🔍 Testování {question_code}...")
            
            lss_order = _get_lss_answer_order(question_code, long_path)
            
            if lss_order:
                print(f"✅ Nalezeno pořadí: {lss_order[:3]}..." if len(lss_order) > 3 else f"✅ Nalezeno pořadí: {lss_order}")
                
                # Speciální kontrola pro G6Q00001 (měla by mít škálové odpovědi)
                if question_code == "G6Q00001":
                    expected_scale = ['rozhodně ano', 'spíše ano', 'spíše ne', 'rozhodně ne']
                    if any(exp in lss_order for exp in expected_scale):
                        print("✅ G6Q00001 má správné škálové pořadí!")
                    else:
                        print(f"⚠️  G6Q00001 nemá očekávané škálové pořadí: {lss_order}")
            else:
                print(f"❌ Žádné pořadí nenalezeno pro {question_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_lss_structure_reading():
    """Test čtení LSS struktury"""
    print("\n🧪 Test čtení LSS struktury...")
    
    try:
        import json
        
        lss_path = "data/dotazniky.urad.online/827822/structure.lss"
        
        if not os.path.exists(lss_path):
            print("⚠️  LSS soubor neexistuje")
            return True
        
        with open(lss_path, 'r', encoding='utf-8') as f:
            lss_structure = json.load(f)
        
        print(f"✅ LSS struktura načtena: {len(lss_structure.get('groups', []))} skupin")
        
        # Najdeme G6Q00001 a zkontrolujeme answeroptions
        for group in lss_structure.get('groups', []):
            for question in group.get('questions', []):
                if question.get('title') == 'G6Q00001':
                    properties = question.get('properties', {})
                    answeroptions = properties.get('answeroptions', {})
                    
                    if answeroptions:
                        print(f"✅ G6Q00001 má answeroptions: {len(answeroptions)} možností")
                        
                        # Zobrazíme první 3 možnosti s order
                        sorted_options = sorted(
                            answeroptions.items(),
                            key=lambda x: x[1].get('order', 999)
                        )
                        
                        for i, (key, opt) in enumerate(sorted_options[:3]):
                            print(f"   {opt.get('order', '?')}: {opt.get('answer', 'N/A')}")
                        
                        return True
                    else:
                        print("❌ G6Q00001 nemá answeroptions")
                        return False
        
        print("❌ G6Q00001 nenalezena v LSS struktuře")
        return False
        
    except Exception as e:
        print(f"❌ Chyba při čtení LSS: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test opravy LSS pořadí odpovědí")
    print("=" * 50)
    
    tests = [
        test_lss_structure_reading,
        test_lss_order_fix
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Oprava LSS pořadí je funkční!")
        print("\n🎯 Nyní:")
        print("   1. Spusť Menu 6 znovu pro regeneraci chart_data.json")
        print("   2. Zkontroluj, že varování 'Nerozpoznán škálový vzor' zmizela")
        print("   3. Spusť Menu 8 pro grafy s správným pořadím")
        return True
    else:
        print("❌ Oprava má problémy")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)