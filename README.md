# Limwrapp - AI-Enhanced <PERSON>eSurvey Wrapper 🚀

Limwrapp je pokročilý nástroj pro vizualizaci a analýzu dat z LimeSurvey s integrovanými AI funkcemi. Umožňuje generování grafů, export dat, správu metadat a AI-powered analýzy.

## 🎉 Nové AI funkce (Leden 2025)

- **🎨 AI WordCloud generování** - automatická analýza textových odpovědí
- **🤖 AI analýza textu** - sentiment analysis, extrakce témat
- **📊 AI návrhy parametrů** - optimalizace nastavení analýz
- **🎯 AI generování scénářů** - automatické plánování analýz
- **💰 Cost tracking** - monitoring nákladů na AI služby

## Základní informace o použití

1. Nainstalujte závislosti:
   ```bash
   pip install -r requirements.txt
   pip install -r requirements_ai.txt  # pro AI funkce
   ```
2. Nakonfigurujte AI (volitelné):
   ```bash
   cp config/ai_config.example.py config/ai_config.py
   # Nastavte OPENAI_API_KEY v .env
   ```
3. Spusťte aplikaci: `python src/main.py`
4. Pro detailní informace viz [dokumentace](docs/)

## Technologie

- Python 3.9+
- PyQt5 pro GUI
- LimeSurvey API
- Datawrapper API
- **OpenAI API** (pro AI funkce)
- **WordCloud, Matplotlib** (pro vizualizace)
- **Redis** (volitelné, pro caching)

## 📚 Dokumentace

- [AI Integration Guide](docs/AI_INTEGRATION.md) - kompletní průvodce AI funkcemi
- [AI Components Summary](docs/AI_COMPONENTS_SUMMARY.md) - přehled AI komponent
- [Plán rozšíření](docs/06_plan_rozsireni.md) - roadmapa vývoje
- [Stav vývoje](docs/05_stav_vyvoje.md) - aktuální stav projektu
